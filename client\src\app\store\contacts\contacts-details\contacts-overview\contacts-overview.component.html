<div class="p-3 w-full surface-card border-round shadow-1">
    <div class="card-heading mb-2 flex align-items-center justify-content-start gap-2">
        <h4 class="m-0 pl-3 left-border relative flex">Contact</h4>
        <p-button [label]="isEditMode ? 'Close' : 'Edit'" [icon]="!isEditMode ? 'pi pi-pencil' : ''" iconPos="right"
            class="ml-auto" [styleClass]="'w-5rem font-semibold px-3'" (click)="toggleEdit()" [rounded]="true" />
    </div>
    <div *ngIf="!isEditMode" [formGroup]="ContactsOverviewForm" class="p-fluid p-formgrid grid m-0">
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">person</span>
                    Name
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    {{ contactsDetails?.bp_full_name || '-'
                    }}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">person</span>
                    Account ID
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    {{contactsDetails?.account_id || '-'}}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">person</span>
                    Account
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    {{contactsDetails?.account_name || '-'}}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">badge</span>
                    Job Title
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    {{ contactsDetails?.job_title || "-" }}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">apartment</span>
                    Department
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    {{ getDepartmentLabel(contactsDetails?.business_department) || "-" }}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">map</span>
                    Country
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    {{ contactsDetails?.destination_location_country ?
                    getCountryName(contactsDetails.destination_location_country) : '-' }}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">location_on</span>
                    Business Address
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    {{contactsDetails?.address || '-'}}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">phone</span>
                    Phone
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    {{ contactsDetails?.phone_number || "-" }}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">smartphone</span>
                    Mobile
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    {{ contactsDetails?.mobile || "-" }}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">mail</span>
                    E-Mail
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    {{ contactsDetails?.email_address || "-" }}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">mail</span>
                    Emails Opt In
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    {{ getOptLabel(contactsDetails?.emails_opt_in) || "-" }}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">print</span>
                    Print Marketing Opt In
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    {{
                    getOptLabel(contactsDetails?.print_marketing_opt_in) ||
                    "-"
                    }}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">sms</span>
                    SMS Promotions Opt In
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    {{
                    getOptLabel(contactsDetails?.sms_promotions_opt_in) ||
                    "-"
                    }}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">how_to_reg</span>
                    Web Registered
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    <p-inputSwitch formControlName="web_registered" class="h-3rem w-full"
                        [readonly]="true"></p-inputSwitch>
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">perm_identity</span>
                    Contact ID
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    {{ contactsDetails?.person_id || "-" }}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">check_circle</span>
                    Status
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    {{ contactsDetails?.status ||
                    '-'
                    }}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">tune</span>
                    Communication Preference
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    {{
                    getComminicationLabel(
                    contactsDetails?.prfrd_comm_medium_type
                    ) || "-"
                    }}
                </div>
            </div>
        </div>
    </div>
    <form *ngIf="isEditMode" [formGroup]="ContactsOverviewForm">
        <div class="p-fluid p-formgrid grid m-0">
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">person</span>
                        First Name
                        <span class="text-red-500">*</span>
                    </label>
                    <input pInputText id="first_name" type="text" formControlName="first_name" placeholder="First Name"
                        [ngClass]="{ 'is-invalid': submitted && f['first_name'].errors }"
                        class="h-3rem w-full p-disabled" [readonly]="true" />
                    <div *ngIf="submitted && f['first_name'].errors" class="invalid-feedback">
                        <div *ngIf="
                submitted &&
                f['first_name'].errors &&
                f['first_name'].errors['required']
              ">
                            First Name is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">person</span>
                        Last Name
                        <span class="text-red-500">*</span>
                    </label>
                    <input pInputText id="last_name" type="text" formControlName="last_name" placeholder="Last Name"
                        [ngClass]="{ 'is-invalid': submitted && f['last_name'].errors }"
                        class="h-3rem w-full p-disabled" [readonly]="true" />
                    <div *ngIf="submitted && f['last_name'].errors" class="invalid-feedback">
                        <div *ngIf="
                submitted &&
                f['last_name'].errors &&
                f['last_name'].errors['required']
              ">
                            Last Name is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">badge</span>Job Title
                    </label>
                    <input pInputText id="job_title" type="text" formControlName="job_title" placeholder="Job Title"
                        class="h-3rem w-full p-disabled" [readonly]="true" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">apartment</span>Department
                    </label>
                    <p-dropdown [options]="cpDepartments" formControlName="business_department" optionLabel="name"
                        dataKey="value" optionValue="value" placeholder="Select Department"
                        [styleClass]="'h-3rem w-full p-disabled'" [readonly]="true">
                    </p-dropdown>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">map</span>Country<span
                            class="text-red-500">*</span>
                    </label>
                    <p-dropdown [options]="countries" optionLabel="name" optionValue="isoCode"
                        [(ngModel)]="selectedCountry" [filter]="true" formControlName="destination_location_country"
                        [styleClass]="'h-3rem w-full p-disabled'" placeholder="Select Country"
                        [ngClass]="{ 'is-invalid': submitted && f['destination_location_country'].errors }"
                        [readonly]="true">
                    </p-dropdown>
                    <div *ngIf="submitted && f['destination_location_country'].errors" class="invalid-feedback">
                        <div *ngIf="
                submitted &&
                f['destination_location_country'].errors &&
                f['destination_location_country'].errors['required']
              ">
                            Country is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">phone</span>Phone
                    </label>
                    <input pInputText id="phone_number" type="text" formControlName="phone_number" placeholder="Phone"
                        class="h-3rem w-full p-disabled" [readonly]="true" />
                    <div
                        *ngIf="ContactsOverviewForm.get('phone_number')?.touched && ContactsOverviewForm.get('phone_number')?.invalid">
                        <div *ngIf="ContactsOverviewForm.get('phone_number')?.errors?.['pattern']" class="p-error">
                            Please enter a valid Phone number.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">smartphone</span>Mobile
                        <span class="text-red-500">*</span>
                    </label>
                    <input pInputText id="mobile" type="text" formControlName="mobile" placeholder="Mobile"
                        class="h-3rem w-full p-disabled" [ngClass]="{ 'is-invalid': submitted && f['mobile'].errors }"
                        [readonly]="true" />
                    <div *ngIf="submitted && f['mobile'].errors" class="invalid-feedback">
                        <div *ngIf="f['mobile'].errors['required']">
                            Mobile is required.
                        </div>
                    </div>
                    <div
                        *ngIf="ContactsOverviewForm.get('mobile')?.touched && ContactsOverviewForm.get('mobile')?.invalid">
                        <div *ngIf="ContactsOverviewForm.get('mobile')?.errors?.['pattern']" class="p-error">
                            Please enter a valid Mobile number.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">mail</span>
                        E-Mail
                        <span class="text-red-500">*</span>
                    </label>
                    <input pInputText id="email_address" type="text" formControlName="email_address"
                        placeholder="Email Address" [ngClass]="{ 'is-invalid': submitted && f['email_address'].errors }"
                        class="h-3rem w-full p-disabled" [readonly]="true" />
                    <div *ngIf="submitted && f['email_address'].errors" class="invalid-feedback">
                        <div *ngIf="f['email_address'].errors['required']">
                            Email is required.
                        </div>
                        <div *ngIf="f['email_address'].errors['email_address']">
                            Email is invalid.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">mail</span>Emails Opt In
                    </label>
                    <p-dropdown [options]="optOptions" formControlName="emails_opt_in" placeholder="Select Emails Opt"
                        [styleClass]="'h-3rem w-full'"
                        [readonly]="!(person_id.startsWith('HY') && webRegisteredValue === false)">
                    </p-dropdown>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">print</span>Print Marketing Opt In
                    </label>
                    <p-dropdown [options]="optOptions" formControlName="print_marketing_opt_in"
                        placeholder="Select Marketing Opt" [styleClass]="'h-3rem w-full p-disabled'" [readonly]="true">
                    </p-dropdown>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">sms</span>SMS Promotions Opt In
                    </label>
                    <p-dropdown [options]="optOptions" formControlName="sms_promotions_opt_in"
                        placeholder="Select Promotions Opt" [styleClass]="'h-3rem w-full p-disabled'" [readonly]="true">
                    </p-dropdown>
                </div>
            </div>
            <ng-container *ngIf="person_id?.startsWith('HY'); else readOnlyBlock">
                <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                    <div class="input-main">
                        <label class="flex align-items-center gap-1 mb-2 font-medium">
                            <span class="material-symbols-rounded text-2xl text-600">how_to_reg</span>Web Registered
                        </label>
                        <p-inputSwitch formControlName="web_registered" class="h-3rem w-full"></p-inputSwitch>
                    </div>
                </div>
            </ng-container>

            <ng-template #readOnlyBlock>
                <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                    <div class="input-main">
                        <label class="flex align-items-center gap-1 mb-2 font-medium">
                            <span class="material-symbols-rounded text-2xl text-600">how_to_reg</span>Web Registered
                        </label>
                        <p-inputSwitch formControlName="web_registered" class="h-3rem w-full"
                            [readonly]="true"></p-inputSwitch>
                    </div>
                </div>
            </ng-template>

            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">tune</span>Communication Preference
                    </label>
                    <p-dropdown [options]="communication_type" id="prfrd_comm_medium_type"
                        formControlName="prfrd_comm_medium_type" placeholder="Select Preference"
                        [styleClass]="'h-3rem w-full'" [readonly]="!person_id.startsWith('HY')">
                    </p-dropdown>
                </div>
            </div>
        </div>
        <div class="flex align-items-center p-3 gap-3 mt-1">
            <button pButton type="submit" label="Save" class="p-button-rounded justify-content-center w-9rem h-3rem"
                (click)="onSubmit()"></button>
        </div>
    </form>
</div>
<div class="p-3 w-full surface-card border-round shadow-1 mt-5" [formGroup]="ContactsWebForm">
    <div class="card-heading mb-2 flex align-items-center justify-content-start gap-2">
        <h4 class="m-0 pl-3 left-border relative flex">Web Details</h4>
    </div>
    <div class="p-fluid p-formgrid grid m-0">
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">badge</span>
                    Web User ID
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    {{ contactsDetails?.web_user_id || "-" }}
                </div>
            </div>
        </div>

        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">access_time</span>
                    Last Login
                </label>
                <div class="readonly-field font-medium text-700 p-2">
                    {{ contactsDetails?.last_login || "-" }}
                </div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">supervisor_account</span>
                    Admin User
                </label>
                <p-inputSwitch formControlName="admin_user" class="h-3rem w-full" [readonly]="true"></p-inputSwitch>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">shopping_cart</span>
                    PunchOut User
                </label>
                <p-inputSwitch formControlName="punch_out_user" class="h-3rem w-full" [readonly]="true"></p-inputSwitch>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">list_alt</span>
                    <a target="_blank"
                        href="https://dev-americanhotel.cfapps.us10-001.hana.ondemand.com/#/store/order-guide"
                        class="text-blue-600 underline hover:text-blue-800">Order Guides</a>
                </label>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <!-- Icon + Label -->
                <div class="flex items-center gap-2 mb-2">
                    <span class="material-symbols-rounded text-2xl text-primary">link</span>
                    <span class="text-800 font-semibold">ASM Links</span>
                </div>

                <!-- Bullet Links -->
                <ul class="list-disc pl-5 text-blue-600 space-y-1">
                    <li>
                        <a href="https://dev-americanhotel.cfapps.us10-001.hana.ondemand.com" target="_blank"
                            rel="noopener noreferrer" class="hover:underline">
                            American Hotel Register
                        </a>
                    </li>
                    <li>
                        <a href="https://dev-americaneducationsupplies.cfapps.us10-001.hana.ondemand.com"
                            target="_blank" rel="noopener noreferrer" class="hover:underline">
                            MyAmtex
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>