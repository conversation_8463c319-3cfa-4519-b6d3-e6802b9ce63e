{"version": 3, "file": "applyInternationalSeparatorStyle.test.js", "names": ["applyInternationalSeparatorStyle", "describe", "it", "should", "equal"], "sources": ["../../source/helpers/applyInternationalSeparatorStyle.test.js"], "sourcesContent": ["import applyInternationalSeparatorStyle from './applyInternationalSeparatorStyle.js'\r\n\r\ndescribe('applyInternationalSeparatorStyle', () => {\r\n\tit('should change Google\\'s international format style', () => {\r\n\t\tapplyInternationalSeparatorStyle('(xxx) xxx-xx-xx').should.equal('xxx xxx xx xx')\r\n\t\tapplyInternationalSeparatorStyle('(xxx)xxx').should.equal('xxx xxx')\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,gCAAP,MAA6C,uCAA7C;AAEAC,QAAQ,CAAC,kCAAD,EAAqC,YAAM;EAClDC,EAAE,CAAC,oDAAD,EAAuD,YAAM;IAC9DF,gCAAgC,CAAC,iBAAD,CAAhC,CAAoDG,MAApD,CAA2DC,KAA3D,CAAiE,eAAjE;IACAJ,gCAAgC,CAAC,UAAD,CAAhC,CAA6CG,MAA7C,CAAoDC,KAApD,CAA0D,SAA1D;EACA,CAHC,CAAF;AAIA,CALO,CAAR"}