{"version": 3, "file": "findPhoneNumbersInitialImplementation.js", "names": ["PLUS_CHARS", "VALID_PUNCTUATION", "VALID_DIGITS", "WHITESPACE", "parse", "VALID_PHONE_NUMBER_WITH_EXTENSION", "createExtensionPattern", "parsePreCandidate", "isValidPreCandidate", "isValidCandidate", "EXTN_PATTERNS_FOR_PARSING", "WHITESPACE_IN_THE_BEGINNING_PATTERN", "RegExp", "PUNCTUATION_IN_THE_END_PATTERN", "VALID_PRECEDING_CHARACTER_PATTERN", "findPhoneNumbers", "text", "options", "metadata", "undefined", "search", "PhoneNumberSearch", "phones", "hasNext", "push", "next", "searchPhoneNumbers", "Symbol", "iterator", "done", "value", "state", "regexp", "matches", "exec", "number", "startsAt", "index", "replace", "length", "result", "parseCandidate", "find", "extended", "phone", "endsAt", "last_match", "Error"], "sources": ["../../source/legacy/findPhoneNumbersInitialImplementation.js"], "sourcesContent": ["// This is a legacy function.\r\n// Use `findNumbers()` instead.\r\n\r\nimport {\r\n\tPLUS_CHARS,\r\n\tVALID_PUNCTUATION,\r\n\tVALID_DIGITS,\r\n\tWHITESPACE\r\n} from '../constants.js'\r\n\r\nimport parse from '../parse.js'\r\nimport { VALID_PHONE_NUMBER_WITH_EXTENSION } from '../helpers/isViablePhoneNumber.js'\r\nimport createExtensionPattern from '../helpers/extension/createExtensionPattern.js'\r\n\r\nimport parsePreCandidate from '../findNumbers/parsePreCandidate.js'\r\nimport isValidPreCandidate from '../findNumbers/isValidPreCandidate.js'\r\nimport isValidCandidate from '../findNumbers/isValidCandidate.js'\r\n\r\n/**\r\n * Regexp of all possible ways to write extensions, for use when parsing. This\r\n * will be run as a case-insensitive regexp match. Wide character versions are\r\n * also provided after each ASCII version. There are three regular expressions\r\n * here. The first covers RFC 3966 format, where the extension is added using\r\n * ';ext='. The second more generic one starts with optional white space and\r\n * ends with an optional full stop (.), followed by zero or more spaces/tabs\r\n * /commas and then the numbers themselves. The other one covers the special\r\n * case of American numbers where the extension is written with a hash at the\r\n * end, such as '- 503#'. Note that the only capturing groups should be around\r\n * the digits that you want to capture as part of the extension, or else parsing\r\n * will fail! We allow two options for representing the accented o - the\r\n * character itself, and one in the unicode decomposed form with the combining\r\n * acute accent.\r\n */\r\nexport const EXTN_PATTERNS_FOR_PARSING = createExtensionPattern('parsing')\r\n\r\nconst WHITESPACE_IN_THE_BEGINNING_PATTERN = new RegExp('^[' + WHITESPACE + ']+')\r\nconst PUNCTUATION_IN_THE_END_PATTERN = new RegExp('[' + VALID_PUNCTUATION + ']+$')\r\n\r\n// // Regular expression for getting opening brackets for a valid number\r\n// // found using `PHONE_NUMBER_START_PATTERN` for prepending those brackets to the number.\r\n// const BEFORE_NUMBER_DIGITS_PUNCTUATION = new RegExp('[' + OPENING_BRACKETS + ']+' + '[' + WHITESPACE + ']*' + '$')\r\n\r\nconst VALID_PRECEDING_CHARACTER_PATTERN = /[^a-zA-Z0-9]/\r\n\r\nexport default function findPhoneNumbers(text, options, metadata) {\r\n\t/* istanbul ignore if */\r\n\tif (options === undefined) {\r\n\t\toptions = {}\r\n\t}\r\n\tconst search = new PhoneNumberSearch(text, options, metadata)\r\n\tconst phones = []\r\n\twhile (search.hasNext()) {\r\n\t\tphones.push(search.next())\r\n\t}\r\n\treturn phones\r\n}\r\n\r\n/**\r\n * @return ES6 `for ... of` iterator.\r\n */\r\nexport function searchPhoneNumbers(text, options, metadata) {\r\n\t/* istanbul ignore if */\r\n\tif (options === undefined) {\r\n\t\toptions = {}\r\n\t}\r\n\tconst search = new PhoneNumberSearch(text, options, metadata)\r\n\treturn  {\r\n\t\t[Symbol.iterator]() {\r\n\t\t\treturn {\r\n\t    \t\tnext: () => {\r\n\t    \t\t\tif (search.hasNext()) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tdone: false,\r\n\t\t\t\t\t\t\tvalue: search.next()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tdone: true\r\n\t\t\t\t\t}\r\n\t    \t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/**\r\n * Extracts a parseable phone number including any opening brackets, etc.\r\n * @param  {string} text - Input.\r\n * @return {object} `{ ?number, ?startsAt, ?endsAt }`.\r\n */\r\nexport class PhoneNumberSearch {\r\n\tconstructor(text, options, metadata) {\r\n\t\tthis.text = text\r\n\t\t// If assigning the `{}` default value is moved to the arguments above,\r\n\t\t// code coverage would decrease for some weird reason.\r\n\t\tthis.options = options || {}\r\n\t\tthis.metadata = metadata\r\n\r\n\t\t// Iteration tristate.\r\n\t\tthis.state = 'NOT_READY'\r\n\r\n\t\tthis.regexp = new RegExp(VALID_PHONE_NUMBER_WITH_EXTENSION, 'ig')\r\n\t}\r\n\r\n\tfind() {\r\n\t\tconst matches = this.regexp.exec(this.text)\r\n\t\tif (!matches) {\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\tlet number = matches[0]\r\n\t\tlet startsAt = matches.index\r\n\r\n\t\tnumber = number.replace(WHITESPACE_IN_THE_BEGINNING_PATTERN, '')\r\n\t\tstartsAt += matches[0].length - number.length\r\n\t\t// Fixes not parsing numbers with whitespace in the end.\r\n\t\t// Also fixes not parsing numbers with opening parentheses in the end.\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/252\r\n\t\tnumber = number.replace(PUNCTUATION_IN_THE_END_PATTERN, '')\r\n\r\n\t\tnumber = parsePreCandidate(number)\r\n\r\n\t\tconst result = this.parseCandidate(number, startsAt)\r\n\t\tif (result) {\r\n\t\t\treturn result\r\n\t\t}\r\n\r\n\t\t// Tail recursion.\r\n\t\t// Try the next one if this one is not a valid phone number.\r\n\t\treturn this.find()\r\n\t}\r\n\r\n\tparseCandidate(number, startsAt) {\r\n\t\tif (!isValidPreCandidate(number, startsAt, this.text)) {\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\t// Don't parse phone numbers which are non-phone numbers\r\n\t\t// due to being part of something else (e.g. a UUID).\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/213\r\n\t\t// Copy-pasted from Google's `PhoneNumberMatcher.js` (`.parseAndValidate()`).\r\n\t\tif (!isValidCandidate(number, startsAt, this.text, this.options.extended ? 'POSSIBLE' : 'VALID')) {\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\t// // Prepend any opening brackets left behind by the\r\n\t\t// // `PHONE_NUMBER_START_PATTERN` regexp.\r\n\t\t// const text_before_number = text.slice(this.searching_from, startsAt)\r\n\t\t// const full_number_starts_at = text_before_number.search(BEFORE_NUMBER_DIGITS_PUNCTUATION)\r\n\t\t// if (full_number_starts_at >= 0)\r\n\t\t// {\r\n\t\t// \tnumber   = text_before_number.slice(full_number_starts_at) + number\r\n\t\t// \tstartsAt = full_number_starts_at\r\n\t\t// }\r\n\t\t//\r\n\t\t// this.searching_from = matches.lastIndex\r\n\r\n\t\tconst result = parse(number, this.options, this.metadata)\r\n\t\tif (!result.phone) {\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\tresult.startsAt = startsAt\r\n\t\tresult.endsAt = startsAt + number.length\r\n\t\treturn result\r\n\t}\r\n\r\n\thasNext() {\r\n\t\tif (this.state === 'NOT_READY') {\r\n\t\t\tthis.last_match = this.find()\r\n\t\t\tif (this.last_match) {\r\n\t\t\t\tthis.state = 'READY'\r\n\t\t\t} else {\r\n\t\t\t\tthis.state = 'DONE'\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn this.state === 'READY'\r\n\t}\r\n\r\n\tnext() {\r\n\t\t// Check the state and find the next match as a side-effect if necessary.\r\n\t\tif (!this.hasNext()) {\r\n\t\t\tthrow new Error('No next element')\r\n\t\t}\r\n\t\t// Don't retain that memory any longer than necessary.\r\n\t\tconst result = this.last_match\r\n\t\tthis.last_match = null\r\n\t\tthis.state = 'NOT_READY'\r\n\t\treturn result\r\n\t}\r\n}"], "mappings": ";;;;;;;;AAAA;AACA;AAEA,SACCA,UADD,EAECC,iBAFD,EAGCC,YAHD,EAICC,UAJD,QAKO,iBALP;AAOA,OAAOC,KAAP,MAAkB,aAAlB;AACA,SAASC,iCAAT,QAAkD,mCAAlD;AACA,OAAOC,sBAAP,MAAmC,gDAAnC;AAEA,OAAOC,iBAAP,MAA8B,qCAA9B;AACA,OAAOC,mBAAP,MAAgC,uCAAhC;AACA,OAAOC,gBAAP,MAA6B,oCAA7B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,IAAMC,yBAAyB,GAAGJ,sBAAsB,CAAC,SAAD,CAAxD;AAEP,IAAMK,mCAAmC,GAAG,IAAIC,MAAJ,CAAW,OAAOT,UAAP,GAAoB,IAA/B,CAA5C;AACA,IAAMU,8BAA8B,GAAG,IAAID,MAAJ,CAAW,MAAMX,iBAAN,GAA0B,KAArC,CAAvC,C,CAEA;AACA;AACA;;AAEA,IAAMa,iCAAiC,GAAG,cAA1C;AAEA,eAAe,SAASC,gBAAT,CAA0BC,IAA1B,EAAgCC,OAAhC,EAAyCC,QAAzC,EAAmD;EACjE;EACA,IAAID,OAAO,KAAKE,SAAhB,EAA2B;IAC1BF,OAAO,GAAG,EAAV;EACA;;EACD,IAAMG,MAAM,GAAG,IAAIC,iBAAJ,CAAsBL,IAAtB,EAA4BC,OAA5B,EAAqCC,QAArC,CAAf;EACA,IAAMI,MAAM,GAAG,EAAf;;EACA,OAAOF,MAAM,CAACG,OAAP,EAAP,EAAyB;IACxBD,MAAM,CAACE,IAAP,CAAYJ,MAAM,CAACK,IAAP,EAAZ;EACA;;EACD,OAAOH,MAAP;AACA;AAED;AACA;AACA;;AACA,OAAO,SAASI,kBAAT,CAA4BV,IAA5B,EAAkCC,OAAlC,EAA2CC,QAA3C,EAAqD;EAC3D;EACA,IAAID,OAAO,KAAKE,SAAhB,EAA2B;IAC1BF,OAAO,GAAG,EAAV;EACA;;EACD,IAAMG,MAAM,GAAG,IAAIC,iBAAJ,CAAsBL,IAAtB,EAA4BC,OAA5B,EAAqCC,QAArC,CAAf;EACA,2BACES,MAAM,CAACC,QADT,cACqB;IACnB,OAAO;MACHH,IAAI,EAAE,gBAAM;QACX,IAAIL,MAAM,CAACG,OAAP,EAAJ,EAAsB;UACxB,OAAO;YACNM,IAAI,EAAE,KADA;YAENC,KAAK,EAAEV,MAAM,CAACK,IAAP;UAFD,CAAP;QAIA;;QACD,OAAO;UACNI,IAAI,EAAE;QADA,CAAP;MAGG;IAXE,CAAP;EAaA,CAfF;AAiBA;AAED;AACA;AACA;AACA;AACA;;AACA,WAAaR,iBAAb;EACC,2BAAYL,IAAZ,EAAkBC,OAAlB,EAA2BC,QAA3B,EAAqC;IAAA;;IACpC,KAAKF,IAAL,GAAYA,IAAZ,CADoC,CAEpC;IACA;;IACA,KAAKC,OAAL,GAAeA,OAAO,IAAI,EAA1B;IACA,KAAKC,QAAL,GAAgBA,QAAhB,CALoC,CAOpC;;IACA,KAAKa,KAAL,GAAa,WAAb;IAEA,KAAKC,MAAL,GAAc,IAAIpB,MAAJ,CAAWP,iCAAX,EAA8C,IAA9C,CAAd;EACA;;EAZF;IAAA;IAAA,OAcC,gBAAO;MACN,IAAM4B,OAAO,GAAG,KAAKD,MAAL,CAAYE,IAAZ,CAAiB,KAAKlB,IAAtB,CAAhB;;MACA,IAAI,CAACiB,OAAL,EAAc;QACb;MACA;;MAED,IAAIE,MAAM,GAAGF,OAAO,CAAC,CAAD,CAApB;MACA,IAAIG,QAAQ,GAAGH,OAAO,CAACI,KAAvB;MAEAF,MAAM,GAAGA,MAAM,CAACG,OAAP,CAAe3B,mCAAf,EAAoD,EAApD,CAAT;MACAyB,QAAQ,IAAIH,OAAO,CAAC,CAAD,CAAP,CAAWM,MAAX,GAAoBJ,MAAM,CAACI,MAAvC,CAVM,CAWN;MACA;MACA;;MACAJ,MAAM,GAAGA,MAAM,CAACG,OAAP,CAAezB,8BAAf,EAA+C,EAA/C,CAAT;MAEAsB,MAAM,GAAG5B,iBAAiB,CAAC4B,MAAD,CAA1B;MAEA,IAAMK,MAAM,GAAG,KAAKC,cAAL,CAAoBN,MAApB,EAA4BC,QAA5B,CAAf;;MACA,IAAII,MAAJ,EAAY;QACX,OAAOA,MAAP;MACA,CArBK,CAuBN;MACA;;;MACA,OAAO,KAAKE,IAAL,EAAP;IACA;EAxCF;IAAA;IAAA,OA0CC,wBAAeP,MAAf,EAAuBC,QAAvB,EAAiC;MAChC,IAAI,CAAC5B,mBAAmB,CAAC2B,MAAD,EAASC,QAAT,EAAmB,KAAKpB,IAAxB,CAAxB,EAAuD;QACtD;MACA,CAH+B,CAKhC;MACA;MACA;MACA;;;MACA,IAAI,CAACP,gBAAgB,CAAC0B,MAAD,EAASC,QAAT,EAAmB,KAAKpB,IAAxB,EAA8B,KAAKC,OAAL,CAAa0B,QAAb,GAAwB,UAAxB,GAAqC,OAAnE,CAArB,EAAkG;QACjG;MACA,CAX+B,CAahC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;;MAEA,IAAMH,MAAM,GAAGpC,KAAK,CAAC+B,MAAD,EAAS,KAAKlB,OAAd,EAAuB,KAAKC,QAA5B,CAApB;;MACA,IAAI,CAACsB,MAAM,CAACI,KAAZ,EAAmB;QAClB;MACA;;MAEDJ,MAAM,CAACJ,QAAP,GAAkBA,QAAlB;MACAI,MAAM,CAACK,MAAP,GAAgBT,QAAQ,GAAGD,MAAM,CAACI,MAAlC;MACA,OAAOC,MAAP;IACA;EA3EF;IAAA;IAAA,OA6EC,mBAAU;MACT,IAAI,KAAKT,KAAL,KAAe,WAAnB,EAAgC;QAC/B,KAAKe,UAAL,GAAkB,KAAKJ,IAAL,EAAlB;;QACA,IAAI,KAAKI,UAAT,EAAqB;UACpB,KAAKf,KAAL,GAAa,OAAb;QACA,CAFD,MAEO;UACN,KAAKA,KAAL,GAAa,MAAb;QACA;MACD;;MACD,OAAO,KAAKA,KAAL,KAAe,OAAtB;IACA;EAvFF;IAAA;IAAA,OAyFC,gBAAO;MACN;MACA,IAAI,CAAC,KAAKR,OAAL,EAAL,EAAqB;QACpB,MAAM,IAAIwB,KAAJ,CAAU,iBAAV,CAAN;MACA,CAJK,CAKN;;;MACA,IAAMP,MAAM,GAAG,KAAKM,UAApB;MACA,KAAKA,UAAL,GAAkB,IAAlB;MACA,KAAKf,KAAL,GAAa,WAAb;MACA,OAAOS,MAAP;IACA;EAnGF;;EAAA;AAAA"}