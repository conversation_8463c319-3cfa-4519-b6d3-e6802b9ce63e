import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { ContactsService } from '../../contacts.service';
import { ProspectsService } from 'src/app/store/prospects/prospects.service';
import { MessageService } from 'primeng/api';
import { Router } from '@angular/router';
import { Country, State } from 'country-state-city';

@Component({
  selector: 'app-contacts-overview',
  templateUrl: './contacts-overview.component.html',
  styleUrl: './contacts-overview.component.scss',
})
export class ContactsOverviewComponent implements OnInit {
  private ngUnsubscribe = new Subject<void>();
  public contactsDetails: any = null;
  public ContactsOverviewForm: FormGroup = this.formBuilder.group({
    first_name: ['', [Validators.required]],
    last_name: ['', [Validators.required]],
    job_title: [''],
    business_department: [''],
    destination_location_country: ['', [Validators.required]],
    phone_number: ['', [Validators.pattern(/^[\d\+\-\s]*$/)]],
    mobile: ['', [Validators.required, Validators.pattern(/^[\d\+\-\s]*$/)]],
    email_address: ['', [Validators.required, Validators.email]],
    emails_opt_in: [''],
    print_marketing_opt_in: [''],
    sms_promotions_opt_in: [''],
    web_registered: [''],
    prfrd_comm_medium_type: [''],
  });

  public ContactsWebForm: FormGroup = this.formBuilder.group({
    web_user_id: [''],
    last_login: [''],
    admin_user: [''],
    punch_out_user: [''],
  });

  public submitted = false;
  public saving = false;
  public existingContact: any;
  public existingContactWeb: any;
  public contact_id: string = '';
  public person_id: string = '';
  public editid: string = '';
  public document_id: string = '';
  public isEditMode = false;
  public cpDepartments: { name: string; value: string }[] = [];
  public communication_type: { label: string; value: string }[] = [];
  public countries: any[] = [];
  public selectedCountry: string = '';
  public webRegisteredValue = false;
  public personIdStartsWithHY = false;
  public optOptions = [
    { label: 'Yes', value: true },
    { label: 'No', value: false },
    { label: 'Unselected', value: null },
  ];

  constructor(
    private formBuilder: FormBuilder,
    private contactsservice: ContactsService,
    private prospectsservice: ProspectsService,
    private messageservice: MessageService,
    private router: Router
  ) {}

  ngOnInit(): void {
    setTimeout(() => {
      const successMessage = sessionStorage.getItem('contactMessage');
      if (successMessage) {
        this.messageservice.add({
          severity: 'success',
          detail: successMessage,
        });
        sessionStorage.removeItem('contactMessage');
      }
    }, 100);
    this.loadCountries();
    this.loadDepartment();
    this.loadCommunicationType();
    this.contactsservice.contact
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((response: any) => {
        if (!response?.business_partner_person) return;
        this.contact_id = response?.bp_company_id;
        this.person_id = response?.bp_person_id;
        this.document_id = response?.documentId;
        const address = response?.business_partner_person?.addresses?.[0] || {};
        const phoneNumbers = address?.phone_numbers || [];
        const formatNumberByType = (type: string): string => {
          const entry = (address?.phone_numbers ?? []).find(
            (p: any) => String(p.phone_number_type) === type
          );
          const phone = entry?.phone_number;
          if (!phone) {
            return '-'; // no number → dash
          }
          return this.prospectsservice.getDialCode(
            entry.destination_location_country,
            phone
          );
        };

        this.contactsDetails = {
          ...address,
          address: [
            address?.house_number,
            address?.street_name,
            address?.city_name,
            address?.region,
            address?.country,
            address?.postal_code,
          ]
            .filter(Boolean)
            .join(', '),
          updated_id: response?.documentId,
          bp_full_name:
            (response?.business_partner_person?.first_name || '') +
            ' ' +
            (response?.business_partner_person?.last_name || ''),
          first_name: response?.business_partner_person?.first_name,
          last_name: response?.business_partner_person?.last_name,
          email_address: address?.emails?.[0]?.email_address,
          country_phone_number: phoneNumbers.find(
            (item: any) => String(item.phone_number_type) === '1'
          )?.phone_number,
          phone_number: formatNumberByType('1'),
          country_mobile: phoneNumbers.find(
            (item: any) => String(item.phone_number_type) === '3'
          )?.phone_number,
          mobile: formatNumberByType('3'),
          destination_location_country: phoneNumbers.find(
            (item: any) => String(item.phone_number_type) === '3'
          )?.destination_location_country,
          account_id: response?.bp_company_id,
          account_name: response?.business_partner_company?.bp_full_name,
          job_title: response?.business_partner_person?.bp_extension?.job_title,
          business_department:
            response?.person_func_and_dept?.contact_person_department,
          prfrd_comm_medium_type: address?.prfrd_comm_medium_type,
          emails_opt_in:
            response?.business_partner_person?.bp_extension?.emails_opt_in,
          print_marketing_opt_in:
            response?.business_partner_person?.bp_extension
              ?.print_marketing_opt_in,
          sms_promotions_opt_in:
            response?.business_partner_person?.bp_extension
              ?.sms_promotions_opt_in,
          last_login:
            response?.business_partner_person?.bp_extension?.last_login,
          web_user_id:
            response?.business_partner_person?.bp_extension?.web_user_id,
          punch_out_user: response?.business_partner_person?.bp_extension
            ?.punch_out_user
            ? true
            : false,
          admin_user: response?.business_partner_person?.bp_extension
            ?.admin_user
            ? true
            : false,
          web_registered_value: response?.business_partner_person?.bp_extension
            ?.web_registered
            ? 'Yes'
            : '-',
          web_registered: response?.business_partner_person?.bp_extension
            ?.web_registered
            ? true
            : false,
          status: response?.business_partner_person?.is_marked_for_archiving
            ? 'Obsolete'
            : 'Active',
          person_id: response?.bp_person_id,
        };
        if (this.contactsDetails) {
          this.fetchContactData(this.contactsDetails);
        }
      });

    const webRegisteredControl =
      this.ContactsOverviewForm.get('web_registered');
    this.webRegisteredValue = webRegisteredControl?.value;

    webRegisteredControl?.valueChanges.subscribe((value) => {
      this.webRegisteredValue = value;
    });
  }

  fetchContactData(contact: any) {
    this.existingContact = {
      first_name: contact.first_name,
      last_name: contact.last_name,
      email_address: contact.email_address,
      phone_number: contact.country_phone_number,
      mobile: contact.country_mobile,
      destination_location_country: contact.destination_location_country,
      job_title: contact.job_title,
      business_department: contact.business_department,
      web_registered: contact.web_registered,
      emails_opt_in: contact.emails_opt_in,
      print_marketing_opt_in: contact.print_marketing_opt_in,
      sms_promotions_opt_in: contact.sms_promotions_opt_in,
      prfrd_comm_medium_type: contact.prfrd_comm_medium_type,
    };

    this.existingContactWeb = {
      web_user_id: contact.web_user_id,
      last_login: contact.last_login,
      admin_user: contact.admin_user,
      punch_out_user: contact.punch_out_user,
    };

    this.editid = contact.updated_id;
    this.ContactsOverviewForm.patchValue(this.existingContact);
    this.ContactsWebForm.patchValue(this.existingContactWeb);
  }

  loadCountries() {
    const allCountries = Country.getAllCountries()
      .map((country: any) => ({
        name: country.name,
        isoCode: country.isoCode,
      }))
      .filter(
        (country) => State.getStatesOfCountry(country.isoCode).length > 0
      );

    const unitedStates = allCountries.find((c) => c.isoCode === 'US');
    const canada = allCountries.find((c) => c.isoCode === 'CA');
    const others = allCountries
      .filter((c) => c.isoCode !== 'US' && c.isoCode !== 'CA')
      .sort((a, b) => a.name.localeCompare(b.name)); // sort others alphabetically

    this.countries = [unitedStates, canada, ...others].filter(Boolean);
  }

  public loadDepartment(): void {
    this.contactsservice
      .getCPDepartment()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((response: any) => {
        if (response && response.data) {
          this.cpDepartments = response.data.map((item: any) => ({
            name: item.description,
            value: item.code,
          }));
        }
      });
  }

  getDepartmentLabel(value: string): string | undefined {
    return this.cpDepartments.find((opt) => opt.value === value)?.name;
  }

  public loadCommunicationType(): void {
    this.contactsservice
      .getCommunicationType()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((response: any) => {
        if (response && response.data) {
          this.communication_type = response.data.map((item: any) => ({
            label: item.description,
            value: item.code,
          }));
        }
      });
  }

  async onSubmit() {
    this.submitted = true;
    if (this.ContactsOverviewForm.invalid) {
      return;
    }
    this.saving = true;
    const value = { ...this.ContactsOverviewForm.value };

    const selectedcodewisecountry = this.countries.find(
      (c) => c.isoCode === this.selectedCountry
    );

    const data = {
      bp_id: this.contact_id,
      first_name: value?.first_name,
      last_name: value?.last_name,
      email_address: value?.email_address,
      phone_number: value?.phone_number,
      mobile: value?.mobile,
      job_title: value?.job_title,
      destination_location_country: selectedcodewisecountry?.isoCode,
      contact_person_department: value?.business_department,
      web_registered: this.person_id?.startsWith('HY') ? 'true' : 'false',
      emails_opt_in: value?.emails_opt_in,
      print_marketing_opt_in: value?.print_marketing_opt_in,
      sms_promotions_opt_in: value?.sms_promotions_opt_in,
      prfrd_comm_medium_type: value?.prfrd_comm_medium_type,
    };
    this.contactsservice
      .updateContact(this.editid, data)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (response: any) => {
          this.messageservice.add({
            severity: 'success',
            detail: 'Contact Updated successFully!',
          });
          this.isEditMode = false;
          this.contactsservice
            .getContactByID(this.document_id)
            .pipe(takeUntil(this.ngUnsubscribe))
            .subscribe();
        },
        error: (res: any) => {
          this.isEditMode = true;
          this.saving = false;
          this.messageservice.add({
            severity: 'error',
            detail: 'Error while processing your request.',
          });
        },
      });
  }

  getCountryName(code: string): string {
    return Country.getCountryByCode(code?.toUpperCase() ?? '')?.name ?? '-';
  }

  getOptLabel(value: string | boolean): string | undefined {
    return this.optOptions.find((opt) => opt.value === value)?.label;
  }

  getComminicationLabel(value: string): string | undefined {
    return this.communication_type.find((opt) => opt.value === value)?.label;
  }

  get f(): any {
    return this.ContactsOverviewForm.controls;
  }

  toggleEdit() {
    this.isEditMode = !this.isEditMode;
  }

  onReset(): void {
    this.submitted = false;
    this.ContactsOverviewForm.reset();
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
