{"version": 3, "file": "AsYouTypeState.js", "names": ["AsYouTypeState", "onCountryChange", "onCallingCodeChange", "country", "callingCode", "international", "missingPlus", "IDDPrefix", "undefined", "digits", "resetNationalSignificantNumber", "initCountryAndCallingCode", "nationalSignificantNumber", "getNationalDigits", "nationalSignificantNumberMatchesInput", "nationalPrefix", "carrierCode", "complexPrefixBeforeNationalSignificantNumber", "properties", "Object", "keys", "key", "setCountry", "setCallingCode", "nextDigits", "slice", "length"], "sources": ["../source/AsYouTypeState.js"], "sourcesContent": ["// This \"state\" object simply holds the state of the \"AsYouType\" parser:\r\n//\r\n// * `country?: string`\r\n// * `callingCode?: string`\r\n// * `digits: string`\r\n// * `international: boolean`\r\n// * `missingPlus: boolean`\r\n// * `IDDPrefix?: string`\r\n// * `carrierCode?: string`\r\n// * `nationalPrefix?: string`\r\n// * `nationalSignificantNumber?: string`\r\n// * `nationalSignificantNumberMatchesInput: boolean`\r\n// * `complexPrefixBeforeNationalSignificantNumber?: string`\r\n//\r\n// `state.country` and `state.callingCode` aren't required to be in sync.\r\n// For example, `state.country` could be `\"AR\"` and `state.callingCode` could be `undefined`.\r\n// So `state.country` and `state.callingCode` are totally independent.\r\n//\r\nexport default class AsYouTypeState {\r\n\tconstructor({ onCountryChange, onCallingCodeChange }) {\r\n\t\tthis.onCountryChange = onCountryChange\r\n\t\tthis.onCallingCodeChange = onCallingCodeChange\r\n\t}\r\n\r\n\treset({ country, callingCode }) {\r\n\t\tthis.international = false\r\n\t\tthis.missingPlus = false\r\n\t\tthis.IDDPrefix = undefined\r\n\t\tthis.callingCode = undefined\r\n\t\tthis.digits = ''\r\n\t\tthis.resetNationalSignificantNumber()\r\n\t\tthis.initCountryAndCallingCode(country, callingCode)\r\n\t}\r\n\r\n\tresetNationalSignificantNumber() {\r\n\t\tthis.nationalSignificantNumber = this.getNationalDigits()\r\n\t\tthis.nationalSignificantNumberMatchesInput = true\r\n\t\tthis.nationalPrefix = undefined\r\n\t\tthis.carrierCode = undefined\r\n\t\tthis.complexPrefixBeforeNationalSignificantNumber = undefined\r\n\t}\r\n\r\n\tupdate(properties) {\r\n\t\tfor (const key of Object.keys(properties)) {\r\n\t\t\tthis[key] = properties[key]\r\n\t\t}\r\n\t}\r\n\r\n\tinitCountryAndCallingCode(country, callingCode) {\r\n\t\tthis.setCountry(country)\r\n\t\tthis.setCallingCode(callingCode)\r\n\t}\r\n\r\n\tsetCountry(country) {\r\n\t\tthis.country = country\r\n\t\tthis.onCountryChange(country)\r\n\t}\r\n\r\n\tsetCallingCode(callingCode) {\r\n\t\tthis.callingCode = callingCode\r\n\t\tthis.onCallingCodeChange(callingCode, this.country)\r\n\t}\r\n\r\n\tstartInternationalNumber(country, callingCode) {\r\n\t\t// Prepend the `+` to parsed input.\r\n\t\tthis.international = true\r\n\t\t// If a default country was set then reset it\r\n\t\t// because an explicitly international phone\r\n\t\t// number is being entered.\r\n\t\tthis.initCountryAndCallingCode(country, callingCode)\r\n\t}\r\n\r\n\tappendDigits(nextDigits) {\r\n\t\tthis.digits += nextDigits\r\n\t}\r\n\r\n\tappendNationalSignificantNumberDigits(nextDigits) {\r\n\t\tthis.nationalSignificantNumber += nextDigits\r\n\t}\r\n\r\n\t/**\r\n\t * Returns the part of `this.digits` that corresponds to the national number.\r\n\t * Basically, all digits that have been input by the user, except for the\r\n\t * international prefix and the country calling code part\r\n\t * (if the number is an international one).\r\n\t * @return {string}\r\n\t */\r\n\tgetNationalDigits() {\r\n\t\tif (this.international) {\r\n\t\t\treturn this.digits.slice(\r\n\t\t\t\t(this.IDDPrefix ? this.IDDPrefix.length : 0) +\r\n\t\t\t\t(this.callingCode ? this.callingCode.length : 0)\r\n\t\t\t)\r\n\t\t}\r\n\t\treturn this.digits\r\n\t}\r\n\r\n\tgetDigitsWithoutInternationalPrefix() {\r\n\t\tif (this.international) {\r\n\t\t\tif (this.IDDPrefix) {\r\n\t\t\t\treturn this.digits.slice(this.IDDPrefix.length)\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn this.digits\r\n\t}\r\n}"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACqBA,c;EACpB,8BAAsD;IAAA,IAAxCC,eAAwC,QAAxCA,eAAwC;IAAA,IAAvBC,mBAAuB,QAAvBA,mBAAuB;;IAAA;;IACrD,KAAKD,eAAL,GAAuBA,eAAvB;IACA,KAAKC,mBAAL,GAA2BA,mBAA3B;EACA;;;;WAED,sBAAgC;MAAA,IAAxBC,OAAwB,SAAxBA,OAAwB;MAAA,IAAfC,WAAe,SAAfA,WAAe;MAC/B,KAAKC,aAAL,GAAqB,KAArB;MACA,KAAKC,WAAL,GAAmB,KAAnB;MACA,KAAKC,SAAL,GAAiBC,SAAjB;MACA,KAAKJ,WAAL,GAAmBI,SAAnB;MACA,KAAKC,MAAL,GAAc,EAAd;MACA,KAAKC,8BAAL;MACA,KAAKC,yBAAL,CAA+BR,OAA/B,EAAwCC,WAAxC;IACA;;;WAED,0CAAiC;MAChC,KAAKQ,yBAAL,GAAiC,KAAKC,iBAAL,EAAjC;MACA,KAAKC,qCAAL,GAA6C,IAA7C;MACA,KAAKC,cAAL,GAAsBP,SAAtB;MACA,KAAKQ,WAAL,GAAmBR,SAAnB;MACA,KAAKS,4CAAL,GAAoDT,SAApD;IACA;;;WAED,gBAAOU,UAAP,EAAmB;MAClB,gCAAkBC,MAAM,CAACC,IAAP,CAAYF,UAAZ,CAAlB,kCAA2C;QAAtC,IAAMG,GAAG,mBAAT;QACJ,KAAKA,GAAL,IAAYH,UAAU,CAACG,GAAD,CAAtB;MACA;IACD;;;WAED,mCAA0BlB,OAA1B,EAAmCC,WAAnC,EAAgD;MAC/C,KAAKkB,UAAL,CAAgBnB,OAAhB;MACA,KAAKoB,cAAL,CAAoBnB,WAApB;IACA;;;WAED,oBAAWD,OAAX,EAAoB;MACnB,KAAKA,OAAL,GAAeA,OAAf;MACA,KAAKF,eAAL,CAAqBE,OAArB;IACA;;;WAED,wBAAeC,WAAf,EAA4B;MAC3B,KAAKA,WAAL,GAAmBA,WAAnB;MACA,KAAKF,mBAAL,CAAyBE,WAAzB,EAAsC,KAAKD,OAA3C;IACA;;;WAED,kCAAyBA,OAAzB,EAAkCC,WAAlC,EAA+C;MAC9C;MACA,KAAKC,aAAL,GAAqB,IAArB,CAF8C,CAG9C;MACA;MACA;;MACA,KAAKM,yBAAL,CAA+BR,OAA/B,EAAwCC,WAAxC;IACA;;;WAED,sBAAaoB,UAAb,EAAyB;MACxB,KAAKf,MAAL,IAAee,UAAf;IACA;;;WAED,+CAAsCA,UAAtC,EAAkD;MACjD,KAAKZ,yBAAL,IAAkCY,UAAlC;IACA;IAED;AACD;AACA;AACA;AACA;AACA;AACA;;;;WACC,6BAAoB;MACnB,IAAI,KAAKnB,aAAT,EAAwB;QACvB,OAAO,KAAKI,MAAL,CAAYgB,KAAZ,CACN,CAAC,KAAKlB,SAAL,GAAiB,KAAKA,SAAL,CAAemB,MAAhC,GAAyC,CAA1C,KACC,KAAKtB,WAAL,GAAmB,KAAKA,WAAL,CAAiBsB,MAApC,GAA6C,CAD9C,CADM,CAAP;MAIA;;MACD,OAAO,KAAKjB,MAAZ;IACA;;;WAED,+CAAsC;MACrC,IAAI,KAAKJ,aAAT,EAAwB;QACvB,IAAI,KAAKE,SAAT,EAAoB;UACnB,OAAO,KAAKE,MAAL,CAAYgB,KAAZ,CAAkB,KAAKlB,SAAL,CAAemB,MAAjC,CAAP;QACA;MACD;;MACD,OAAO,KAAKjB,MAAZ;IACA;;;;;;SAtFmBT,c"}