import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CMS_APIContstant } from 'src/app/constants/api.constants';
import { AuthService } from 'src/app/core/authentication/auth.service';

@Injectable({
  providedIn: 'root',
})
export class OpportunitiesService {
  public opportunitySubject = new BehaviorSubject<any>(null);
  public opportunity = this.opportunitySubject.asObservable();

  constructor(private http: HttpClient, private authservice: AuthService) {}

  createOpportunity(data: any): Observable<any> {
    return this.http.post(
      `${CMS_APIContstant.CRM_OPPORTUNITY_REGISTRATION}`,
      data
    );
  }

  createFollowup(data: any): Observable<any> {
    return this.http.post(
      `${CMS_APIContstant.CRM_OPPORTUNITY_FOLLOW_UP_REGISTRATION}`,
      data
    );
  }

  createNote(data: any): Observable<any> {
    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {
      data,
    });
  }

  createEmployee(data: any): Observable<any> {
    return this.http.post(`${CMS_APIContstant.CRM_OPPORTUNITY_SALES_TEAM}`, {
      data,
    });
  }

  createHierarchy(data: any): Observable<any> {
    return this.http.post(`${CMS_APIContstant.CRM_OPPORTUNITY_FOLLOWUP}`, {
      data,
    });
  }

  createExistingContact(data: any): Observable<any> {
    return this.http.post(`${CMS_APIContstant.CRM_OPPORTUNITY_CONTACT}`, {
      data,
    });
  }

  createOpportunityContact(data: any): Observable<any> {
    return this.http.post(
      `${CMS_APIContstant.CRM_OPPORTUNITY_CONTACT_REGISTRATION}`,
      data
    );
  }

  updateOpportunity(Id: string, data: any): Observable<any> {
    return this.http.put(`${CMS_APIContstant.CRM_OPPORTUNITY}/${Id}`, {
      data,
    });
  }

  updateEmployee(Id: string, data: any): Observable<any> {
    return this.http.put(
      `${CMS_APIContstant.CRM_OPPORTUNITY_SALES_TEAM}/${Id}`,
      { data }
    );
  }

  updateNote(Id: string, data: any): Observable<any> {
    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {
      data,
    });
  }

  deleteNote(id: string) {
    return this.http.delete<any>(`${CMS_APIContstant.CRM_NOTE}/${id}`);
  }

  deleteFollowupItem(id: string) {
    return this.http.delete<any>(
      `${CMS_APIContstant.CRM_OPPORTUNITY_FOLLOWUP}/${id}`
    );
  }

  deleteEmployee(id: string) {
    return this.http.delete<any>(
      `${CMS_APIContstant.CRM_OPPORTUNITY_SALES_TEAM}/${id}`
    );
  }

  deleteHierarchy(id: string) {
    return this.http.delete<any>(
      `${CMS_APIContstant.CRM_OPPORTUNITY_FOLLOWUP}/${id}`
    );
  }

  deleteContact(id: string) {
    return this.http.delete<any>(
      `${CMS_APIContstant.CRM_OPPORTUNITY_CONTACT}/${id}`
    );
  }

  getOpportunityDropdownOptions(type: string) {
    const params = new HttpParams()
      .set('filters[is_active][$eq]', 'true')
      .set('filters[type][$eq]', type);

    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });
  }

  getPartners(params: any) {
    return this.http.get<any>(`${CMS_APIContstant.PARTNERS}`, { params }).pipe(
      map((response) =>
        (response?.data || []).map((item: any) => {
          return {
            bp_id: item?.bp_id || '',
            bp_full_name: item?.bp_full_name || '',
          };
        })
      )
    );
  }

  getHierarchy(parentid: string) {
    const params = new HttpParams()
      .set(
        'filters[business_transaction_document_relationship_role_code][$eq]',
        '14'
      )
      .set('filters[parent_object_id][$eq]', parentid);

    return this.http.get<any>(`${CMS_APIContstant.CRM_OPPORTUNITY_FOLLOWUP}`, {
      params,
    });
  }

  getHierarchyOpportunity(params: any) {
    return this.http
      .get<any>(`${CMS_APIContstant.CRM_OPPORTUNITY}`, { params })
      .pipe(
        map((response) =>
          (response?.data || []).map((item: any) => {
            return {
              opportunity_id: item?.opportunity_id || '',
              name: item?.name || '',
            };
          })
        )
      );
  }

  getOpportunities(
    page: number,
    pageSize: number,
    sortField?: string,
    sortOrder?: number,
    searchTerm?: string,
    filter?: string
  ): Observable<any[]> {
    let params = new HttpParams()
      .set('pagination[page]', page.toString())
      .set('pagination[pageSize]', pageSize.toString())
      .set(
        'fields',
        'opportunity_id,name,expected_revenue_amount,need_help,life_cycle_status_code,probability_percent,expected_revenue_start_date,expected_revenue_end_date,updatedAt,last_changed_by'
      )
      .set('populate[business_partner][fields][0]', 'bp_full_name')
      .set('populate[business_partner_owner][fields][0]', 'bp_full_name');

    if (sortField && sortOrder !== undefined) {
      const order = sortOrder === 1 ? 'asc' : 'desc';
      params = params.set('sort', `${sortField}:${order}`);
    } else {
      params = params.set('sort', 'updatedAt:desc');
    }

    if (searchTerm) {
      params = params.set(
        'filters[$or][0][opportunity_id][$containsi]',
        searchTerm
      );
      params = params.set('filters[$or][1][name][$containsi]', searchTerm);
      params = params.set(
        'filters[$or][2][business_partner][bp_full_name][$containsi]',
        searchTerm
      );
      params = params.set(
        'filters[$or][3][business_partner_owner][bp_full_name][$containsi]',
        searchTerm
      );
      params = params.set(
        'filters[$or][4][expected_revenue_amount][$containsi]',
        searchTerm
      );
      params = params.set(
        'filters[$or][5][probability_percent][$containsi]',
        searchTerm
      );
      params = params.set(
        'filters[$or][6][life_cycle_status_code][$containsi]',
        searchTerm
      );
    }

    if (filter) {
      if (filter === 'MO') {
        const email = this.authservice.getUserEmail();
        if (email) {
          params = params
            .set(
              `filters[$and][0][business_partner][customer][partner_functions][partner_function][$eq]`,
              'YI'
            )
            .set(
              `filters[$and][1][business_partner][customer][partner_functions][business_partner][addresses][emails][email_address][$containsi]`,
              email
            );
        }
      }
    }

    return this.http.get<any[]>(`${CMS_APIContstant.CRM_OPPORTUNITY}`, {
      params,
    });
  }

  getOpportunityByID(opportunityId: string) {
    const params = new HttpParams()
      .set('filters[opportunity_id][$eq]', opportunityId)
      .set('populate[business_partner][fields][0]', 'bp_full_name')
      .set('populate[business_partner][fields][1]', 'bp_id')
      .set(
        'populate[business_partner][populate][customer][populate][partner_functions][fields][0]',
        'partner_function'
      )
      .set(
        'populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][0]',
        'first_name'
      )
      .set(
        'populate[business_partner][populate][contact_companies][populate][business_partner_person][fields][1]',
        'last_name'
      )
      .set(
        'populate[business_partner][populate][addresses][populate][address_usages][fields][0]',
        'address_usage'
      )
      .set(
        'populate[business_partner][populate][addresses][fields][0]',
        'house_number'
      )
      .set(
        'populate[business_partner][populate][addresses][fields][1]',
        'street_name'
      )
      .set(
        'populate[business_partner][populate][addresses][fields][2]',
        'city_name'
      )
      .set(
        'populate[business_partner][populate][addresses][fields][3]',
        'region'
      )
      .set(
        'populate[business_partner][populate][addresses][fields][4]',
        'country'
      )
      .set(
        'populate[business_partner][populate][addresses][fields][5]',
        'postal_code'
      )
      .set(
        'populate[business_partner][populate][addresses][populate][emails][fields][0]',
        'email_address'
      )
      .set(
        'populate[business_partner][populate][addresses][populate][phone_numbers][fields][0]',
        'phone_number'
      )
      .set(
        'populate[business_partner][populate][addresses][populate][home_page_urls][fields][0]',
        'website_url'
      )
      .set('populate[business_partner_owner][fields][0]', 'bp_full_name')
      .set('populate[business_partner_contact][fields][0]', 'bp_full_name')
      .set(
        'populate[business_partner_contact][populate][contact_persons][fields][0]',
        'bp_company_id'
      )
      .set('populate[notes][populate]', '*')
      .set('populate[opportunity_followups][populate]', '*')
      .set(
        'populate[opportunity_contact_parties][populate][business_partner][populate][addresses][populate]',
        '*'
      )
      .set(
        'populate[opportunity_contact_parties][populate][business_partner][populate][contact_person_func_and_depts][fields][0]',
        'contact_person_department'
      )
      .set(
        'populate[opportunity_contact_parties][populate][business_partner][populate][contact_person_func_and_depts][fields][1]',
        'contact_person_department_name'
      )
      .set(
        'populate[opportunity_contact_parties][populate][business_partner][populate][contact_person_func_and_depts][fields][2]',
        'contact_person_function'
      )
      .set(
        'populate[opportunity_contact_parties][populate][business_partner][populate][contact_person_func_and_depts][fields][3]',
        'contact_person_function_name'
      )
      .set(
        'populate[opportunity_contact_parties][populate][business_partner][populate][contact_persons][fields][0]',
        'id'
      )
      .set('populate[opportunity_sales_team_parties][fields][0]', 'role_code')
      .set('populate[opportunity_sales_team_parties][fields][1]', 'party_id')
      .set(
        'populate[opportunity_sales_team_parties][fields][2]',
        'opportunity_id'
      )
      .set(
        'populate[opportunity_sales_team_parties][populate][business_partner][fields][0]',
        'first_name'
      )
      .set(
        'populate[opportunity_sales_team_parties][populate][business_partner][fields][1]',
        'last_name'
      )
      .set(
        'populate[opportunity_sales_team_parties][populate][business_partner][fields][2]',
        'bp_full_name'
      )
      .set(
        'populate[opportunity_sales_team_parties][populate][business_partner][populate][addresses][populate][emails][fields][0]',
        'email_address'
      );

    return this.http
      .get<any[]>(`${CMS_APIContstant.CRM_OPPORTUNITY}`, { params })
      .pipe(
        map((response: any) => {
          const opportunityDetails = response?.data[0] || null;
          this.opportunitySubject.next(opportunityDetails);
          return response;
        })
      );
  }

  getOpportunity(partnerId: string): Observable<{ data: any[]; meta: any }> {
    let params = new HttpParams()
      .set('filters[prospect_party_id][$eq]', partnerId)
      .set(
        'fields',
        'opportunity_id,name,expected_revenue_end_date,life_cycle_status_code,group_code,last_changed_by'
      )
      .set('populate[business_partner_owner][fields][0]', 'bp_full_name');

    return this.http.get<{ data: any[]; meta: any }>(
      `${CMS_APIContstant.CRM_OPPORTUNITY}`,
      { params }
    );
  }
}
