{"version": 3, "file": "PhoneNumberMatcher.test.js", "names": ["test", "text", "defaultCountry", "expectedNumbers", "nationalNumber", "matcher", "PhoneNumberMatcher", "v2", "metadata", "hasNext", "number", "next", "phoneNumber", "shift", "startsAt", "undefined", "should", "equal", "endsAt", "country", "length", "describe", "it", "expect", "leniency", "to"], "sources": ["../source/PhoneNumberMatcher.test.js"], "sourcesContent": ["import PhoneNumberMatcher from './PhoneNumberMatcher.js'\r\nimport metadata from '../metadata.min.json' assert { type: 'json' }\r\n\r\nfunction test(text, defaultCountry, expectedNumbers) {\r\n\tif (typeof expectedNumbers === 'string') {\r\n\t\texpectedNumbers = [{\r\n\t\t\tnationalNumber: expectedNumbers\r\n\t\t}]\r\n\t}\r\n\tconst matcher = new PhoneNumberMatcher(text, { defaultCountry, v2: true }, metadata)\r\n\twhile (matcher.hasNext()) {\r\n\t\tconst number = matcher.next()\r\n\t\tconst phoneNumber = expectedNumbers.shift()\r\n\t\tif (phoneNumber.startsAt !== undefined) {\r\n\t\t\tnumber.startsAt.should.equal(phoneNumber.startsAt)\r\n\t\t}\r\n\t\tif (phoneNumber.endsAt !== undefined) {\r\n\t\t\tnumber.endsAt.should.equal(phoneNumber.endsAt)\r\n\t\t}\r\n\t\tnumber.number.country.should.equal(phoneNumber.country || defaultCountry)\r\n\t\tnumber.number.nationalNumber.should.equal(phoneNumber.nationalNumber)\r\n\t}\r\n\texpectedNumbers.length.should.equal(0)\r\n}\r\n\r\ndescribe('PhoneNumberMatcher', () => {\r\n\tit('should find phone numbers', () => {\r\n\t\ttest(\r\n\t\t\t'The number is +7 (800) 555-35-35 and not (************* as written in the document.',\r\n\t\t\t'US',\r\n\t\t\t[{\r\n\t\t\t\tcountry: 'RU',\r\n\t\t\t\tnationalNumber: '8005553535',\r\n\t\t\t\tstartsAt: 14,\r\n\t\t\t\tendsAt: 32\r\n\t\t\t}, {\r\n\t\t\t\tcountry: 'US',\r\n\t\t\t\tnationalNumber: '2133734253',\r\n\t\t\t\tstartsAt: 41,\r\n\t\t\t\tendsAt: 55\r\n\t\t\t}]\r\n\t\t)\r\n\t})\r\n\r\n\tit('should find phone numbers from Mexico', () => {\r\n\t\t// Test parsing fixed-line numbers of Mexico.\r\n\t\ttest('+52 (449)978-0001', 'MX', '4499780001')\r\n\t\ttest('01 (449)978-0001', 'MX', '4499780001')\r\n\t\ttest('(449)978-0001', 'MX', '4499780001')\r\n\r\n\t\t// \"Dialling tokens 01, 02, 044, 045 and 1 are removed as they are\r\n\t\t//  no longer valid since August 2019.\"\r\n\t\t// // Test parsing mobile numbers of Mexico.\r\n\t\t// test('+52 1 33 1234-5678', 'MX', '3312345678')\r\n\t\t// test('044 (33) 1234-5678', 'MX', '3312345678')\r\n\t\t// test('045 33 1234-5678', 'MX', '3312345678')\r\n\t})\r\n\r\n\tit('should find phone numbers from Argentina', () => {\r\n\t\t// Test parsing mobile numbers of Argentina.\r\n\t\ttest('+54 9 ************', 'AR', '93435551212')\r\n\t\ttest('0343 15-555-1212', 'AR', '93435551212')\r\n\r\n\t\ttest('+54 9 3715 65 4320', 'AR', '93715654320')\r\n\t\ttest('03715 15 65 4320', 'AR', '93715654320')\r\n\r\n\t\t// Test parsing fixed-line numbers of Argentina.\r\n\t\ttest('+54 11 3797 0000', 'AR', '1137970000')\r\n\t\ttest('011 3797 0000', 'AR', '1137970000')\r\n\r\n\t\ttest('+54 3715 65 4321', 'AR', '3715654321')\r\n\t\ttest('03715 65 4321', 'AR', '3715654321')\r\n\r\n\t\ttest('+54 23 1234 0000', 'AR', '2312340000')\r\n\t\ttest('023 1234 0000', 'AR', '2312340000')\r\n\t})\r\n\r\n\tit('should only support the supported leniency values', function() {\r\n\t\texpect(() => new PhoneNumberMatcher('+54 23 1234 0000', { leniency: 'STRICT_GROUPING', v2: true }, metadata)).to.throw('Supported values: \"POSSIBLE\", \"VALID\".')\r\n\t})\r\n})"], "mappings": ";;AAAA;;AACA;;;;AAEA,SAASA,IAAT,CAAcC,IAAd,EAAoBC,cAApB,EAAoCC,eAApC,EAAqD;EACpD,IAAI,OAAOA,eAAP,KAA2B,QAA/B,EAAyC;IACxCA,eAAe,GAAG,CAAC;MAClBC,cAAc,EAAED;IADE,CAAD,CAAlB;EAGA;;EACD,IAAME,OAAO,GAAG,IAAIC,8BAAJ,CAAuBL,IAAvB,EAA6B;IAAEC,cAAc,EAAdA,cAAF;IAAkBK,EAAE,EAAE;EAAtB,CAA7B,EAA2DC,uBAA3D,CAAhB;;EACA,OAAOH,OAAO,CAACI,OAAR,EAAP,EAA0B;IACzB,IAAMC,MAAM,GAAGL,OAAO,CAACM,IAAR,EAAf;IACA,IAAMC,WAAW,GAAGT,eAAe,CAACU,KAAhB,EAApB;;IACA,IAAID,WAAW,CAACE,QAAZ,KAAyBC,SAA7B,EAAwC;MACvCL,MAAM,CAACI,QAAP,CAAgBE,MAAhB,CAAuBC,KAAvB,CAA6BL,WAAW,CAACE,QAAzC;IACA;;IACD,IAAIF,WAAW,CAACM,MAAZ,KAAuBH,SAA3B,EAAsC;MACrCL,MAAM,CAACQ,MAAP,CAAcF,MAAd,CAAqBC,KAArB,CAA2BL,WAAW,CAACM,MAAvC;IACA;;IACDR,MAAM,CAACA,MAAP,CAAcS,OAAd,CAAsBH,MAAtB,CAA6BC,KAA7B,CAAmCL,WAAW,CAACO,OAAZ,IAAuBjB,cAA1D;IACAQ,MAAM,CAACA,MAAP,CAAcN,cAAd,CAA6BY,MAA7B,CAAoCC,KAApC,CAA0CL,WAAW,CAACR,cAAtD;EACA;;EACDD,eAAe,CAACiB,MAAhB,CAAuBJ,MAAvB,CAA8BC,KAA9B,CAAoC,CAApC;AACA;;AAEDI,QAAQ,CAAC,oBAAD,EAAuB,YAAM;EACpCC,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrCtB,IAAI,CACH,qFADG,EAEH,IAFG,EAGH,CAAC;MACAmB,OAAO,EAAE,IADT;MAEAf,cAAc,EAAE,YAFhB;MAGAU,QAAQ,EAAE,EAHV;MAIAI,MAAM,EAAE;IAJR,CAAD,EAKG;MACFC,OAAO,EAAE,IADP;MAEFf,cAAc,EAAE,YAFd;MAGFU,QAAQ,EAAE,EAHR;MAIFI,MAAM,EAAE;IAJN,CALH,CAHG,CAAJ;EAeA,CAhBC,CAAF;EAkBAI,EAAE,CAAC,uCAAD,EAA0C,YAAM;IACjD;IACAtB,IAAI,CAAC,mBAAD,EAAsB,IAAtB,EAA4B,YAA5B,CAAJ;IACAA,IAAI,CAAC,kBAAD,EAAqB,IAArB,EAA2B,YAA3B,CAAJ;IACAA,IAAI,CAAC,eAAD,EAAkB,IAAlB,EAAwB,YAAxB,CAAJ,CAJiD,CAMjD;IACA;IACA;IACA;IACA;IACA;EACA,CAZC,CAAF;EAcAsB,EAAE,CAAC,0CAAD,EAA6C,YAAM;IACpD;IACAtB,IAAI,CAAC,oBAAD,EAAuB,IAAvB,EAA6B,aAA7B,CAAJ;IACAA,IAAI,CAAC,kBAAD,EAAqB,IAArB,EAA2B,aAA3B,CAAJ;IAEAA,IAAI,CAAC,oBAAD,EAAuB,IAAvB,EAA6B,aAA7B,CAAJ;IACAA,IAAI,CAAC,kBAAD,EAAqB,IAArB,EAA2B,aAA3B,CAAJ,CANoD,CAQpD;;IACAA,IAAI,CAAC,kBAAD,EAAqB,IAArB,EAA2B,YAA3B,CAAJ;IACAA,IAAI,CAAC,eAAD,EAAkB,IAAlB,EAAwB,YAAxB,CAAJ;IAEAA,IAAI,CAAC,kBAAD,EAAqB,IAArB,EAA2B,YAA3B,CAAJ;IACAA,IAAI,CAAC,eAAD,EAAkB,IAAlB,EAAwB,YAAxB,CAAJ;IAEAA,IAAI,CAAC,kBAAD,EAAqB,IAArB,EAA2B,YAA3B,CAAJ;IACAA,IAAI,CAAC,eAAD,EAAkB,IAAlB,EAAwB,YAAxB,CAAJ;EACA,CAjBC,CAAF;EAmBAsB,EAAE,CAAC,mDAAD,EAAsD,YAAW;IAClEC,MAAM,CAAC;MAAA,OAAM,IAAIjB,8BAAJ,CAAuB,kBAAvB,EAA2C;QAAEkB,QAAQ,EAAE,iBAAZ;QAA+BjB,EAAE,EAAE;MAAnC,CAA3C,EAAsFC,uBAAtF,CAAN;IAAA,CAAD,CAAN,CAA8GiB,EAA9G,UAAuH,wCAAvH;EACA,CAFC,CAAF;AAGA,CAvDO,CAAR"}