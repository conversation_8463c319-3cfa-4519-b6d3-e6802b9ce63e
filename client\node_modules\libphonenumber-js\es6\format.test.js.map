{"version": 3, "file": "format.test.js", "names": ["metadata", "type", "formatNumber_", "parsePhoneNumber", "formatNumber", "v2", "parameters", "length", "push", "undefined", "extract", "apply", "describe", "it", "should", "equal", "defaultCountry", "phone", "countryCallingCode", "options", "formatExtension", "number", "extension", "country", "ext", "nationalPrefix", "deep", "thrower", "expect", "to", "be", "fromCountry", "humanReadable"], "sources": ["../source/format.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' assert { type: 'json' }\r\nimport formatNumber_ from './format.js'\r\nimport parsePhoneNumber from './parsePhoneNumber.js'\r\n\r\nfunction formatNumber(...parameters) {\r\n\tlet v2\r\n\tif (parameters.length < 1) {\r\n\t\t// `input` parameter.\r\n\t\tparameters.push(undefined)\r\n\t} else {\r\n\t\t// Convert string `input` to a `PhoneNumber` instance.\r\n\t\tif (typeof parameters[0] === 'string') {\r\n\t\t\tv2 = true\r\n\t\t\tparameters[0] = parsePhoneNumber(parameters[0], {\r\n\t\t\t\t...parameters[2],\r\n\t\t\t\textract: false\r\n\t\t\t}, metadata)\r\n\t\t}\r\n\t}\r\n\tif (parameters.length < 2) {\r\n\t\t// `format` parameter.\r\n\t\tparameters.push(undefined)\r\n\t}\r\n\tif (parameters.length < 3) {\r\n\t\t// `options` parameter.\r\n\t\tparameters.push(undefined)\r\n\t}\r\n\t// Set `v2` flag.\r\n\tparameters[2] = {\r\n\t\tv2,\r\n\t\t...parameters[2]\r\n\t}\r\n\t// Add `metadata` parameter.\r\n\tparameters.push(metadata)\r\n\t// Call the function.\r\n\treturn formatNumber_.apply(this, parameters)\r\n}\r\n\r\ndescribe('format', () => {\r\n\tit('should work with the first argument being a E.164 number', () => {\r\n\t\tformatNumber('+12133734253', 'NATIONAL').should.equal('(*************')\r\n\t\tformatNumber('+12133734253', 'INTERNATIONAL').should.equal('****** 373 4253')\r\n\r\n\t\t// Invalid number.\r\n\t\tformatNumber('+12111111111', 'NATIONAL').should.equal('(*************')\r\n\r\n\t\t// Formatting invalid E.164 numbers.\r\n\t\tformatNumber('+11111', 'INTERNATIONAL').should.equal('+1 1111')\r\n\t\tformatNumber('+11111', 'NATIONAL').should.equal('1111')\r\n\t})\r\n\r\n\tit('should work with the first object argument expanded', () => {\r\n\t\tformatNumber('2133734253', 'NATIONAL', { defaultCountry: 'US' }).should.equal('(*************')\r\n\t\tformatNumber('2133734253', 'INTERNATIONAL', { defaultCountry: 'US' }).should.equal('****** 373 4253')\r\n\t})\r\n\r\n\tit('should format using formats with no leading digits (`format.leadingDigitsPatterns().length === 0`)', () => {\r\n\t\tformatNumber({ phone: '12345678901', countryCallingCode: 888 }, 'INTERNATIONAL').should.equal('+888 123 456 78901')\r\n\t})\r\n\r\n\tit('should sort out the arguments', () => {\r\n\t\tconst options = {\r\n\t\t\tformatExtension: (number, extension) => `${number} доб. ${extension}`\r\n\t\t}\r\n\r\n\t\tformatNumber({\r\n\t\t\tphone   : '8005553535',\r\n\t\t\tcountry : 'RU',\r\n\t\t\text     : '123'\r\n\t\t},\r\n\t\t'NATIONAL', options).should.equal('8 (800) 555-35-35 доб. 123')\r\n\r\n\t\t// Parse number from string.\r\n\t\tformatNumber('+78005553535', 'NATIONAL', options).should.equal('8 (800) 555-35-35')\r\n\t\tformatNumber('8005553535', 'NATIONAL', { ...options, defaultCountry: 'RU' }).should.equal('8 (800) 555-35-35')\r\n\t})\r\n\r\n\tit('should format with national prefix when specifically instructed', () => {\r\n\t\t// With national prefix.\r\n\t\tformatNumber('88005553535', 'NATIONAL', { defaultCountry: 'RU' }).should.equal('8 (800) 555-35-35')\r\n\t\t// Without national prefix via an explicitly set option.\r\n\t\tformatNumber('88005553535', 'NATIONAL', { nationalPrefix: false, defaultCountry: 'RU' }).should.equal('800 555-35-35')\r\n\t})\r\n\r\n\tit('should format valid phone numbers', () => {\r\n\t\t// Switzerland\r\n\t\tformatNumber({ country: 'CH', phone: '446681800' }, 'INTERNATIONAL').should.equal('+41 44 668 18 00')\r\n\t\tformatNumber({ country: 'CH', phone: '446681800' }, 'E.164').should.equal('+41446681800')\r\n\t\tformatNumber({ country: 'CH', phone: '446681800' }, 'RFC3966').should.equal('tel:+41446681800')\r\n\t\tformatNumber({ country: 'CH', phone: '446681800' }, 'NATIONAL').should.equal('044 668 18 00')\r\n\r\n\t\t// France\r\n\t\tformatNumber({ country: 'FR', phone: '169454850' }, 'NATIONAL').should.equal('01 69 45 48 50')\r\n\r\n\t\t// Kazakhstan\r\n\t\tformatNumber('****** 211 1111', 'NATIONAL').should.deep.equal('8 (*************')\r\n\t})\r\n\r\n\tit('should format national numbers with national prefix even if it\\'s optional', () => {\r\n\t\t// Russia\r\n\t\tformatNumber({ country: 'RU', phone: '9991234567' }, 'NATIONAL').should.equal('8 (999) 123-45-67')\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\tlet thrower\r\n\r\n\t\t// // No phone number\r\n\t\t// formatNumber('', 'INTERNATIONAL', { defaultCountry: 'RU' }).should.equal('')\r\n\t\t// formatNumber('', 'NATIONAL', { defaultCountry: 'RU' }).should.equal('')\r\n\r\n\t\tformatNumber({ country: 'RU', phone: '' }, 'INTERNATIONAL').should.equal('+7')\r\n\t\tformatNumber({ country: 'RU', phone: '' }, 'NATIONAL').should.equal('')\r\n\r\n\t\t// No suitable format\r\n\t\tformatNumber('+121337342530', 'NATIONAL', { defaultCountry: 'US' }).should.equal('21337342530')\r\n\t\t// No suitable format (leading digits mismatch)\r\n\t\tformatNumber('28199999', 'NATIONAL', { defaultCountry: 'AD' }).should.equal('28199999')\r\n\r\n\t\t// // Numerical `value`\r\n\t\t// thrower = () => formatNumber(89150000000, 'NATIONAL', { defaultCountry: 'RU' })\r\n\t\t// thrower.should.throw('A phone number must either be a string or an object of shape { phone, [country] }.')\r\n\r\n\t\t// // No metadata for country\r\n\t\t// expect(() => formatNumber('+121337342530', 'NATIONAL', { defaultCountry: 'USA' })).to.throw('Unknown country')\r\n\t\t// expect(() => formatNumber('21337342530', 'NATIONAL', { defaultCountry: 'USA' })).to.throw('Unknown country')\r\n\r\n\t\t// No format type\r\n\t\tthrower = () => formatNumber('+123')\r\n\t\tthrower.should.throw('Unknown \"format\" argument')\r\n\r\n\t\t// Unknown format type\r\n\t\tthrower = () => formatNumber('123', 'Gay', { defaultCountry: 'US' })\r\n\t\tthrower.should.throw('Unknown \"format\" argument')\r\n\r\n\t\t// // No metadata\r\n\t\t// thrower = () => _formatNumber('123', 'E.164', { defaultCountry: 'RU' })\r\n\t\t// thrower.should.throw('`metadata`')\r\n\r\n\t\t// No formats\r\n\t\tformatNumber('012345', 'NATIONAL', { defaultCountry: 'AC' }).should.equal('012345')\r\n\r\n\t\t// No `fromCountry` for `IDD` format.\r\n\t\texpect(formatNumber('+78005553535', 'IDD')).to.be.undefined\r\n\r\n\t\t// `fromCountry` has no default IDD prefix.\r\n\t\texpect(formatNumber('+78005553535', 'IDD', { fromCountry: 'BO' })).to.be.undefined\r\n\r\n\t\t// No such country.\r\n\t\texpect(() => formatNumber({ phone: '123', country: 'USA' }, 'NATIONAL')).to.throw('Unknown country')\r\n\t})\r\n\r\n\tit('should format phone number extensions', () => {\r\n\t\t// National\r\n\t\tformatNumber({\r\n\t\t\tcountry: 'US',\r\n\t\t\tphone: '2133734253',\r\n\t\t\text: '123'\r\n\t\t},\r\n\t\t'NATIONAL').should.equal('(************* ext. 123')\r\n\r\n\t\t// International\r\n\t\tformatNumber({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\text     : '123'\r\n\t\t},\r\n\t\t'INTERNATIONAL').should.equal('****** 373 4253 ext. 123')\r\n\r\n\t\t// International\r\n\t\tformatNumber({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\text     : '123'\r\n\t\t},\r\n\t\t'INTERNATIONAL').should.equal('****** 373 4253 ext. 123')\r\n\r\n\t\t// E.164\r\n\t\tformatNumber({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\text     : '123'\r\n\t\t},\r\n\t\t'E.164').should.equal('+12133734253')\r\n\r\n\t\t// RFC3966\r\n\t\tformatNumber({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\text     : '123'\r\n\t\t},\r\n\t\t'RFC3966').should.equal('tel:+12133734253;ext=123')\r\n\r\n\t\t// Custom ext prefix.\r\n\t\tformatNumber({\r\n\t\t\tcountry : 'GB',\r\n\t\t\tphone   : '7912345678',\r\n\t\t\text     : '123'\r\n\t\t},\r\n\t\t'INTERNATIONAL').should.equal('+44 7912 345678 x123')\r\n\t})\r\n\r\n\tit('should work with Argentina numbers', () => {\r\n\t\t// The same mobile number is written differently\r\n\t\t// in different formats in Argentina:\r\n\t\t// `9` gets prepended in international format.\r\n\t\tformatNumber({ country: 'AR', phone: '3435551212' }, 'INTERNATIONAL')\r\n\t\t\t.should.equal('+54 3435 55 1212')\r\n\t\tformatNumber({ country: 'AR', phone: '3435551212' }, 'NATIONAL')\r\n\t\t\t.should.equal('03435 55-1212')\r\n\t})\r\n\r\n\tit('should work with Mexico numbers', () => {\r\n\t\t// Fixed line.\r\n\t\tformatNumber({ country: 'MX', phone: '4499780001' }, 'INTERNATIONAL')\r\n\t\t\t.should.equal('+52 ************')\r\n\t\tformatNumber({ country: 'MX', phone: '4499780001' }, 'NATIONAL')\r\n\t\t\t.should.equal('************')\r\n\t\t\t// or '(449)978-0001'.\r\n\t\t// Mobile.\r\n\t\t// `1` is prepended before area code to mobile numbers in international format.\r\n\t\tformatNumber({ country: 'MX', phone: '3312345678' }, 'INTERNATIONAL')\r\n\t\t\t.should.equal('+52 33 1234 5678')\r\n\t\tformatNumber({ country: 'MX', phone: '3312345678' }, 'NATIONAL')\r\n\t\t\t.should.equal('33 1234 5678')\r\n\t\t\t// or '045 33 1234-5678'.\r\n\t})\r\n\r\n\tit('should format possible numbers', () => {\r\n\t\tformatNumber({ countryCallingCode: '7', phone: '1111111111' }, 'E.164')\r\n\t\t\t.should.equal('+71111111111')\r\n\r\n\t\tformatNumber({ countryCallingCode: '7', phone: '1111111111' }, 'NATIONAL')\r\n\t\t\t.should.equal('1111111111')\r\n\r\n\t\tformatNumber({ countryCallingCode: '7', phone: '1111111111' }, 'INTERNATIONAL')\r\n\t\t\t.should.equal('*************')\r\n\t})\r\n\r\n\tit('should format IDD-prefixed number', () => {\r\n\t\t// No `fromCountry`.\r\n\t\texpect(formatNumber('+78005553535', 'IDD')).to.be.undefined\r\n\r\n\t\t// No default IDD prefix.\r\n\t\texpect(formatNumber('+78005553535', 'IDD', { fromCountry: 'BO' })).to.be.undefined\r\n\r\n\t\t// Same country calling code.\r\n\t\tformatNumber('+12133734253', 'IDD', { fromCountry: 'CA', humanReadable: true }).should.equal('1 (*************')\r\n\t\tformatNumber('+78005553535', 'IDD', { fromCountry: 'KZ', humanReadable: true }).should.equal('8 (800) 555-35-35')\r\n\r\n\t\t// formatNumber('+78005553535', 'IDD', { fromCountry: 'US' }).should.equal('01178005553535')\r\n\t\tformatNumber('+78005553535', 'IDD', { fromCountry: 'US', humanReadable: true }).should.equal('011 7 800 555 35 35')\r\n\t})\r\n\r\n\tit('should format non-geographic numbering plan phone numbers', () => {\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/323\r\n\t\tformatNumber('+870773111632', 'INTERNATIONAL').should.equal('+870 773 111 632')\r\n\t\tformatNumber('+870773111632', 'NATIONAL').should.equal('773 111 632')\r\n\t})\r\n\r\n\tit('should use the default IDD prefix when formatting a phone number', () => {\r\n\t\t// Testing preferred international prefixes with ~ are supported.\r\n\t\t// (\"~\" designates waiting on a line until proceeding with the input).\r\n\t\tformatNumber('+390236618300', 'IDD', { fromCountry: 'BY' }).should.equal('8~10 39 02 3661 8300')\r\n\t})\r\n})"], "mappings": ";;;;;;AAAA,OAAOA,QAAP,MAAqB,sBAArB,UAAqDC,IAAI,EAAE,MAA3D;AACA,OAAOC,aAAP,MAA0B,aAA1B;AACA,OAAOC,gBAAP,MAA6B,uBAA7B;;AAEA,SAASC,YAAT,GAAqC;EACpC,IAAIC,EAAJ;;EADoC,kCAAZC,UAAY;IAAZA,UAAY;EAAA;;EAEpC,IAAIA,UAAU,CAACC,MAAX,GAAoB,CAAxB,EAA2B;IAC1B;IACAD,UAAU,CAACE,IAAX,CAAgBC,SAAhB;EACA,CAHD,MAGO;IACN;IACA,IAAI,OAAOH,UAAU,CAAC,CAAD,CAAjB,KAAyB,QAA7B,EAAuC;MACtCD,EAAE,GAAG,IAAL;MACAC,UAAU,CAAC,CAAD,CAAV,GAAgBH,gBAAgB,CAACG,UAAU,CAAC,CAAD,CAAX,kCAC5BA,UAAU,CAAC,CAAD,CADkB;QAE/BI,OAAO,EAAE;MAFsB,IAG7BV,QAH6B,CAAhC;IAIA;EACD;;EACD,IAAIM,UAAU,CAACC,MAAX,GAAoB,CAAxB,EAA2B;IAC1B;IACAD,UAAU,CAACE,IAAX,CAAgBC,SAAhB;EACA;;EACD,IAAIH,UAAU,CAACC,MAAX,GAAoB,CAAxB,EAA2B;IAC1B;IACAD,UAAU,CAACE,IAAX,CAAgBC,SAAhB;EACA,CAtBmC,CAuBpC;;;EACAH,UAAU,CAAC,CAAD,CAAV;IACCD,EAAE,EAAFA;EADD,GAEIC,UAAU,CAAC,CAAD,CAFd,EAxBoC,CA4BpC;;EACAA,UAAU,CAACE,IAAX,CAAgBR,QAAhB,EA7BoC,CA8BpC;;EACA,OAAOE,aAAa,CAACS,KAAd,CAAoB,IAApB,EAA0BL,UAA1B,CAAP;AACA;;AAEDM,QAAQ,CAAC,QAAD,EAAW,YAAM;EACxBC,EAAE,CAAC,0DAAD,EAA6D,YAAM;IACpET,YAAY,CAAC,cAAD,EAAiB,UAAjB,CAAZ,CAAyCU,MAAzC,CAAgDC,KAAhD,CAAsD,gBAAtD;IACAX,YAAY,CAAC,cAAD,EAAiB,eAAjB,CAAZ,CAA8CU,MAA9C,CAAqDC,KAArD,CAA2D,iBAA3D,EAFoE,CAIpE;;IACAX,YAAY,CAAC,cAAD,EAAiB,UAAjB,CAAZ,CAAyCU,MAAzC,CAAgDC,KAAhD,CAAsD,gBAAtD,EALoE,CAOpE;;IACAX,YAAY,CAAC,QAAD,EAAW,eAAX,CAAZ,CAAwCU,MAAxC,CAA+CC,KAA/C,CAAqD,SAArD;IACAX,YAAY,CAAC,QAAD,EAAW,UAAX,CAAZ,CAAmCU,MAAnC,CAA0CC,KAA1C,CAAgD,MAAhD;EACA,CAVC,CAAF;EAYAF,EAAE,CAAC,qDAAD,EAAwD,YAAM;IAC/DT,YAAY,CAAC,YAAD,EAAe,UAAf,EAA2B;MAAEY,cAAc,EAAE;IAAlB,CAA3B,CAAZ,CAAiEF,MAAjE,CAAwEC,KAAxE,CAA8E,gBAA9E;IACAX,YAAY,CAAC,YAAD,EAAe,eAAf,EAAgC;MAAEY,cAAc,EAAE;IAAlB,CAAhC,CAAZ,CAAsEF,MAAtE,CAA6EC,KAA7E,CAAmF,iBAAnF;EACA,CAHC,CAAF;EAKAF,EAAE,CAAC,oGAAD,EAAuG,YAAM;IAC9GT,YAAY,CAAC;MAAEa,KAAK,EAAE,aAAT;MAAwBC,kBAAkB,EAAE;IAA5C,CAAD,EAAoD,eAApD,CAAZ,CAAiFJ,MAAjF,CAAwFC,KAAxF,CAA8F,oBAA9F;EACA,CAFC,CAAF;EAIAF,EAAE,CAAC,+BAAD,EAAkC,YAAM;IACzC,IAAMM,OAAO,GAAG;MACfC,eAAe,EAAE,yBAACC,MAAD,EAASC,SAAT;QAAA,iBAA0BD,MAA1B,kCAAyCC,SAAzC;MAAA;IADF,CAAhB;IAIAlB,YAAY,CAAC;MACZa,KAAK,EAAK,YADE;MAEZM,OAAO,EAAG,IAFE;MAGZC,GAAG,EAAO;IAHE,CAAD,EAKZ,UALY,EAKAL,OALA,CAAZ,CAKqBL,MALrB,CAK4BC,KAL5B,CAKkC,4BALlC,EALyC,CAYzC;;IACAX,YAAY,CAAC,cAAD,EAAiB,UAAjB,EAA6Be,OAA7B,CAAZ,CAAkDL,MAAlD,CAAyDC,KAAzD,CAA+D,mBAA/D;IACAX,YAAY,CAAC,YAAD,EAAe,UAAf,kCAAgCe,OAAhC;MAAyCH,cAAc,EAAE;IAAzD,GAAZ,CAA6EF,MAA7E,CAAoFC,KAApF,CAA0F,mBAA1F;EACA,CAfC,CAAF;EAiBAF,EAAE,CAAC,iEAAD,EAAoE,YAAM;IAC3E;IACAT,YAAY,CAAC,aAAD,EAAgB,UAAhB,EAA4B;MAAEY,cAAc,EAAE;IAAlB,CAA5B,CAAZ,CAAkEF,MAAlE,CAAyEC,KAAzE,CAA+E,mBAA/E,EAF2E,CAG3E;;IACAX,YAAY,CAAC,aAAD,EAAgB,UAAhB,EAA4B;MAAEqB,cAAc,EAAE,KAAlB;MAAyBT,cAAc,EAAE;IAAzC,CAA5B,CAAZ,CAAyFF,MAAzF,CAAgGC,KAAhG,CAAsG,eAAtG;EACA,CALC,CAAF;EAOAF,EAAE,CAAC,mCAAD,EAAsC,YAAM;IAC7C;IACAT,YAAY,CAAC;MAAEmB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAwC,eAAxC,CAAZ,CAAqEH,MAArE,CAA4EC,KAA5E,CAAkF,kBAAlF;IACAX,YAAY,CAAC;MAAEmB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAwC,OAAxC,CAAZ,CAA6DH,MAA7D,CAAoEC,KAApE,CAA0E,cAA1E;IACAX,YAAY,CAAC;MAAEmB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAwC,SAAxC,CAAZ,CAA+DH,MAA/D,CAAsEC,KAAtE,CAA4E,kBAA5E;IACAX,YAAY,CAAC;MAAEmB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAwC,UAAxC,CAAZ,CAAgEH,MAAhE,CAAuEC,KAAvE,CAA6E,eAA7E,EAL6C,CAO7C;;IACAX,YAAY,CAAC;MAAEmB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAwC,UAAxC,CAAZ,CAAgEH,MAAhE,CAAuEC,KAAvE,CAA6E,gBAA7E,EAR6C,CAU7C;;IACAX,YAAY,CAAC,iBAAD,EAAoB,UAApB,CAAZ,CAA4CU,MAA5C,CAAmDY,IAAnD,CAAwDX,KAAxD,CAA8D,kBAA9D;EACA,CAZC,CAAF;EAcAF,EAAE,CAAC,4EAAD,EAA+E,YAAM;IACtF;IACAT,YAAY,CAAC;MAAEmB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAyC,UAAzC,CAAZ,CAAiEH,MAAjE,CAAwEC,KAAxE,CAA8E,mBAA9E;EACA,CAHC,CAAF;EAKAF,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC,IAAIc,OAAJ,CADqC,CAGrC;IACA;IACA;;IAEAvB,YAAY,CAAC;MAAEmB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAA+B,eAA/B,CAAZ,CAA4DH,MAA5D,CAAmEC,KAAnE,CAAyE,IAAzE;IACAX,YAAY,CAAC;MAAEmB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAA+B,UAA/B,CAAZ,CAAuDH,MAAvD,CAA8DC,KAA9D,CAAoE,EAApE,EARqC,CAUrC;;IACAX,YAAY,CAAC,eAAD,EAAkB,UAAlB,EAA8B;MAAEY,cAAc,EAAE;IAAlB,CAA9B,CAAZ,CAAoEF,MAApE,CAA2EC,KAA3E,CAAiF,aAAjF,EAXqC,CAYrC;;IACAX,YAAY,CAAC,UAAD,EAAa,UAAb,EAAyB;MAAEY,cAAc,EAAE;IAAlB,CAAzB,CAAZ,CAA+DF,MAA/D,CAAsEC,KAAtE,CAA4E,UAA5E,EAbqC,CAerC;IACA;IACA;IAEA;IACA;IACA;IAEA;;IACAY,OAAO,GAAG;MAAA,OAAMvB,YAAY,CAAC,MAAD,CAAlB;IAAA,CAAV;;IACAuB,OAAO,CAACb,MAAR,UAAqB,2BAArB,EAzBqC,CA2BrC;;IACAa,OAAO,GAAG;MAAA,OAAMvB,YAAY,CAAC,KAAD,EAAQ,KAAR,EAAe;QAAEY,cAAc,EAAE;MAAlB,CAAf,CAAlB;IAAA,CAAV;;IACAW,OAAO,CAACb,MAAR,UAAqB,2BAArB,EA7BqC,CA+BrC;IACA;IACA;IAEA;;IACAV,YAAY,CAAC,QAAD,EAAW,UAAX,EAAuB;MAAEY,cAAc,EAAE;IAAlB,CAAvB,CAAZ,CAA6DF,MAA7D,CAAoEC,KAApE,CAA0E,QAA1E,EApCqC,CAsCrC;;IACAa,MAAM,CAACxB,YAAY,CAAC,cAAD,EAAiB,KAAjB,CAAb,CAAN,CAA4CyB,EAA5C,CAA+CC,EAA/C,CAAkDrB,SAAlD,CAvCqC,CAyCrC;;IACAmB,MAAM,CAACxB,YAAY,CAAC,cAAD,EAAiB,KAAjB,EAAwB;MAAE2B,WAAW,EAAE;IAAf,CAAxB,CAAb,CAAN,CAAmEF,EAAnE,CAAsEC,EAAtE,CAAyErB,SAAzE,CA1CqC,CA4CrC;;IACAmB,MAAM,CAAC;MAAA,OAAMxB,YAAY,CAAC;QAAEa,KAAK,EAAE,KAAT;QAAgBM,OAAO,EAAE;MAAzB,CAAD,EAAmC,UAAnC,CAAlB;IAAA,CAAD,CAAN,CAAyEM,EAAzE,UAAkF,iBAAlF;EACA,CA9CC,CAAF;EAgDAhB,EAAE,CAAC,uCAAD,EAA0C,YAAM;IACjD;IACAT,YAAY,CAAC;MACZmB,OAAO,EAAE,IADG;MAEZN,KAAK,EAAE,YAFK;MAGZO,GAAG,EAAE;IAHO,CAAD,EAKZ,UALY,CAAZ,CAKYV,MALZ,CAKmBC,KALnB,CAKyB,yBALzB,EAFiD,CASjD;;IACAX,YAAY,CAAC;MACZmB,OAAO,EAAG,IADE;MAEZN,KAAK,EAAK,YAFE;MAGZO,GAAG,EAAO;IAHE,CAAD,EAKZ,eALY,CAAZ,CAKiBV,MALjB,CAKwBC,KALxB,CAK8B,0BAL9B,EAViD,CAiBjD;;IACAX,YAAY,CAAC;MACZmB,OAAO,EAAG,IADE;MAEZN,KAAK,EAAK,YAFE;MAGZO,GAAG,EAAO;IAHE,CAAD,EAKZ,eALY,CAAZ,CAKiBV,MALjB,CAKwBC,KALxB,CAK8B,0BAL9B,EAlBiD,CAyBjD;;IACAX,YAAY,CAAC;MACZmB,OAAO,EAAG,IADE;MAEZN,KAAK,EAAK,YAFE;MAGZO,GAAG,EAAO;IAHE,CAAD,EAKZ,OALY,CAAZ,CAKSV,MALT,CAKgBC,KALhB,CAKsB,cALtB,EA1BiD,CAiCjD;;IACAX,YAAY,CAAC;MACZmB,OAAO,EAAG,IADE;MAEZN,KAAK,EAAK,YAFE;MAGZO,GAAG,EAAO;IAHE,CAAD,EAKZ,SALY,CAAZ,CAKWV,MALX,CAKkBC,KALlB,CAKwB,0BALxB,EAlCiD,CAyCjD;;IACAX,YAAY,CAAC;MACZmB,OAAO,EAAG,IADE;MAEZN,KAAK,EAAK,YAFE;MAGZO,GAAG,EAAO;IAHE,CAAD,EAKZ,eALY,CAAZ,CAKiBV,MALjB,CAKwBC,KALxB,CAK8B,sBAL9B;EAMA,CAhDC,CAAF;EAkDAF,EAAE,CAAC,oCAAD,EAAuC,YAAM;IAC9C;IACA;IACA;IACAT,YAAY,CAAC;MAAEmB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAyC,eAAzC,CAAZ,CACEH,MADF,CACSC,KADT,CACe,kBADf;IAEAX,YAAY,CAAC;MAAEmB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAyC,UAAzC,CAAZ,CACEH,MADF,CACSC,KADT,CACe,eADf;EAEA,CARC,CAAF;EAUAF,EAAE,CAAC,iCAAD,EAAoC,YAAM;IAC3C;IACAT,YAAY,CAAC;MAAEmB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAyC,eAAzC,CAAZ,CACEH,MADF,CACSC,KADT,CACe,kBADf;IAEAX,YAAY,CAAC;MAAEmB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAyC,UAAzC,CAAZ,CACEH,MADF,CACSC,KADT,CACe,cADf,EAJ2C,CAM1C;IACD;IACA;;IACAX,YAAY,CAAC;MAAEmB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAyC,eAAzC,CAAZ,CACEH,MADF,CACSC,KADT,CACe,kBADf;IAEAX,YAAY,CAAC;MAAEmB,OAAO,EAAE,IAAX;MAAiBN,KAAK,EAAE;IAAxB,CAAD,EAAyC,UAAzC,CAAZ,CACEH,MADF,CACSC,KADT,CACe,cADf,EAX2C,CAa1C;EACD,CAdC,CAAF;EAgBAF,EAAE,CAAC,gCAAD,EAAmC,YAAM;IAC1CT,YAAY,CAAC;MAAEc,kBAAkB,EAAE,GAAtB;MAA2BD,KAAK,EAAE;IAAlC,CAAD,EAAmD,OAAnD,CAAZ,CACEH,MADF,CACSC,KADT,CACe,cADf;IAGAX,YAAY,CAAC;MAAEc,kBAAkB,EAAE,GAAtB;MAA2BD,KAAK,EAAE;IAAlC,CAAD,EAAmD,UAAnD,CAAZ,CACEH,MADF,CACSC,KADT,CACe,YADf;IAGAX,YAAY,CAAC;MAAEc,kBAAkB,EAAE,GAAtB;MAA2BD,KAAK,EAAE;IAAlC,CAAD,EAAmD,eAAnD,CAAZ,CACEH,MADF,CACSC,KADT,CACe,eADf;EAEA,CATC,CAAF;EAWAF,EAAE,CAAC,mCAAD,EAAsC,YAAM;IAC7C;IACAe,MAAM,CAACxB,YAAY,CAAC,cAAD,EAAiB,KAAjB,CAAb,CAAN,CAA4CyB,EAA5C,CAA+CC,EAA/C,CAAkDrB,SAAlD,CAF6C,CAI7C;;IACAmB,MAAM,CAACxB,YAAY,CAAC,cAAD,EAAiB,KAAjB,EAAwB;MAAE2B,WAAW,EAAE;IAAf,CAAxB,CAAb,CAAN,CAAmEF,EAAnE,CAAsEC,EAAtE,CAAyErB,SAAzE,CAL6C,CAO7C;;IACAL,YAAY,CAAC,cAAD,EAAiB,KAAjB,EAAwB;MAAE2B,WAAW,EAAE,IAAf;MAAqBC,aAAa,EAAE;IAApC,CAAxB,CAAZ,CAAgFlB,MAAhF,CAAuFC,KAAvF,CAA6F,kBAA7F;IACAX,YAAY,CAAC,cAAD,EAAiB,KAAjB,EAAwB;MAAE2B,WAAW,EAAE,IAAf;MAAqBC,aAAa,EAAE;IAApC,CAAxB,CAAZ,CAAgFlB,MAAhF,CAAuFC,KAAvF,CAA6F,mBAA7F,EAT6C,CAW7C;;IACAX,YAAY,CAAC,cAAD,EAAiB,KAAjB,EAAwB;MAAE2B,WAAW,EAAE,IAAf;MAAqBC,aAAa,EAAE;IAApC,CAAxB,CAAZ,CAAgFlB,MAAhF,CAAuFC,KAAvF,CAA6F,qBAA7F;EACA,CAbC,CAAF;EAeAF,EAAE,CAAC,2DAAD,EAA8D,YAAM;IACrE;IACAT,YAAY,CAAC,eAAD,EAAkB,eAAlB,CAAZ,CAA+CU,MAA/C,CAAsDC,KAAtD,CAA4D,kBAA5D;IACAX,YAAY,CAAC,eAAD,EAAkB,UAAlB,CAAZ,CAA0CU,MAA1C,CAAiDC,KAAjD,CAAuD,aAAvD;EACA,CAJC,CAAF;EAMAF,EAAE,CAAC,kEAAD,EAAqE,YAAM;IAC5E;IACA;IACAT,YAAY,CAAC,eAAD,EAAkB,KAAlB,EAAyB;MAAE2B,WAAW,EAAE;IAAf,CAAzB,CAAZ,CAA4DjB,MAA5D,CAAmEC,KAAnE,CAAyE,sBAAzE;EACA,CAJC,CAAF;AAKA,CAlOO,CAAR"}