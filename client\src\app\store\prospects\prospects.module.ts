import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgSelectModule } from '@ng-select/ng-select';
import { ProspectsRoutingModule } from './prospects-routing.module';
import { ProspectsComponent } from './prospects.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { CalendarModule } from 'primeng/calendar';
import { EditorModule } from 'primeng/editor';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { TableModule } from 'primeng/table';
import { ProspectsDetailsComponent } from './prospects-details/prospects-details.component';
import { ProspectsOverviewComponent } from './prospects-details/prospects-overview/prospects-overview.component';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { TabViewModule } from 'primeng/tabview';
import { ToastModule } from 'primeng/toast';
import { MessageService, ConfirmationService } from 'primeng/api';
import { ProspectsAiInsightsComponent } from './prospects-details/prospects-ai-insights/prospects-ai-insights.component';
import { ProspectsAttachmentsComponent } from './prospects-details/prospects-attachments/prospects-attachments.component';
import { ProspectsContactsComponent } from './prospects-details/prospects-contacts/prospects-contacts.component';
import { ProspectsNotesComponent } from './prospects-details/prospects-notes/prospects-notes.component';
import { ProspectsOrganizationDataComponent } from './prospects-details/prospects-organization-data/prospects-organization-data.component';
import { ProspectsSalesTeamComponent } from './prospects-details/prospects-sales-team/prospects-sales-team.component';
import { AddProspectComponent } from './add-prospect/add-prospect.component';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { ProspectActivitiesComponent } from './prospects-details/prospect-activities/prospect-activities.component';
import { EmployeeSelectComponent } from './employee-select/employee-select.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { CommonFormModule } from '../common-form/common-form.module';
import { MultiSelectModule } from 'primeng/multiselect';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';

@NgModule({
  declarations: [
    ProspectsComponent,
    ProspectsDetailsComponent,
    ProspectsOverviewComponent,
    ProspectsContactsComponent,
    ProspectsSalesTeamComponent,
    ProspectsAiInsightsComponent,
    ProspectsOrganizationDataComponent,
    ProspectsAttachmentsComponent,
    ProspectsNotesComponent,
    AddProspectComponent,
    ProspectActivitiesComponent,
    EmployeeSelectComponent,
  ],
  imports: [
    CommonModule,
    NgSelectModule,
    ProspectsRoutingModule,
    FormsModule,
    TableModule,
    ReactiveFormsModule,
    ButtonModule,
    DropdownModule,
    TabViewModule,
    AutoCompleteModule,
    BreadcrumbModule,
    CalendarModule,
    ToastModule,
    EditorModule,
    InputTextModule,
    ConfirmDialogModule,
    DialogModule,
    CheckboxModule,
    SharedModule,
    CommonFormModule,
    MultiSelectModule,
    NgxMaskDirective,
  ],
  providers: [MessageService, ConfirmationService,provideNgxMask()],
})
export class ProspectsModule {}
