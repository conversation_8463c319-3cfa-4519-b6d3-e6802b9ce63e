{"version": 3, "file": "AsYouTypeFormatter.complete.js", "names": ["checkNumberLength", "parseDigits", "formatNationalNumberUsingFormat", "formatCompleteNumber", "state", "format", "metadata", "shouldTryNationalPrefixFormattingRule", "getSeparatorAfterNationalPrefix", "matcher", "RegExp", "pattern", "test", "nationalSignificantNumber", "formatNationalNumberWithAndWithoutNationalPrefixFormattingRule", "canFormatCompleteNumber", "international", "nationalPrefix", "carrierCode", "formattedNumber", "formatNationalNumber", "useNationalPrefixFormattingRule", "formattedNationalNumber", "useInternationalFormat", "withNationalPrefix", "complexPrefixBeforeNationalSignificantNumber", "isValidFormattedNationalNumber", "getNationalDigits"], "sources": ["../source/AsYouTypeFormatter.complete.js"], "sourcesContent": ["import checkNumberLength from './helpers/checkNumberLength.js'\r\nimport parseDigits from './helpers/parseDigits.js'\r\nimport formatNationalNumberUsingFormat from './helpers/formatNationalNumberUsingFormat.js'\r\n\r\nexport default function formatCompleteNumber(state, format, {\r\n\tmetadata,\r\n\tshouldTryNationalPrefixFormattingRule,\r\n\tgetSeparatorAfterNationalPrefix\r\n}) {\r\n\tconst matcher = new RegExp(`^(?:${format.pattern()})$`)\r\n\tif (matcher.test(state.nationalSignificantNumber)) {\r\n\t\treturn formatNationalNumberWithAndWithoutNationalPrefixFormattingRule(\r\n\t\t\tstate,\r\n\t\t\tformat,\r\n\t\t\t{\r\n\t\t\t\tmetadata,\r\n\t\t\t\tshouldTryNationalPrefixFormattingRule,\r\n\t\t\t\tgetSeparatorAfterNationalPrefix\r\n\t\t\t}\r\n\t\t)\r\n\t}\r\n}\r\n\r\nexport function canFormatCompleteNumber(nationalSignificantNumber, metadata) {\r\n\treturn checkNumberLength(nationalSignificantNumber, metadata) === 'IS_POSSIBLE'\r\n}\r\n\r\nfunction formatNationalNumberWithAndWithoutNationalPrefixFormattingRule(state, format, {\r\n\tmetadata,\r\n\tshouldTryNationalPrefixFormattingRule,\r\n\tgetSeparatorAfterNationalPrefix\r\n}) {\r\n\t// `format` has already been checked for `nationalPrefix` requirement.\r\n\r\n\tconst {\r\n\t\tnationalSignificantNumber,\r\n\t\tinternational,\r\n\t\tnationalPrefix,\r\n\t\tcarrierCode\r\n\t} = state\r\n\r\n\t// Format the number with using `national_prefix_formatting_rule`.\r\n\t// If the resulting formatted number is a valid formatted number, then return it.\r\n\t//\r\n\t// Google's AsYouType formatter is different in a way that it doesn't try\r\n\t// to format using the \"national prefix formatting rule\", and instead it\r\n\t// simply prepends a national prefix followed by a \" \" character.\r\n\t// This code does that too, but as a fallback.\r\n\t// The reason is that \"national prefix formatting rule\" may use parentheses,\r\n\t// which wouldn't be included has it used the simpler Google's way.\r\n\t//\r\n\tif (shouldTryNationalPrefixFormattingRule(format)) {\r\n\t\tconst formattedNumber = formatNationalNumber(state, format, {\r\n\t\t\tuseNationalPrefixFormattingRule: true,\r\n\t\t\tgetSeparatorAfterNationalPrefix,\r\n\t\t\tmetadata\r\n\t\t})\r\n\t\tif (formattedNumber) {\r\n\t\t\treturn formattedNumber\r\n\t\t}\r\n\t}\r\n\r\n\t// Format the number without using `national_prefix_formatting_rule`.\r\n\treturn formatNationalNumber(state, format, {\r\n\t\tuseNationalPrefixFormattingRule: false,\r\n\t\tgetSeparatorAfterNationalPrefix,\r\n\t\tmetadata\r\n\t})\r\n}\r\n\r\nfunction formatNationalNumber(state, format, {\r\n\tmetadata,\r\n\tuseNationalPrefixFormattingRule,\r\n\tgetSeparatorAfterNationalPrefix\r\n}) {\r\n\tlet formattedNationalNumber = formatNationalNumberUsingFormat(\r\n\t\tstate.nationalSignificantNumber,\r\n\t\tformat,\r\n\t\t{\r\n\t\t\tcarrierCode: state.carrierCode,\r\n\t\t\tuseInternationalFormat: state.international,\r\n\t\t\twithNationalPrefix: useNationalPrefixFormattingRule,\r\n\t\t\tmetadata\r\n\t\t}\r\n\t)\r\n\tif (!useNationalPrefixFormattingRule) {\r\n\t\tif (state.nationalPrefix) {\r\n\t\t\t// If a national prefix was extracted, then just prepend it,\r\n\t\t\t// followed by a \" \" character.\r\n\t\t\tformattedNationalNumber = state.nationalPrefix +\r\n\t\t\t\tgetSeparatorAfterNationalPrefix(format) +\r\n\t\t\t\tformattedNationalNumber\r\n\t\t} else if (state.complexPrefixBeforeNationalSignificantNumber) {\r\n\t\t\tformattedNationalNumber = state.complexPrefixBeforeNationalSignificantNumber +\r\n\t\t\t\t' ' +\r\n\t\t\t\tformattedNationalNumber\r\n\t\t}\r\n\t}\r\n\tif (isValidFormattedNationalNumber(formattedNationalNumber, state)) {\r\n\t\treturn formattedNationalNumber\r\n\t}\r\n}\r\n\r\n// Check that the formatted phone number contains exactly\r\n// the same digits that have been input by the user.\r\n// For example, when \"0111523456789\" is input for `AR` country,\r\n// the extracted `this.nationalSignificantNumber` is \"91123456789\",\r\n// which means that the national part of `this.digits` isn't simply equal to\r\n// `this.nationalPrefix` + `this.nationalSignificantNumber`.\r\n//\r\n// Also, a `format` can add extra digits to the `this.nationalSignificantNumber`\r\n// being formatted via `metadata[country].national_prefix_transform_rule`.\r\n// For example, for `VI` country, it prepends `340` to the national number,\r\n// and if this check hasn't been implemented, then there would be a bug\r\n// when `340` \"area coude\" is \"duplicated\" during input for `VI` country:\r\n// https://github.com/catamphetamine/libphonenumber-js/issues/318\r\n//\r\n// So, all these \"gotchas\" are filtered out.\r\n//\r\n// In the original Google's code, the comments say:\r\n// \"Check that we didn't remove nor add any extra digits when we matched\r\n// this formatting pattern. This usually happens after we entered the last\r\n// digit during AYTF. Eg: In case of MX, we swallow mobile token (1) when\r\n// formatted but AYTF should retain all the number entered and not change\r\n// in order to match a format (of same leading digits and length) display\r\n// in that way.\"\r\n// \"If it's the same (i.e entered number and format is same), then it's\r\n// safe to return this in formatted number as nothing is lost / added.\"\r\n// Otherwise, don't use this format.\r\n// https://github.com/google/libphonenumber/commit/3e7c1f04f5e7200f87fb131e6f85c6e99d60f510#diff-9149457fa9f5d608a11bb975c6ef4bc5\r\n// https://github.com/google/libphonenumber/commit/3ac88c7106e7dcb553bcc794b15f19185928a1c6#diff-2dcb77e833422ee304da348b905cde0b\r\n//\r\nfunction isValidFormattedNationalNumber(formattedNationalNumber, state) {\r\n\treturn parseDigits(formattedNationalNumber) === state.getNationalDigits()\r\n}"], "mappings": "AAAA,OAAOA,iBAAP,MAA8B,gCAA9B;AACA,OAAOC,WAAP,MAAwB,0BAAxB;AACA,OAAOC,+BAAP,MAA4C,8CAA5C;AAEA,eAAe,SAASC,oBAAT,CAA8BC,KAA9B,EAAqCC,MAArC,QAIZ;EAAA,IAHFC,QAGE,QAHFA,QAGE;EAAA,IAFFC,qCAEE,QAFFA,qCAEE;EAAA,IADFC,+BACE,QADFA,+BACE;EACF,IAAMC,OAAO,GAAG,IAAIC,MAAJ,eAAkBL,MAAM,CAACM,OAAP,EAAlB,QAAhB;;EACA,IAAIF,OAAO,CAACG,IAAR,CAAaR,KAAK,CAACS,yBAAnB,CAAJ,EAAmD;IAClD,OAAOC,8DAA8D,CACpEV,KADoE,EAEpEC,MAFoE,EAGpE;MACCC,QAAQ,EAARA,QADD;MAECC,qCAAqC,EAArCA,qCAFD;MAGCC,+BAA+B,EAA/BA;IAHD,CAHoE,CAArE;EASA;AACD;AAED,OAAO,SAASO,uBAAT,CAAiCF,yBAAjC,EAA4DP,QAA5D,EAAsE;EAC5E,OAAON,iBAAiB,CAACa,yBAAD,EAA4BP,QAA5B,CAAjB,KAA2D,aAAlE;AACA;;AAED,SAASQ,8DAAT,CAAwEV,KAAxE,EAA+EC,MAA/E,SAIG;EAAA,IAHFC,QAGE,SAHFA,QAGE;EAAA,IAFFC,qCAEE,SAFFA,qCAEE;EAAA,IADFC,+BACE,SADFA,+BACE;EACF;EAEA,IACCK,yBADD,GAKIT,KALJ,CACCS,yBADD;EAAA,IAECG,aAFD,GAKIZ,KALJ,CAECY,aAFD;EAAA,IAGCC,cAHD,GAKIb,KALJ,CAGCa,cAHD;EAAA,IAICC,WAJD,GAKId,KALJ,CAICc,WAJD,CAHE,CAUF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,IAAIX,qCAAqC,CAACF,MAAD,CAAzC,EAAmD;IAClD,IAAMc,eAAe,GAAGC,oBAAoB,CAAChB,KAAD,EAAQC,MAAR,EAAgB;MAC3DgB,+BAA+B,EAAE,IAD0B;MAE3Db,+BAA+B,EAA/BA,+BAF2D;MAG3DF,QAAQ,EAARA;IAH2D,CAAhB,CAA5C;;IAKA,IAAIa,eAAJ,EAAqB;MACpB,OAAOA,eAAP;IACA;EACD,CA7BC,CA+BF;;;EACA,OAAOC,oBAAoB,CAAChB,KAAD,EAAQC,MAAR,EAAgB;IAC1CgB,+BAA+B,EAAE,KADS;IAE1Cb,+BAA+B,EAA/BA,+BAF0C;IAG1CF,QAAQ,EAARA;EAH0C,CAAhB,CAA3B;AAKA;;AAED,SAASc,oBAAT,CAA8BhB,KAA9B,EAAqCC,MAArC,SAIG;EAAA,IAHFC,QAGE,SAHFA,QAGE;EAAA,IAFFe,+BAEE,SAFFA,+BAEE;EAAA,IADFb,+BACE,SADFA,+BACE;EACF,IAAIc,uBAAuB,GAAGpB,+BAA+B,CAC5DE,KAAK,CAACS,yBADsD,EAE5DR,MAF4D,EAG5D;IACCa,WAAW,EAAEd,KAAK,CAACc,WADpB;IAECK,sBAAsB,EAAEnB,KAAK,CAACY,aAF/B;IAGCQ,kBAAkB,EAAEH,+BAHrB;IAICf,QAAQ,EAARA;EAJD,CAH4D,CAA7D;;EAUA,IAAI,CAACe,+BAAL,EAAsC;IACrC,IAAIjB,KAAK,CAACa,cAAV,EAA0B;MACzB;MACA;MACAK,uBAAuB,GAAGlB,KAAK,CAACa,cAAN,GACzBT,+BAA+B,CAACH,MAAD,CADN,GAEzBiB,uBAFD;IAGA,CAND,MAMO,IAAIlB,KAAK,CAACqB,4CAAV,EAAwD;MAC9DH,uBAAuB,GAAGlB,KAAK,CAACqB,4CAAN,GACzB,GADyB,GAEzBH,uBAFD;IAGA;EACD;;EACD,IAAII,8BAA8B,CAACJ,uBAAD,EAA0BlB,KAA1B,CAAlC,EAAoE;IACnE,OAAOkB,uBAAP;EACA;AACD,C,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASI,8BAAT,CAAwCJ,uBAAxC,EAAiElB,KAAjE,EAAwE;EACvE,OAAOH,WAAW,CAACqB,uBAAD,CAAX,KAAyClB,KAAK,CAACuB,iBAAN,EAAhD;AACA"}