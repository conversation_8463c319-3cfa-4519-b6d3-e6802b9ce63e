/**
 * Checks whether the entire input sequence can be matched
 * against the regular expression.
 * @return {boolean}
 */
export default function matchesEntirely(text, regular_expression) {
	// If assigning the `''` default value is moved to the arguments above,
	// code coverage would decrease for some weird reason.
	text = text || ''
	return new RegExp('^(?:' + regular_expression + ')$').test(text)
}