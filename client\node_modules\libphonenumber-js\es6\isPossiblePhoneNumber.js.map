{"version": 3, "file": "isPossiblePhoneNumber.js", "names": ["normalizeArguments", "parsePhoneNumber", "isPossiblePhoneNumber", "arguments", "text", "options", "metadata", "extract", "phoneNumber", "isPossible"], "sources": ["../source/isPossiblePhoneNumber.js"], "sourcesContent": ["import normalizeArguments from './normalizeArguments.js'\r\nimport parsePhoneNumber from './parsePhoneNumber_.js'\r\n\r\nexport default function isPossiblePhoneNumber() {\r\n\tlet { text, options, metadata } = normalizeArguments(arguments)\r\n\toptions = {\r\n\t\t...options,\r\n\t\textract: false\r\n\t}\r\n\tconst phoneNumber = parsePhoneNumber(text, options, metadata)\r\n\treturn phoneNumber && phoneNumber.isPossible() || false\r\n}"], "mappings": ";;;;;;AAAA,OAAOA,kBAAP,MAA+B,yBAA/B;AACA,OAAOC,gBAAP,MAA6B,wBAA7B;AAEA,eAAe,SAASC,qBAAT,GAAiC;EAC/C,0BAAkCF,kBAAkB,CAACG,SAAD,CAApD;EAAA,IAAMC,IAAN,uBAAMA,IAAN;EAAA,IAAYC,OAAZ,uBAAYA,OAAZ;EAAA,IAAqBC,QAArB,uBAAqBA,QAArB;;EACAD,OAAO,mCACHA,OADG;IAENE,OAAO,EAAE;EAFH,EAAP;EAIA,IAAMC,WAAW,GAAGP,gBAAgB,CAACG,IAAD,EAAOC,OAAP,EAAgBC,QAAhB,CAApC;EACA,OAAOE,WAAW,IAAIA,WAAW,CAACC,UAAZ,EAAf,IAA2C,KAAlD;AACA"}