{"ast": null, "code": "import withMetadataArgument from './withMetadataArgument.js';\nimport { getExtPrefix as _getExtPrefix } from '../../core/index.js';\nexport function getExtPrefix() {\n  return withMetadataArgument(_getExtPrefix, arguments);\n}", "map": {"version": 3, "names": ["withMetadataArgument", "getExtPrefix", "_getExtPrefix", "arguments"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/max/exports/getExtPrefix.js"], "sourcesContent": ["import withMetadataArgument from './withMetadataArgument.js'\r\nimport { getExtPrefix as _getExtPrefix } from '../../core/index.js'\r\n\r\nexport function getExtPrefix() {\r\n\treturn withMetadataArgument(_getExtPrefix, arguments)\r\n}"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,2BAA2B;AAC5D,SAASC,YAAY,IAAIC,aAAa,QAAQ,qBAAqB;AAEnE,OAAO,SAASD,YAAYA,CAAA,EAAG;EAC9B,OAAOD,oBAAoB,CAACE,aAAa,EAAEC,SAAS,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}