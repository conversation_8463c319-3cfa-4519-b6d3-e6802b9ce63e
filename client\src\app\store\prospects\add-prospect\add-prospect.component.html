<p-toast position="top-right" [life]="3000"></p-toast>
<form [formGroup]="ProspectForm">
  <div class="card shadow-1 border-round-xl surface-0 p-5 mb-4 border-1 border-solid border-50">
    <h3 class="mb-2 flex align-items-center h-3rem">Create Prospect</h3>
    <div class="p-fluid p-formgrid grid mt-0">
      <!-- <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">sticky_note_2</span> Prospect ID
                        <span class="text-red-500">*</span>
                    </label>
                    <input pInputText id="prospect_id" type="text" formControlName="prospect_id"
                        placeholder="Prospect ID" [ngClass]="{ 'is-invalid': submitted && f['prospect_id'].errors }"
                        class="h-3rem w-full" />
                    <div *ngIf="submitted && f['prospect_id'].errors" class="invalid-feedback">
                        <div *ngIf=" submitted && f['prospect_id'].errors && f['prospect_id'].errors['required'] ">
                            Prospect ID is required.
                        </div>
                    </div>
                    <small class="invalid-feedback" *ngIf="existingMessage">{{ existingMessage }}</small>
                </div>
            </div> -->
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-300">person</span>
            Name
            <span class="text-red-500">*</span>
          </label>
          <input pInputText id="bp_full_name" type="text" formControlName="bp_full_name" placeholder="Name"
            [ngClass]="{ 'is-invalid': submitted && f['bp_full_name'].errors }" class="h-3rem w-full" />
          <div *ngIf="submitted && f['bp_full_name'].errors" class="p-error">
            <div *ngIf="
                submitted &&
                f['bp_full_name'].errors &&
                f['bp_full_name'].errors['required']
              ">
              Name is required.
            </div>
          </div>
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">mail</span>
            Email Address <span class="text-red-500">*</span>
          </label>
          <input pInputText id="email_address" type="text" formControlName="email_address" placeholder="Email Address"
            [ngClass]="{ 'is-invalid': submitted && f['email_address'].errors }" class="h-3rem w-full" />
          <div *ngIf="submitted && f['email_address'].errors" class="p-error">
            <div *ngIf="f['email_address'].errors['required']">
              Email is required.
            </div>
            <div *ngIf="f['email_address'].errors['email']">
              Email is invalid.
            </div>
          </div>
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">globe</span>
            Wesbite
          </label>
          <input pInputText id="website_url" type="text" formControlName="website_url" placeholder="Website"
            class="h-3rem w-full" [ngClass]="{ 'is-invalid': submitted && f['website_url'].errors }" />
          <div *ngIf="submitted && f['website_url'].errors" class="p-error">
            <div *ngIf="f['website_url'].errors['pattern']">
              Please enter a valid website URL.
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">supervisor_account</span>
                        Owner
                    </label>
                    <input pInputText id="owner" type="text" formControlName="owner" placeholder="Owner"
                        class="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">pin_drop</span>
                        Address Line
                    </label>
                    <input pInputText id="additional_street_prefix_name" type="text"
                        formControlName="additional_street_prefix_name" placeholder="Address Line 1"
                        class="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">pin_drop</span>
                        Address Line 2
                    </label>
                    <input pInputText id="additional_street_suffix_name" type="text"
                        formControlName="additional_street_suffix_name" placeholder="Address Line 2"
                        class="h-3rem w-full" />
                </div>
            </div> -->
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">pin</span>
            House Number
          </label>
          <input pInputText id="house_number" type="text" formControlName="house_number" placeholder="House Number"
            class="h-3rem w-full" />
        </div>
      </div>

      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">near_me</span>
            Street
          </label>
          <input pInputText id="street_name" type="text" formControlName="street_name" placeholder="Street"
            class="h-3rem w-full" />
        </div>
      </div>

      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">home_pin</span>
            City
          </label>
          <input pInputText id="city_name" type="text" formControlName="city_name" placeholder="City"
            class="h-3rem w-full" />
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">map</span>
            Country <span class="text-red-500">*</span>
          </label>
          <p-dropdown [options]="countries" optionLabel="name" optionValue="isoCode" [(ngModel)]="selectedCountry"
            (onChange)="onCountryChange()" [filter]="true" formControlName="country" [styleClass]="'h-3rem w-full'"
            placeholder="Select Country" [ngClass]="{ 'is-invalid': submitted && f['country'].errors }">
          </p-dropdown>
          <div *ngIf="submitted && f['country'].errors" class="p-error">
            <div *ngIf="
                submitted &&
                f['country'].errors &&
                f['country'].errors['required']
              ">
              Country is required.
            </div>
          </div>
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">location_on</span>
            State <span class="text-red-500">*</span>
          </label>
          <p-dropdown [options]="states" optionLabel="name" optionValue="isoCode" [(ngModel)]="selectedState"
            formControlName="region" placeholder="Select State" [disabled]="!selectedCountry"
            [styleClass]="'h-3rem w-full'" [ngClass]="{ 'is-invalid': submitted && f['region'].errors }">
          </p-dropdown>
          <div *ngIf="submitted && f['region'].errors" class="p-error">
            <div *ngIf="
                submitted &&
                f['region'].errors &&
                f['region'].errors['required']
              ">
              State is required.
            </div>
          </div>
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">code_blocks</span>
            Zip Code <span class="text-red-500">*</span>
          </label>
          <input pInputText id="postal_code" type="text" formControlName="postal_code" placeholder="Zip Code"
            class="h-3rem w-full" [ngClass]="{ 'is-invalid': submitted && f['postal_code'].errors }" />
          <div *ngIf="submitted && f['postal_code'].errors" class="p-error">
            <div *ngIf="
                submitted &&
                f['postal_code'].errors &&
                f['postal_code'].errors['required']
              ">
              Zip Code is required.
            </div>
          </div>
          <div *ngIf="ProspectForm.get('postal_code')?.touched && ProspectForm.get('postal_code')?.invalid">
            <div *ngIf="ProspectForm.get('postal_code')?.errors?.['pattern']" class="p-error">
              ZIP code must be exactly 5 letters or digits.
            </div>
          </div>
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">fax</span>
            Fax Number
          </label>
          <input pInputText id="fax_number" type="text" formControlName="fax_number" placeholder="Fax Number"
            class="h-3rem w-full" />
        </div>
      </div>

      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">phone_iphone</span>
            Mobile
          </label>
          <div class="flex align-items-center gap-2">
            <app-country-wise-mobile [formGroup]="ProspectForm" controlName="country" phoneFieldName="mobile"
              [selectedCountry]="selectedCountryForMobile"
              (validationResult)="mobileValidationMessage = $event"></app-country-wise-mobile>
            <input pInputText id="mobile" type="text" formControlName="mobile" placeholder="Mobile"
              class="h-3rem w-full" (input)="triggerMobileValidation()" />
          </div>
          <div class="p-error" *ngIf="ProspectForm.get('mobile')?.touched">
            <div *ngIf="mobileValidationMessage">{{ mobileValidationMessage }}</div>
          </div>

        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">phone</span>
            Phone <span class="text-red-500">*</span>
          </label>
          <div class="flex align-items-center gap-2">
            <app-country-wise-mobile [formGroup]="ProspectForm" controlName="country" phoneFieldName="phone_number"
              [selectedCountry]="selectedCountryForMobile"
              (validationResult)="phoneValidationMessage = $event"></app-country-wise-mobile>
            <input pInputText id="phone_number" type="text" formControlName="phone_number" placeholder="Phone"
              class="h-3rem w-full" (input)="triggerMobileValidation()"
              [ngClass]="{ 'is-invalid': submitted && f['phone_number'].errors }" />
          </div>
          <div class="p-error">
            <div *ngIf="(ProspectForm.get('phone_number')?.touched || submitted) && phoneValidationMessage">
              {{ phoneValidationMessage }}
            </div>
            <div *ngIf="(ProspectForm.get('phone_number')?.touched || submitted) && f['phone_number'].errors?.required">
              Phone is required.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="border-top-2 border-gray-400 my-5"></div>

  <div class="card shadow-1 border-round-xl p-5 mb-4 border-1 border-solid border-50">
    <div class="flex justify-content-between align-items-center mb-3">
      <h3 class="m-0 flex align-items-center h-3rem">Contacts</h3>

      <div class="flex gap-3">
        <p-button label="Existing Contact" (click)="showExistingDialog('right')" icon="pi pi-users" iconPos="right"
          [styleClass]="'font-semibold px-3'" [rounded]="true"></p-button>
        <button pButton type="button" pTooltip="New Contact" tooltipPosition="top" icon="pi pi-plus"
          class="p-button-rounded p-button-primary" iconPos="right" label="New Contact"
          (click)="addNewContact()"></button>
      </div>
    </div>

    <p-table #dt [value]="contacts?.controls" [paginator]="false" [rows]="10" responsiveLayout="scroll"
      class="prospect-add-table">
      <ng-template pTemplate="header">
        <tr>
          <th class="text-left w-2">
            <span class="flex align-items-center gap-1 text-color font-medium">
              <span class="material-symbols-rounded text-2xl text-600">person</span>
              First Name<span class="text-red-500">*</span>
            </span>
          </th>
          <th class="text-left w-2">
            <span class="flex align-items-center gap-1 text-color font-medium">
              <span class="material-symbols-rounded text-2xl text-600">person</span>
              Last Name<span class="text-red-500">*</span>
            </span>
          </th>

          <th class="text-left w-2">
            <span class="flex align-items-center gap-1 text-color font-medium">
              <span class="material-symbols-rounded text-2xl text-600">inbox_text_person</span>
              Department<span class="text-red-500">*</span>
            </span>
          </th>
          <th class="text-left w-2">
            <span class="flex align-items-center gap-1 text-color font-medium">
              <span class="material-symbols-rounded text-2xl text-600">mail</span>
              Email Address<span class="text-red-500">*</span>
            </span>
          </th>
          <th class="text-left w-2">
            <span class="flex align-items-center gap-1 text-color font-medium">
              <span class="material-symbols-rounded text-2xl text-600">phone_iphone</span>
              Mobile<span class="text-red-500">*</span>
            </span>
          </th>
          <th class="text-left" style="width: 60px;">
            <span class="flex align-items-center gap-1 text-color font-medium">
              <span class="material-symbols-rounded text-2xl text-600">delete</span>
              Action
            </span>
          </th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-contact let-i="rowIndex">
        <tr [formGroup]="contact">
          <!-- First Name -->
          <td>
            <input pInputText type="text" class="h-3rem w-full" formControlName="first_name"
              placeholder="Enter a First Name" />
            <small class="p-error" *ngIf="isFieldInvalid(i, 'first_name', 'contacts')">This is Required.</small>
          </td>
          <!-- Last Name -->
          <td>
            <input pInputText type="text" class="h-3rem w-full" formControlName="last_name"
              placeholder="Enter a Last Name" />
            <small class="p-error" *ngIf="isFieldInvalid(i, 'last_name', 'contacts')">This is Required.</small>
          </td>

          <!-- Department -->
          <td>
            <p-dropdown [options]="cpDepartments" optionLabel="name" appendTo="body" placeholder="Select Department"
              [styleClass]="'h-3rem w-full'" (onChange)="onChangeDepartment($event, contact)"></p-dropdown>
            <small class="p-error" *ngIf="
                isFieldInvalid(i, 'contact_person_department', 'contacts') ||
                isFieldInvalid(i, 'contact_person_department_name', 'contacts')
              ">
              Select a valid Department.</small>
          </td>
          <!-- Email -->
          <td>
            <input pInputText type="email" class="h-3rem w-full" formControlName="email_address"
              placeholder="Enter Email" />
            <small class="p-error" *ngIf="isFieldInvalid(i, 'email_address', 'contacts')">Enter a valid email.</small>
          </td>
          <!-- Phone Number -->
          <td>
            <input pInputText type="text" class="h-3rem w-full" formControlName="mobile" placeholder="Enter Mobile" />
            <small class="p-error" *ngIf="isFieldInvalid(i, 'mobile', 'contacts')">Enter a valid phone.</small>
          </td>
          <!-- Delete Button -->
          <td class="pl-5">
            <button pButton pRipple type="button" icon="pi pi-trash" class="p-button-rounded p-button-danger"
              (click)="deleteContact(i)" title="Delete" *ngIf="contacts.length > 1"></button>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>

  <div class="border-top-2 border-gray-400 my-5"></div>

  <div class="card shadow-1 border-round-xl p-5 mb-4 border-1 border-solid border-50">
    <div class="flex justify-content-between align-items-center mb-3">
      <h3 class="m-0 flex align-items-center h-3rem">Employees</h3>
      <button pButton type="button" pTooltip="New Employee" tooltipPosition="top" icon="pi pi-plus"
        class="p-button-rounded p-button-primary" iconPos="right" label="Add Employee"
        (click)="addNewEmployee()"></button>
    </div>

    <p-table #dt [value]="employees?.controls" [paginator]="false" [rows]="10" responsiveLayout="scroll"
      class="prospect-add-table">
      <ng-template pTemplate="header">
        <tr>
          <th class="text-left w-4">
            <span class="flex align-items-center gap-1 text-color font-medium">
              <span class="material-symbols-rounded text-2xl text-600">supervisor_account</span>
              Role
            </span>
          </th>
          <th class="text-left w-4">
            <span class="flex align-items-center gap-1 text-color font-medium">
              <span class="material-symbols-rounded text-2xl text-600">badge</span>
              Employee
            </span>
          </th>
          <th class="text-left w-2">
            <span class="flex align-items-center gap-1 text-color font-medium">
              <span class="material-symbols-rounded text-2xl text-600">delete</span>
              Action
            </span>
          </th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-employee let-i="rowIndex">
        <tr [formGroup]="employee">
          <td>
            <p-dropdown [options]="partnerfunction" optionLabel="label" optionValue="value" appendTo="body"
              formControlName="partner_function" loading="partnerLoading" placeholder="Select Partner Function"
              styleClass="h-3rem w-full">
            </p-dropdown>
          </td>

          <td>
            <app-employee-select formControlName="bp_customer_number"></app-employee-select>
          </td>

          <!-- Delete Button -->
          <td class="pl-5">
            <button pButton pRipple type="button" icon="pi pi-trash" class="p-button-rounded p-button-danger"
              (click)="deleteEmployee(i)" title="Delete" *ngIf="employees.length > 1"></button>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
  <div class="flex align-items-center gap-3 mt-4">
    <button pButton type="button" label="Cancel"
      class="p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem"
      (click)="onCancel()"></button>
    <button pButton type="submit" label="Create" class="p-button-rounded justify-content-center w-9rem h-3rem"
      (click)="onSubmit()"></button>
  </div>
</form>
<p-dialog [modal]="true" [(visible)]="existingDialogVisible" [style]="{ width: '42rem' }" [position]="'right'"
  [draggable]="false" class="prospect-popup">
  <ng-template pTemplate="header">
    <h4>Contact Information</h4>
  </ng-template>

  <form [formGroup]="ProspectForm" class="relative flex flex-column gap-1">
    <div class="field flex align-items-center text-base">
      <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Contacts">
        <span class="material-symbols-rounded">person</span>Contacts
      </label>
      <div class="form-input flex-1 relative">
        <ng-select pInputText [items]="contacts$ | async" bindLabel="bp_full_name" [hideSelected]="true"
          [loading]="contactLoading" [minTermLength]="0" formControlName="contactexisting" [typeahead]="contactInput$"
          [maxSelectedItems]="10" appendTo="body" [class]="'multiselect-dropdown p-inputtext p-component p-element'"
          placeholder="Search for a contact">
          <ng-template ng-option-tmp let-item="item">
            <span>{{ item.bp_id }}</span>
            <span *ngIf="item.bp_full_name"> : {{ item.bp_full_name }}</span>
            <span *ngIf="item.email"> : {{ item.email }}</span>
            <span *ngIf="item.mobile"> : {{ item.mobile }}</span>
          </ng-template>
        </ng-select>
      </div>
    </div>
    <div class="flex justify-content-end gap-2 mt-3">
      <button pButton type="button"
        class="p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center font-medium w-9rem h-3rem"
        (click)="existingDialogVisible = false">
        Cancel
      </button>
      <button pButton type="button" class="p-button-rounded justify-content-center w-9rem h-3rem"
        (click)="selectExistingContact()">
        Save
      </button>
    </div>
  </form>
</p-dialog>