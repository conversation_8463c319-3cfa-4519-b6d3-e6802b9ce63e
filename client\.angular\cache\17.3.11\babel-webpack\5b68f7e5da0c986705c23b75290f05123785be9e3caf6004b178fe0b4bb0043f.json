{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, inject, Injectable, ElementRef, Renderer2, makeEnvironmentProviders, input, output, signal, HostListener, Directive, Pipe } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS } from '@angular/forms';\nvar MaskExpression;\n(function (MaskExpression) {\n  MaskExpression[\"SEPARATOR\"] = \"separator\";\n  MaskExpression[\"PERCENT\"] = \"percent\";\n  MaskExpression[\"IP\"] = \"IP\";\n  MaskExpression[\"CPF_CNPJ\"] = \"CPF_CNPJ\";\n  MaskExpression[\"MONTH\"] = \"M\";\n  MaskExpression[\"MONTHS\"] = \"M0\";\n  MaskExpression[\"MINUTE\"] = \"m\";\n  MaskExpression[\"HOUR\"] = \"h\";\n  MaskExpression[\"HOURS\"] = \"H\";\n  MaskExpression[\"MINUTES\"] = \"m0\";\n  MaskExpression[\"HOURS_HOUR\"] = \"Hh\";\n  MaskExpression[\"SECONDS\"] = \"s0\";\n  MaskExpression[\"HOURS_MINUTES_SECONDS\"] = \"Hh:m0:s0\";\n  MaskExpression[\"EMAIL_MASK\"] = \"A*@A*.A*\";\n  MaskExpression[\"HOURS_MINUTES\"] = \"Hh:m0\";\n  MaskExpression[\"MINUTES_SECONDS\"] = \"m0:s0\";\n  MaskExpression[\"DAYS_MONTHS_YEARS\"] = \"d0/M0/0000\";\n  MaskExpression[\"DAYS_MONTHS\"] = \"d0/M0\";\n  MaskExpression[\"DAYS\"] = \"d0\";\n  MaskExpression[\"DAY\"] = \"d\";\n  MaskExpression[\"SECOND\"] = \"s\";\n  MaskExpression[\"LETTER_S\"] = \"S\";\n  MaskExpression[\"DOT\"] = \".\";\n  MaskExpression[\"COMMA\"] = \",\";\n  MaskExpression[\"CURLY_BRACKETS_LEFT\"] = \"{\";\n  MaskExpression[\"CURLY_BRACKETS_RIGHT\"] = \"}\";\n  MaskExpression[\"MINUS\"] = \"-\";\n  MaskExpression[\"OR\"] = \"||\";\n  MaskExpression[\"HASH\"] = \"#\";\n  MaskExpression[\"EMPTY_STRING\"] = \"\";\n  MaskExpression[\"SYMBOL_STAR\"] = \"*\";\n  MaskExpression[\"SYMBOL_QUESTION\"] = \"?\";\n  MaskExpression[\"SLASH\"] = \"/\";\n  MaskExpression[\"WHITE_SPACE\"] = \" \";\n  MaskExpression[\"NUMBER_ZERO\"] = \"0\";\n  MaskExpression[\"NUMBER_NINE\"] = \"9\";\n  MaskExpression[\"BACKSPACE\"] = \"Backspace\";\n  MaskExpression[\"DELETE\"] = \"Delete\";\n  MaskExpression[\"ARROW_LEFT\"] = \"ArrowLeft\";\n  MaskExpression[\"ARROW_UP\"] = \"ArrowUp\";\n  MaskExpression[\"DOUBLE_ZERO\"] = \"00\";\n})(MaskExpression || (MaskExpression = {}));\nconst NGX_MASK_CONFIG = new InjectionToken('ngx-mask config');\nconst NEW_CONFIG = new InjectionToken('new ngx-mask config');\nconst INITIAL_CONFIG = new InjectionToken('initial ngx-mask config');\nconst initialConfig = {\n  suffix: '',\n  prefix: '',\n  thousandSeparator: ' ',\n  decimalMarker: ['.', ','],\n  clearIfNotMatch: false,\n  showMaskTyped: false,\n  instantPrefix: false,\n  placeHolderCharacter: '_',\n  dropSpecialCharacters: true,\n  hiddenInput: false,\n  shownMaskExpression: '',\n  separatorLimit: '',\n  allowNegativeNumbers: false,\n  validation: true,\n  specialCharacters: ['-', '/', '(', ')', '.', ':', ' ', '+', ',', '@', '[', ']', '\"', \"'\"],\n  leadZeroDateTime: false,\n  apm: false,\n  leadZero: false,\n  keepCharacterPositions: false,\n  triggerOnMaskChange: false,\n  inputTransformFn: value => value,\n  outputTransformFn: value => value,\n  maskFilled: new EventEmitter(),\n  patterns: {\n    '0': {\n      pattern: new RegExp('\\\\d')\n    },\n    '9': {\n      pattern: new RegExp('\\\\d'),\n      optional: true\n    },\n    X: {\n      pattern: new RegExp('\\\\d'),\n      symbol: '*'\n    },\n    A: {\n      pattern: new RegExp('[a-zA-Z0-9]')\n    },\n    S: {\n      pattern: new RegExp('[a-zA-Z]')\n    },\n    U: {\n      pattern: new RegExp('[A-Z]')\n    },\n    L: {\n      pattern: new RegExp('[a-z]')\n    },\n    d: {\n      pattern: new RegExp('\\\\d')\n    },\n    m: {\n      pattern: new RegExp('\\\\d')\n    },\n    M: {\n      pattern: new RegExp('\\\\d')\n    },\n    H: {\n      pattern: new RegExp('\\\\d')\n    },\n    h: {\n      pattern: new RegExp('\\\\d')\n    },\n    s: {\n      pattern: new RegExp('\\\\d')\n    }\n  }\n};\nconst timeMasks = [MaskExpression.HOURS_MINUTES_SECONDS, MaskExpression.HOURS_MINUTES, MaskExpression.MINUTES_SECONDS];\nconst withoutValidation = [MaskExpression.PERCENT, MaskExpression.HOURS_HOUR, MaskExpression.SECONDS, MaskExpression.MINUTES, MaskExpression.SEPARATOR, MaskExpression.DAYS_MONTHS_YEARS, MaskExpression.DAYS_MONTHS, MaskExpression.DAYS, MaskExpression.MONTHS];\nclass NgxMaskApplierService {\n  _config = inject(NGX_MASK_CONFIG);\n  dropSpecialCharacters = this._config.dropSpecialCharacters;\n  hiddenInput = this._config.hiddenInput;\n  clearIfNotMatch = this._config.clearIfNotMatch;\n  specialCharacters = this._config.specialCharacters;\n  patterns = this._config.patterns;\n  prefix = this._config.prefix;\n  suffix = this._config.suffix;\n  thousandSeparator = this._config.thousandSeparator;\n  decimalMarker = this._config.decimalMarker;\n  customPattern;\n  showMaskTyped = this._config.showMaskTyped;\n  placeHolderCharacter = this._config.placeHolderCharacter;\n  validation = this._config.validation;\n  separatorLimit = this._config.separatorLimit;\n  allowNegativeNumbers = this._config.allowNegativeNumbers;\n  leadZeroDateTime = this._config.leadZeroDateTime;\n  leadZero = this._config.leadZero;\n  apm = this._config.apm;\n  inputTransformFn = this._config.inputTransformFn;\n  outputTransformFn = this._config.outputTransformFn;\n  keepCharacterPositions = this._config.keepCharacterPositions;\n  instantPrefix = this._config.instantPrefix;\n  triggerOnMaskChange = this._config.triggerOnMaskChange;\n  _shift = new Set();\n  plusOnePosition = false;\n  maskExpression = '';\n  actualValue = '';\n  showKeepCharacterExp = '';\n  shownMaskExpression = this._config.shownMaskExpression;\n  deletedSpecialCharacter = false;\n  ipError;\n  cpfCnpjError;\n  applyMask(inputValue, maskExpression, position = 0, justPasted = false, backspaced = false,\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  cb = () => {}) {\n    if (!maskExpression || typeof inputValue !== 'string') {\n      return MaskExpression.EMPTY_STRING;\n    }\n    let cursor = 0;\n    let result = '';\n    let multi = false;\n    let backspaceShift = false;\n    let shift = 1;\n    let stepBack = false;\n    let processedValue = inputValue;\n    let processedPosition = position;\n    if (processedValue.slice(0, this.prefix.length) === this.prefix) {\n      processedValue = processedValue.slice(this.prefix.length, processedValue.length);\n    }\n    if (!!this.suffix && processedValue.length > 0) {\n      processedValue = this.checkAndRemoveSuffix(processedValue);\n    }\n    if (processedValue === '(' && this.prefix) {\n      processedValue = '';\n    }\n    const inputArray = processedValue.toString().split(MaskExpression.EMPTY_STRING);\n    if (this.allowNegativeNumbers && processedValue.slice(cursor, cursor + 1) === MaskExpression.MINUS) {\n      result += processedValue.slice(cursor, cursor + 1);\n    }\n    if (maskExpression === MaskExpression.IP) {\n      const valuesIP = processedValue.split(MaskExpression.DOT);\n      this.ipError = this._validIP(valuesIP);\n      // eslint-disable-next-line no-param-reassign\n      maskExpression = '***************';\n    }\n    const arr = [];\n    // eslint-disable-next-line @typescript-eslint/prefer-for-of\n    for (let i = 0; i < processedValue.length; i++) {\n      if (processedValue[i]?.match('\\\\d')) {\n        arr.push(processedValue[i] ?? MaskExpression.EMPTY_STRING);\n      }\n    }\n    if (maskExpression === MaskExpression.CPF_CNPJ) {\n      this.cpfCnpjError = arr.length !== 11 && arr.length !== 14;\n      if (arr.length > 11) {\n        // eslint-disable-next-line no-param-reassign\n        maskExpression = '00.000.000/0000-00';\n      } else {\n        // eslint-disable-next-line no-param-reassign\n        maskExpression = '000.000.000-00';\n      }\n    }\n    if (maskExpression.startsWith(MaskExpression.PERCENT)) {\n      if (processedValue.match('[a-z]|[A-Z]') ||\n      // eslint-disable-next-line no-useless-escape\n      processedValue.match(/[-!$%^&*()_+|~=`{}\\[\\]:\";'<>?,\\/.]/) && !backspaced) {\n        processedValue = this._stripToDecimal(processedValue);\n        const precision = this.getPrecision(maskExpression);\n        processedValue = this.checkInputPrecision(processedValue, precision, this.decimalMarker);\n      }\n      const decimalMarker = typeof this.decimalMarker === 'string' ? this.decimalMarker : MaskExpression.DOT;\n      if (processedValue.indexOf(decimalMarker) > 0 && !this.percentage(processedValue.substring(0, processedValue.indexOf(decimalMarker)))) {\n        let base = processedValue.substring(0, processedValue.indexOf(decimalMarker) - 1);\n        if (this.allowNegativeNumbers && processedValue.slice(cursor, cursor + 1) === MaskExpression.MINUS && !backspaced) {\n          base = processedValue.substring(0, processedValue.indexOf(decimalMarker));\n        }\n        processedValue = `${base}${processedValue.substring(processedValue.indexOf(decimalMarker), processedValue.length)}`;\n      }\n      let value = '';\n      // eslint-disable-next-line @typescript-eslint/no-unused-expressions\n      this.allowNegativeNumbers && processedValue.slice(cursor, cursor + 1) === MaskExpression.MINUS ? value = `${MaskExpression.MINUS}${processedValue.slice(cursor + 1, cursor + processedValue.length)}` : value = processedValue;\n      if (this.percentage(value)) {\n        result = this._splitPercentZero(processedValue);\n      } else {\n        result = this._splitPercentZero(processedValue.substring(0, processedValue.length - 1));\n      }\n    } else if (maskExpression.startsWith(MaskExpression.SEPARATOR)) {\n      if (processedValue.match('[wа-яА-Я]') || processedValue.match('[ЁёА-я]') || processedValue.match('[a-z]|[A-Z]') || processedValue.match(/[-@#!$%\\\\^&*()_£¬'+|~=`{}\\]:\";<>.?/]/) || processedValue.match('[^A-Za-z0-9,]')) {\n        processedValue = this._stripToDecimal(processedValue);\n      }\n      const precision = this.getPrecision(maskExpression);\n      let decimalMarker = this.decimalMarker;\n      if (Array.isArray(this.decimalMarker)) {\n        if (this.actualValue.includes(this.decimalMarker[0]) || this.actualValue.includes(this.decimalMarker[1])) {\n          decimalMarker = this.actualValue.includes(this.decimalMarker[0]) ? this.decimalMarker[0] : this.decimalMarker[1];\n        } else {\n          decimalMarker = this.decimalMarker.find(dm => dm !== this.thousandSeparator);\n        }\n      }\n      if (backspaced) {\n        const {\n          decimalMarkerIndex,\n          nonZeroIndex\n        } = this._findFirstNonZeroAndDecimalIndex(processedValue, decimalMarker);\n        const zeroIndexMinus = processedValue[0] === MaskExpression.MINUS;\n        const zeroIndexNumberZero = processedValue[0] === MaskExpression.NUMBER_ZERO;\n        const zeroIndexDecimalMarker = processedValue[0] === decimalMarker;\n        const firstIndexDecimalMarker = processedValue[1] === decimalMarker;\n        if (zeroIndexDecimalMarker && !nonZeroIndex || zeroIndexMinus && firstIndexDecimalMarker && !nonZeroIndex || zeroIndexNumberZero && !decimalMarkerIndex && !nonZeroIndex) {\n          processedValue = MaskExpression.NUMBER_ZERO;\n        }\n        if (decimalMarkerIndex && nonZeroIndex && zeroIndexMinus && processedPosition === 1) {\n          if (decimalMarkerIndex < nonZeroIndex || decimalMarkerIndex > nonZeroIndex) {\n            processedValue = MaskExpression.MINUS + processedValue.slice(nonZeroIndex);\n          }\n        }\n        if (!decimalMarkerIndex && nonZeroIndex && processedValue.length > nonZeroIndex) {\n          processedValue = zeroIndexMinus ? MaskExpression.MINUS + processedValue.slice(nonZeroIndex) : processedValue.slice(nonZeroIndex);\n        }\n        if (decimalMarkerIndex && nonZeroIndex && processedPosition === 0) {\n          if (decimalMarkerIndex < nonZeroIndex) {\n            processedValue = processedValue.slice(decimalMarkerIndex - 1);\n          }\n          if (decimalMarkerIndex > nonZeroIndex) {\n            processedValue = processedValue.slice(nonZeroIndex);\n          }\n        }\n      }\n      if (precision === 0) {\n        processedValue = this.allowNegativeNumbers ? processedValue.length > 2 && processedValue[0] === MaskExpression.MINUS && processedValue[1] === MaskExpression.NUMBER_ZERO && processedValue[2] !== this.thousandSeparator && processedValue[2] !== MaskExpression.COMMA && processedValue[2] !== MaskExpression.DOT ? '-' + processedValue.slice(2, processedValue.length) : processedValue[0] === MaskExpression.NUMBER_ZERO && processedValue.length > 1 && processedValue[1] !== this.thousandSeparator && processedValue[1] !== MaskExpression.COMMA && processedValue[1] !== MaskExpression.DOT ? processedValue.slice(1, processedValue.length) : processedValue : processedValue.length > 1 && processedValue[0] === MaskExpression.NUMBER_ZERO && processedValue[1] !== this.thousandSeparator && processedValue[1] !== MaskExpression.COMMA && processedValue[1] !== MaskExpression.DOT ? processedValue.slice(1, processedValue.length) : processedValue;\n      } else {\n        if (processedValue[0] === decimalMarker && processedValue.length > 1 && !backspaced) {\n          processedValue = MaskExpression.NUMBER_ZERO + processedValue.slice(0, processedValue.length + 1);\n          this.plusOnePosition = true;\n        }\n        if (processedValue[0] === MaskExpression.NUMBER_ZERO && processedValue[1] !== decimalMarker && processedValue[1] !== this.thousandSeparator && !backspaced) {\n          processedValue = processedValue.length > 1 ? processedValue.slice(0, 1) + decimalMarker + processedValue.slice(1, processedValue.length + 1) : processedValue;\n          this.plusOnePosition = true;\n        }\n        if (this.allowNegativeNumbers && !backspaced && processedValue[0] === MaskExpression.MINUS && (processedValue[1] === decimalMarker || processedValue[1] === MaskExpression.NUMBER_ZERO)) {\n          processedValue = processedValue[1] === decimalMarker && processedValue.length > 2 ? processedValue.slice(0, 1) + MaskExpression.NUMBER_ZERO + processedValue.slice(1, processedValue.length) : processedValue[1] === MaskExpression.NUMBER_ZERO && processedValue.length > 2 && processedValue[2] !== decimalMarker ? processedValue.slice(0, 2) + decimalMarker + processedValue.slice(2, processedValue.length) : processedValue;\n          this.plusOnePosition = true;\n        }\n      }\n      // TODO: we had different rexexps here for the different cases... but tests dont seam to bother - check this\n      //  separator: no COMMA, dot-sep: no SPACE, COMMA OK, comma-sep: no SPACE, COMMA OK\n      const thousandSeparatorCharEscaped = this._charToRegExpExpression(this.thousandSeparator);\n      let invalidChars = '@#!$%^&*()_+|~=`{}\\\\[\\\\]:\\\\s,\\\\.\";<>?\\\\/'.replace(thousandSeparatorCharEscaped, '');\n      //.replace(decimalMarkerEscaped, '');\n      if (Array.isArray(this.decimalMarker)) {\n        for (const marker of this.decimalMarker) {\n          invalidChars = invalidChars.replace(this._charToRegExpExpression(marker), MaskExpression.EMPTY_STRING);\n        }\n      } else {\n        invalidChars = invalidChars.replace(this._charToRegExpExpression(this.decimalMarker), '');\n      }\n      const invalidCharRegexp = new RegExp('[' + invalidChars + ']');\n      if (processedValue.match(invalidCharRegexp)) {\n        processedValue = processedValue.substring(0, processedValue.length - 1);\n      }\n      processedValue = this.checkInputPrecision(processedValue, precision, this.decimalMarker);\n      const strForSep = processedValue.replace(new RegExp(thousandSeparatorCharEscaped, 'g'), '');\n      result = this._formatWithSeparators(strForSep, this.thousandSeparator, this.decimalMarker, precision);\n      const commaShift = result.indexOf(MaskExpression.COMMA) - processedValue.indexOf(MaskExpression.COMMA);\n      const shiftStep = result.length - processedValue.length;\n      const backspacedDecimalMarkerWithSeparatorLimit = backspaced && result.length < inputValue.length && this.separatorLimit;\n      if ((result[processedPosition - 1] === this.thousandSeparator || result[processedPosition - this.prefix.length]) && this.prefix && backspaced) {\n        processedPosition = processedPosition - 1;\n      } else if (shiftStep > 0 && result[processedPosition] !== this.thousandSeparator || backspacedDecimalMarkerWithSeparatorLimit) {\n        backspaceShift = true;\n        let _shift = 0;\n        do {\n          this._shift.add(processedPosition + _shift);\n          _shift++;\n        } while (_shift < shiftStep);\n      } else if (result[processedPosition - 1] === this.thousandSeparator || shiftStep === -4 || shiftStep === -3 || result[processedPosition] === this.thousandSeparator) {\n        this._shift.clear();\n        this._shift.add(processedPosition - 1);\n      } else if (commaShift !== 0 && processedPosition > 0 && !(result.indexOf(MaskExpression.COMMA) >= processedPosition && processedPosition > 3) || !(result.indexOf(MaskExpression.DOT) >= processedPosition && processedPosition > 3) && shiftStep <= 0) {\n        this._shift.clear();\n        backspaceShift = true;\n        shift = shiftStep;\n        processedPosition += shiftStep;\n        this._shift.add(processedPosition);\n      } else {\n        this._shift.clear();\n      }\n    } else {\n      for (\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      let i = 0, inputSymbol = inputArray[0]; i < inputArray.length; i++, inputSymbol = inputArray[i] ?? MaskExpression.EMPTY_STRING) {\n        if (cursor === maskExpression.length) {\n          break;\n        }\n        const symbolStarInPattern = (MaskExpression.SYMBOL_STAR in this.patterns);\n        if (this._checkSymbolMask(inputSymbol, maskExpression[cursor] ?? MaskExpression.EMPTY_STRING) && maskExpression[cursor + 1] === MaskExpression.SYMBOL_QUESTION) {\n          result += inputSymbol;\n          cursor += 2;\n        } else if (maskExpression[cursor + 1] === MaskExpression.SYMBOL_STAR && multi && this._checkSymbolMask(inputSymbol, maskExpression[cursor + 2] ?? MaskExpression.EMPTY_STRING)) {\n          result += inputSymbol;\n          cursor += 3;\n          multi = false;\n        } else if (this._checkSymbolMask(inputSymbol, maskExpression[cursor] ?? MaskExpression.EMPTY_STRING) && maskExpression[cursor + 1] === MaskExpression.SYMBOL_STAR && !symbolStarInPattern) {\n          result += inputSymbol;\n          multi = true;\n        } else if (maskExpression[cursor + 1] === MaskExpression.SYMBOL_QUESTION && this._checkSymbolMask(inputSymbol, maskExpression[cursor + 2] ?? MaskExpression.EMPTY_STRING)) {\n          result += inputSymbol;\n          cursor += 3;\n        } else if (this._checkSymbolMask(inputSymbol, maskExpression[cursor] ?? MaskExpression.EMPTY_STRING)) {\n          if (maskExpression[cursor] === MaskExpression.HOURS) {\n            if (this.apm ? Number(inputSymbol) > 9 : Number(inputSymbol) > 2) {\n              processedPosition = !this.leadZeroDateTime ? processedPosition + 1 : processedPosition;\n              cursor += 1;\n              this._shiftStep(cursor);\n              i--;\n              if (this.leadZeroDateTime) {\n                result += '0';\n              }\n              continue;\n            }\n          }\n          if (maskExpression[cursor] === MaskExpression.HOUR) {\n            if (this.apm ? result.length === 1 && Number(result) > 1 || result === '1' && Number(inputSymbol) > 2 || processedValue.slice(cursor - 1, cursor).length === 1 && Number(processedValue.slice(cursor - 1, cursor)) > 2 || processedValue.slice(cursor - 1, cursor) === '1' && Number(inputSymbol) > 2 : result === '2' && Number(inputSymbol) > 3 || (result.slice(cursor - 2, cursor) === '2' || result.slice(cursor - 3, cursor) === '2' || result.slice(cursor - 4, cursor) === '2' || result.slice(cursor - 1, cursor) === '2') && Number(inputSymbol) > 3 && cursor > 10) {\n              processedPosition = processedPosition + 1;\n              cursor += 1;\n              i--;\n              continue;\n            }\n          }\n          if (maskExpression[cursor] === MaskExpression.MINUTE || maskExpression[cursor] === MaskExpression.SECOND) {\n            if (Number(inputSymbol) > 5) {\n              processedPosition = !this.leadZeroDateTime ? processedPosition + 1 : processedPosition;\n              cursor += 1;\n              this._shiftStep(cursor);\n              i--;\n              if (this.leadZeroDateTime) {\n                result += '0';\n              }\n              continue;\n            }\n          }\n          const daysCount = 31;\n          const inputValueCursor = processedValue[cursor];\n          const inputValueCursorPlusOne = processedValue[cursor + 1];\n          const inputValueCursorPlusTwo = processedValue[cursor + 2];\n          const inputValueCursorMinusOne = processedValue[cursor - 1];\n          const inputValueCursorMinusTwo = processedValue[cursor - 2];\n          const inputValueSliceMinusThreeMinusOne = processedValue.slice(cursor - 3, cursor - 1);\n          const inputValueSliceMinusOnePlusOne = processedValue.slice(cursor - 1, cursor + 1);\n          const inputValueSliceCursorPlusTwo = processedValue.slice(cursor, cursor + 2);\n          const inputValueSliceMinusTwoCursor = processedValue.slice(cursor - 2, cursor);\n          if (maskExpression[cursor] === MaskExpression.DAY) {\n            const maskStartWithMonth = maskExpression.slice(0, 2) === MaskExpression.MONTHS;\n            const startWithMonthInput = maskExpression.slice(0, 2) === MaskExpression.MONTHS && this.specialCharacters.includes(inputValueCursorMinusTwo);\n            if (Number(inputSymbol) > 3 && this.leadZeroDateTime || !maskStartWithMonth && (Number(inputValueSliceCursorPlusTwo) > daysCount || Number(inputValueSliceMinusOnePlusOne) > daysCount || this.specialCharacters.includes(inputValueCursorPlusOne)) || (startWithMonthInput ? Number(inputValueSliceMinusOnePlusOne) > daysCount || !this.specialCharacters.includes(inputValueCursor) && this.specialCharacters.includes(inputValueCursorPlusTwo) || this.specialCharacters.includes(inputValueCursor) : Number(inputValueSliceCursorPlusTwo) > daysCount || this.specialCharacters.includes(inputValueCursorPlusOne) && !backspaced)) {\n              processedPosition = !this.leadZeroDateTime ? processedPosition + 1 : processedPosition;\n              cursor += 1;\n              this._shiftStep(cursor);\n              i--;\n              if (this.leadZeroDateTime) {\n                result += '0';\n              }\n              continue;\n            }\n          }\n          if (maskExpression[cursor] === MaskExpression.MONTH) {\n            const monthsCount = 12;\n            // mask without day\n            const withoutDays = cursor === 0 && (Number(inputSymbol) > 2 || Number(inputValueSliceCursorPlusTwo) > monthsCount || this.specialCharacters.includes(inputValueCursorPlusOne) && !backspaced);\n            // day<10 && month<12 for input\n            const specialChart = maskExpression.slice(cursor + 2, cursor + 3);\n            const day1monthInput = inputValueSliceMinusThreeMinusOne.includes(specialChart) && maskExpression.includes('d0') && (this.specialCharacters.includes(inputValueCursorMinusTwo) && Number(inputValueSliceMinusOnePlusOne) > monthsCount && !this.specialCharacters.includes(inputValueCursor) || this.specialCharacters.includes(inputValueCursor));\n            //  month<12 && day<10 for input\n            const day2monthInput = Number(inputValueSliceMinusThreeMinusOne) <= daysCount && !this.specialCharacters.includes(inputValueSliceMinusThreeMinusOne) && this.specialCharacters.includes(inputValueCursorMinusOne) && (Number(inputValueSliceCursorPlusTwo) > monthsCount || this.specialCharacters.includes(inputValueCursorPlusOne));\n            // cursor === 5 && without days\n            const day2monthInputDot = Number(inputValueSliceCursorPlusTwo) > monthsCount && cursor === 5 || this.specialCharacters.includes(inputValueCursorPlusOne) && cursor === 5;\n            // // day<10 && month<12 for paste whole data\n            const day1monthPaste = Number(inputValueSliceMinusThreeMinusOne) > daysCount && !this.specialCharacters.includes(inputValueSliceMinusThreeMinusOne) && !this.specialCharacters.includes(inputValueSliceMinusTwoCursor) && Number(inputValueSliceMinusTwoCursor) > monthsCount && maskExpression.includes('d0');\n            // 10<day<31 && month<12 for paste whole data\n            const day2monthPaste = Number(inputValueSliceMinusThreeMinusOne) <= daysCount && !this.specialCharacters.includes(inputValueSliceMinusThreeMinusOne) && !this.specialCharacters.includes(inputValueCursorMinusOne) && Number(inputValueSliceMinusOnePlusOne) > monthsCount;\n            if (Number(inputSymbol) > 1 && this.leadZeroDateTime || withoutDays || day1monthInput || day2monthPaste || day1monthPaste || day2monthInput || day2monthInputDot && !this.leadZeroDateTime) {\n              processedPosition = !this.leadZeroDateTime ? processedPosition + 1 : processedPosition;\n              cursor += 1;\n              this._shiftStep(cursor);\n              i--;\n              if (this.leadZeroDateTime) {\n                result += '0';\n              }\n              continue;\n            }\n          }\n          result += inputSymbol;\n          cursor++;\n        } else if (this.specialCharacters.includes(inputSymbol) && maskExpression[cursor] === inputSymbol) {\n          result += inputSymbol;\n          cursor++;\n        } else if (this.specialCharacters.indexOf(maskExpression[cursor] ?? MaskExpression.EMPTY_STRING) !== -1) {\n          result += maskExpression[cursor];\n          cursor++;\n          this._shiftStep(cursor);\n          i--;\n        } else if (maskExpression[cursor] === MaskExpression.NUMBER_NINE && this.showMaskTyped) {\n          this._shiftStep(cursor);\n        } else if (this.patterns[maskExpression[cursor] ?? MaskExpression.EMPTY_STRING] && this.patterns[maskExpression[cursor] ?? MaskExpression.EMPTY_STRING]?.optional) {\n          if (!!inputArray[cursor] && maskExpression !== '***************' && maskExpression !== '000.000.000-00' && maskExpression !== '00.000.000/0000-00' && !maskExpression.match(/^9+\\.0+$/) && !this.patterns[maskExpression[cursor] ?? MaskExpression.EMPTY_STRING]?.optional) {\n            result += inputArray[cursor];\n          }\n          if (maskExpression.includes(MaskExpression.NUMBER_NINE + MaskExpression.SYMBOL_STAR) && maskExpression.includes(MaskExpression.NUMBER_ZERO + MaskExpression.SYMBOL_STAR)) {\n            cursor++;\n          }\n          cursor++;\n          i--;\n        } else if (this.maskExpression[cursor + 1] === MaskExpression.SYMBOL_STAR && this._findSpecialChar(this.maskExpression[cursor + 2] ?? MaskExpression.EMPTY_STRING) && this._findSpecialChar(inputSymbol) === this.maskExpression[cursor + 2] && multi) {\n          cursor += 3;\n          result += inputSymbol;\n        } else if (this.maskExpression[cursor + 1] === MaskExpression.SYMBOL_QUESTION && this._findSpecialChar(this.maskExpression[cursor + 2] ?? MaskExpression.EMPTY_STRING) && this._findSpecialChar(inputSymbol) === this.maskExpression[cursor + 2] && multi) {\n          cursor += 3;\n          result += inputSymbol;\n        } else if (this.showMaskTyped && this.specialCharacters.indexOf(inputSymbol) < 0 && inputSymbol !== this.placeHolderCharacter && this.placeHolderCharacter.length === 1) {\n          stepBack = true;\n        }\n      }\n    }\n    if (result[processedPosition - 1] && result.length + 1 === maskExpression.length && this.specialCharacters.indexOf(maskExpression[maskExpression.length - 1] ?? MaskExpression.EMPTY_STRING) !== -1) {\n      result += maskExpression[maskExpression.length - 1];\n    }\n    let newPosition = processedPosition + 1;\n    while (this._shift.has(newPosition)) {\n      shift++;\n      newPosition++;\n    }\n    let actualShift = justPasted && !maskExpression.startsWith(MaskExpression.SEPARATOR) ? cursor : this._shift.has(processedPosition) ? shift : 0;\n    if (stepBack) {\n      actualShift--;\n    }\n    cb(actualShift, backspaceShift);\n    if (shift < 0) {\n      this._shift.clear();\n    }\n    let onlySpecial = false;\n    if (backspaced) {\n      onlySpecial = inputArray.every(char => this.specialCharacters.includes(char));\n    }\n    let res = `${this.prefix}${onlySpecial ? MaskExpression.EMPTY_STRING : result}${this.showMaskTyped ? '' : this.suffix}`;\n    if (result.length === 0) {\n      res = this.instantPrefix ? `${this.prefix}${result}` : `${result}`;\n    }\n    const isSpecialCharacterMaskFirstSymbol = processedValue.length === 1 && this.specialCharacters.includes(maskExpression[0]) && processedValue !== maskExpression[0];\n    if (!this._checkSymbolMask(processedValue, maskExpression[1]) && isSpecialCharacterMaskFirstSymbol) {\n      return '';\n    }\n    if (result.includes(MaskExpression.MINUS) && this.prefix && this.allowNegativeNumbers) {\n      if (backspaced && result === MaskExpression.MINUS) {\n        return '';\n      }\n      res = `${MaskExpression.MINUS}${this.prefix}${result.split(MaskExpression.MINUS).join(MaskExpression.EMPTY_STRING)}${this.suffix}`;\n    }\n    return res;\n  }\n  _findDropSpecialChar(inputSymbol) {\n    if (Array.isArray(this.dropSpecialCharacters)) {\n      return this.dropSpecialCharacters.find(val => val === inputSymbol);\n    }\n    return this._findSpecialChar(inputSymbol);\n  }\n  _findSpecialChar(inputSymbol) {\n    return this.specialCharacters.find(val => val === inputSymbol);\n  }\n  _checkSymbolMask(inputSymbol, maskSymbol) {\n    this.patterns = this.customPattern ? this.customPattern : this.patterns;\n    return (this.patterns[maskSymbol]?.pattern && this.patterns[maskSymbol]?.pattern.test(inputSymbol)) ?? false;\n  }\n  _formatWithSeparators = (str, thousandSeparatorChar, decimalChars, precision) => {\n    let x = [];\n    let decimalChar = '';\n    if (Array.isArray(decimalChars)) {\n      const regExp = new RegExp(decimalChars.map(v => '[\\\\^$.|?*+()'.indexOf(v) >= 0 ? `\\\\${v}` : v).join('|'));\n      x = str.split(regExp);\n      decimalChar = str.match(regExp)?.[0] ?? MaskExpression.EMPTY_STRING;\n    } else {\n      x = str.split(decimalChars);\n      decimalChar = decimalChars;\n    }\n    const decimals = x.length > 1 ? `${decimalChar}${x[1]}` : MaskExpression.EMPTY_STRING;\n    let res = x[0] ?? MaskExpression.EMPTY_STRING;\n    const separatorLimit = this.separatorLimit.replace(/\\s/g, MaskExpression.EMPTY_STRING);\n    if (separatorLimit && +separatorLimit) {\n      if (res[0] === MaskExpression.MINUS) {\n        res = `-${res.slice(1, res.length).slice(0, separatorLimit.length)}`;\n      } else {\n        res = res.slice(0, separatorLimit.length);\n      }\n    }\n    const rgx = /(\\d+)(\\d{3})/;\n    while (thousandSeparatorChar && rgx.test(res)) {\n      res = res.replace(rgx, '$1' + thousandSeparatorChar + '$2');\n    }\n    if (typeof precision === 'undefined') {\n      return res + decimals;\n    } else if (precision === 0) {\n      return res;\n    }\n    return res + decimals.substring(0, precision + 1);\n  };\n  percentage = str => {\n    const sanitizedStr = str.replace(',', '.');\n    const value = Number(this.allowNegativeNumbers && str.includes(MaskExpression.MINUS) ? sanitizedStr.slice(1, str.length) : sanitizedStr);\n    return !isNaN(value) && value >= 0 && value <= 100;\n  };\n  getPrecision = maskExpression => {\n    const x = maskExpression.split(MaskExpression.DOT);\n    if (x.length > 1) {\n      return Number(x[x.length - 1]);\n    }\n    return Infinity;\n  };\n  checkAndRemoveSuffix = inputValue => {\n    for (let i = this.suffix?.length - 1; i >= 0; i--) {\n      const substr = this.suffix.substring(i, this.suffix?.length);\n      if (inputValue.includes(substr) && i !== this.suffix?.length - 1 && (i - 1 < 0 || !inputValue.includes(this.suffix.substring(i - 1, this.suffix?.length)))) {\n        return inputValue.replace(substr, MaskExpression.EMPTY_STRING);\n      }\n    }\n    return inputValue;\n  };\n  checkInputPrecision = (inputValue, precision, decimalMarker) => {\n    let processedInputValue = inputValue;\n    let processedDecimalMarker = decimalMarker;\n    if (precision < Infinity) {\n      // TODO need think about decimalMarker\n      if (Array.isArray(processedDecimalMarker)) {\n        const marker = processedDecimalMarker.find(dm => dm !== this.thousandSeparator);\n        processedDecimalMarker = marker ? marker : processedDecimalMarker[0];\n      }\n      const precisionRegEx = new RegExp(this._charToRegExpExpression(processedDecimalMarker) + `\\\\d{${precision}}.*$`);\n      const precisionMatch = processedInputValue.match(precisionRegEx);\n      const precisionMatchLength = (precisionMatch && precisionMatch[0]?.length) ?? 0;\n      if (precisionMatchLength - 1 > precision) {\n        const diff = precisionMatchLength - 1 - precision;\n        processedInputValue = processedInputValue.substring(0, processedInputValue.length - diff);\n      }\n      if (precision === 0 && this._compareOrIncludes(processedInputValue[processedInputValue.length - 1], processedDecimalMarker, this.thousandSeparator)) {\n        processedInputValue = processedInputValue.substring(0, processedInputValue.length - 1);\n      }\n    }\n    return processedInputValue;\n  };\n  _stripToDecimal(str) {\n    return str.split(MaskExpression.EMPTY_STRING).filter((i, idx) => {\n      const isDecimalMarker = typeof this.decimalMarker === 'string' ? i === this.decimalMarker :\n      // TODO (inepipenko) use utility type\n      this.decimalMarker.includes(i);\n      return i.match('^-?\\\\d') || i === this.thousandSeparator || isDecimalMarker || i === MaskExpression.MINUS && idx === 0 && this.allowNegativeNumbers;\n    }).join(MaskExpression.EMPTY_STRING);\n  }\n  _charToRegExpExpression(char) {\n    // if (Array.isArray(char)) {\n    // \treturn char.map((v) => ('[\\\\^$.|?*+()'.indexOf(v) >= 0 ? `\\\\${v}` : v)).join('|');\n    // }\n    if (char) {\n      const charsToEscape = '[\\\\^$.|?*+()';\n      return char === ' ' ? '\\\\s' : charsToEscape.indexOf(char) >= 0 ? `\\\\${char}` : char;\n    }\n    return char;\n  }\n  _shiftStep(cursor) {\n    this._shift.add(cursor + this.prefix.length || 0);\n  }\n  _compareOrIncludes(value, comparedValue, excludedValue) {\n    return Array.isArray(comparedValue) ? comparedValue.filter(v => v !== excludedValue).includes(value) : value === comparedValue;\n  }\n  _validIP(valuesIP) {\n    return !(valuesIP.length === 4 && !valuesIP.some((value, index) => {\n      if (valuesIP.length !== index + 1) {\n        return value === MaskExpression.EMPTY_STRING || Number(value) > 255;\n      }\n      return value === MaskExpression.EMPTY_STRING || Number(value.substring(0, 3)) > 255;\n    }));\n  }\n  _splitPercentZero(value) {\n    if (value === MaskExpression.MINUS && this.allowNegativeNumbers) {\n      return value;\n    }\n    const decimalIndex = typeof this.decimalMarker === 'string' ? value.indexOf(this.decimalMarker) : value.indexOf(MaskExpression.DOT);\n    const emptyOrMinus = this.allowNegativeNumbers && value.includes(MaskExpression.MINUS) ? '-' : '';\n    if (decimalIndex === -1) {\n      const parsedValue = parseInt(emptyOrMinus ? value.slice(1, value.length) : value, 10);\n      return isNaN(parsedValue) ? MaskExpression.EMPTY_STRING : `${emptyOrMinus}${parsedValue}`;\n    } else {\n      const integerPart = parseInt(value.replace('-', '').substring(0, decimalIndex), 10);\n      const decimalPart = value.substring(decimalIndex + 1);\n      const integerString = isNaN(integerPart) ? '' : integerPart.toString();\n      const decimal = typeof this.decimalMarker === 'string' ? this.decimalMarker : MaskExpression.DOT;\n      return integerString === MaskExpression.EMPTY_STRING ? MaskExpression.EMPTY_STRING : `${emptyOrMinus}${integerString}${decimal}${decimalPart}`;\n    }\n  }\n  _findFirstNonZeroAndDecimalIndex(inputString, decimalMarker) {\n    let decimalMarkerIndex = null;\n    let nonZeroIndex = null;\n    for (let i = 0; i < inputString.length; i++) {\n      const char = inputString[i];\n      if (char === decimalMarker && decimalMarkerIndex === null) {\n        decimalMarkerIndex = i;\n      }\n      if (char && char >= '1' && char <= '9' && nonZeroIndex === null) {\n        nonZeroIndex = i;\n      }\n      if (decimalMarkerIndex !== null && nonZeroIndex !== null) {\n        break;\n      }\n    }\n    return {\n      decimalMarkerIndex,\n      nonZeroIndex\n    };\n  }\n  static ɵfac = function NgxMaskApplierService_Factory(t) {\n    return new (t || NgxMaskApplierService)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NgxMaskApplierService,\n    factory: NgxMaskApplierService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaskApplierService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass NgxMaskService extends NgxMaskApplierService {\n  isNumberValue = false;\n  maskIsShown = '';\n  selStart = null;\n  selEnd = null;\n  maskChanged = false;\n  maskExpressionArray = [];\n  previousValue = '';\n  currentValue = '';\n  /**\n   * Whether we are currently in writeValue function, in this case when applying the mask we don't want to trigger onChange function,\n   * since writeValue should be a one way only process of writing the DOM value based on the Angular model value.\n   */\n  writingValue = false;\n  _emitValue = false;\n  _start;\n  _end;\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onChange = _ => {};\n  _elementRef = inject(ElementRef, {\n    optional: true\n  });\n  document = inject(DOCUMENT);\n  _config = inject(NGX_MASK_CONFIG);\n  _renderer = inject(Renderer2, {\n    optional: true\n  });\n  /**\n   * Applies the mask to the input value.\n   * @param inputValue The input value to be masked.\n   * @param maskExpression The mask expression to apply.\n   * @param position The position in the input value.\n   * @param justPasted Whether the value was just pasted.\n   * @param backspaced Whether the value was backspaced.\n   * @param cb Callback function.\n   * @returns The masked value.\n   */\n  applyMask(inputValue, maskExpression, position = 0, justPasted = false, backspaced = false,\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  cb = () => {}) {\n    // If no mask expression, return the input value or the actual value\n    if (!maskExpression) {\n      return inputValue !== this.actualValue ? this.actualValue : inputValue;\n    }\n    // Show mask in input if required\n    this.maskIsShown = this.showMaskTyped ? this.showMaskInInput() : MaskExpression.EMPTY_STRING;\n    // Handle specific mask expressions\n    if (this.maskExpression === MaskExpression.IP && this.showMaskTyped) {\n      this.maskIsShown = this.showMaskInInput(inputValue || MaskExpression.HASH);\n    }\n    if (this.maskExpression === MaskExpression.CPF_CNPJ && this.showMaskTyped) {\n      this.maskIsShown = this.showMaskInInput(inputValue || MaskExpression.HASH);\n    }\n    // Handle empty input value with mask typed\n    if (!inputValue && this.showMaskTyped) {\n      this.formControlResult(this.prefix);\n      return `${this.prefix}${this.maskIsShown}${this.suffix}`;\n    }\n    const getSymbol = !!inputValue && typeof this.selStart === 'number' ? inputValue[this.selStart] ?? MaskExpression.EMPTY_STRING : MaskExpression.EMPTY_STRING;\n    let newInputValue = '';\n    let newPosition = position;\n    // Handle hidden input or input with asterisk symbol\n    if ((this.hiddenInput || inputValue && inputValue.indexOf(MaskExpression.SYMBOL_STAR) >= 0) && !this.writingValue) {\n      let actualResult = inputValue && inputValue.length === 1 ? inputValue.split(MaskExpression.EMPTY_STRING) : this.actualValue.split(MaskExpression.EMPTY_STRING);\n      // Handle backspace\n      if (backspaced) {\n        actualResult = actualResult.slice(0, position).concat(actualResult.slice(position + 1));\n      }\n      // Remove mask if showMaskTyped is true\n      if (this.showMaskTyped) {\n        // eslint-disable-next-line no-param-reassign\n        inputValue = this.removeMask(inputValue);\n        actualResult = this.removeMask(actualResult.join('')).split(MaskExpression.EMPTY_STRING);\n      }\n      // Handle selection start and end\n      if (typeof this.selStart === 'object' && typeof this.selEnd === 'object') {\n        this.selStart = Number(this.selStart);\n        this.selEnd = Number(this.selEnd);\n      } else {\n        if (inputValue !== MaskExpression.EMPTY_STRING && actualResult.length) {\n          if (typeof this.selStart === 'number' && typeof this.selEnd === 'number') {\n            if (inputValue.length > actualResult.length) {\n              actualResult.splice(this.selStart, 0, getSymbol);\n            } else if (inputValue.length < actualResult.length) {\n              if (actualResult.length - inputValue.length === 1) {\n                if (backspaced) {\n                  actualResult.splice(this.selStart - 1, 1);\n                } else {\n                  actualResult.splice(inputValue.length - 1, 1);\n                }\n              } else {\n                actualResult.splice(this.selStart, this.selEnd - this.selStart);\n              }\n            }\n          }\n        } else {\n          actualResult = [];\n        }\n      }\n      // Remove mask if showMaskTyped is true and hiddenInput is false\n      if (this.showMaskTyped && !this.hiddenInput) {\n        newInputValue = this.removeMask(inputValue);\n      }\n      // Handle actual value length\n      if (this.actualValue.length) {\n        if (actualResult.length < inputValue.length) {\n          newInputValue = this.shiftTypedSymbols(actualResult.join(MaskExpression.EMPTY_STRING));\n        } else if (actualResult.length === inputValue.length) {\n          newInputValue = actualResult.join(MaskExpression.EMPTY_STRING);\n        } else {\n          newInputValue = inputValue;\n        }\n      } else {\n        newInputValue = inputValue;\n      }\n    }\n    // Handle just pasted input\n    if (justPasted && (this.hiddenInput || !this.hiddenInput)) {\n      newInputValue = inputValue;\n    }\n    // Handle backspace with special characters\n    if (backspaced && this.specialCharacters.indexOf(this.maskExpression[newPosition] ?? MaskExpression.EMPTY_STRING) !== -1 && this.showMaskTyped && !this.prefix) {\n      newInputValue = this.currentValue;\n    }\n    // Handle deleted special character\n    if (this.deletedSpecialCharacter && newPosition) {\n      if (this.specialCharacters.includes(this.actualValue.slice(newPosition, newPosition + 1))) {\n        newPosition = newPosition + 1;\n      } else if (maskExpression.slice(newPosition - 1, newPosition + 1) !== MaskExpression.MONTHS) {\n        newPosition = newPosition - 2;\n      }\n      this.deletedSpecialCharacter = false;\n    }\n    // Remove mask if showMaskTyped is true and placeHolderCharacter length is 1\n    if (this.showMaskTyped && this.placeHolderCharacter.length === 1 && !this.leadZeroDateTime) {\n      newInputValue = this.removeMask(newInputValue);\n    }\n    // Handle mask changed\n    if (this.maskChanged) {\n      newInputValue = inputValue;\n    } else {\n      newInputValue = Boolean(newInputValue) && newInputValue.length ? newInputValue : inputValue;\n    }\n    // Handle showMaskTyped and keepCharacterPositions\n    if (this.showMaskTyped && this.keepCharacterPositions && this.actualValue && !justPasted && !this.writingValue) {\n      const value = this.dropSpecialCharacters ? this.removeMask(this.actualValue) : this.actualValue;\n      this.formControlResult(value);\n      return this.actualValue ? this.actualValue : `${this.prefix}${this.maskIsShown}${this.suffix}`;\n    }\n    // Apply the mask using the parent class method\n    const result = super.applyMask(newInputValue, maskExpression, newPosition, justPasted, backspaced, cb);\n    this.actualValue = this.getActualValue(result);\n    // handle some separator implications:\n    // a.) adjust decimalMarker default (. -> ,) if thousandSeparator is a dot\n    if (this.thousandSeparator === MaskExpression.DOT && this.decimalMarker === MaskExpression.DOT) {\n      this.decimalMarker = MaskExpression.COMMA;\n    }\n    // b) remove decimal marker from list of special characters to mask\n    if (this.maskExpression.startsWith(MaskExpression.SEPARATOR) && this.dropSpecialCharacters === true) {\n      this.specialCharacters = this.specialCharacters.filter(item => !this._compareOrIncludes(item, this.decimalMarker, this.thousandSeparator) //item !== this.decimalMarker, // !\n      );\n    }\n    // Update previous and current values\n    if (result || result === '') {\n      this.previousValue = this.currentValue;\n      this.currentValue = result;\n      this._emitValue = this.previousValue !== this.currentValue || newInputValue !== this.currentValue && this.writingValue || this.previousValue === this.currentValue && justPasted;\n    }\n    // Propagate the input value back to the Angular model\n    // eslint-disable-next-line no-unused-expressions,@typescript-eslint/no-unused-expressions\n    this._emitValue ? this.formControlResult(result) : '';\n    // Handle hidden input and showMaskTyped\n    if (!this.showMaskTyped || this.showMaskTyped && this.hiddenInput) {\n      if (this.hiddenInput) {\n        return `${this.hideInput(result, this.maskExpression)}${this.maskIsShown.slice(result.length)}`;\n      }\n      return result;\n    }\n    const resLen = result.length;\n    const prefNmask = `${this.prefix}${this.maskIsShown}${this.suffix}`;\n    // Handle specific mask expressions\n    if (this.maskExpression.includes(MaskExpression.HOURS)) {\n      const countSkipedSymbol = this._numberSkipedSymbols(result);\n      return `${result}${prefNmask.slice(resLen + countSkipedSymbol)}`;\n    } else if (this.maskExpression === MaskExpression.IP || this.maskExpression === MaskExpression.CPF_CNPJ) {\n      return `${result}${prefNmask}`;\n    }\n    return `${result}${prefNmask.slice(resLen)}`;\n  }\n  // get the number of characters that were shifted\n  _numberSkipedSymbols(value) {\n    const regex = /(^|\\D)(\\d\\D)/g;\n    let match = regex.exec(value);\n    let countSkipedSymbol = 0;\n    while (match != null) {\n      countSkipedSymbol += 1;\n      match = regex.exec(value);\n    }\n    return countSkipedSymbol;\n  }\n  applyValueChanges(position, justPasted, backspaced,\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  cb = () => {}) {\n    const formElement = this._elementRef?.nativeElement;\n    if (!formElement) {\n      return;\n    }\n    formElement.value = this.applyMask(formElement.value, this.maskExpression, position, justPasted, backspaced, cb);\n    if (formElement === this._getActiveElement()) {\n      return;\n    }\n    this.clearIfNotMatchFn();\n  }\n  hideInput(inputValue, maskExpression) {\n    return inputValue.split(MaskExpression.EMPTY_STRING).map((curr, index) => {\n      if (this.patterns && this.patterns[maskExpression[index] ?? MaskExpression.EMPTY_STRING] && this.patterns[maskExpression[index] ?? MaskExpression.EMPTY_STRING]?.symbol) {\n        return this.patterns[maskExpression[index] ?? MaskExpression.EMPTY_STRING]?.symbol;\n      }\n      return curr;\n    }).join(MaskExpression.EMPTY_STRING);\n  }\n  // this function is not necessary, it checks result against maskExpression\n  getActualValue(res) {\n    const compare = res.split(MaskExpression.EMPTY_STRING).filter((symbol, i) => {\n      const maskChar = this.maskExpression[i] ?? MaskExpression.EMPTY_STRING;\n      return this._checkSymbolMask(symbol, maskChar) || this.specialCharacters.includes(maskChar) && symbol === maskChar;\n    });\n    if (compare.join(MaskExpression.EMPTY_STRING) === res) {\n      return compare.join(MaskExpression.EMPTY_STRING);\n    }\n    return res;\n  }\n  shiftTypedSymbols(inputValue) {\n    let symbolToReplace = '';\n    const newInputValue = inputValue && inputValue.split(MaskExpression.EMPTY_STRING).map((currSymbol, index) => {\n      if (this.specialCharacters.includes(inputValue[index + 1] ?? MaskExpression.EMPTY_STRING) && inputValue[index + 1] !== this.maskExpression[index + 1]) {\n        symbolToReplace = currSymbol;\n        return inputValue[index + 1];\n      }\n      if (symbolToReplace.length) {\n        const replaceSymbol = symbolToReplace;\n        symbolToReplace = MaskExpression.EMPTY_STRING;\n        return replaceSymbol;\n      }\n      return currSymbol;\n    }) || [];\n    return newInputValue.join(MaskExpression.EMPTY_STRING);\n  }\n  /**\n   * Convert number value to string\n   * 3.1415 -> '3.1415'\n   * 1e-7 -> '0.0000001'\n   */\n  numberToString(value) {\n    if (!value && value !== 0 || this.maskExpression.startsWith(MaskExpression.SEPARATOR) && (this.leadZero || !this.dropSpecialCharacters) || this.maskExpression.startsWith(MaskExpression.SEPARATOR) && this.separatorLimit.length > 14 && String(value).length > 14) {\n      return String(value);\n    }\n    return Number(value).toLocaleString('fullwide', {\n      useGrouping: false,\n      maximumFractionDigits: 20\n    }).replace(`/${MaskExpression.MINUS}/`, MaskExpression.MINUS);\n  }\n  showMaskInInput(inputVal) {\n    if (this.showMaskTyped && !!this.shownMaskExpression) {\n      if (this.maskExpression.length !== this.shownMaskExpression.length) {\n        throw new Error('Mask expression must match mask placeholder length');\n      } else {\n        return this.shownMaskExpression;\n      }\n    } else if (this.showMaskTyped) {\n      if (inputVal) {\n        if (this.maskExpression === MaskExpression.IP) {\n          return this._checkForIp(inputVal);\n        }\n        if (this.maskExpression === MaskExpression.CPF_CNPJ) {\n          return this._checkForCpfCnpj(inputVal);\n        }\n      }\n      if (this.placeHolderCharacter.length === this.maskExpression.length) {\n        return this.placeHolderCharacter;\n      }\n      return this.maskExpression.replace(/\\w/g, this.placeHolderCharacter);\n    }\n    return '';\n  }\n  clearIfNotMatchFn() {\n    const formElement = this._elementRef?.nativeElement;\n    if (!formElement) {\n      return;\n    }\n    if (this.clearIfNotMatch && this.prefix.length + this.maskExpression.length + this.suffix.length !== formElement.value.replace(this.placeHolderCharacter, MaskExpression.EMPTY_STRING).length) {\n      this.formElementProperty = ['value', MaskExpression.EMPTY_STRING];\n      this.applyMask('', this.maskExpression);\n    }\n  }\n  set formElementProperty([name, value]) {\n    if (!this._renderer || !this._elementRef) {\n      return;\n    }\n    //[TODO]: andriikamaldinov1 find better solution\n    Promise.resolve().then(() => this._renderer?.setProperty(this._elementRef?.nativeElement, name, value));\n  }\n  checkDropSpecialCharAmount(mask) {\n    const chars = mask.split(MaskExpression.EMPTY_STRING).filter(item => this._findDropSpecialChar(item));\n    return chars.length;\n  }\n  removeMask(inputValue) {\n    return this._removeMask(this._removeSuffix(this._removePrefix(inputValue)), this.specialCharacters.concat('_').concat(this.placeHolderCharacter));\n  }\n  _checkForIp(inputVal) {\n    if (inputVal === MaskExpression.HASH) {\n      return `${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n    }\n    const arr = [];\n    // eslint-disable-next-line @typescript-eslint/prefer-for-of\n    for (let i = 0; i < inputVal.length; i++) {\n      const value = inputVal[i] ?? MaskExpression.EMPTY_STRING;\n      if (!value) {\n        continue;\n      }\n      if (value.match('\\\\d')) {\n        arr.push(value);\n      }\n    }\n    if (arr.length <= 3) {\n      return `${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n    }\n    if (arr.length > 3 && arr.length <= 6) {\n      return `${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n    }\n    if (arr.length > 6 && arr.length <= 9) {\n      return this.placeHolderCharacter;\n    }\n    if (arr.length > 9 && arr.length <= 12) {\n      return '';\n    }\n    return '';\n  }\n  _checkForCpfCnpj(inputVal) {\n    const cpf = `${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `-${this.placeHolderCharacter}${this.placeHolderCharacter}`;\n    const cnpj = `${this.placeHolderCharacter}${this.placeHolderCharacter}` + `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `/${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `-${this.placeHolderCharacter}${this.placeHolderCharacter}`;\n    if (inputVal === MaskExpression.HASH) {\n      return cpf;\n    }\n    const arr = [];\n    // eslint-disable-next-line @typescript-eslint/prefer-for-of\n    for (let i = 0; i < inputVal.length; i++) {\n      const value = inputVal[i] ?? MaskExpression.EMPTY_STRING;\n      if (!value) {\n        continue;\n      }\n      if (value.match('\\\\d')) {\n        arr.push(value);\n      }\n    }\n    if (arr.length <= 3) {\n      return cpf.slice(arr.length, cpf.length);\n    }\n    if (arr.length > 3 && arr.length <= 6) {\n      return cpf.slice(arr.length + 1, cpf.length);\n    }\n    if (arr.length > 6 && arr.length <= 9) {\n      return cpf.slice(arr.length + 2, cpf.length);\n    }\n    if (arr.length > 9 && arr.length < 11) {\n      return cpf.slice(arr.length + 3, cpf.length);\n    }\n    if (arr.length === 11) {\n      return '';\n    }\n    if (arr.length === 12) {\n      if (inputVal.length === 17) {\n        return cnpj.slice(16, cnpj.length);\n      }\n      return cnpj.slice(15, cnpj.length);\n    }\n    if (arr.length > 12 && arr.length <= 14) {\n      return cnpj.slice(arr.length + 4, cnpj.length);\n    }\n    return '';\n  }\n  /**\n   * Recursively determine the current active element by navigating the Shadow DOM until the Active Element is found.\n   */\n  _getActiveElement(document = this.document) {\n    const shadowRootEl = document?.activeElement?.shadowRoot;\n    if (!shadowRootEl?.activeElement) {\n      return document.activeElement;\n    } else {\n      return this._getActiveElement(shadowRootEl);\n    }\n  }\n  /**\n   * Propogates the input value back to the Angular model by triggering the onChange function. It won't do this if writingValue\n   * is true. If that is true it means we are currently in the writeValue function, which is supposed to only update the actual\n   * DOM element based on the Angular model value. It should be a one way process, i.e. writeValue should not be modifying the Angular\n   * model value too. Therefore, we don't trigger onChange in this scenario.\n   * @param inputValue the current form input value\n   */\n  formControlResult(inputValue) {\n    const outputTransformFn = this.outputTransformFn ? this.outputTransformFn : v => v;\n    this.writingValue = false;\n    this.maskChanged = false;\n    if (Array.isArray(this.dropSpecialCharacters)) {\n      this.onChange(outputTransformFn(this._toNumber(this._checkSymbols(this._removeMask(this._removeSuffix(this._removePrefix(inputValue)), this.dropSpecialCharacters)))));\n    } else if (this.dropSpecialCharacters || !this.dropSpecialCharacters && this.prefix === inputValue) {\n      this.onChange(outputTransformFn(this._toNumber(this._checkSymbols(this._removeSuffix(this._removePrefix(inputValue))))));\n    } else {\n      this.onChange(outputTransformFn(this._toNumber(inputValue)));\n    }\n  }\n  _toNumber(value) {\n    if (!this.isNumberValue || value === MaskExpression.EMPTY_STRING) {\n      return value;\n    }\n    if (this.maskExpression.startsWith(MaskExpression.SEPARATOR) && (this.leadZero || !this.dropSpecialCharacters)) {\n      return value;\n    }\n    if (String(value).length > 14 && this.maskExpression.startsWith(MaskExpression.SEPARATOR)) {\n      return String(value);\n    }\n    const num = Number(value);\n    if (this.maskExpression.startsWith(MaskExpression.SEPARATOR) && Number.isNaN(num)) {\n      const val = String(value).replace(',', '.');\n      return Number(val);\n    }\n    return Number.isNaN(num) ? value : num;\n  }\n  _removeMask(value, specialCharactersForRemove) {\n    if (this.maskExpression.startsWith(MaskExpression.PERCENT) && value.includes(MaskExpression.DOT)) {\n      return value;\n    }\n    return value ? value.replace(this._regExpForRemove(specialCharactersForRemove), MaskExpression.EMPTY_STRING) : value;\n  }\n  _removePrefix(value) {\n    if (!this.prefix) {\n      return value;\n    }\n    return value ? value.replace(this.prefix, MaskExpression.EMPTY_STRING) : value;\n  }\n  _removeSuffix(value) {\n    if (!this.suffix) {\n      return value;\n    }\n    return value ? value.replace(this.suffix, MaskExpression.EMPTY_STRING) : value;\n  }\n  _retrieveSeparatorValue(result) {\n    let specialCharacters = Array.isArray(this.dropSpecialCharacters) ? this.specialCharacters.filter(v => {\n      return this.dropSpecialCharacters.includes(v);\n    }) : this.specialCharacters;\n    if (!this.deletedSpecialCharacter && this._checkPatternForSpace() && result.includes(MaskExpression.WHITE_SPACE) && this.maskExpression.includes(MaskExpression.SYMBOL_STAR)) {\n      specialCharacters = specialCharacters.filter(char => char !== MaskExpression.WHITE_SPACE);\n    }\n    return this._removeMask(result, specialCharacters);\n  }\n  _regExpForRemove(specialCharactersForRemove) {\n    return new RegExp(specialCharactersForRemove.map(item => `\\\\${item}`).join('|'), 'gi');\n  }\n  _replaceDecimalMarkerToDot(value) {\n    const markers = Array.isArray(this.decimalMarker) ? this.decimalMarker : [this.decimalMarker];\n    return value.replace(this._regExpForRemove(markers), MaskExpression.DOT);\n  }\n  _checkSymbols(result) {\n    let processedResult = result;\n    if (processedResult === MaskExpression.EMPTY_STRING) {\n      return processedResult;\n    }\n    if (this.maskExpression.startsWith(MaskExpression.PERCENT) && this.decimalMarker === MaskExpression.COMMA) {\n      processedResult = processedResult.replace(MaskExpression.COMMA, MaskExpression.DOT);\n    }\n    const separatorPrecision = this._retrieveSeparatorPrecision(this.maskExpression);\n    const separatorValue = this.specialCharacters.length === 0 ? this._retrieveSeparatorValue(processedResult) : this._replaceDecimalMarkerToDot(this._retrieveSeparatorValue(processedResult));\n    if (!this.isNumberValue) {\n      return separatorValue;\n    }\n    if (separatorPrecision) {\n      if (processedResult === this.decimalMarker) {\n        return null;\n      }\n      if (separatorValue.length > 14) {\n        return String(separatorValue);\n      }\n      return this._checkPrecision(this.maskExpression, separatorValue);\n    } else {\n      return separatorValue;\n    }\n  }\n  _checkPatternForSpace() {\n    for (const key in this.patterns) {\n      // eslint-disable-next-line no-prototype-builtins\n      if (this.patterns[key] && this.patterns[key]?.hasOwnProperty('pattern')) {\n        const patternString = this.patterns[key]?.pattern.toString();\n        const pattern = this.patterns[key]?.pattern;\n        if (patternString?.includes(MaskExpression.WHITE_SPACE) && pattern?.test(this.maskExpression)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  // TODO should think about helpers or separting decimal precision to own property\n  _retrieveSeparatorPrecision(maskExpretion) {\n    const matcher = maskExpretion.match(new RegExp(`^separator\\\\.([^d]*)`));\n    return matcher ? Number(matcher[1]) : null;\n  }\n  _checkPrecision(separatorExpression, separatorValue) {\n    const separatorPrecision = this.getPrecision(separatorExpression);\n    let value = separatorValue;\n    if (separatorExpression.indexOf('2') > 0 || this.leadZero && Number(separatorPrecision) > 0) {\n      if (this.decimalMarker === MaskExpression.COMMA && this.leadZero) {\n        value = value.replace(',', '.');\n      }\n      return this.leadZero ? Number(value).toFixed(Number(separatorPrecision)) : Number(value).toFixed(2);\n    }\n    return this.numberToString(value);\n  }\n  _repeatPatternSymbols(maskExp) {\n    return maskExp.match(/{[0-9]+}/) && maskExp.split(MaskExpression.EMPTY_STRING).reduce((accum, currVal, index) => {\n      this._start = currVal === MaskExpression.CURLY_BRACKETS_LEFT ? index : this._start;\n      if (currVal !== MaskExpression.CURLY_BRACKETS_RIGHT) {\n        return this._findSpecialChar(currVal) ? accum + currVal : accum;\n      }\n      this._end = index;\n      const repeatNumber = Number(maskExp.slice(this._start + 1, this._end));\n      const replaceWith = new Array(repeatNumber + 1).join(maskExp[this._start - 1]);\n      if (maskExp.slice(0, this._start).length > 1 && maskExp.includes(MaskExpression.LETTER_S)) {\n        const symbols = maskExp.slice(0, this._start - 1);\n        return symbols.includes(MaskExpression.CURLY_BRACKETS_LEFT) ? accum + replaceWith : symbols + accum + replaceWith;\n      } else {\n        return accum + replaceWith;\n      }\n    }, '') || maskExp;\n  }\n  currentLocaleDecimalMarker() {\n    return 1.1.toLocaleString().substring(1, 2);\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵNgxMaskService_BaseFactory;\n    return function NgxMaskService_Factory(t) {\n      return (ɵNgxMaskService_BaseFactory || (ɵNgxMaskService_BaseFactory = i0.ɵɵgetInheritedFactory(NgxMaskService)))(t || NgxMaskService);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NgxMaskService,\n    factory: NgxMaskService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaskService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * @internal\n */\nfunction _configFactory() {\n  const initConfig = inject(INITIAL_CONFIG);\n  const configValue = inject(NEW_CONFIG);\n  return configValue instanceof Function ? {\n    ...initConfig,\n    ...configValue()\n  } : {\n    ...initConfig,\n    ...configValue\n  };\n}\nfunction provideNgxMask(configValue) {\n  return [{\n    provide: NEW_CONFIG,\n    useValue: configValue\n  }, {\n    provide: INITIAL_CONFIG,\n    useValue: initialConfig\n  }, {\n    provide: NGX_MASK_CONFIG,\n    useFactory: _configFactory\n  }, NgxMaskService];\n}\nfunction provideEnvironmentNgxMask(configValue) {\n  return makeEnvironmentProviders(provideNgxMask(configValue));\n}\nclass NgxMaskDirective {\n  mask = input('');\n  specialCharacters = input([]);\n  patterns = input({});\n  prefix = input('');\n  suffix = input('');\n  thousandSeparator = input(' ');\n  decimalMarker = input('.');\n  dropSpecialCharacters = input(null);\n  hiddenInput = input(null);\n  showMaskTyped = input(null);\n  placeHolderCharacter = input(null);\n  shownMaskExpression = input(null);\n  clearIfNotMatch = input(null);\n  validation = input(null);\n  separatorLimit = input('');\n  allowNegativeNumbers = input(null);\n  leadZeroDateTime = input(null);\n  leadZero = input(null);\n  triggerOnMaskChange = input(null);\n  apm = input(null);\n  inputTransformFn = input(null);\n  outputTransformFn = input(null);\n  keepCharacterPositions = input(null);\n  instantPrefix = input(null);\n  maskFilled = output();\n  _maskValue = signal('');\n  _inputValue = signal('');\n  _position = signal(null);\n  _code = signal('');\n  _maskExpressionArray = signal([]);\n  _justPasted = signal(false);\n  _isFocused = signal(false);\n  /**For IME composition event */\n  _isComposing = signal(false);\n  _maskService = inject(NgxMaskService, {\n    self: true\n  });\n  document = inject(DOCUMENT);\n  _config = inject(NGX_MASK_CONFIG);\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onChange = _ => {};\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onTouch = () => {};\n  ngOnChanges(changes) {\n    const {\n      mask,\n      specialCharacters,\n      patterns,\n      prefix,\n      suffix,\n      thousandSeparator,\n      decimalMarker,\n      dropSpecialCharacters,\n      hiddenInput,\n      showMaskTyped,\n      placeHolderCharacter,\n      shownMaskExpression,\n      clearIfNotMatch,\n      validation,\n      separatorLimit,\n      allowNegativeNumbers,\n      leadZeroDateTime,\n      leadZero,\n      triggerOnMaskChange,\n      apm,\n      inputTransformFn,\n      outputTransformFn,\n      keepCharacterPositions,\n      instantPrefix\n    } = changes;\n    if (mask) {\n      if (mask.currentValue !== mask.previousValue && !mask.firstChange) {\n        this._maskService.maskChanged = true;\n      }\n      if (mask.currentValue && mask.currentValue.split(MaskExpression.OR).length > 1) {\n        this._maskExpressionArray.set(mask.currentValue.split(MaskExpression.OR).sort((a, b) => {\n          return a.length - b.length;\n        }));\n        this._setMask();\n      } else {\n        this._maskExpressionArray.set([]);\n        this._maskValue.set(mask.currentValue || MaskExpression.EMPTY_STRING);\n        this._maskService.maskExpression = this._maskValue();\n      }\n    }\n    if (specialCharacters) {\n      if (!specialCharacters.currentValue || !Array.isArray(specialCharacters.currentValue)) {\n        return;\n      } else {\n        this._maskService.specialCharacters = specialCharacters.currentValue || [];\n      }\n    }\n    if (allowNegativeNumbers) {\n      this._maskService.allowNegativeNumbers = allowNegativeNumbers.currentValue;\n      if (this._maskService.allowNegativeNumbers) {\n        this._maskService.specialCharacters = this._maskService.specialCharacters.filter(c => c !== MaskExpression.MINUS);\n      }\n    }\n    // Only overwrite the mask available patterns if a pattern has actually been passed in\n    if (patterns && patterns.currentValue) {\n      this._maskService.patterns = patterns.currentValue;\n    }\n    if (apm && apm.currentValue) {\n      this._maskService.apm = apm.currentValue;\n    }\n    if (instantPrefix) {\n      this._maskService.instantPrefix = instantPrefix.currentValue;\n    }\n    if (prefix) {\n      this._maskService.prefix = prefix.currentValue;\n    }\n    if (suffix) {\n      this._maskService.suffix = suffix.currentValue;\n    }\n    if (thousandSeparator) {\n      this._maskService.thousandSeparator = thousandSeparator.currentValue;\n      if (thousandSeparator.previousValue && thousandSeparator.currentValue) {\n        const previousDecimalMarker = this._maskService.decimalMarker;\n        if (thousandSeparator.currentValue === this._maskService.decimalMarker) {\n          this._maskService.decimalMarker = thousandSeparator.currentValue === MaskExpression.COMMA ? MaskExpression.DOT : MaskExpression.COMMA;\n        }\n        if (this._maskService.dropSpecialCharacters === true) {\n          this._maskService.specialCharacters = this._config.specialCharacters;\n        }\n        if (typeof previousDecimalMarker === 'string' && typeof this._maskService.decimalMarker === 'string') {\n          this._inputValue.set(this._inputValue().split(thousandSeparator.previousValue).join('').replace(previousDecimalMarker, this._maskService.decimalMarker));\n          this._maskService.actualValue = this._inputValue();\n        }\n        this._maskService.writingValue = true;\n      }\n    }\n    if (decimalMarker) {\n      this._maskService.decimalMarker = decimalMarker.currentValue;\n    }\n    if (dropSpecialCharacters) {\n      this._maskService.dropSpecialCharacters = dropSpecialCharacters.currentValue;\n    }\n    if (hiddenInput) {\n      this._maskService.hiddenInput = hiddenInput.currentValue;\n      if (hiddenInput.previousValue === true && hiddenInput.currentValue === false) {\n        this._inputValue.set(this._maskService.actualValue);\n      }\n    }\n    if (showMaskTyped) {\n      this._maskService.showMaskTyped = showMaskTyped.currentValue;\n      if (showMaskTyped.previousValue === false && showMaskTyped.currentValue === true && this._isFocused()) {\n        requestAnimationFrame(() => {\n          this._maskService._elementRef?.nativeElement.click();\n        });\n      }\n    }\n    if (placeHolderCharacter) {\n      this._maskService.placeHolderCharacter = placeHolderCharacter.currentValue;\n    }\n    if (shownMaskExpression) {\n      this._maskService.shownMaskExpression = shownMaskExpression.currentValue;\n    }\n    if (clearIfNotMatch) {\n      this._maskService.clearIfNotMatch = clearIfNotMatch.currentValue;\n    }\n    if (validation) {\n      this._maskService.validation = validation.currentValue;\n    }\n    if (separatorLimit) {\n      this._maskService.separatorLimit = separatorLimit.currentValue;\n    }\n    if (leadZeroDateTime) {\n      this._maskService.leadZeroDateTime = leadZeroDateTime.currentValue;\n    }\n    if (leadZero) {\n      this._maskService.leadZero = leadZero.currentValue;\n    }\n    if (triggerOnMaskChange) {\n      this._maskService.triggerOnMaskChange = triggerOnMaskChange.currentValue;\n    }\n    if (inputTransformFn) {\n      this._maskService.inputTransformFn = inputTransformFn.currentValue;\n    }\n    if (outputTransformFn) {\n      this._maskService.outputTransformFn = outputTransformFn.currentValue;\n    }\n    if (keepCharacterPositions) {\n      this._maskService.keepCharacterPositions = keepCharacterPositions.currentValue;\n    }\n    this._applyMask();\n  }\n  validate({\n    value\n  }) {\n    const processedValue = typeof value === 'number' ? String(value) : value;\n    const maskValue = this._maskValue();\n    if (!this._maskService.validation || !maskValue) {\n      return null;\n    }\n    if (this._maskService.ipError) {\n      return this._createValidationError(processedValue);\n    }\n    if (this._maskService.cpfCnpjError) {\n      return this._createValidationError(processedValue);\n    }\n    if (maskValue.startsWith(MaskExpression.SEPARATOR)) {\n      return null;\n    }\n    if (withoutValidation.includes(maskValue)) {\n      return null;\n    }\n    if (this._maskService.clearIfNotMatch) {\n      return null;\n    }\n    if (timeMasks.includes(maskValue)) {\n      return this._validateTime(processedValue);\n    }\n    if (maskValue === MaskExpression.EMAIL_MASK) {\n      const emailPattern = /^[^@]+@[^@]+\\.[^@]+$/;\n      if (!emailPattern.test(processedValue) && processedValue) {\n        return this._createValidationError(processedValue);\n      } else {\n        return null;\n      }\n    }\n    if (processedValue && processedValue.length >= 1) {\n      let counterOfOpt = 0;\n      if (maskValue.includes(MaskExpression.CURLY_BRACKETS_LEFT) && maskValue.includes(MaskExpression.CURLY_BRACKETS_RIGHT)) {\n        const lengthInsideCurlyBrackets = maskValue.slice(maskValue.indexOf(MaskExpression.CURLY_BRACKETS_LEFT) + 1, maskValue.indexOf(MaskExpression.CURLY_BRACKETS_RIGHT));\n        return lengthInsideCurlyBrackets === String(processedValue.length) ? null : this._createValidationError(processedValue);\n      }\n      if (maskValue.startsWith(MaskExpression.PERCENT)) {\n        return null;\n      }\n      for (const key in this._maskService.patterns) {\n        if (this._maskService.patterns[key]?.optional) {\n          if (maskValue.indexOf(key) !== maskValue.lastIndexOf(key)) {\n            const opt = maskValue.split(MaskExpression.EMPTY_STRING).filter(i => i === key).join(MaskExpression.EMPTY_STRING);\n            counterOfOpt += opt.length;\n          } else if (maskValue.indexOf(key) !== -1) {\n            counterOfOpt++;\n          }\n          if (maskValue.indexOf(key) !== -1 && processedValue.length >= maskValue.indexOf(key)) {\n            return null;\n          }\n          if (counterOfOpt === maskValue.length) {\n            return null;\n          }\n        }\n      }\n      if (maskValue.indexOf(MaskExpression.SYMBOL_STAR) > 1 && processedValue.length < maskValue.indexOf(MaskExpression.SYMBOL_STAR) || maskValue.indexOf(MaskExpression.SYMBOL_QUESTION) > 1 && processedValue.length < maskValue.indexOf(MaskExpression.SYMBOL_QUESTION)) {\n        return this._createValidationError(processedValue);\n      }\n      if (maskValue.indexOf(MaskExpression.SYMBOL_STAR) === -1 || maskValue.indexOf(MaskExpression.SYMBOL_QUESTION) === -1) {\n        const array = maskValue.split('*');\n        const length = this._maskService.dropSpecialCharacters ? maskValue.length - this._maskService.checkDropSpecialCharAmount(maskValue) - counterOfOpt : this.prefix() ? maskValue.length + this.prefix().length - counterOfOpt : maskValue.length - counterOfOpt;\n        if (array.length === 1) {\n          if (processedValue.length < length) {\n            return this._createValidationError(processedValue);\n          }\n        }\n        if (array.length > 1) {\n          const lastIndexArray = array[array.length - 1];\n          if (lastIndexArray && this._maskService.specialCharacters.includes(lastIndexArray[0]) && String(processedValue).includes(lastIndexArray[0] ?? '') && !this.dropSpecialCharacters()) {\n            const special = value.split(lastIndexArray[0]);\n            return special[special.length - 1].length === lastIndexArray.length - 1 ? null : this._createValidationError(processedValue);\n          } else if ((lastIndexArray && !this._maskService.specialCharacters.includes(lastIndexArray[0]) || !lastIndexArray || this._maskService.dropSpecialCharacters) && processedValue.length >= length - 1) {\n            return null;\n          } else {\n            return this._createValidationError(processedValue);\n          }\n        }\n      }\n      if (maskValue.indexOf(MaskExpression.SYMBOL_STAR) === 1 || maskValue.indexOf(MaskExpression.SYMBOL_QUESTION) === 1) {\n        return null;\n      }\n    }\n    if (value) {\n      this.maskFilled.emit();\n      return null;\n    }\n    return null;\n  }\n  onPaste() {\n    this._justPasted.set(true);\n  }\n  onFocus() {\n    this._isFocused.set(true);\n  }\n  onModelChange(value) {\n    // on form reset we need to update the actualValue\n    if ((value === MaskExpression.EMPTY_STRING || value === null || typeof value === 'undefined') && this._maskService.actualValue) {\n      this._maskService.actualValue = this._maskService.getActualValue(MaskExpression.EMPTY_STRING);\n    }\n  }\n  onInput(e) {\n    // If IME is composing text, we wait for the composed text.\n    if (this._isComposing()) {\n      return;\n    }\n    const el = e.target;\n    const transformedValue = this._maskService.inputTransformFn ? this._maskService.inputTransformFn(el.value) : el.value;\n    if (el.type !== 'number') {\n      if (typeof transformedValue === 'string' || typeof transformedValue === 'number') {\n        el.value = transformedValue.toString();\n        this._inputValue.set(el.value);\n        this._setMask();\n        if (!this._maskValue()) {\n          this.onChange(el.value);\n          return;\n        }\n        let position = el.selectionStart === 1 ? el.selectionStart + this._maskService.prefix.length : el.selectionStart;\n        if (this.showMaskTyped() && this.keepCharacterPositions() && this._maskService.placeHolderCharacter.length === 1) {\n          const suffix = this.suffix();\n          const prefix = this.prefix();\n          const inputSymbol = el.value.slice(position - 1, position);\n          const prefixLength = prefix.length;\n          const checkSymbols = this._maskService._checkSymbolMask(inputSymbol, this._maskService.maskExpression[position - 1 - prefixLength] ?? MaskExpression.EMPTY_STRING);\n          const checkSpecialCharacter = this._maskService._checkSymbolMask(inputSymbol, this._maskService.maskExpression[position + 1 - prefixLength] ?? MaskExpression.EMPTY_STRING);\n          const selectRangeBackspace = this._maskService.selStart === this._maskService.selEnd;\n          const selStart = Number(this._maskService.selStart) - prefixLength;\n          const selEnd = Number(this._maskService.selEnd) - prefixLength;\n          const backspaceOrDelete = this._code() === MaskExpression.BACKSPACE || this._code() === MaskExpression.DELETE;\n          if (backspaceOrDelete) {\n            if (!selectRangeBackspace) {\n              if (this._maskService.selStart === prefixLength) {\n                this._maskService.actualValue = `${prefix}${this._maskService.maskIsShown.slice(0, selEnd)}${this._inputValue().split(prefix).join('')}`;\n              } else if (this._maskService.selStart === this._maskService.maskIsShown.length + prefixLength) {\n                this._maskService.actualValue = `${this._inputValue()}${this._maskService.maskIsShown.slice(selStart, selEnd)}`;\n              } else {\n                this._maskService.actualValue = `${prefix}${this._inputValue().split(prefix).join('').slice(0, selStart)}${this._maskService.maskIsShown.slice(selStart, selEnd)}${this._maskService.actualValue.slice(selEnd + prefixLength, this._maskService.maskIsShown.length + prefixLength)}${suffix}`;\n              }\n            } else if (!this._maskService.specialCharacters.includes(this._maskService.maskExpression.slice(position - prefixLength, position + 1 - prefixLength)) && selectRangeBackspace) {\n              if (selStart === 1 && prefix) {\n                this._maskService.actualValue = `${prefix}${this._maskService.placeHolderCharacter}${el.value.split(prefix).join('').split(suffix).join('')}${suffix}`;\n                position = position - 1;\n              } else {\n                const part1 = el.value.substring(0, position);\n                const part2 = el.value.substring(position);\n                this._maskService.actualValue = `${part1}${this._maskService.placeHolderCharacter}${part2}`;\n              }\n            }\n            position = this._code() === MaskExpression.DELETE ? position + 1 : position;\n          }\n          if (!backspaceOrDelete) {\n            if (!checkSymbols && !checkSpecialCharacter && selectRangeBackspace) {\n              position = Number(el.selectionStart) - 1;\n            } else if (this._maskService.specialCharacters.includes(el.value.slice(position, position + 1)) && checkSpecialCharacter && !this._maskService.specialCharacters.includes(el.value.slice(position + 1, position + 2))) {\n              this._maskService.actualValue = `${el.value.slice(0, position - 1)}${el.value.slice(position, position + 1)}${inputSymbol}${el.value.slice(position + 2)}`;\n              position = position + 1;\n            } else if (checkSymbols) {\n              if (el.value.length === 1 && position === 1) {\n                this._maskService.actualValue = `${prefix}${inputSymbol}${this._maskService.maskIsShown.slice(1, this._maskService.maskIsShown.length)}${suffix}`;\n              } else {\n                this._maskService.actualValue = `${el.value.slice(0, position - 1)}${inputSymbol}${el.value.slice(position + 1).split(suffix).join('')}${suffix}`;\n              }\n            } else if (prefix && el.value.length === 1 && position - prefixLength === 1 && this._maskService._checkSymbolMask(el.value, this._maskService.maskExpression[position - 1 - prefixLength] ?? MaskExpression.EMPTY_STRING)) {\n              this._maskService.actualValue = `${prefix}${el.value}${this._maskService.maskIsShown.slice(1, this._maskService.maskIsShown.length)}${suffix}`;\n            }\n          }\n        }\n        let caretShift = 0;\n        let backspaceShift = false;\n        if (this._code() === MaskExpression.DELETE && MaskExpression.SEPARATOR) {\n          this._maskService.deletedSpecialCharacter = true;\n        }\n        if (this._inputValue().length >= this._maskService.maskExpression.length - 1 && this._code() !== MaskExpression.BACKSPACE && this._maskService.maskExpression === MaskExpression.DAYS_MONTHS_YEARS && position < 10) {\n          const inputSymbol = this._inputValue().slice(position - 1, position);\n          el.value = this._inputValue().slice(0, position - 1) + inputSymbol + this._inputValue().slice(position + 1);\n        }\n        if (this._maskService.maskExpression === MaskExpression.DAYS_MONTHS_YEARS && this.leadZeroDateTime()) {\n          if (position < 3 && Number(el.value) > 31 && Number(el.value) < 40 || position === 5 && Number(el.value.slice(3, 5)) > 12) {\n            position = position + 2;\n          }\n        }\n        if (this._maskService.maskExpression === MaskExpression.HOURS_MINUTES_SECONDS && this.apm()) {\n          if (this._justPasted() && el.value.slice(0, 2) === MaskExpression.DOUBLE_ZERO) {\n            el.value = el.value.slice(1, 2) + el.value.slice(2, el.value.length);\n          }\n          el.value = el.value === MaskExpression.DOUBLE_ZERO ? MaskExpression.NUMBER_ZERO : el.value;\n        }\n        this._maskService.applyValueChanges(position, this._justPasted(), this._code() === MaskExpression.BACKSPACE || this._code() === MaskExpression.DELETE, (shift, _backspaceShift) => {\n          this._justPasted.set(false);\n          caretShift = shift;\n          backspaceShift = _backspaceShift;\n        });\n        // only set the selection if the element is active\n        if (this._getActiveElement() !== el) {\n          return;\n        }\n        if (this._maskService.plusOnePosition) {\n          position = position + 1;\n          this._maskService.plusOnePosition = false;\n        }\n        // update position after applyValueChanges to prevent cursor on wrong position when it has an array of maskExpression\n        if (this._maskExpressionArray().length) {\n          if (this._code() === MaskExpression.BACKSPACE) {\n            const specialChartMinusOne = this.specialCharacters().includes(this._maskService.actualValue.slice(position - 1, position));\n            const allowFewMaskChangeMask = this._maskService.removeMask(this._inputValue())?.length === this._maskService.removeMask(this._maskService.maskExpression)?.length;\n            const specialChartPlusOne = this.specialCharacters().includes(this._maskService.actualValue.slice(position, position + 1));\n            if (allowFewMaskChangeMask && !specialChartPlusOne) {\n              position = el.selectionStart + 1;\n            } else {\n              position = specialChartMinusOne ? position - 1 : position;\n            }\n          } else {\n            position = el.selectionStart === 1 ? el.selectionStart + this._maskService.prefix.length : el.selectionStart;\n          }\n        }\n        this._position.set(this._position() === 1 && this._inputValue().length === 1 ? null : this._position());\n        let positionToApply = this._position() ? this._inputValue().length + position + caretShift : position + (this._code() === MaskExpression.BACKSPACE && !backspaceShift ? 0 : caretShift);\n        if (positionToApply > this._getActualInputLength()) {\n          positionToApply = el.value === this._maskService.decimalMarker && el.value.length === 1 ? this._getActualInputLength() + 1 : this._getActualInputLength();\n        }\n        if (positionToApply < 0) {\n          positionToApply = 0;\n        }\n        el.setSelectionRange(positionToApply, positionToApply);\n        this._position.set(null);\n      } else {\n        // eslint-disable-next-line no-console\n        console.warn('Ngx-mask writeValue work with string | number, your current value:', typeof transformedValue);\n      }\n    } else {\n      if (!this._maskValue()) {\n        this.onChange(el.value);\n        return;\n      }\n      this._maskService.applyValueChanges(el.value.length, this._justPasted(), this._code() === MaskExpression.BACKSPACE || this._code() === MaskExpression.DELETE);\n    }\n  }\n  // IME starts\n  onCompositionStart() {\n    this._isComposing.set(true);\n  }\n  // IME completes\n  onCompositionEnd(e) {\n    this._isComposing.set(false);\n    this._justPasted.set(true);\n    this.onInput(e);\n  }\n  onBlur(e) {\n    if (this._maskValue()) {\n      const el = e.target;\n      if (this._maskService.leadZero && el.value.length > 0 && typeof this._maskService.decimalMarker === 'string') {\n        const maskExpression = this._maskService.maskExpression;\n        const decimalMarker = this._maskService.decimalMarker;\n        const suffix = this._maskService.suffix;\n        const precision = Number(this._maskService.maskExpression.slice(maskExpression.length - 1, maskExpression.length));\n        if (precision > 0) {\n          el.value = suffix ? el.value.split(suffix).join('') : el.value;\n          const decimalPart = el.value.split(decimalMarker)[1];\n          el.value = el.value.includes(decimalMarker) ? el.value + MaskExpression.NUMBER_ZERO.repeat(precision - decimalPart.length) + suffix : el.value + decimalMarker + MaskExpression.NUMBER_ZERO.repeat(precision) + suffix;\n          this._maskService.actualValue = el.value;\n        }\n      }\n      this._maskService.clearIfNotMatchFn();\n    }\n    this._isFocused.set(false);\n    this.onTouch();\n  }\n  onClick(e) {\n    if (!this._maskValue()) {\n      return;\n    }\n    const el = e.target;\n    const posStart = 0;\n    const posEnd = 0;\n    if (el !== null && el.selectionStart !== null && el.selectionStart === el.selectionEnd && el.selectionStart > this._maskService.prefix.length && e.keyCode !== 38) {\n      if (this._maskService.showMaskTyped && !this.keepCharacterPositions()) {\n        // We are showing the mask in the input\n        this._maskService.maskIsShown = this._maskService.showMaskInInput();\n        if (el.setSelectionRange && this._maskService.prefix + this._maskService.maskIsShown === el.value) {\n          // the input ONLY contains the mask, so position the cursor at the start\n          el.focus();\n          el.setSelectionRange(posStart, posEnd);\n        } else {\n          // the input contains some characters already\n          if (el.selectionStart > this._maskService.actualValue.length) {\n            // if the user clicked beyond our value's length, position the cursor at the end of our value\n            el.setSelectionRange(this._maskService.actualValue.length, this._maskService.actualValue.length);\n          }\n        }\n      }\n    }\n    const nextValue = el && (el.value === this._maskService.prefix ? this._maskService.prefix + this._maskService.maskIsShown : el.value);\n    /** Fix of cursor position jumping to end in most browsers no matter where cursor is inserted onFocus */\n    if (el && el.value !== nextValue) {\n      el.value = nextValue;\n    }\n    /** fix of cursor position with prefix when mouse click occur */\n    if (el && el.type !== 'number' && (el.selectionStart || el.selectionEnd) <= this._maskService.prefix.length) {\n      const specialCharactersAtTheStart = this._maskService.maskExpression.match(new RegExp(`^[${this._maskService.specialCharacters.map(c => `\\\\${c}`).join('')}]+`))?.[0].length || 0;\n      el.selectionStart = this._maskService.prefix.length + specialCharactersAtTheStart;\n      return;\n    }\n    /** select only inserted text */\n    if (el && el.selectionEnd > this._getActualInputLength()) {\n      el.selectionEnd = this._getActualInputLength();\n    }\n  }\n  onKeyDown(e) {\n    if (!this._maskValue()) {\n      return;\n    }\n    if (this._isComposing()) {\n      // User finalize their choice from IME composition, so trigger onInput() for the composed text.\n      if (e.key === 'Enter') {\n        this.onCompositionEnd(e);\n      }\n      return;\n    }\n    this._code.set(e.code ? e.code : e.key);\n    const el = e.target;\n    this._inputValue.set(el.value);\n    this._setMask();\n    if (el.type !== 'number') {\n      if (e.key === MaskExpression.ARROW_UP) {\n        e.preventDefault();\n      }\n      if (e.key === MaskExpression.ARROW_LEFT || e.key === MaskExpression.BACKSPACE || e.key === MaskExpression.DELETE) {\n        if (e.key === MaskExpression.BACKSPACE && el.value.length === 0) {\n          el.selectionStart = el.selectionEnd;\n        }\n        if (e.key === MaskExpression.BACKSPACE && el.selectionStart !== 0) {\n          const prefixLength = this.prefix().length;\n          // If specialChars is false, (shouldn't ever happen) then set to the defaults\n          const specialCharacters = this.specialCharacters().length ? this.specialCharacters() : this._config.specialCharacters;\n          if (prefixLength > 1 && el.selectionStart <= prefixLength) {\n            el.setSelectionRange(prefixLength, el.selectionEnd);\n          } else {\n            if (this._inputValue().length !== el.selectionStart && el.selectionStart !== 1) {\n              while (specialCharacters.includes((this._inputValue()[el.selectionStart - 1] ?? MaskExpression.EMPTY_STRING).toString()) && (prefixLength >= 1 && el.selectionStart > prefixLength || prefixLength === 0)) {\n                el.setSelectionRange(el.selectionStart - 1, el.selectionEnd);\n              }\n            }\n          }\n        }\n        this.checkSelectionOnDeletion(el);\n        if (this._maskService.prefix.length && el.selectionStart <= this._maskService.prefix.length && el.selectionEnd <= this._maskService.prefix.length) {\n          e.preventDefault();\n        }\n        const cursorStart = el.selectionStart;\n        if (e.key === MaskExpression.BACKSPACE && !el.readOnly && cursorStart === 0 && el.selectionEnd === el.value.length && el.value.length !== 0) {\n          this._position.set(this._maskService.prefix ? this._maskService.prefix.length : 0);\n          this._maskService.applyMask(this._maskService.prefix, this._maskService.maskExpression, this._position());\n        }\n      }\n      if (!!this.suffix() && this.suffix().length > 1 && this._inputValue().length - this.suffix().length < el.selectionStart) {\n        el.setSelectionRange(this._inputValue().length - this.suffix().length, this._inputValue().length);\n      } else if (e.code === 'KeyA' && e.ctrlKey || e.code === 'KeyA' && e.metaKey // Cmd + A (Mac)\n      ) {\n        el.setSelectionRange(0, this._getActualInputLength());\n        e.preventDefault();\n      }\n      this._maskService.selStart = el.selectionStart;\n      this._maskService.selEnd = el.selectionEnd;\n    }\n  }\n  /** It writes the value in the input */\n  writeValue(controlValue) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      let value = controlValue;\n      const inputTransformFn = _this._maskService.inputTransformFn;\n      if (typeof value === 'object' && value !== null && 'value' in value) {\n        if ('disable' in value) {\n          _this.setDisabledState(Boolean(value.disable));\n        }\n        value = value.value;\n      }\n      if (value !== null) {\n        value = inputTransformFn ? inputTransformFn(value) : value;\n      }\n      if (typeof value === 'string' || typeof value === 'number' || value === null || typeof value === 'undefined') {\n        if (value === null || typeof value === 'undefined' || value === '') {\n          _this._maskService.currentValue = '';\n          _this._maskService.previousValue = '';\n        }\n        let inputValue = value;\n        if (typeof inputValue === 'number' || _this._maskValue().startsWith(MaskExpression.SEPARATOR)) {\n          inputValue = String(inputValue);\n          const localeDecimalMarker = _this._maskService.currentLocaleDecimalMarker();\n          if (!Array.isArray(_this._maskService.decimalMarker)) {\n            inputValue = _this._maskService.decimalMarker !== localeDecimalMarker ? inputValue.replace(localeDecimalMarker, _this._maskService.decimalMarker) : inputValue;\n          }\n          if (_this._maskService.leadZero && inputValue && _this.mask() && _this.dropSpecialCharacters() !== false) {\n            inputValue = _this._maskService._checkPrecision(_this._maskService.maskExpression, inputValue);\n          }\n          if (_this._maskService.decimalMarker === MaskExpression.COMMA || Array.isArray(_this._maskService.decimalMarker) && _this._maskService.thousandSeparator === MaskExpression.DOT) {\n            inputValue = inputValue.toString().replace(MaskExpression.DOT, MaskExpression.COMMA);\n          }\n          if (_this.mask()?.startsWith(MaskExpression.SEPARATOR) && _this.leadZero()) {\n            requestAnimationFrame(() => {\n              _this._maskService.applyMask(inputValue?.toString() ?? '', _this._maskService.maskExpression);\n            });\n          }\n          _this._maskService.isNumberValue = true;\n        }\n        if (typeof inputValue !== 'string' || value === null || typeof value === 'undefined') {\n          inputValue = '';\n        }\n        _this._inputValue.set(inputValue);\n        _this._setMask();\n        if (inputValue && _this._maskService.maskExpression || _this._maskService.maskExpression && (_this._maskService.prefix || _this._maskService.showMaskTyped)) {\n          // Let the service we know we are writing value so that triggering onChange function won't happen during applyMask\n          _this._maskService.writingValue = true;\n          _this._maskService.formElementProperty = ['value', _this._maskService.applyMask(inputValue, _this._maskService.maskExpression)];\n          // Let the service know we've finished writing value\n          _this._maskService.writingValue = false;\n        } else {\n          _this._maskService.formElementProperty = ['value', inputValue];\n        }\n        _this._inputValue.set(inputValue);\n      } else {\n        // eslint-disable-next-line no-console\n        console.warn('Ngx-mask writeValue work with string | number, your current value:', typeof value);\n      }\n    })();\n  }\n  registerOnChange(fn) {\n    this._maskService.onChange = this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouch = fn;\n  }\n  _getActiveElement(document = this.document) {\n    const shadowRootEl = document?.activeElement?.shadowRoot;\n    if (!shadowRootEl?.activeElement) {\n      return document.activeElement;\n    } else {\n      return this._getActiveElement(shadowRootEl);\n    }\n  }\n  checkSelectionOnDeletion(el) {\n    const prefixLength = this.prefix().length;\n    const suffixLength = this.suffix().length;\n    const inputValueLength = this._inputValue().length;\n    el.selectionStart = Math.min(Math.max(prefixLength, el.selectionStart), inputValueLength - suffixLength);\n    el.selectionEnd = Math.min(Math.max(prefixLength, el.selectionEnd), inputValueLength - suffixLength);\n  }\n  /** It disables the input element */\n  setDisabledState(isDisabled) {\n    this._maskService.formElementProperty = ['disabled', isDisabled];\n  }\n  _applyMask() {\n    this._maskService.maskExpression = this._maskService._repeatPatternSymbols(this._maskValue() || '');\n    this._maskService.formElementProperty = ['value', this._maskService.applyMask(this._inputValue(), this._maskService.maskExpression)];\n  }\n  _validateTime(value) {\n    const rowMaskLen = this._maskValue().split(MaskExpression.EMPTY_STRING).filter(s => s !== ':').length;\n    if (!value) {\n      return null; // Don't validate empty values to allow for optional form control\n    }\n    if (+(value[value.length - 1] ?? -1) === 0 && value.length < rowMaskLen || value.length <= rowMaskLen - 2) {\n      return this._createValidationError(value);\n    }\n    return null;\n  }\n  _getActualInputLength() {\n    return this._maskService.actualValue.length || this._maskService.actualValue.length + this._maskService.prefix.length;\n  }\n  _createValidationError(actualValue) {\n    return {\n      mask: {\n        requiredMask: this._maskValue(),\n        actualValue\n      }\n    };\n  }\n  _setMask() {\n    this._maskExpressionArray().some(mask => {\n      const specialChart = mask.split(MaskExpression.EMPTY_STRING).some(char => this._maskService.specialCharacters.includes(char));\n      if (specialChart && this._inputValue() && this._areAllCharactersInEachStringSame(this._maskExpressionArray()) || mask.includes(MaskExpression.CURLY_BRACKETS_LEFT)) {\n        const test = this._maskService.removeMask(this._inputValue())?.length <= this._maskService.removeMask(mask)?.length;\n        if (test) {\n          const maskValue = mask.includes(MaskExpression.CURLY_BRACKETS_LEFT) ? this._maskService._repeatPatternSymbols(mask) : mask;\n          this._maskValue.set(maskValue);\n          this._maskService.maskExpression = maskValue;\n          return test;\n        } else {\n          const expression = this._maskExpressionArray()[this._maskExpressionArray().length - 1] ?? MaskExpression.EMPTY_STRING;\n          const maskValue = expression.includes(MaskExpression.CURLY_BRACKETS_LEFT) ? this._maskService._repeatPatternSymbols(expression) : expression;\n          this._maskValue.set(maskValue);\n          this._maskService.maskExpression = maskValue;\n        }\n      } else {\n        const cleanMask = this._maskService.removeMask(mask);\n        const check = this._maskService.removeMask(this._inputValue())?.split(MaskExpression.EMPTY_STRING).every((character, index) => {\n          const indexMask = cleanMask.charAt(index);\n          return this._maskService._checkSymbolMask(character, indexMask);\n        });\n        if (check || this._justPasted()) {\n          this._maskValue.set(mask);\n          this._maskService.maskExpression = mask;\n          return check;\n        }\n      }\n    });\n  }\n  _areAllCharactersInEachStringSame(array) {\n    const specialCharacters = this._maskService.specialCharacters;\n    function removeSpecialCharacters(str) {\n      const regex = new RegExp(`[${specialCharacters.map(ch => `\\\\${ch}`).join('')}]`, 'g');\n      return str.replace(regex, '');\n    }\n    const processedArr = array.map(removeSpecialCharacters);\n    return processedArr.every(str => {\n      const uniqueCharacters = new Set(str);\n      return uniqueCharacters.size === 1;\n    });\n  }\n  static ɵfac = function NgxMaskDirective_Factory(t) {\n    return new (t || NgxMaskDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgxMaskDirective,\n    selectors: [[\"input\", \"mask\", \"\"], [\"textarea\", \"mask\", \"\"]],\n    hostBindings: function NgxMaskDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"paste\", function NgxMaskDirective_paste_HostBindingHandler() {\n          return ctx.onPaste();\n        })(\"focus\", function NgxMaskDirective_focus_HostBindingHandler($event) {\n          return ctx.onFocus($event);\n        })(\"ngModelChange\", function NgxMaskDirective_ngModelChange_HostBindingHandler($event) {\n          return ctx.onModelChange($event);\n        })(\"input\", function NgxMaskDirective_input_HostBindingHandler($event) {\n          return ctx.onInput($event);\n        })(\"compositionstart\", function NgxMaskDirective_compositionstart_HostBindingHandler($event) {\n          return ctx.onCompositionStart($event);\n        })(\"compositionend\", function NgxMaskDirective_compositionend_HostBindingHandler($event) {\n          return ctx.onCompositionEnd($event);\n        })(\"blur\", function NgxMaskDirective_blur_HostBindingHandler($event) {\n          return ctx.onBlur($event);\n        })(\"click\", function NgxMaskDirective_click_HostBindingHandler($event) {\n          return ctx.onClick($event);\n        })(\"keydown\", function NgxMaskDirective_keydown_HostBindingHandler($event) {\n          return ctx.onKeyDown($event);\n        });\n      }\n    },\n    inputs: {\n      mask: [i0.ɵɵInputFlags.SignalBased, \"mask\"],\n      specialCharacters: [i0.ɵɵInputFlags.SignalBased, \"specialCharacters\"],\n      patterns: [i0.ɵɵInputFlags.SignalBased, \"patterns\"],\n      prefix: [i0.ɵɵInputFlags.SignalBased, \"prefix\"],\n      suffix: [i0.ɵɵInputFlags.SignalBased, \"suffix\"],\n      thousandSeparator: [i0.ɵɵInputFlags.SignalBased, \"thousandSeparator\"],\n      decimalMarker: [i0.ɵɵInputFlags.SignalBased, \"decimalMarker\"],\n      dropSpecialCharacters: [i0.ɵɵInputFlags.SignalBased, \"dropSpecialCharacters\"],\n      hiddenInput: [i0.ɵɵInputFlags.SignalBased, \"hiddenInput\"],\n      showMaskTyped: [i0.ɵɵInputFlags.SignalBased, \"showMaskTyped\"],\n      placeHolderCharacter: [i0.ɵɵInputFlags.SignalBased, \"placeHolderCharacter\"],\n      shownMaskExpression: [i0.ɵɵInputFlags.SignalBased, \"shownMaskExpression\"],\n      clearIfNotMatch: [i0.ɵɵInputFlags.SignalBased, \"clearIfNotMatch\"],\n      validation: [i0.ɵɵInputFlags.SignalBased, \"validation\"],\n      separatorLimit: [i0.ɵɵInputFlags.SignalBased, \"separatorLimit\"],\n      allowNegativeNumbers: [i0.ɵɵInputFlags.SignalBased, \"allowNegativeNumbers\"],\n      leadZeroDateTime: [i0.ɵɵInputFlags.SignalBased, \"leadZeroDateTime\"],\n      leadZero: [i0.ɵɵInputFlags.SignalBased, \"leadZero\"],\n      triggerOnMaskChange: [i0.ɵɵInputFlags.SignalBased, \"triggerOnMaskChange\"],\n      apm: [i0.ɵɵInputFlags.SignalBased, \"apm\"],\n      inputTransformFn: [i0.ɵɵInputFlags.SignalBased, \"inputTransformFn\"],\n      outputTransformFn: [i0.ɵɵInputFlags.SignalBased, \"outputTransformFn\"],\n      keepCharacterPositions: [i0.ɵɵInputFlags.SignalBased, \"keepCharacterPositions\"],\n      instantPrefix: [i0.ɵɵInputFlags.SignalBased, \"instantPrefix\"]\n    },\n    outputs: {\n      maskFilled: \"maskFilled\"\n    },\n    exportAs: [\"mask\", \"ngxMask\"],\n    standalone: true,\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: NgxMaskDirective,\n      multi: true\n    }, {\n      provide: NG_VALIDATORS,\n      useExisting: NgxMaskDirective,\n      multi: true\n    }, NgxMaskService]), i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaskDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'input[mask], textarea[mask]',\n      standalone: true,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: NgxMaskDirective,\n        multi: true\n      }, {\n        provide: NG_VALIDATORS,\n        useExisting: NgxMaskDirective,\n        multi: true\n      }, NgxMaskService],\n      exportAs: 'mask,ngxMask'\n    }]\n  }], null, {\n    onPaste: [{\n      type: HostListener,\n      args: ['paste']\n    }],\n    onFocus: [{\n      type: HostListener,\n      args: ['focus', ['$event']]\n    }],\n    onModelChange: [{\n      type: HostListener,\n      args: ['ngModelChange', ['$event']]\n    }],\n    onInput: [{\n      type: HostListener,\n      args: ['input', ['$event']]\n    }],\n    onCompositionStart: [{\n      type: HostListener,\n      args: ['compositionstart', ['$event']]\n    }],\n    onCompositionEnd: [{\n      type: HostListener,\n      args: ['compositionend', ['$event']]\n    }],\n    onBlur: [{\n      type: HostListener,\n      args: ['blur', ['$event']]\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }],\n    onKeyDown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\nclass NgxMaskPipe {\n  defaultOptions = inject(NGX_MASK_CONFIG);\n  _maskService = inject(NgxMaskService);\n  _maskExpressionArray = [];\n  mask = '';\n  transform(value, mask, {\n    patterns,\n    ...config\n  } = {}) {\n    let processedValue = value;\n    const currentConfig = {\n      maskExpression: mask,\n      ...this.defaultOptions,\n      ...config,\n      patterns: {\n        ...this._maskService.patterns,\n        ...patterns\n      }\n    };\n    Object.entries(currentConfig).forEach(([key, val]) => {\n      this._maskService[key] = val;\n    });\n    if (mask.includes('||')) {\n      const maskParts = mask.split('||');\n      if (maskParts.length > 1) {\n        this._maskExpressionArray = maskParts.sort((a, b) => a.length - b.length);\n        this._setMask(`${processedValue}`);\n        return this._maskService.applyMask(`${processedValue}`, this.mask);\n      } else {\n        this._maskExpressionArray = [];\n        return this._maskService.applyMask(`${processedValue}`, this.mask);\n      }\n    }\n    if (mask.includes(MaskExpression.CURLY_BRACKETS_LEFT)) {\n      return this._maskService.applyMask(`${processedValue}`, this._maskService._repeatPatternSymbols(mask));\n    }\n    if (mask.startsWith(MaskExpression.SEPARATOR)) {\n      if (config.decimalMarker) {\n        this._maskService.decimalMarker = config.decimalMarker;\n      }\n      if (config.thousandSeparator) {\n        this._maskService.thousandSeparator = config.thousandSeparator;\n      }\n      if (config.leadZero) {\n        this._maskService.leadZero = config.leadZero;\n      }\n      processedValue = String(processedValue);\n      const localeDecimalMarker = this._maskService.currentLocaleDecimalMarker();\n      if (!Array.isArray(this._maskService.decimalMarker)) {\n        processedValue = this._maskService.decimalMarker !== localeDecimalMarker ? processedValue.replace(localeDecimalMarker, this._maskService.decimalMarker) : processedValue;\n      }\n      if (this._maskService.leadZero && processedValue && this._maskService.dropSpecialCharacters !== false) {\n        processedValue = this._maskService._checkPrecision(mask, processedValue);\n      }\n      if (this._maskService.decimalMarker === MaskExpression.COMMA) {\n        processedValue = processedValue.replace(MaskExpression.DOT, MaskExpression.COMMA);\n      }\n      this._maskService.isNumberValue = true;\n    }\n    if (processedValue === null || typeof processedValue === 'undefined') {\n      return this._maskService.applyMask('', mask);\n    }\n    return this._maskService.applyMask(`${processedValue}`, mask);\n  }\n  _setMask(value) {\n    if (this._maskExpressionArray.length > 0) {\n      this._maskExpressionArray.some(mask => {\n        const test = this._maskService.removeMask(value)?.length <= this._maskService.removeMask(mask)?.length;\n        if (value && test) {\n          this.mask = mask;\n          return test;\n        } else {\n          this.mask = this._maskExpressionArray[this._maskExpressionArray.length - 1] ?? MaskExpression.EMPTY_STRING;\n        }\n      });\n    }\n  }\n  static ɵfac = function NgxMaskPipe_Factory(t) {\n    return new (t || NgxMaskPipe)();\n  };\n  static ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"mask\",\n    type: NgxMaskPipe,\n    pure: true,\n    standalone: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaskPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'mask',\n      pure: true,\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INITIAL_CONFIG, NEW_CONFIG, NGX_MASK_CONFIG, NgxMaskDirective, NgxMaskPipe, NgxMaskService, initialConfig, provideEnvironmentNgxMask, provideNgxMask, timeMasks, withoutValidation };", "map": {"version": 3, "names": ["i0", "InjectionToken", "EventEmitter", "inject", "Injectable", "ElementRef", "Renderer2", "makeEnvironmentProviders", "input", "output", "signal", "HostListener", "Directive", "<PERSON><PERSON>", "DOCUMENT", "NG_VALUE_ACCESSOR", "NG_VALIDATORS", "MaskExpression", "NGX_MASK_CONFIG", "NEW_CONFIG", "INITIAL_CONFIG", "initialConfig", "suffix", "prefix", "thousandSeparator", "decimalMarker", "clearIfNotMatch", "showMaskTyped", "instantPrefix", "placeHolderCharacter", "dropSpecialCharacters", "hiddenInput", "shownMaskExpression", "separatorLimit", "allowNegativeNumbers", "validation", "specialCharacters", "leadZeroDateTime", "apm", "leadZero", "keepCharacterPositions", "triggerOnMaskChange", "inputTransformFn", "value", "outputTransformFn", "maskFilled", "patterns", "pattern", "RegExp", "optional", "X", "symbol", "A", "S", "U", "L", "d", "m", "M", "H", "h", "s", "timeMasks", "HOURS_MINUTES_SECONDS", "HOURS_MINUTES", "MINUTES_SECONDS", "withoutValidation", "PERCENT", "HOURS_HOUR", "SECONDS", "MINUTES", "SEPARATOR", "DAYS_MONTHS_YEARS", "DAYS_MONTHS", "DAYS", "MONTHS", "NgxMaskApplierService", "_config", "customPattern", "_shift", "Set", "plusOnePosition", "maskExpression", "actualValue", "showKeepCharacterExp", "deletedSpecialCharacter", "ipError", "cpfCnpjError", "applyMask", "inputValue", "position", "justPasted", "backspaced", "cb", "EMPTY_STRING", "cursor", "result", "multi", "backspaceShift", "shift", "stepBack", "processedValue", "processedPosition", "slice", "length", "checkAndRemoveSuffix", "inputArray", "toString", "split", "MINUS", "IP", "valuesIP", "DOT", "_validIP", "arr", "i", "match", "push", "CPF_CNPJ", "startsWith", "_stripToDecimal", "precision", "getPrecision", "checkInputPrecision", "indexOf", "percentage", "substring", "base", "_splitPercentZero", "Array", "isArray", "includes", "find", "dm", "decimalMarkerIndex", "nonZeroIndex", "_findFirstNonZeroAndDecimalIndex", "zeroIndexMinus", "zeroIndexNumberZero", "NUMBER_ZERO", "zeroIndexDecimalMarker", "firstIndexDecimalMarker", "COMMA", "thousandSeparatorCharEscaped", "_charToRegExpExpression", "invalid<PERSON>hars", "replace", "marker", "invalidCharRegexp", "strForSep", "_formatWithSeparators", "commaShift", "shiftStep", "backspacedDecimalMarkerWithSeparatorLimit", "add", "clear", "inputSymbol", "symbolStarInPattern", "SYMBOL_STAR", "_checkSymbolMask", "SYMBOL_QUESTION", "HOURS", "Number", "_shiftStep", "HOUR", "MINUTE", "SECOND", "daysCount", "inputValueCursor", "inputValueCursorPlusOne", "inputValueCursorPlusTwo", "inputValueCursorMinusOne", "inputValueCursorMinusTwo", "inputValueSliceMinusThreeMinusOne", "inputValueSliceMinusOnePlusOne", "inputValueSliceCursorPlusTwo", "inputValueSliceMinusTwoCursor", "DAY", "maskStartWithMonth", "startWithMonthInput", "MONTH", "monthsCount", "withoutDays", "<PERSON><PERSON><PERSON>", "day1monthInput", "day2monthInput", "day2monthInputDot", "day1monthPaste", "day2monthPaste", "NUMBER_NINE", "_findSpecialChar", "newPosition", "has", "actualShift", "onlySpecial", "every", "char", "res", "isSpecialCharacterMaskFirstSymbol", "join", "_findDropSpecialChar", "val", "maskSymbol", "test", "str", "thousandSeparatorChar", "decimalChars", "x", "decimalChar", "regExp", "map", "v", "decimals", "rgx", "sanitizedStr", "isNaN", "Infinity", "substr", "processedInputValue", "processedDecimalMarker", "precisionRegEx", "precisionMatch", "precisionMatchLength", "diff", "_compareOrIncludes", "filter", "idx", "isDecimalMarker", "charsToEscape", "comparedValue", "excludedValue", "some", "index", "decimalIndex", "emptyOrMinus", "parsedValue", "parseInt", "integerPart", "decimalPart", "integerString", "decimal", "inputString", "ɵfac", "NgxMaskApplierService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "NgxMaskService", "isNumberValue", "maskIsShown", "selStart", "selEnd", "maskChanged", "maskExpressionArray", "previousValue", "currentValue", "writingValue", "_emitValue", "_start", "_end", "onChange", "_", "_elementRef", "document", "_renderer", "showMaskInInput", "HASH", "formControlResult", "getSymbol", "newInputValue", "actualResult", "concat", "removeMask", "splice", "shiftTypedSymbols", "Boolean", "getActualValue", "item", "hideInput", "resLen", "prefNmask", "countSkipedSymbol", "_numberSkipedSymbols", "regex", "exec", "applyValueChanges", "formElement", "nativeElement", "_getActiveElement", "clearIfNotMatchFn", "curr", "compare", "maskChar", "symbolToReplace", "currSymbol", "replaceSymbol", "numberToString", "String", "toLocaleString", "useGrouping", "maximumFractionDigits", "inputVal", "Error", "_checkForIp", "_checkForCpfCnpj", "formElementProperty", "name", "Promise", "resolve", "then", "setProperty", "checkDropSpecialCharAmount", "mask", "chars", "_removeMask", "_removeSuffix", "_removePrefix", "cpf", "cnpj", "shadowRootEl", "activeElement", "shadowRoot", "_toNumber", "_checkSymbols", "num", "specialCharactersForRemove", "_regExpForRemove", "_retrieveSeparatorV<PERSON>ue", "_checkPatternForSpace", "WHITE_SPACE", "_replaceDecimalMarkerToDot", "markers", "processedResult", "separatorPrecision", "_retrieveSeparatorPrecision", "separatorValue", "_checkPrecision", "key", "hasOwnProperty", "patternString", "maskExpretion", "matcher", "separatorExpression", "toFixed", "_repeatPatternSymbols", "maskExp", "reduce", "accum", "currVal", "CURLY_BRACKETS_LEFT", "CURLY_BRACKETS_RIGHT", "repeatNumber", "replaceWith", "LETTER_S", "symbols", "currentLocaleDecimalMarker", "ɵNgxMaskService_BaseFactory", "NgxMaskService_Factory", "ɵɵgetInheritedFactory", "_configFactory", "initConfig", "config<PERSON><PERSON><PERSON>", "Function", "provideNgxMask", "provide", "useValue", "useFactory", "provideEnvironmentNgxMask", "NgxMaskDirective", "_maskValue", "_inputValue", "_position", "_code", "_maskExpressionArray", "_justPasted", "_isFocused", "_isComposing", "_maskService", "self", "onTouch", "ngOnChanges", "changes", "firstChange", "OR", "set", "sort", "a", "b", "_setMask", "c", "previousDecimalMarker", "requestAnimationFrame", "click", "_applyMask", "validate", "maskValue", "_createValidationError", "_validateTime", "EMAIL_MASK", "emailPattern", "counterOfOpt", "lengthInsideCurlyBrackets", "lastIndexOf", "opt", "array", "lastIndexArray", "special", "emit", "onPaste", "onFocus", "onModelChange", "onInput", "e", "el", "target", "transformedValue", "selectionStart", "prefixLength", "checkSymbols", "checkSpecialCharacter", "selectRangeBackspace", "backspaceOrDelete", "BACKSPACE", "DELETE", "part1", "part2", "caretShift", "DOUBLE_ZERO", "_backspaceShift", "specialChartMinusOne", "allowFewMaskChangeMask", "specialChartPlusOne", "positionToApply", "_getActualInputLength", "setSelectionRange", "console", "warn", "onCompositionStart", "onCompositionEnd", "onBlur", "repeat", "onClick", "posStart", "posEnd", "selectionEnd", "keyCode", "focus", "nextValue", "specialCharactersAtTheStart", "onKeyDown", "code", "ARROW_UP", "preventDefault", "ARROW_LEFT", "checkSelectionOnDeletion", "cursorStart", "readOnly", "ctrl<PERSON>ey", "metaKey", "writeValue", "controlValue", "_this", "_asyncToGenerator", "setDisabledState", "disable", "localeDecimalMarker", "registerOnChange", "fn", "registerOnTouched", "suffixLength", "inputValueLength", "Math", "min", "max", "isDisabled", "rowMaskLen", "requiredMask", "_areAllCharactersInEachStringSame", "expression", "cleanMask", "check", "character", "indexMask", "char<PERSON>t", "removeSpecialCharacters", "ch", "processedArr", "uniqueCharacters", "size", "NgxMaskDirective_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "hostBindings", "NgxMaskDirective_HostBindings", "rf", "ctx", "ɵɵlistener", "NgxMaskDirective_paste_HostBindingHandler", "NgxMaskDirective_focus_HostBindingHandler", "$event", "NgxMaskDirective_ngModelChange_HostBindingHandler", "NgxMaskDirective_input_HostBindingHandler", "NgxMaskDirective_compositionstart_HostBindingHandler", "NgxMaskDirective_compositionend_HostBindingHandler", "NgxMaskDirective_blur_HostBindingHandler", "NgxMaskDirective_click_HostBindingHandler", "NgxMaskDirective_keydown_HostBindingHandler", "inputs", "ɵɵInputFlags", "SignalBased", "outputs", "exportAs", "standalone", "features", "ɵɵProvidersFeature", "useExisting", "ɵɵNgOnChangesFeature", "args", "selector", "providers", "NgxMaskPipe", "defaultOptions", "transform", "config", "currentConfig", "Object", "entries", "for<PERSON>ach", "maskParts", "NgxMaskPipe_Factory", "ɵpipe", "ɵɵdefinePipe", "pure"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/ngx-mask/fesm2022/ngx-mask.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, inject, Injectable, ElementRef, Renderer2, makeEnvironmentProviders, input, output, signal, HostListener, Directive, Pipe } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS } from '@angular/forms';\n\nvar MaskExpression;\n(function (MaskExpression) {\n    MaskExpression[\"SEPARATOR\"] = \"separator\";\n    MaskExpression[\"PERCENT\"] = \"percent\";\n    MaskExpression[\"IP\"] = \"IP\";\n    MaskExpression[\"CPF_CNPJ\"] = \"CPF_CNPJ\";\n    MaskExpression[\"MONTH\"] = \"M\";\n    MaskExpression[\"MONTHS\"] = \"M0\";\n    MaskExpression[\"MINUTE\"] = \"m\";\n    MaskExpression[\"HOUR\"] = \"h\";\n    MaskExpression[\"HOURS\"] = \"H\";\n    MaskExpression[\"MINUTES\"] = \"m0\";\n    MaskExpression[\"HOURS_HOUR\"] = \"Hh\";\n    MaskExpression[\"SECONDS\"] = \"s0\";\n    MaskExpression[\"HOURS_MINUTES_SECONDS\"] = \"Hh:m0:s0\";\n    MaskExpression[\"EMAIL_MASK\"] = \"A*@A*.A*\";\n    MaskExpression[\"HOURS_MINUTES\"] = \"Hh:m0\";\n    MaskExpression[\"MINUTES_SECONDS\"] = \"m0:s0\";\n    MaskExpression[\"DAYS_MONTHS_YEARS\"] = \"d0/M0/0000\";\n    MaskExpression[\"DAYS_MONTHS\"] = \"d0/M0\";\n    MaskExpression[\"DAYS\"] = \"d0\";\n    MaskExpression[\"DAY\"] = \"d\";\n    MaskExpression[\"SECOND\"] = \"s\";\n    MaskExpression[\"LETTER_S\"] = \"S\";\n    MaskExpression[\"DOT\"] = \".\";\n    MaskExpression[\"COMMA\"] = \",\";\n    MaskExpression[\"CURLY_BRACKETS_LEFT\"] = \"{\";\n    MaskExpression[\"CURLY_BRACKETS_RIGHT\"] = \"}\";\n    MaskExpression[\"MINUS\"] = \"-\";\n    MaskExpression[\"OR\"] = \"||\";\n    MaskExpression[\"HASH\"] = \"#\";\n    MaskExpression[\"EMPTY_STRING\"] = \"\";\n    MaskExpression[\"SYMBOL_STAR\"] = \"*\";\n    MaskExpression[\"SYMBOL_QUESTION\"] = \"?\";\n    MaskExpression[\"SLASH\"] = \"/\";\n    MaskExpression[\"WHITE_SPACE\"] = \" \";\n    MaskExpression[\"NUMBER_ZERO\"] = \"0\";\n    MaskExpression[\"NUMBER_NINE\"] = \"9\";\n    MaskExpression[\"BACKSPACE\"] = \"Backspace\";\n    MaskExpression[\"DELETE\"] = \"Delete\";\n    MaskExpression[\"ARROW_LEFT\"] = \"ArrowLeft\";\n    MaskExpression[\"ARROW_UP\"] = \"ArrowUp\";\n    MaskExpression[\"DOUBLE_ZERO\"] = \"00\";\n})(MaskExpression || (MaskExpression = {}));\n\nconst NGX_MASK_CONFIG = new InjectionToken('ngx-mask config');\nconst NEW_CONFIG = new InjectionToken('new ngx-mask config');\nconst INITIAL_CONFIG = new InjectionToken('initial ngx-mask config');\nconst initialConfig = {\n    suffix: '',\n    prefix: '',\n    thousandSeparator: ' ',\n    decimalMarker: ['.', ','],\n    clearIfNotMatch: false,\n    showMaskTyped: false,\n    instantPrefix: false,\n    placeHolderCharacter: '_',\n    dropSpecialCharacters: true,\n    hiddenInput: false,\n    shownMaskExpression: '',\n    separatorLimit: '',\n    allowNegativeNumbers: false,\n    validation: true,\n    specialCharacters: ['-', '/', '(', ')', '.', ':', ' ', '+', ',', '@', '[', ']', '\"', \"'\"],\n    leadZeroDateTime: false,\n    apm: false,\n    leadZero: false,\n    keepCharacterPositions: false,\n    triggerOnMaskChange: false,\n    inputTransformFn: (value) => value,\n    outputTransformFn: (value) => value,\n    maskFilled: new EventEmitter(),\n    patterns: {\n        '0': {\n            pattern: new RegExp('\\\\d'),\n        },\n        '9': {\n            pattern: new RegExp('\\\\d'),\n            optional: true,\n        },\n        X: {\n            pattern: new RegExp('\\\\d'),\n            symbol: '*',\n        },\n        A: {\n            pattern: new RegExp('[a-zA-Z0-9]'),\n        },\n        S: {\n            pattern: new RegExp('[a-zA-Z]'),\n        },\n        U: {\n            pattern: new RegExp('[A-Z]'),\n        },\n        L: {\n            pattern: new RegExp('[a-z]'),\n        },\n        d: {\n            pattern: new RegExp('\\\\d'),\n        },\n        m: {\n            pattern: new RegExp('\\\\d'),\n        },\n        M: {\n            pattern: new RegExp('\\\\d'),\n        },\n        H: {\n            pattern: new RegExp('\\\\d'),\n        },\n        h: {\n            pattern: new RegExp('\\\\d'),\n        },\n        s: {\n            pattern: new RegExp('\\\\d'),\n        },\n    },\n};\nconst timeMasks = [\n    MaskExpression.HOURS_MINUTES_SECONDS,\n    MaskExpression.HOURS_MINUTES,\n    MaskExpression.MINUTES_SECONDS,\n];\nconst withoutValidation = [\n    MaskExpression.PERCENT,\n    MaskExpression.HOURS_HOUR,\n    MaskExpression.SECONDS,\n    MaskExpression.MINUTES,\n    MaskExpression.SEPARATOR,\n    MaskExpression.DAYS_MONTHS_YEARS,\n    MaskExpression.DAYS_MONTHS,\n    MaskExpression.DAYS,\n    MaskExpression.MONTHS,\n];\n\nclass NgxMaskApplierService {\n    _config = inject(NGX_MASK_CONFIG);\n    dropSpecialCharacters = this._config.dropSpecialCharacters;\n    hiddenInput = this._config.hiddenInput;\n    clearIfNotMatch = this._config.clearIfNotMatch;\n    specialCharacters = this._config.specialCharacters;\n    patterns = this._config.patterns;\n    prefix = this._config.prefix;\n    suffix = this._config.suffix;\n    thousandSeparator = this._config.thousandSeparator;\n    decimalMarker = this._config.decimalMarker;\n    customPattern;\n    showMaskTyped = this._config.showMaskTyped;\n    placeHolderCharacter = this._config.placeHolderCharacter;\n    validation = this._config.validation;\n    separatorLimit = this._config.separatorLimit;\n    allowNegativeNumbers = this._config.allowNegativeNumbers;\n    leadZeroDateTime = this._config.leadZeroDateTime;\n    leadZero = this._config.leadZero;\n    apm = this._config.apm;\n    inputTransformFn = this._config.inputTransformFn;\n    outputTransformFn = this._config.outputTransformFn;\n    keepCharacterPositions = this._config.keepCharacterPositions;\n    instantPrefix = this._config.instantPrefix;\n    triggerOnMaskChange = this._config.triggerOnMaskChange;\n    _shift = new Set();\n    plusOnePosition = false;\n    maskExpression = '';\n    actualValue = '';\n    showKeepCharacterExp = '';\n    shownMaskExpression = this._config.shownMaskExpression;\n    deletedSpecialCharacter = false;\n    ipError;\n    cpfCnpjError;\n    applyMask(inputValue, maskExpression, position = 0, justPasted = false, backspaced = false, \n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    cb = () => { }) {\n        if (!maskExpression || typeof inputValue !== 'string') {\n            return MaskExpression.EMPTY_STRING;\n        }\n        let cursor = 0;\n        let result = '';\n        let multi = false;\n        let backspaceShift = false;\n        let shift = 1;\n        let stepBack = false;\n        let processedValue = inputValue;\n        let processedPosition = position;\n        if (processedValue.slice(0, this.prefix.length) === this.prefix) {\n            processedValue = processedValue.slice(this.prefix.length, processedValue.length);\n        }\n        if (!!this.suffix && processedValue.length > 0) {\n            processedValue = this.checkAndRemoveSuffix(processedValue);\n        }\n        if (processedValue === '(' && this.prefix) {\n            processedValue = '';\n        }\n        const inputArray = processedValue.toString().split(MaskExpression.EMPTY_STRING);\n        if (this.allowNegativeNumbers &&\n            processedValue.slice(cursor, cursor + 1) === MaskExpression.MINUS) {\n            result += processedValue.slice(cursor, cursor + 1);\n        }\n        if (maskExpression === MaskExpression.IP) {\n            const valuesIP = processedValue.split(MaskExpression.DOT);\n            this.ipError = this._validIP(valuesIP);\n            // eslint-disable-next-line no-param-reassign\n            maskExpression = '***************';\n        }\n        const arr = [];\n        // eslint-disable-next-line @typescript-eslint/prefer-for-of\n        for (let i = 0; i < processedValue.length; i++) {\n            if (processedValue[i]?.match('\\\\d')) {\n                arr.push(processedValue[i] ?? MaskExpression.EMPTY_STRING);\n            }\n        }\n        if (maskExpression === MaskExpression.CPF_CNPJ) {\n            this.cpfCnpjError = arr.length !== 11 && arr.length !== 14;\n            if (arr.length > 11) {\n                // eslint-disable-next-line no-param-reassign\n                maskExpression = '00.000.000/0000-00';\n            }\n            else {\n                // eslint-disable-next-line no-param-reassign\n                maskExpression = '000.000.000-00';\n            }\n        }\n        if (maskExpression.startsWith(MaskExpression.PERCENT)) {\n            if (processedValue.match('[a-z]|[A-Z]') ||\n                // eslint-disable-next-line no-useless-escape\n                (processedValue.match(/[-!$%^&*()_+|~=`{}\\[\\]:\";'<>?,\\/.]/) && !backspaced)) {\n                processedValue = this._stripToDecimal(processedValue);\n                const precision = this.getPrecision(maskExpression);\n                processedValue = this.checkInputPrecision(processedValue, precision, this.decimalMarker);\n            }\n            const decimalMarker = typeof this.decimalMarker === 'string' ? this.decimalMarker : MaskExpression.DOT;\n            if (processedValue.indexOf(decimalMarker) > 0 &&\n                !this.percentage(processedValue.substring(0, processedValue.indexOf(decimalMarker)))) {\n                let base = processedValue.substring(0, processedValue.indexOf(decimalMarker) - 1);\n                if (this.allowNegativeNumbers &&\n                    processedValue.slice(cursor, cursor + 1) === MaskExpression.MINUS &&\n                    !backspaced) {\n                    base = processedValue.substring(0, processedValue.indexOf(decimalMarker));\n                }\n                processedValue = `${base}${processedValue.substring(processedValue.indexOf(decimalMarker), processedValue.length)}`;\n            }\n            let value = '';\n            // eslint-disable-next-line @typescript-eslint/no-unused-expressions\n            this.allowNegativeNumbers &&\n                processedValue.slice(cursor, cursor + 1) === MaskExpression.MINUS\n                ? (value = `${MaskExpression.MINUS}${processedValue.slice(cursor + 1, cursor + processedValue.length)}`)\n                : (value = processedValue);\n            if (this.percentage(value)) {\n                result = this._splitPercentZero(processedValue);\n            }\n            else {\n                result = this._splitPercentZero(processedValue.substring(0, processedValue.length - 1));\n            }\n        }\n        else if (maskExpression.startsWith(MaskExpression.SEPARATOR)) {\n            if (processedValue.match('[wа-яА-Я]') ||\n                processedValue.match('[ЁёА-я]') ||\n                processedValue.match('[a-z]|[A-Z]') ||\n                processedValue.match(/[-@#!$%\\\\^&*()_£¬'+|~=`{}\\]:\";<>.?/]/) ||\n                processedValue.match('[^A-Za-z0-9,]')) {\n                processedValue = this._stripToDecimal(processedValue);\n            }\n            const precision = this.getPrecision(maskExpression);\n            let decimalMarker = this.decimalMarker;\n            if (Array.isArray(this.decimalMarker)) {\n                if (this.actualValue.includes(this.decimalMarker[0]) ||\n                    this.actualValue.includes(this.decimalMarker[1])) {\n                    decimalMarker = this.actualValue.includes(this.decimalMarker[0])\n                        ? this.decimalMarker[0]\n                        : this.decimalMarker[1];\n                }\n                else {\n                    decimalMarker = this.decimalMarker.find((dm) => dm !== this.thousandSeparator);\n                }\n            }\n            if (backspaced) {\n                const { decimalMarkerIndex, nonZeroIndex } = this._findFirstNonZeroAndDecimalIndex(processedValue, decimalMarker);\n                const zeroIndexMinus = processedValue[0] === MaskExpression.MINUS;\n                const zeroIndexNumberZero = processedValue[0] === MaskExpression.NUMBER_ZERO;\n                const zeroIndexDecimalMarker = processedValue[0] === decimalMarker;\n                const firstIndexDecimalMarker = processedValue[1] === decimalMarker;\n                if ((zeroIndexDecimalMarker && !nonZeroIndex) ||\n                    (zeroIndexMinus && firstIndexDecimalMarker && !nonZeroIndex) ||\n                    (zeroIndexNumberZero && !decimalMarkerIndex && !nonZeroIndex)) {\n                    processedValue = MaskExpression.NUMBER_ZERO;\n                }\n                if (decimalMarkerIndex &&\n                    nonZeroIndex &&\n                    zeroIndexMinus &&\n                    processedPosition === 1) {\n                    if (decimalMarkerIndex < nonZeroIndex || decimalMarkerIndex > nonZeroIndex) {\n                        processedValue = MaskExpression.MINUS + processedValue.slice(nonZeroIndex);\n                    }\n                }\n                if (!decimalMarkerIndex && nonZeroIndex && processedValue.length > nonZeroIndex) {\n                    processedValue = zeroIndexMinus\n                        ? MaskExpression.MINUS + processedValue.slice(nonZeroIndex)\n                        : processedValue.slice(nonZeroIndex);\n                }\n                if (decimalMarkerIndex && nonZeroIndex && processedPosition === 0) {\n                    if (decimalMarkerIndex < nonZeroIndex) {\n                        processedValue = processedValue.slice(decimalMarkerIndex - 1);\n                    }\n                    if (decimalMarkerIndex > nonZeroIndex) {\n                        processedValue = processedValue.slice(nonZeroIndex);\n                    }\n                }\n            }\n            if (precision === 0) {\n                processedValue = this.allowNegativeNumbers\n                    ? processedValue.length > 2 &&\n                        processedValue[0] === MaskExpression.MINUS &&\n                        processedValue[1] === MaskExpression.NUMBER_ZERO &&\n                        processedValue[2] !== this.thousandSeparator &&\n                        processedValue[2] !== MaskExpression.COMMA &&\n                        processedValue[2] !== MaskExpression.DOT\n                        ? '-' + processedValue.slice(2, processedValue.length)\n                        : processedValue[0] === MaskExpression.NUMBER_ZERO &&\n                            processedValue.length > 1 &&\n                            processedValue[1] !== this.thousandSeparator &&\n                            processedValue[1] !== MaskExpression.COMMA &&\n                            processedValue[1] !== MaskExpression.DOT\n                            ? processedValue.slice(1, processedValue.length)\n                            : processedValue\n                    : processedValue.length > 1 &&\n                        processedValue[0] === MaskExpression.NUMBER_ZERO &&\n                        processedValue[1] !== this.thousandSeparator &&\n                        processedValue[1] !== MaskExpression.COMMA &&\n                        processedValue[1] !== MaskExpression.DOT\n                        ? processedValue.slice(1, processedValue.length)\n                        : processedValue;\n            }\n            else {\n                if (processedValue[0] === decimalMarker &&\n                    processedValue.length > 1 &&\n                    !backspaced) {\n                    processedValue =\n                        MaskExpression.NUMBER_ZERO +\n                            processedValue.slice(0, processedValue.length + 1);\n                    this.plusOnePosition = true;\n                }\n                if (processedValue[0] === MaskExpression.NUMBER_ZERO &&\n                    processedValue[1] !== decimalMarker &&\n                    processedValue[1] !== this.thousandSeparator &&\n                    !backspaced) {\n                    processedValue =\n                        processedValue.length > 1\n                            ? processedValue.slice(0, 1) +\n                                decimalMarker +\n                                processedValue.slice(1, processedValue.length + 1)\n                            : processedValue;\n                    this.plusOnePosition = true;\n                }\n                if (this.allowNegativeNumbers &&\n                    !backspaced &&\n                    processedValue[0] === MaskExpression.MINUS &&\n                    (processedValue[1] === decimalMarker ||\n                        processedValue[1] === MaskExpression.NUMBER_ZERO)) {\n                    processedValue =\n                        processedValue[1] === decimalMarker && processedValue.length > 2\n                            ? processedValue.slice(0, 1) +\n                                MaskExpression.NUMBER_ZERO +\n                                processedValue.slice(1, processedValue.length)\n                            : processedValue[1] === MaskExpression.NUMBER_ZERO &&\n                                processedValue.length > 2 &&\n                                processedValue[2] !== decimalMarker\n                                ? processedValue.slice(0, 2) +\n                                    decimalMarker +\n                                    processedValue.slice(2, processedValue.length)\n                                : processedValue;\n                    this.plusOnePosition = true;\n                }\n            }\n            // TODO: we had different rexexps here for the different cases... but tests dont seam to bother - check this\n            //  separator: no COMMA, dot-sep: no SPACE, COMMA OK, comma-sep: no SPACE, COMMA OK\n            const thousandSeparatorCharEscaped = this._charToRegExpExpression(this.thousandSeparator);\n            let invalidChars = '@#!$%^&*()_+|~=`{}\\\\[\\\\]:\\\\s,\\\\.\";<>?\\\\/'.replace(thousandSeparatorCharEscaped, '');\n            //.replace(decimalMarkerEscaped, '');\n            if (Array.isArray(this.decimalMarker)) {\n                for (const marker of this.decimalMarker) {\n                    invalidChars = invalidChars.replace(this._charToRegExpExpression(marker), MaskExpression.EMPTY_STRING);\n                }\n            }\n            else {\n                invalidChars = invalidChars.replace(this._charToRegExpExpression(this.decimalMarker), '');\n            }\n            const invalidCharRegexp = new RegExp('[' + invalidChars + ']');\n            if (processedValue.match(invalidCharRegexp)) {\n                processedValue = processedValue.substring(0, processedValue.length - 1);\n            }\n            processedValue = this.checkInputPrecision(processedValue, precision, this.decimalMarker);\n            const strForSep = processedValue.replace(new RegExp(thousandSeparatorCharEscaped, 'g'), '');\n            result = this._formatWithSeparators(strForSep, this.thousandSeparator, this.decimalMarker, precision);\n            const commaShift = result.indexOf(MaskExpression.COMMA) - processedValue.indexOf(MaskExpression.COMMA);\n            const shiftStep = result.length - processedValue.length;\n            const backspacedDecimalMarkerWithSeparatorLimit = backspaced && result.length < inputValue.length && this.separatorLimit;\n            if ((result[processedPosition - 1] === this.thousandSeparator ||\n                result[processedPosition - this.prefix.length]) &&\n                this.prefix &&\n                backspaced) {\n                processedPosition = processedPosition - 1;\n            }\n            else if ((shiftStep > 0 && result[processedPosition] !== this.thousandSeparator) ||\n                backspacedDecimalMarkerWithSeparatorLimit) {\n                backspaceShift = true;\n                let _shift = 0;\n                do {\n                    this._shift.add(processedPosition + _shift);\n                    _shift++;\n                } while (_shift < shiftStep);\n            }\n            else if (result[processedPosition - 1] === this.thousandSeparator ||\n                shiftStep === -4 ||\n                shiftStep === -3 ||\n                result[processedPosition] === this.thousandSeparator) {\n                this._shift.clear();\n                this._shift.add(processedPosition - 1);\n            }\n            else if ((commaShift !== 0 &&\n                processedPosition > 0 &&\n                !(result.indexOf(MaskExpression.COMMA) >= processedPosition &&\n                    processedPosition > 3)) ||\n                (!(result.indexOf(MaskExpression.DOT) >= processedPosition && processedPosition > 3) &&\n                    shiftStep <= 0)) {\n                this._shift.clear();\n                backspaceShift = true;\n                shift = shiftStep;\n                processedPosition += shiftStep;\n                this._shift.add(processedPosition);\n            }\n            else {\n                this._shift.clear();\n            }\n        }\n        else {\n            for (\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            let i = 0, inputSymbol = inputArray[0]; i < inputArray.length; i++, inputSymbol = inputArray[i] ?? MaskExpression.EMPTY_STRING) {\n                if (cursor === maskExpression.length) {\n                    break;\n                }\n                const symbolStarInPattern = MaskExpression.SYMBOL_STAR in this.patterns;\n                if (this._checkSymbolMask(inputSymbol, maskExpression[cursor] ?? MaskExpression.EMPTY_STRING) &&\n                    maskExpression[cursor + 1] === MaskExpression.SYMBOL_QUESTION) {\n                    result += inputSymbol;\n                    cursor += 2;\n                }\n                else if (maskExpression[cursor + 1] === MaskExpression.SYMBOL_STAR &&\n                    multi &&\n                    this._checkSymbolMask(inputSymbol, maskExpression[cursor + 2] ?? MaskExpression.EMPTY_STRING)) {\n                    result += inputSymbol;\n                    cursor += 3;\n                    multi = false;\n                }\n                else if (this._checkSymbolMask(inputSymbol, maskExpression[cursor] ?? MaskExpression.EMPTY_STRING) &&\n                    maskExpression[cursor + 1] === MaskExpression.SYMBOL_STAR &&\n                    !symbolStarInPattern) {\n                    result += inputSymbol;\n                    multi = true;\n                }\n                else if (maskExpression[cursor + 1] === MaskExpression.SYMBOL_QUESTION &&\n                    this._checkSymbolMask(inputSymbol, maskExpression[cursor + 2] ?? MaskExpression.EMPTY_STRING)) {\n                    result += inputSymbol;\n                    cursor += 3;\n                }\n                else if (this._checkSymbolMask(inputSymbol, maskExpression[cursor] ?? MaskExpression.EMPTY_STRING)) {\n                    if (maskExpression[cursor] === MaskExpression.HOURS) {\n                        if (this.apm ? Number(inputSymbol) > 9 : Number(inputSymbol) > 2) {\n                            processedPosition = !this.leadZeroDateTime\n                                ? processedPosition + 1\n                                : processedPosition;\n                            cursor += 1;\n                            this._shiftStep(cursor);\n                            i--;\n                            if (this.leadZeroDateTime) {\n                                result += '0';\n                            }\n                            continue;\n                        }\n                    }\n                    if (maskExpression[cursor] === MaskExpression.HOUR) {\n                        if (this.apm\n                            ? (result.length === 1 && Number(result) > 1) ||\n                                (result === '1' && Number(inputSymbol) > 2) ||\n                                (processedValue.slice(cursor - 1, cursor).length === 1 &&\n                                    Number(processedValue.slice(cursor - 1, cursor)) > 2) ||\n                                (processedValue.slice(cursor - 1, cursor) === '1' &&\n                                    Number(inputSymbol) > 2)\n                            : (result === '2' && Number(inputSymbol) > 3) ||\n                                ((result.slice(cursor - 2, cursor) === '2' ||\n                                    result.slice(cursor - 3, cursor) === '2' ||\n                                    result.slice(cursor - 4, cursor) === '2' ||\n                                    result.slice(cursor - 1, cursor) === '2') &&\n                                    Number(inputSymbol) > 3 &&\n                                    cursor > 10)) {\n                            processedPosition = processedPosition + 1;\n                            cursor += 1;\n                            i--;\n                            continue;\n                        }\n                    }\n                    if (maskExpression[cursor] === MaskExpression.MINUTE ||\n                        maskExpression[cursor] === MaskExpression.SECOND) {\n                        if (Number(inputSymbol) > 5) {\n                            processedPosition = !this.leadZeroDateTime\n                                ? processedPosition + 1\n                                : processedPosition;\n                            cursor += 1;\n                            this._shiftStep(cursor);\n                            i--;\n                            if (this.leadZeroDateTime) {\n                                result += '0';\n                            }\n                            continue;\n                        }\n                    }\n                    const daysCount = 31;\n                    const inputValueCursor = processedValue[cursor];\n                    const inputValueCursorPlusOne = processedValue[cursor + 1];\n                    const inputValueCursorPlusTwo = processedValue[cursor + 2];\n                    const inputValueCursorMinusOne = processedValue[cursor - 1];\n                    const inputValueCursorMinusTwo = processedValue[cursor - 2];\n                    const inputValueSliceMinusThreeMinusOne = processedValue.slice(cursor - 3, cursor - 1);\n                    const inputValueSliceMinusOnePlusOne = processedValue.slice(cursor - 1, cursor + 1);\n                    const inputValueSliceCursorPlusTwo = processedValue.slice(cursor, cursor + 2);\n                    const inputValueSliceMinusTwoCursor = processedValue.slice(cursor - 2, cursor);\n                    if (maskExpression[cursor] === MaskExpression.DAY) {\n                        const maskStartWithMonth = maskExpression.slice(0, 2) === MaskExpression.MONTHS;\n                        const startWithMonthInput = maskExpression.slice(0, 2) === MaskExpression.MONTHS &&\n                            this.specialCharacters.includes(inputValueCursorMinusTwo);\n                        if ((Number(inputSymbol) > 3 && this.leadZeroDateTime) ||\n                            (!maskStartWithMonth &&\n                                (Number(inputValueSliceCursorPlusTwo) > daysCount ||\n                                    Number(inputValueSliceMinusOnePlusOne) > daysCount ||\n                                    this.specialCharacters.includes(inputValueCursorPlusOne))) ||\n                            (startWithMonthInput\n                                ? Number(inputValueSliceMinusOnePlusOne) > daysCount ||\n                                    (!this.specialCharacters.includes(inputValueCursor) &&\n                                        this.specialCharacters.includes(inputValueCursorPlusTwo)) ||\n                                    this.specialCharacters.includes(inputValueCursor)\n                                : Number(inputValueSliceCursorPlusTwo) > daysCount ||\n                                    (this.specialCharacters.includes(inputValueCursorPlusOne) &&\n                                        !backspaced))) {\n                            processedPosition = !this.leadZeroDateTime\n                                ? processedPosition + 1\n                                : processedPosition;\n                            cursor += 1;\n                            this._shiftStep(cursor);\n                            i--;\n                            if (this.leadZeroDateTime) {\n                                result += '0';\n                            }\n                            continue;\n                        }\n                    }\n                    if (maskExpression[cursor] === MaskExpression.MONTH) {\n                        const monthsCount = 12;\n                        // mask without day\n                        const withoutDays = cursor === 0 &&\n                            (Number(inputSymbol) > 2 ||\n                                Number(inputValueSliceCursorPlusTwo) > monthsCount ||\n                                (this.specialCharacters.includes(inputValueCursorPlusOne) &&\n                                    !backspaced));\n                        // day<10 && month<12 for input\n                        const specialChart = maskExpression.slice(cursor + 2, cursor + 3);\n                        const day1monthInput = inputValueSliceMinusThreeMinusOne.includes(specialChart) &&\n                            maskExpression.includes('d0') &&\n                            ((this.specialCharacters.includes(inputValueCursorMinusTwo) &&\n                                Number(inputValueSliceMinusOnePlusOne) > monthsCount &&\n                                !this.specialCharacters.includes(inputValueCursor)) ||\n                                this.specialCharacters.includes(inputValueCursor));\n                        //  month<12 && day<10 for input\n                        const day2monthInput = Number(inputValueSliceMinusThreeMinusOne) <= daysCount &&\n                            !this.specialCharacters.includes(inputValueSliceMinusThreeMinusOne) &&\n                            this.specialCharacters.includes(inputValueCursorMinusOne) &&\n                            (Number(inputValueSliceCursorPlusTwo) > monthsCount ||\n                                this.specialCharacters.includes(inputValueCursorPlusOne));\n                        // cursor === 5 && without days\n                        const day2monthInputDot = (Number(inputValueSliceCursorPlusTwo) > monthsCount && cursor === 5) ||\n                            (this.specialCharacters.includes(inputValueCursorPlusOne) &&\n                                cursor === 5);\n                        // // day<10 && month<12 for paste whole data\n                        const day1monthPaste = Number(inputValueSliceMinusThreeMinusOne) > daysCount &&\n                            !this.specialCharacters.includes(inputValueSliceMinusThreeMinusOne) &&\n                            !this.specialCharacters.includes(inputValueSliceMinusTwoCursor) &&\n                            Number(inputValueSliceMinusTwoCursor) > monthsCount &&\n                            maskExpression.includes('d0');\n                        // 10<day<31 && month<12 for paste whole data\n                        const day2monthPaste = Number(inputValueSliceMinusThreeMinusOne) <= daysCount &&\n                            !this.specialCharacters.includes(inputValueSliceMinusThreeMinusOne) &&\n                            !this.specialCharacters.includes(inputValueCursorMinusOne) &&\n                            Number(inputValueSliceMinusOnePlusOne) > monthsCount;\n                        if ((Number(inputSymbol) > 1 && this.leadZeroDateTime) ||\n                            withoutDays ||\n                            day1monthInput ||\n                            day2monthPaste ||\n                            day1monthPaste ||\n                            day2monthInput ||\n                            (day2monthInputDot && !this.leadZeroDateTime)) {\n                            processedPosition = !this.leadZeroDateTime\n                                ? processedPosition + 1\n                                : processedPosition;\n                            cursor += 1;\n                            this._shiftStep(cursor);\n                            i--;\n                            if (this.leadZeroDateTime) {\n                                result += '0';\n                            }\n                            continue;\n                        }\n                    }\n                    result += inputSymbol;\n                    cursor++;\n                }\n                else if (this.specialCharacters.includes(inputSymbol) &&\n                    maskExpression[cursor] === inputSymbol) {\n                    result += inputSymbol;\n                    cursor++;\n                }\n                else if (this.specialCharacters.indexOf(maskExpression[cursor] ?? MaskExpression.EMPTY_STRING) !== -1) {\n                    result += maskExpression[cursor];\n                    cursor++;\n                    this._shiftStep(cursor);\n                    i--;\n                }\n                else if (maskExpression[cursor] === MaskExpression.NUMBER_NINE &&\n                    this.showMaskTyped) {\n                    this._shiftStep(cursor);\n                }\n                else if (this.patterns[maskExpression[cursor] ?? MaskExpression.EMPTY_STRING] &&\n                    this.patterns[maskExpression[cursor] ?? MaskExpression.EMPTY_STRING]?.optional) {\n                    if (!!inputArray[cursor] &&\n                        maskExpression !== '***************' &&\n                        maskExpression !== '000.000.000-00' &&\n                        maskExpression !== '00.000.000/0000-00' &&\n                        !maskExpression.match(/^9+\\.0+$/) &&\n                        !this.patterns[maskExpression[cursor] ?? MaskExpression.EMPTY_STRING]\n                            ?.optional) {\n                        result += inputArray[cursor];\n                    }\n                    if (maskExpression.includes(MaskExpression.NUMBER_NINE + MaskExpression.SYMBOL_STAR) &&\n                        maskExpression.includes(MaskExpression.NUMBER_ZERO + MaskExpression.SYMBOL_STAR)) {\n                        cursor++;\n                    }\n                    cursor++;\n                    i--;\n                }\n                else if (this.maskExpression[cursor + 1] === MaskExpression.SYMBOL_STAR &&\n                    this._findSpecialChar(this.maskExpression[cursor + 2] ?? MaskExpression.EMPTY_STRING) &&\n                    this._findSpecialChar(inputSymbol) === this.maskExpression[cursor + 2] &&\n                    multi) {\n                    cursor += 3;\n                    result += inputSymbol;\n                }\n                else if (this.maskExpression[cursor + 1] === MaskExpression.SYMBOL_QUESTION &&\n                    this._findSpecialChar(this.maskExpression[cursor + 2] ?? MaskExpression.EMPTY_STRING) &&\n                    this._findSpecialChar(inputSymbol) === this.maskExpression[cursor + 2] &&\n                    multi) {\n                    cursor += 3;\n                    result += inputSymbol;\n                }\n                else if (this.showMaskTyped &&\n                    this.specialCharacters.indexOf(inputSymbol) < 0 &&\n                    inputSymbol !== this.placeHolderCharacter &&\n                    this.placeHolderCharacter.length === 1) {\n                    stepBack = true;\n                }\n            }\n        }\n        if (result[processedPosition - 1] &&\n            result.length + 1 === maskExpression.length &&\n            this.specialCharacters.indexOf(maskExpression[maskExpression.length - 1] ?? MaskExpression.EMPTY_STRING) !== -1) {\n            result += maskExpression[maskExpression.length - 1];\n        }\n        let newPosition = processedPosition + 1;\n        while (this._shift.has(newPosition)) {\n            shift++;\n            newPosition++;\n        }\n        let actualShift = justPasted && !maskExpression.startsWith(MaskExpression.SEPARATOR)\n            ? cursor\n            : this._shift.has(processedPosition)\n                ? shift\n                : 0;\n        if (stepBack) {\n            actualShift--;\n        }\n        cb(actualShift, backspaceShift);\n        if (shift < 0) {\n            this._shift.clear();\n        }\n        let onlySpecial = false;\n        if (backspaced) {\n            onlySpecial = inputArray.every((char) => this.specialCharacters.includes(char));\n        }\n        let res = `${this.prefix}${onlySpecial ? MaskExpression.EMPTY_STRING : result}${this.showMaskTyped ? '' : this.suffix}`;\n        if (result.length === 0) {\n            res = this.instantPrefix ? `${this.prefix}${result}` : `${result}`;\n        }\n        const isSpecialCharacterMaskFirstSymbol = processedValue.length === 1 &&\n            this.specialCharacters.includes(maskExpression[0]) &&\n            processedValue !== maskExpression[0];\n        if (!this._checkSymbolMask(processedValue, maskExpression[1]) &&\n            isSpecialCharacterMaskFirstSymbol) {\n            return '';\n        }\n        if (result.includes(MaskExpression.MINUS) && this.prefix && this.allowNegativeNumbers) {\n            if (backspaced && result === MaskExpression.MINUS) {\n                return '';\n            }\n            res = `${MaskExpression.MINUS}${this.prefix}${result\n                .split(MaskExpression.MINUS)\n                .join(MaskExpression.EMPTY_STRING)}${this.suffix}`;\n        }\n        return res;\n    }\n    _findDropSpecialChar(inputSymbol) {\n        if (Array.isArray(this.dropSpecialCharacters)) {\n            return this.dropSpecialCharacters.find((val) => val === inputSymbol);\n        }\n        return this._findSpecialChar(inputSymbol);\n    }\n    _findSpecialChar(inputSymbol) {\n        return this.specialCharacters.find((val) => val === inputSymbol);\n    }\n    _checkSymbolMask(inputSymbol, maskSymbol) {\n        this.patterns = this.customPattern ? this.customPattern : this.patterns;\n        return ((this.patterns[maskSymbol]?.pattern &&\n            this.patterns[maskSymbol]?.pattern.test(inputSymbol)) ??\n            false);\n    }\n    _formatWithSeparators = (str, thousandSeparatorChar, decimalChars, precision) => {\n        let x = [];\n        let decimalChar = '';\n        if (Array.isArray(decimalChars)) {\n            const regExp = new RegExp(decimalChars.map((v) => ('[\\\\^$.|?*+()'.indexOf(v) >= 0 ? `\\\\${v}` : v)).join('|'));\n            x = str.split(regExp);\n            decimalChar = str.match(regExp)?.[0] ?? MaskExpression.EMPTY_STRING;\n        }\n        else {\n            x = str.split(decimalChars);\n            decimalChar = decimalChars;\n        }\n        const decimals = x.length > 1 ? `${decimalChar}${x[1]}` : MaskExpression.EMPTY_STRING;\n        let res = x[0] ?? MaskExpression.EMPTY_STRING;\n        const separatorLimit = this.separatorLimit.replace(/\\s/g, MaskExpression.EMPTY_STRING);\n        if (separatorLimit && +separatorLimit) {\n            if (res[0] === MaskExpression.MINUS) {\n                res = `-${res.slice(1, res.length).slice(0, separatorLimit.length)}`;\n            }\n            else {\n                res = res.slice(0, separatorLimit.length);\n            }\n        }\n        const rgx = /(\\d+)(\\d{3})/;\n        while (thousandSeparatorChar && rgx.test(res)) {\n            res = res.replace(rgx, '$1' + thousandSeparatorChar + '$2');\n        }\n        if (typeof precision === 'undefined') {\n            return res + decimals;\n        }\n        else if (precision === 0) {\n            return res;\n        }\n        return res + decimals.substring(0, precision + 1);\n    };\n    percentage = (str) => {\n        const sanitizedStr = str.replace(',', '.');\n        const value = Number(this.allowNegativeNumbers && str.includes(MaskExpression.MINUS)\n            ? sanitizedStr.slice(1, str.length)\n            : sanitizedStr);\n        return !isNaN(value) && value >= 0 && value <= 100;\n    };\n    getPrecision = (maskExpression) => {\n        const x = maskExpression.split(MaskExpression.DOT);\n        if (x.length > 1) {\n            return Number(x[x.length - 1]);\n        }\n        return Infinity;\n    };\n    checkAndRemoveSuffix = (inputValue) => {\n        for (let i = this.suffix?.length - 1; i >= 0; i--) {\n            const substr = this.suffix.substring(i, this.suffix?.length);\n            if (inputValue.includes(substr) &&\n                i !== this.suffix?.length - 1 &&\n                (i - 1 < 0 ||\n                    !inputValue.includes(this.suffix.substring(i - 1, this.suffix?.length)))) {\n                return inputValue.replace(substr, MaskExpression.EMPTY_STRING);\n            }\n        }\n        return inputValue;\n    };\n    checkInputPrecision = (inputValue, precision, decimalMarker) => {\n        let processedInputValue = inputValue;\n        let processedDecimalMarker = decimalMarker;\n        if (precision < Infinity) {\n            // TODO need think about decimalMarker\n            if (Array.isArray(processedDecimalMarker)) {\n                const marker = processedDecimalMarker.find((dm) => dm !== this.thousandSeparator);\n                processedDecimalMarker = marker ? marker : processedDecimalMarker[0];\n            }\n            const precisionRegEx = new RegExp(this._charToRegExpExpression(processedDecimalMarker) + `\\\\d{${precision}}.*$`);\n            const precisionMatch = processedInputValue.match(precisionRegEx);\n            const precisionMatchLength = (precisionMatch && precisionMatch[0]?.length) ?? 0;\n            if (precisionMatchLength - 1 > precision) {\n                const diff = precisionMatchLength - 1 - precision;\n                processedInputValue = processedInputValue.substring(0, processedInputValue.length - diff);\n            }\n            if (precision === 0 &&\n                this._compareOrIncludes(processedInputValue[processedInputValue.length - 1], processedDecimalMarker, this.thousandSeparator)) {\n                processedInputValue = processedInputValue.substring(0, processedInputValue.length - 1);\n            }\n        }\n        return processedInputValue;\n    };\n    _stripToDecimal(str) {\n        return str\n            .split(MaskExpression.EMPTY_STRING)\n            .filter((i, idx) => {\n            const isDecimalMarker = typeof this.decimalMarker === 'string'\n                ? i === this.decimalMarker\n                : // TODO (inepipenko) use utility type\n                    this.decimalMarker.includes(i);\n            return (i.match('^-?\\\\d') ||\n                i === this.thousandSeparator ||\n                isDecimalMarker ||\n                (i === MaskExpression.MINUS && idx === 0 && this.allowNegativeNumbers));\n        })\n            .join(MaskExpression.EMPTY_STRING);\n    }\n    _charToRegExpExpression(char) {\n        // if (Array.isArray(char)) {\n        // \treturn char.map((v) => ('[\\\\^$.|?*+()'.indexOf(v) >= 0 ? `\\\\${v}` : v)).join('|');\n        // }\n        if (char) {\n            const charsToEscape = '[\\\\^$.|?*+()';\n            return char === ' ' ? '\\\\s' : charsToEscape.indexOf(char) >= 0 ? `\\\\${char}` : char;\n        }\n        return char;\n    }\n    _shiftStep(cursor) {\n        this._shift.add(cursor + this.prefix.length || 0);\n    }\n    _compareOrIncludes(value, comparedValue, excludedValue) {\n        return Array.isArray(comparedValue)\n            ? comparedValue.filter((v) => v !== excludedValue).includes(value)\n            : value === comparedValue;\n    }\n    _validIP(valuesIP) {\n        return !(valuesIP.length === 4 &&\n            !valuesIP.some((value, index) => {\n                if (valuesIP.length !== index + 1) {\n                    return value === MaskExpression.EMPTY_STRING || Number(value) > 255;\n                }\n                return value === MaskExpression.EMPTY_STRING || Number(value.substring(0, 3)) > 255;\n            }));\n    }\n    _splitPercentZero(value) {\n        if (value === MaskExpression.MINUS && this.allowNegativeNumbers) {\n            return value;\n        }\n        const decimalIndex = typeof this.decimalMarker === 'string'\n            ? value.indexOf(this.decimalMarker)\n            : value.indexOf(MaskExpression.DOT);\n        const emptyOrMinus = this.allowNegativeNumbers && value.includes(MaskExpression.MINUS) ? '-' : '';\n        if (decimalIndex === -1) {\n            const parsedValue = parseInt(emptyOrMinus ? value.slice(1, value.length) : value, 10);\n            return isNaN(parsedValue)\n                ? MaskExpression.EMPTY_STRING\n                : `${emptyOrMinus}${parsedValue}`;\n        }\n        else {\n            const integerPart = parseInt(value.replace('-', '').substring(0, decimalIndex), 10);\n            const decimalPart = value.substring(decimalIndex + 1);\n            const integerString = isNaN(integerPart) ? '' : integerPart.toString();\n            const decimal = typeof this.decimalMarker === 'string' ? this.decimalMarker : MaskExpression.DOT;\n            return integerString === MaskExpression.EMPTY_STRING\n                ? MaskExpression.EMPTY_STRING\n                : `${emptyOrMinus}${integerString}${decimal}${decimalPart}`;\n        }\n    }\n    _findFirstNonZeroAndDecimalIndex(inputString, decimalMarker) {\n        let decimalMarkerIndex = null;\n        let nonZeroIndex = null;\n        for (let i = 0; i < inputString.length; i++) {\n            const char = inputString[i];\n            if (char === decimalMarker && decimalMarkerIndex === null) {\n                decimalMarkerIndex = i;\n            }\n            if (char && char >= '1' && char <= '9' && nonZeroIndex === null) {\n                nonZeroIndex = i;\n            }\n            if (decimalMarkerIndex !== null && nonZeroIndex !== null) {\n                break;\n            }\n        }\n        return {\n            decimalMarkerIndex,\n            nonZeroIndex,\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: NgxMaskApplierService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: NgxMaskApplierService });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: NgxMaskApplierService, decorators: [{\n            type: Injectable\n        }] });\n\nclass NgxMaskService extends NgxMaskApplierService {\n    isNumberValue = false;\n    maskIsShown = '';\n    selStart = null;\n    selEnd = null;\n    maskChanged = false;\n    maskExpressionArray = [];\n    previousValue = '';\n    currentValue = '';\n    /**\n     * Whether we are currently in writeValue function, in this case when applying the mask we don't want to trigger onChange function,\n     * since writeValue should be a one way only process of writing the DOM value based on the Angular model value.\n     */\n    writingValue = false;\n    _emitValue = false;\n    _start;\n    _end;\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    onChange = (_) => { };\n    _elementRef = inject(ElementRef, { optional: true });\n    document = inject(DOCUMENT);\n    _config = inject(NGX_MASK_CONFIG);\n    _renderer = inject(Renderer2, { optional: true });\n    /**\n     * Applies the mask to the input value.\n     * @param inputValue The input value to be masked.\n     * @param maskExpression The mask expression to apply.\n     * @param position The position in the input value.\n     * @param justPasted Whether the value was just pasted.\n     * @param backspaced Whether the value was backspaced.\n     * @param cb Callback function.\n     * @returns The masked value.\n     */\n    applyMask(inputValue, maskExpression, position = 0, justPasted = false, backspaced = false, \n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    cb = () => { }) {\n        // If no mask expression, return the input value or the actual value\n        if (!maskExpression) {\n            return inputValue !== this.actualValue ? this.actualValue : inputValue;\n        }\n        // Show mask in input if required\n        this.maskIsShown = this.showMaskTyped\n            ? this.showMaskInInput()\n            : MaskExpression.EMPTY_STRING;\n        // Handle specific mask expressions\n        if (this.maskExpression === MaskExpression.IP && this.showMaskTyped) {\n            this.maskIsShown = this.showMaskInInput(inputValue || MaskExpression.HASH);\n        }\n        if (this.maskExpression === MaskExpression.CPF_CNPJ && this.showMaskTyped) {\n            this.maskIsShown = this.showMaskInInput(inputValue || MaskExpression.HASH);\n        }\n        // Handle empty input value with mask typed\n        if (!inputValue && this.showMaskTyped) {\n            this.formControlResult(this.prefix);\n            return `${this.prefix}${this.maskIsShown}${this.suffix}`;\n        }\n        const getSymbol = !!inputValue && typeof this.selStart === 'number'\n            ? (inputValue[this.selStart] ?? MaskExpression.EMPTY_STRING)\n            : MaskExpression.EMPTY_STRING;\n        let newInputValue = '';\n        let newPosition = position;\n        // Handle hidden input or input with asterisk symbol\n        if ((this.hiddenInput ||\n            (inputValue && inputValue.indexOf(MaskExpression.SYMBOL_STAR) >= 0)) &&\n            !this.writingValue) {\n            let actualResult = inputValue && inputValue.length === 1\n                ? inputValue.split(MaskExpression.EMPTY_STRING)\n                : this.actualValue.split(MaskExpression.EMPTY_STRING);\n            // Handle backspace\n            if (backspaced) {\n                actualResult = actualResult\n                    .slice(0, position)\n                    .concat(actualResult.slice(position + 1));\n            }\n            // Remove mask if showMaskTyped is true\n            if (this.showMaskTyped) {\n                // eslint-disable-next-line no-param-reassign\n                inputValue = this.removeMask(inputValue);\n                actualResult = this.removeMask(actualResult.join('')).split(MaskExpression.EMPTY_STRING);\n            }\n            // Handle selection start and end\n            if (typeof this.selStart === 'object' && typeof this.selEnd === 'object') {\n                this.selStart = Number(this.selStart);\n                this.selEnd = Number(this.selEnd);\n            }\n            else {\n                if (inputValue !== MaskExpression.EMPTY_STRING && actualResult.length) {\n                    if (typeof this.selStart === 'number' && typeof this.selEnd === 'number') {\n                        if (inputValue.length > actualResult.length) {\n                            actualResult.splice(this.selStart, 0, getSymbol);\n                        }\n                        else if (inputValue.length < actualResult.length) {\n                            if (actualResult.length - inputValue.length === 1) {\n                                if (backspaced) {\n                                    actualResult.splice(this.selStart - 1, 1);\n                                }\n                                else {\n                                    actualResult.splice(inputValue.length - 1, 1);\n                                }\n                            }\n                            else {\n                                actualResult.splice(this.selStart, this.selEnd - this.selStart);\n                            }\n                        }\n                    }\n                }\n                else {\n                    actualResult = [];\n                }\n            }\n            // Remove mask if showMaskTyped is true and hiddenInput is false\n            if (this.showMaskTyped && !this.hiddenInput) {\n                newInputValue = this.removeMask(inputValue);\n            }\n            // Handle actual value length\n            if (this.actualValue.length) {\n                if (actualResult.length < inputValue.length) {\n                    newInputValue = this.shiftTypedSymbols(actualResult.join(MaskExpression.EMPTY_STRING));\n                }\n                else if (actualResult.length === inputValue.length) {\n                    newInputValue = actualResult.join(MaskExpression.EMPTY_STRING);\n                }\n                else {\n                    newInputValue = inputValue;\n                }\n            }\n            else {\n                newInputValue = inputValue;\n            }\n        }\n        // Handle just pasted input\n        if (justPasted && (this.hiddenInput || !this.hiddenInput)) {\n            newInputValue = inputValue;\n        }\n        // Handle backspace with special characters\n        if (backspaced &&\n            this.specialCharacters.indexOf(this.maskExpression[newPosition] ?? MaskExpression.EMPTY_STRING) !== -1 &&\n            this.showMaskTyped &&\n            !this.prefix) {\n            newInputValue = this.currentValue;\n        }\n        // Handle deleted special character\n        if (this.deletedSpecialCharacter && newPosition) {\n            if (this.specialCharacters.includes(this.actualValue.slice(newPosition, newPosition + 1))) {\n                newPosition = newPosition + 1;\n            }\n            else if (maskExpression.slice(newPosition - 1, newPosition + 1) !== MaskExpression.MONTHS) {\n                newPosition = newPosition - 2;\n            }\n            this.deletedSpecialCharacter = false;\n        }\n        // Remove mask if showMaskTyped is true and placeHolderCharacter length is 1\n        if (this.showMaskTyped &&\n            this.placeHolderCharacter.length === 1 &&\n            !this.leadZeroDateTime) {\n            newInputValue = this.removeMask(newInputValue);\n        }\n        // Handle mask changed\n        if (this.maskChanged) {\n            newInputValue = inputValue;\n        }\n        else {\n            newInputValue =\n                Boolean(newInputValue) && newInputValue.length ? newInputValue : inputValue;\n        }\n        // Handle showMaskTyped and keepCharacterPositions\n        if (this.showMaskTyped &&\n            this.keepCharacterPositions &&\n            this.actualValue &&\n            !justPasted &&\n            !this.writingValue) {\n            const value = this.dropSpecialCharacters\n                ? this.removeMask(this.actualValue)\n                : this.actualValue;\n            this.formControlResult(value);\n            return this.actualValue\n                ? this.actualValue\n                : `${this.prefix}${this.maskIsShown}${this.suffix}`;\n        }\n        // Apply the mask using the parent class method\n        const result = super.applyMask(newInputValue, maskExpression, newPosition, justPasted, backspaced, cb);\n        this.actualValue = this.getActualValue(result);\n        // handle some separator implications:\n        // a.) adjust decimalMarker default (. -> ,) if thousandSeparator is a dot\n        if (this.thousandSeparator === MaskExpression.DOT &&\n            this.decimalMarker === MaskExpression.DOT) {\n            this.decimalMarker = MaskExpression.COMMA;\n        }\n        // b) remove decimal marker from list of special characters to mask\n        if (this.maskExpression.startsWith(MaskExpression.SEPARATOR) &&\n            this.dropSpecialCharacters === true) {\n            this.specialCharacters = this.specialCharacters.filter((item) => !this._compareOrIncludes(item, this.decimalMarker, this.thousandSeparator) //item !== this.decimalMarker, // !\n            );\n        }\n        // Update previous and current values\n        if (result || result === '') {\n            this.previousValue = this.currentValue;\n            this.currentValue = result;\n            this._emitValue =\n                this.previousValue !== this.currentValue ||\n                    (newInputValue !== this.currentValue && this.writingValue) ||\n                    (this.previousValue === this.currentValue && justPasted);\n        }\n        // Propagate the input value back to the Angular model\n        // eslint-disable-next-line no-unused-expressions,@typescript-eslint/no-unused-expressions\n        this._emitValue ? this.formControlResult(result) : '';\n        // Handle hidden input and showMaskTyped\n        if (!this.showMaskTyped || (this.showMaskTyped && this.hiddenInput)) {\n            if (this.hiddenInput) {\n                return `${this.hideInput(result, this.maskExpression)}${this.maskIsShown.slice(result.length)}`;\n            }\n            return result;\n        }\n        const resLen = result.length;\n        const prefNmask = `${this.prefix}${this.maskIsShown}${this.suffix}`;\n        // Handle specific mask expressions\n        if (this.maskExpression.includes(MaskExpression.HOURS)) {\n            const countSkipedSymbol = this._numberSkipedSymbols(result);\n            return `${result}${prefNmask.slice(resLen + countSkipedSymbol)}`;\n        }\n        else if (this.maskExpression === MaskExpression.IP ||\n            this.maskExpression === MaskExpression.CPF_CNPJ) {\n            return `${result}${prefNmask}`;\n        }\n        return `${result}${prefNmask.slice(resLen)}`;\n    }\n    // get the number of characters that were shifted\n    _numberSkipedSymbols(value) {\n        const regex = /(^|\\D)(\\d\\D)/g;\n        let match = regex.exec(value);\n        let countSkipedSymbol = 0;\n        while (match != null) {\n            countSkipedSymbol += 1;\n            match = regex.exec(value);\n        }\n        return countSkipedSymbol;\n    }\n    applyValueChanges(position, justPasted, backspaced, \n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    cb = () => { }) {\n        const formElement = this._elementRef?.nativeElement;\n        if (!formElement) {\n            return;\n        }\n        formElement.value = this.applyMask(formElement.value, this.maskExpression, position, justPasted, backspaced, cb);\n        if (formElement === this._getActiveElement()) {\n            return;\n        }\n        this.clearIfNotMatchFn();\n    }\n    hideInput(inputValue, maskExpression) {\n        return inputValue\n            .split(MaskExpression.EMPTY_STRING)\n            .map((curr, index) => {\n            if (this.patterns &&\n                this.patterns[maskExpression[index] ?? MaskExpression.EMPTY_STRING] &&\n                this.patterns[maskExpression[index] ?? MaskExpression.EMPTY_STRING]?.symbol) {\n                return this.patterns[maskExpression[index] ?? MaskExpression.EMPTY_STRING]\n                    ?.symbol;\n            }\n            return curr;\n        })\n            .join(MaskExpression.EMPTY_STRING);\n    }\n    // this function is not necessary, it checks result against maskExpression\n    getActualValue(res) {\n        const compare = res\n            .split(MaskExpression.EMPTY_STRING)\n            .filter((symbol, i) => {\n            const maskChar = this.maskExpression[i] ?? MaskExpression.EMPTY_STRING;\n            return (this._checkSymbolMask(symbol, maskChar) ||\n                (this.specialCharacters.includes(maskChar) && symbol === maskChar));\n        });\n        if (compare.join(MaskExpression.EMPTY_STRING) === res) {\n            return compare.join(MaskExpression.EMPTY_STRING);\n        }\n        return res;\n    }\n    shiftTypedSymbols(inputValue) {\n        let symbolToReplace = '';\n        const newInputValue = (inputValue &&\n            inputValue\n                .split(MaskExpression.EMPTY_STRING)\n                .map((currSymbol, index) => {\n                if (this.specialCharacters.includes(inputValue[index + 1] ?? MaskExpression.EMPTY_STRING) &&\n                    inputValue[index + 1] !== this.maskExpression[index + 1]) {\n                    symbolToReplace = currSymbol;\n                    return inputValue[index + 1];\n                }\n                if (symbolToReplace.length) {\n                    const replaceSymbol = symbolToReplace;\n                    symbolToReplace = MaskExpression.EMPTY_STRING;\n                    return replaceSymbol;\n                }\n                return currSymbol;\n            })) ||\n            [];\n        return newInputValue.join(MaskExpression.EMPTY_STRING);\n    }\n    /**\n     * Convert number value to string\n     * 3.1415 -> '3.1415'\n     * 1e-7 -> '0.0000001'\n     */\n    numberToString(value) {\n        if ((!value && value !== 0) ||\n            (this.maskExpression.startsWith(MaskExpression.SEPARATOR) &&\n                (this.leadZero || !this.dropSpecialCharacters)) ||\n            (this.maskExpression.startsWith(MaskExpression.SEPARATOR) &&\n                this.separatorLimit.length > 14 &&\n                String(value).length > 14)) {\n            return String(value);\n        }\n        return Number(value)\n            .toLocaleString('fullwide', {\n            useGrouping: false,\n            maximumFractionDigits: 20,\n        })\n            .replace(`/${MaskExpression.MINUS}/`, MaskExpression.MINUS);\n    }\n    showMaskInInput(inputVal) {\n        if (this.showMaskTyped && !!this.shownMaskExpression) {\n            if (this.maskExpression.length !== this.shownMaskExpression.length) {\n                throw new Error('Mask expression must match mask placeholder length');\n            }\n            else {\n                return this.shownMaskExpression;\n            }\n        }\n        else if (this.showMaskTyped) {\n            if (inputVal) {\n                if (this.maskExpression === MaskExpression.IP) {\n                    return this._checkForIp(inputVal);\n                }\n                if (this.maskExpression === MaskExpression.CPF_CNPJ) {\n                    return this._checkForCpfCnpj(inputVal);\n                }\n            }\n            if (this.placeHolderCharacter.length === this.maskExpression.length) {\n                return this.placeHolderCharacter;\n            }\n            return this.maskExpression.replace(/\\w/g, this.placeHolderCharacter);\n        }\n        return '';\n    }\n    clearIfNotMatchFn() {\n        const formElement = this._elementRef?.nativeElement;\n        if (!formElement) {\n            return;\n        }\n        if (this.clearIfNotMatch &&\n            this.prefix.length + this.maskExpression.length + this.suffix.length !==\n                formElement.value.replace(this.placeHolderCharacter, MaskExpression.EMPTY_STRING)\n                    .length) {\n            this.formElementProperty = ['value', MaskExpression.EMPTY_STRING];\n            this.applyMask('', this.maskExpression);\n        }\n    }\n    set formElementProperty([name, value]) {\n        if (!this._renderer || !this._elementRef) {\n            return;\n        }\n        //[TODO]: andriikamaldinov1 find better solution\n        Promise.resolve().then(() => this._renderer?.setProperty(this._elementRef?.nativeElement, name, value));\n    }\n    checkDropSpecialCharAmount(mask) {\n        const chars = mask\n            .split(MaskExpression.EMPTY_STRING)\n            .filter((item) => this._findDropSpecialChar(item));\n        return chars.length;\n    }\n    removeMask(inputValue) {\n        return this._removeMask(this._removeSuffix(this._removePrefix(inputValue)), this.specialCharacters.concat('_').concat(this.placeHolderCharacter));\n    }\n    _checkForIp(inputVal) {\n        if (inputVal === MaskExpression.HASH) {\n            return `${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n        }\n        const arr = [];\n        // eslint-disable-next-line @typescript-eslint/prefer-for-of\n        for (let i = 0; i < inputVal.length; i++) {\n            const value = inputVal[i] ?? MaskExpression.EMPTY_STRING;\n            if (!value) {\n                continue;\n            }\n            if (value.match('\\\\d')) {\n                arr.push(value);\n            }\n        }\n        if (arr.length <= 3) {\n            return `${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n        }\n        if (arr.length > 3 && arr.length <= 6) {\n            return `${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n        }\n        if (arr.length > 6 && arr.length <= 9) {\n            return this.placeHolderCharacter;\n        }\n        if (arr.length > 9 && arr.length <= 12) {\n            return '';\n        }\n        return '';\n    }\n    _checkForCpfCnpj(inputVal) {\n        const cpf = `${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `-${this.placeHolderCharacter}${this.placeHolderCharacter}`;\n        const cnpj = `${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `/${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `-${this.placeHolderCharacter}${this.placeHolderCharacter}`;\n        if (inputVal === MaskExpression.HASH) {\n            return cpf;\n        }\n        const arr = [];\n        // eslint-disable-next-line @typescript-eslint/prefer-for-of\n        for (let i = 0; i < inputVal.length; i++) {\n            const value = inputVal[i] ?? MaskExpression.EMPTY_STRING;\n            if (!value) {\n                continue;\n            }\n            if (value.match('\\\\d')) {\n                arr.push(value);\n            }\n        }\n        if (arr.length <= 3) {\n            return cpf.slice(arr.length, cpf.length);\n        }\n        if (arr.length > 3 && arr.length <= 6) {\n            return cpf.slice(arr.length + 1, cpf.length);\n        }\n        if (arr.length > 6 && arr.length <= 9) {\n            return cpf.slice(arr.length + 2, cpf.length);\n        }\n        if (arr.length > 9 && arr.length < 11) {\n            return cpf.slice(arr.length + 3, cpf.length);\n        }\n        if (arr.length === 11) {\n            return '';\n        }\n        if (arr.length === 12) {\n            if (inputVal.length === 17) {\n                return cnpj.slice(16, cnpj.length);\n            }\n            return cnpj.slice(15, cnpj.length);\n        }\n        if (arr.length > 12 && arr.length <= 14) {\n            return cnpj.slice(arr.length + 4, cnpj.length);\n        }\n        return '';\n    }\n    /**\n     * Recursively determine the current active element by navigating the Shadow DOM until the Active Element is found.\n     */\n    _getActiveElement(document = this.document) {\n        const shadowRootEl = document?.activeElement?.shadowRoot;\n        if (!shadowRootEl?.activeElement) {\n            return document.activeElement;\n        }\n        else {\n            return this._getActiveElement(shadowRootEl);\n        }\n    }\n    /**\n     * Propogates the input value back to the Angular model by triggering the onChange function. It won't do this if writingValue\n     * is true. If that is true it means we are currently in the writeValue function, which is supposed to only update the actual\n     * DOM element based on the Angular model value. It should be a one way process, i.e. writeValue should not be modifying the Angular\n     * model value too. Therefore, we don't trigger onChange in this scenario.\n     * @param inputValue the current form input value\n     */\n    formControlResult(inputValue) {\n        const outputTransformFn = this.outputTransformFn\n            ? this.outputTransformFn\n            : (v) => v;\n        this.writingValue = false;\n        this.maskChanged = false;\n        if (Array.isArray(this.dropSpecialCharacters)) {\n            this.onChange(outputTransformFn(this._toNumber(this._checkSymbols(this._removeMask(this._removeSuffix(this._removePrefix(inputValue)), this.dropSpecialCharacters)))));\n        }\n        else if (this.dropSpecialCharacters ||\n            (!this.dropSpecialCharacters && this.prefix === inputValue)) {\n            this.onChange(outputTransformFn(this._toNumber(this._checkSymbols(this._removeSuffix(this._removePrefix(inputValue))))));\n        }\n        else {\n            this.onChange(outputTransformFn(this._toNumber(inputValue)));\n        }\n    }\n    _toNumber(value) {\n        if (!this.isNumberValue || value === MaskExpression.EMPTY_STRING) {\n            return value;\n        }\n        if (this.maskExpression.startsWith(MaskExpression.SEPARATOR) &&\n            (this.leadZero || !this.dropSpecialCharacters)) {\n            return value;\n        }\n        if (String(value).length > 14 && this.maskExpression.startsWith(MaskExpression.SEPARATOR)) {\n            return String(value);\n        }\n        const num = Number(value);\n        if (this.maskExpression.startsWith(MaskExpression.SEPARATOR) && Number.isNaN(num)) {\n            const val = String(value).replace(',', '.');\n            return Number(val);\n        }\n        return Number.isNaN(num) ? value : num;\n    }\n    _removeMask(value, specialCharactersForRemove) {\n        if (this.maskExpression.startsWith(MaskExpression.PERCENT) &&\n            value.includes(MaskExpression.DOT)) {\n            return value;\n        }\n        return value\n            ? value.replace(this._regExpForRemove(specialCharactersForRemove), MaskExpression.EMPTY_STRING)\n            : value;\n    }\n    _removePrefix(value) {\n        if (!this.prefix) {\n            return value;\n        }\n        return value ? value.replace(this.prefix, MaskExpression.EMPTY_STRING) : value;\n    }\n    _removeSuffix(value) {\n        if (!this.suffix) {\n            return value;\n        }\n        return value ? value.replace(this.suffix, MaskExpression.EMPTY_STRING) : value;\n    }\n    _retrieveSeparatorValue(result) {\n        let specialCharacters = Array.isArray(this.dropSpecialCharacters)\n            ? this.specialCharacters.filter((v) => {\n                return this.dropSpecialCharacters.includes(v);\n            })\n            : this.specialCharacters;\n        if (!this.deletedSpecialCharacter &&\n            this._checkPatternForSpace() &&\n            result.includes(MaskExpression.WHITE_SPACE) &&\n            this.maskExpression.includes(MaskExpression.SYMBOL_STAR)) {\n            specialCharacters = specialCharacters.filter((char) => char !== MaskExpression.WHITE_SPACE);\n        }\n        return this._removeMask(result, specialCharacters);\n    }\n    _regExpForRemove(specialCharactersForRemove) {\n        return new RegExp(specialCharactersForRemove.map((item) => `\\\\${item}`).join('|'), 'gi');\n    }\n    _replaceDecimalMarkerToDot(value) {\n        const markers = Array.isArray(this.decimalMarker)\n            ? this.decimalMarker\n            : [this.decimalMarker];\n        return value.replace(this._regExpForRemove(markers), MaskExpression.DOT);\n    }\n    _checkSymbols(result) {\n        let processedResult = result;\n        if (processedResult === MaskExpression.EMPTY_STRING) {\n            return processedResult;\n        }\n        if (this.maskExpression.startsWith(MaskExpression.PERCENT) &&\n            this.decimalMarker === MaskExpression.COMMA) {\n            processedResult = processedResult.replace(MaskExpression.COMMA, MaskExpression.DOT);\n        }\n        const separatorPrecision = this._retrieveSeparatorPrecision(this.maskExpression);\n        const separatorValue = this.specialCharacters.length === 0\n            ? this._retrieveSeparatorValue(processedResult)\n            : this._replaceDecimalMarkerToDot(this._retrieveSeparatorValue(processedResult));\n        if (!this.isNumberValue) {\n            return separatorValue;\n        }\n        if (separatorPrecision) {\n            if (processedResult === this.decimalMarker) {\n                return null;\n            }\n            if (separatorValue.length > 14) {\n                return String(separatorValue);\n            }\n            return this._checkPrecision(this.maskExpression, separatorValue);\n        }\n        else {\n            return separatorValue;\n        }\n    }\n    _checkPatternForSpace() {\n        for (const key in this.patterns) {\n            // eslint-disable-next-line no-prototype-builtins\n            if (this.patterns[key] && this.patterns[key]?.hasOwnProperty('pattern')) {\n                const patternString = this.patterns[key]?.pattern.toString();\n                const pattern = this.patterns[key]?.pattern;\n                if (patternString?.includes(MaskExpression.WHITE_SPACE) &&\n                    pattern?.test(this.maskExpression)) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n    // TODO should think about helpers or separting decimal precision to own property\n    _retrieveSeparatorPrecision(maskExpretion) {\n        const matcher = maskExpretion.match(new RegExp(`^separator\\\\.([^d]*)`));\n        return matcher ? Number(matcher[1]) : null;\n    }\n    _checkPrecision(separatorExpression, separatorValue) {\n        const separatorPrecision = this.getPrecision(separatorExpression);\n        let value = separatorValue;\n        if (separatorExpression.indexOf('2') > 0 ||\n            (this.leadZero && Number(separatorPrecision) > 0)) {\n            if (this.decimalMarker === MaskExpression.COMMA && this.leadZero) {\n                value = value.replace(',', '.');\n            }\n            return this.leadZero\n                ? Number(value).toFixed(Number(separatorPrecision))\n                : Number(value).toFixed(2);\n        }\n        return this.numberToString(value);\n    }\n    _repeatPatternSymbols(maskExp) {\n        return ((maskExp.match(/{[0-9]+}/) &&\n            maskExp\n                .split(MaskExpression.EMPTY_STRING)\n                .reduce((accum, currVal, index) => {\n                this._start =\n                    currVal === MaskExpression.CURLY_BRACKETS_LEFT ? index : this._start;\n                if (currVal !== MaskExpression.CURLY_BRACKETS_RIGHT) {\n                    return this._findSpecialChar(currVal) ? accum + currVal : accum;\n                }\n                this._end = index;\n                const repeatNumber = Number(maskExp.slice(this._start + 1, this._end));\n                const replaceWith = new Array(repeatNumber + 1).join(maskExp[this._start - 1]);\n                if (maskExp.slice(0, this._start).length > 1 &&\n                    maskExp.includes(MaskExpression.LETTER_S)) {\n                    const symbols = maskExp.slice(0, this._start - 1);\n                    return symbols.includes(MaskExpression.CURLY_BRACKETS_LEFT)\n                        ? accum + replaceWith\n                        : symbols + accum + replaceWith;\n                }\n                else {\n                    return accum + replaceWith;\n                }\n            }, '')) ||\n            maskExp);\n    }\n    currentLocaleDecimalMarker() {\n        return (1.1).toLocaleString().substring(1, 2);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: NgxMaskService, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: NgxMaskService });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: NgxMaskService, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * @internal\n */\nfunction _configFactory() {\n    const initConfig = inject(INITIAL_CONFIG);\n    const configValue = inject(NEW_CONFIG);\n    return configValue instanceof Function\n        ? { ...initConfig, ...configValue() }\n        : { ...initConfig, ...configValue };\n}\nfunction provideNgxMask(configValue) {\n    return [\n        {\n            provide: NEW_CONFIG,\n            useValue: configValue,\n        },\n        {\n            provide: INITIAL_CONFIG,\n            useValue: initialConfig,\n        },\n        {\n            provide: NGX_MASK_CONFIG,\n            useFactory: _configFactory,\n        },\n        NgxMaskService,\n    ];\n}\nfunction provideEnvironmentNgxMask(configValue) {\n    return makeEnvironmentProviders(provideNgxMask(configValue));\n}\n\nclass NgxMaskDirective {\n    mask = input('');\n    specialCharacters = input([]);\n    patterns = input({});\n    prefix = input('');\n    suffix = input('');\n    thousandSeparator = input(' ');\n    decimalMarker = input('.');\n    dropSpecialCharacters = input(null);\n    hiddenInput = input(null);\n    showMaskTyped = input(null);\n    placeHolderCharacter = input(null);\n    shownMaskExpression = input(null);\n    clearIfNotMatch = input(null);\n    validation = input(null);\n    separatorLimit = input('');\n    allowNegativeNumbers = input(null);\n    leadZeroDateTime = input(null);\n    leadZero = input(null);\n    triggerOnMaskChange = input(null);\n    apm = input(null);\n    inputTransformFn = input(null);\n    outputTransformFn = input(null);\n    keepCharacterPositions = input(null);\n    instantPrefix = input(null);\n    maskFilled = output();\n    _maskValue = signal('');\n    _inputValue = signal('');\n    _position = signal(null);\n    _code = signal('');\n    _maskExpressionArray = signal([]);\n    _justPasted = signal(false);\n    _isFocused = signal(false);\n    /**For IME composition event */\n    _isComposing = signal(false);\n    _maskService = inject(NgxMaskService, { self: true });\n    document = inject(DOCUMENT);\n    _config = inject(NGX_MASK_CONFIG);\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    onChange = (_) => { };\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    onTouch = () => { };\n    ngOnChanges(changes) {\n        const { mask, specialCharacters, patterns, prefix, suffix, thousandSeparator, decimalMarker, dropSpecialCharacters, hiddenInput, showMaskTyped, placeHolderCharacter, shownMaskExpression, clearIfNotMatch, validation, separatorLimit, allowNegativeNumbers, leadZeroDateTime, leadZero, triggerOnMaskChange, apm, inputTransformFn, outputTransformFn, keepCharacterPositions, instantPrefix, } = changes;\n        if (mask) {\n            if (mask.currentValue !== mask.previousValue && !mask.firstChange) {\n                this._maskService.maskChanged = true;\n            }\n            if (mask.currentValue && mask.currentValue.split(MaskExpression.OR).length > 1) {\n                this._maskExpressionArray.set(mask.currentValue.split(MaskExpression.OR).sort((a, b) => {\n                    return a.length - b.length;\n                }));\n                this._setMask();\n            }\n            else {\n                this._maskExpressionArray.set([]);\n                this._maskValue.set(mask.currentValue || MaskExpression.EMPTY_STRING);\n                this._maskService.maskExpression = this._maskValue();\n            }\n        }\n        if (specialCharacters) {\n            if (!specialCharacters.currentValue || !Array.isArray(specialCharacters.currentValue)) {\n                return;\n            }\n            else {\n                this._maskService.specialCharacters = specialCharacters.currentValue || [];\n            }\n        }\n        if (allowNegativeNumbers) {\n            this._maskService.allowNegativeNumbers = allowNegativeNumbers.currentValue;\n            if (this._maskService.allowNegativeNumbers) {\n                this._maskService.specialCharacters = this._maskService.specialCharacters.filter((c) => c !== MaskExpression.MINUS);\n            }\n        }\n        // Only overwrite the mask available patterns if a pattern has actually been passed in\n        if (patterns && patterns.currentValue) {\n            this._maskService.patterns = patterns.currentValue;\n        }\n        if (apm && apm.currentValue) {\n            this._maskService.apm = apm.currentValue;\n        }\n        if (instantPrefix) {\n            this._maskService.instantPrefix = instantPrefix.currentValue;\n        }\n        if (prefix) {\n            this._maskService.prefix = prefix.currentValue;\n        }\n        if (suffix) {\n            this._maskService.suffix = suffix.currentValue;\n        }\n        if (thousandSeparator) {\n            this._maskService.thousandSeparator = thousandSeparator.currentValue;\n            if (thousandSeparator.previousValue && thousandSeparator.currentValue) {\n                const previousDecimalMarker = this._maskService.decimalMarker;\n                if (thousandSeparator.currentValue === this._maskService.decimalMarker) {\n                    this._maskService.decimalMarker =\n                        thousandSeparator.currentValue === MaskExpression.COMMA\n                            ? MaskExpression.DOT\n                            : MaskExpression.COMMA;\n                }\n                if (this._maskService.dropSpecialCharacters === true) {\n                    this._maskService.specialCharacters = this._config.specialCharacters;\n                }\n                if (typeof previousDecimalMarker === 'string' &&\n                    typeof this._maskService.decimalMarker === 'string') {\n                    this._inputValue.set(this._inputValue()\n                        .split(thousandSeparator.previousValue)\n                        .join('')\n                        .replace(previousDecimalMarker, this._maskService.decimalMarker));\n                    this._maskService.actualValue = this._inputValue();\n                }\n                this._maskService.writingValue = true;\n            }\n        }\n        if (decimalMarker) {\n            this._maskService.decimalMarker = decimalMarker.currentValue;\n        }\n        if (dropSpecialCharacters) {\n            this._maskService.dropSpecialCharacters = dropSpecialCharacters.currentValue;\n        }\n        if (hiddenInput) {\n            this._maskService.hiddenInput = hiddenInput.currentValue;\n            if (hiddenInput.previousValue === true && hiddenInput.currentValue === false) {\n                this._inputValue.set(this._maskService.actualValue);\n            }\n        }\n        if (showMaskTyped) {\n            this._maskService.showMaskTyped = showMaskTyped.currentValue;\n            if (showMaskTyped.previousValue === false &&\n                showMaskTyped.currentValue === true &&\n                this._isFocused()) {\n                requestAnimationFrame(() => {\n                    this._maskService._elementRef?.nativeElement.click();\n                });\n            }\n        }\n        if (placeHolderCharacter) {\n            this._maskService.placeHolderCharacter = placeHolderCharacter.currentValue;\n        }\n        if (shownMaskExpression) {\n            this._maskService.shownMaskExpression = shownMaskExpression.currentValue;\n        }\n        if (clearIfNotMatch) {\n            this._maskService.clearIfNotMatch = clearIfNotMatch.currentValue;\n        }\n        if (validation) {\n            this._maskService.validation = validation.currentValue;\n        }\n        if (separatorLimit) {\n            this._maskService.separatorLimit = separatorLimit.currentValue;\n        }\n        if (leadZeroDateTime) {\n            this._maskService.leadZeroDateTime = leadZeroDateTime.currentValue;\n        }\n        if (leadZero) {\n            this._maskService.leadZero = leadZero.currentValue;\n        }\n        if (triggerOnMaskChange) {\n            this._maskService.triggerOnMaskChange = triggerOnMaskChange.currentValue;\n        }\n        if (inputTransformFn) {\n            this._maskService.inputTransformFn = inputTransformFn.currentValue;\n        }\n        if (outputTransformFn) {\n            this._maskService.outputTransformFn = outputTransformFn.currentValue;\n        }\n        if (keepCharacterPositions) {\n            this._maskService.keepCharacterPositions = keepCharacterPositions.currentValue;\n        }\n        this._applyMask();\n    }\n    validate({ value }) {\n        const processedValue = typeof value === 'number' ? String(value) : value;\n        const maskValue = this._maskValue();\n        if (!this._maskService.validation || !maskValue) {\n            return null;\n        }\n        if (this._maskService.ipError) {\n            return this._createValidationError(processedValue);\n        }\n        if (this._maskService.cpfCnpjError) {\n            return this._createValidationError(processedValue);\n        }\n        if (maskValue.startsWith(MaskExpression.SEPARATOR)) {\n            return null;\n        }\n        if (withoutValidation.includes(maskValue)) {\n            return null;\n        }\n        if (this._maskService.clearIfNotMatch) {\n            return null;\n        }\n        if (timeMasks.includes(maskValue)) {\n            return this._validateTime(processedValue);\n        }\n        if (maskValue === MaskExpression.EMAIL_MASK) {\n            const emailPattern = /^[^@]+@[^@]+\\.[^@]+$/;\n            if (!emailPattern.test(processedValue) && processedValue) {\n                return this._createValidationError(processedValue);\n            }\n            else {\n                return null;\n            }\n        }\n        if (processedValue && processedValue.length >= 1) {\n            let counterOfOpt = 0;\n            if (maskValue.includes(MaskExpression.CURLY_BRACKETS_LEFT) &&\n                maskValue.includes(MaskExpression.CURLY_BRACKETS_RIGHT)) {\n                const lengthInsideCurlyBrackets = maskValue.slice(maskValue.indexOf(MaskExpression.CURLY_BRACKETS_LEFT) + 1, maskValue.indexOf(MaskExpression.CURLY_BRACKETS_RIGHT));\n                return lengthInsideCurlyBrackets === String(processedValue.length)\n                    ? null\n                    : this._createValidationError(processedValue);\n            }\n            if (maskValue.startsWith(MaskExpression.PERCENT)) {\n                return null;\n            }\n            for (const key in this._maskService.patterns) {\n                if (this._maskService.patterns[key]?.optional) {\n                    if (maskValue.indexOf(key) !== maskValue.lastIndexOf(key)) {\n                        const opt = maskValue\n                            .split(MaskExpression.EMPTY_STRING)\n                            .filter((i) => i === key)\n                            .join(MaskExpression.EMPTY_STRING);\n                        counterOfOpt += opt.length;\n                    }\n                    else if (maskValue.indexOf(key) !== -1) {\n                        counterOfOpt++;\n                    }\n                    if (maskValue.indexOf(key) !== -1 &&\n                        processedValue.length >= maskValue.indexOf(key)) {\n                        return null;\n                    }\n                    if (counterOfOpt === maskValue.length) {\n                        return null;\n                    }\n                }\n            }\n            if ((maskValue.indexOf(MaskExpression.SYMBOL_STAR) > 1 &&\n                processedValue.length < maskValue.indexOf(MaskExpression.SYMBOL_STAR)) ||\n                (maskValue.indexOf(MaskExpression.SYMBOL_QUESTION) > 1 &&\n                    processedValue.length < maskValue.indexOf(MaskExpression.SYMBOL_QUESTION))) {\n                return this._createValidationError(processedValue);\n            }\n            if (maskValue.indexOf(MaskExpression.SYMBOL_STAR) === -1 ||\n                maskValue.indexOf(MaskExpression.SYMBOL_QUESTION) === -1) {\n                const array = maskValue.split('*');\n                const length = this._maskService.dropSpecialCharacters\n                    ? maskValue.length -\n                        this._maskService.checkDropSpecialCharAmount(maskValue) -\n                        counterOfOpt\n                    : this.prefix()\n                        ? maskValue.length + this.prefix().length - counterOfOpt\n                        : maskValue.length - counterOfOpt;\n                if (array.length === 1) {\n                    if (processedValue.length < length) {\n                        return this._createValidationError(processedValue);\n                    }\n                }\n                if (array.length > 1) {\n                    const lastIndexArray = array[array.length - 1];\n                    if (lastIndexArray &&\n                        this._maskService.specialCharacters.includes(lastIndexArray[0]) &&\n                        String(processedValue).includes(lastIndexArray[0] ?? '') &&\n                        !this.dropSpecialCharacters()) {\n                        const special = value.split(lastIndexArray[0]);\n                        return special[special.length - 1].length === lastIndexArray.length - 1\n                            ? null\n                            : this._createValidationError(processedValue);\n                    }\n                    else if (((lastIndexArray &&\n                        !this._maskService.specialCharacters.includes(lastIndexArray[0])) ||\n                        !lastIndexArray ||\n                        this._maskService.dropSpecialCharacters) &&\n                        processedValue.length >= length - 1) {\n                        return null;\n                    }\n                    else {\n                        return this._createValidationError(processedValue);\n                    }\n                }\n            }\n            if (maskValue.indexOf(MaskExpression.SYMBOL_STAR) === 1 ||\n                maskValue.indexOf(MaskExpression.SYMBOL_QUESTION) === 1) {\n                return null;\n            }\n        }\n        if (value) {\n            this.maskFilled.emit();\n            return null;\n        }\n        return null;\n    }\n    onPaste() {\n        this._justPasted.set(true);\n    }\n    onFocus() {\n        this._isFocused.set(true);\n    }\n    onModelChange(value) {\n        // on form reset we need to update the actualValue\n        if ((value === MaskExpression.EMPTY_STRING ||\n            value === null ||\n            typeof value === 'undefined') &&\n            this._maskService.actualValue) {\n            this._maskService.actualValue = this._maskService.getActualValue(MaskExpression.EMPTY_STRING);\n        }\n    }\n    onInput(e) {\n        // If IME is composing text, we wait for the composed text.\n        if (this._isComposing()) {\n            return;\n        }\n        const el = e.target;\n        const transformedValue = this._maskService.inputTransformFn\n            ? this._maskService.inputTransformFn(el.value)\n            : el.value;\n        if (el.type !== 'number') {\n            if (typeof transformedValue === 'string' || typeof transformedValue === 'number') {\n                el.value = transformedValue.toString();\n                this._inputValue.set(el.value);\n                this._setMask();\n                if (!this._maskValue()) {\n                    this.onChange(el.value);\n                    return;\n                }\n                let position = el.selectionStart === 1\n                    ? el.selectionStart + this._maskService.prefix.length\n                    : el.selectionStart;\n                if (this.showMaskTyped() &&\n                    this.keepCharacterPositions() &&\n                    this._maskService.placeHolderCharacter.length === 1) {\n                    const suffix = this.suffix();\n                    const prefix = this.prefix();\n                    const inputSymbol = el.value.slice(position - 1, position);\n                    const prefixLength = prefix.length;\n                    const checkSymbols = this._maskService._checkSymbolMask(inputSymbol, this._maskService.maskExpression[position - 1 - prefixLength] ??\n                        MaskExpression.EMPTY_STRING);\n                    const checkSpecialCharacter = this._maskService._checkSymbolMask(inputSymbol, this._maskService.maskExpression[position + 1 - prefixLength] ??\n                        MaskExpression.EMPTY_STRING);\n                    const selectRangeBackspace = this._maskService.selStart === this._maskService.selEnd;\n                    const selStart = Number(this._maskService.selStart) - prefixLength;\n                    const selEnd = Number(this._maskService.selEnd) - prefixLength;\n                    const backspaceOrDelete = this._code() === MaskExpression.BACKSPACE ||\n                        this._code() === MaskExpression.DELETE;\n                    if (backspaceOrDelete) {\n                        if (!selectRangeBackspace) {\n                            if (this._maskService.selStart === prefixLength) {\n                                this._maskService.actualValue = `${prefix}${this._maskService.maskIsShown.slice(0, selEnd)}${this._inputValue().split(prefix).join('')}`;\n                            }\n                            else if (this._maskService.selStart ===\n                                this._maskService.maskIsShown.length + prefixLength) {\n                                this._maskService.actualValue = `${this._inputValue()}${this._maskService.maskIsShown.slice(selStart, selEnd)}`;\n                            }\n                            else {\n                                this._maskService.actualValue = `${prefix}${this._inputValue()\n                                    .split(prefix)\n                                    .join('')\n                                    .slice(0, selStart)}${this._maskService.maskIsShown.slice(selStart, selEnd)}${this._maskService.actualValue.slice(selEnd + prefixLength, this._maskService.maskIsShown.length + prefixLength)}${suffix}`;\n                            }\n                        }\n                        else if (!this._maskService.specialCharacters.includes(this._maskService.maskExpression.slice(position - prefixLength, position + 1 - prefixLength)) &&\n                            selectRangeBackspace) {\n                            if (selStart === 1 && prefix) {\n                                this._maskService.actualValue = `${prefix}${this._maskService.placeHolderCharacter}${el.value\n                                    .split(prefix)\n                                    .join('')\n                                    .split(suffix)\n                                    .join('')}${suffix}`;\n                                position = position - 1;\n                            }\n                            else {\n                                const part1 = el.value.substring(0, position);\n                                const part2 = el.value.substring(position);\n                                this._maskService.actualValue = `${part1}${this._maskService.placeHolderCharacter}${part2}`;\n                            }\n                        }\n                        position = this._code() === MaskExpression.DELETE ? position + 1 : position;\n                    }\n                    if (!backspaceOrDelete) {\n                        if (!checkSymbols && !checkSpecialCharacter && selectRangeBackspace) {\n                            position = Number(el.selectionStart) - 1;\n                        }\n                        else if (this._maskService.specialCharacters.includes(el.value.slice(position, position + 1)) &&\n                            checkSpecialCharacter &&\n                            !this._maskService.specialCharacters.includes(el.value.slice(position + 1, position + 2))) {\n                            this._maskService.actualValue = `${el.value.slice(0, position - 1)}${el.value.slice(position, position + 1)}${inputSymbol}${el.value.slice(position + 2)}`;\n                            position = position + 1;\n                        }\n                        else if (checkSymbols) {\n                            if (el.value.length === 1 && position === 1) {\n                                this._maskService.actualValue = `${prefix}${inputSymbol}${this._maskService.maskIsShown.slice(1, this._maskService.maskIsShown.length)}${suffix}`;\n                            }\n                            else {\n                                this._maskService.actualValue = `${el.value.slice(0, position - 1)}${inputSymbol}${el.value\n                                    .slice(position + 1)\n                                    .split(suffix)\n                                    .join('')}${suffix}`;\n                            }\n                        }\n                        else if (prefix &&\n                            el.value.length === 1 &&\n                            position - prefixLength === 1 &&\n                            this._maskService._checkSymbolMask(el.value, this._maskService.maskExpression[position - 1 - prefixLength] ??\n                                MaskExpression.EMPTY_STRING)) {\n                            this._maskService.actualValue = `${prefix}${el.value}${this._maskService.maskIsShown.slice(1, this._maskService.maskIsShown.length)}${suffix}`;\n                        }\n                    }\n                }\n                let caretShift = 0;\n                let backspaceShift = false;\n                if (this._code() === MaskExpression.DELETE && MaskExpression.SEPARATOR) {\n                    this._maskService.deletedSpecialCharacter = true;\n                }\n                if (this._inputValue().length >= this._maskService.maskExpression.length - 1 &&\n                    this._code() !== MaskExpression.BACKSPACE &&\n                    this._maskService.maskExpression === MaskExpression.DAYS_MONTHS_YEARS &&\n                    position < 10) {\n                    const inputSymbol = this._inputValue().slice(position - 1, position);\n                    el.value =\n                        this._inputValue().slice(0, position - 1) +\n                            inputSymbol +\n                            this._inputValue().slice(position + 1);\n                }\n                if (this._maskService.maskExpression === MaskExpression.DAYS_MONTHS_YEARS &&\n                    this.leadZeroDateTime()) {\n                    if ((position < 3 && Number(el.value) > 31 && Number(el.value) < 40) ||\n                        (position === 5 && Number(el.value.slice(3, 5)) > 12)) {\n                        position = position + 2;\n                    }\n                }\n                if (this._maskService.maskExpression === MaskExpression.HOURS_MINUTES_SECONDS &&\n                    this.apm()) {\n                    if (this._justPasted() && el.value.slice(0, 2) === MaskExpression.DOUBLE_ZERO) {\n                        el.value = el.value.slice(1, 2) + el.value.slice(2, el.value.length);\n                    }\n                    el.value =\n                        el.value === MaskExpression.DOUBLE_ZERO\n                            ? MaskExpression.NUMBER_ZERO\n                            : el.value;\n                }\n                this._maskService.applyValueChanges(position, this._justPasted(), this._code() === MaskExpression.BACKSPACE ||\n                    this._code() === MaskExpression.DELETE, (shift, _backspaceShift) => {\n                    this._justPasted.set(false);\n                    caretShift = shift;\n                    backspaceShift = _backspaceShift;\n                });\n                // only set the selection if the element is active\n                if (this._getActiveElement() !== el) {\n                    return;\n                }\n                if (this._maskService.plusOnePosition) {\n                    position = position + 1;\n                    this._maskService.plusOnePosition = false;\n                }\n                // update position after applyValueChanges to prevent cursor on wrong position when it has an array of maskExpression\n                if (this._maskExpressionArray().length) {\n                    if (this._code() === MaskExpression.BACKSPACE) {\n                        const specialChartMinusOne = this.specialCharacters().includes(this._maskService.actualValue.slice(position - 1, position));\n                        const allowFewMaskChangeMask = this._maskService.removeMask(this._inputValue())?.length ===\n                            this._maskService.removeMask(this._maskService.maskExpression)?.length;\n                        const specialChartPlusOne = this.specialCharacters().includes(this._maskService.actualValue.slice(position, position + 1));\n                        if (allowFewMaskChangeMask && !specialChartPlusOne) {\n                            position = el.selectionStart + 1;\n                        }\n                        else {\n                            position = specialChartMinusOne ? position - 1 : position;\n                        }\n                    }\n                    else {\n                        position =\n                            el.selectionStart === 1\n                                ? el.selectionStart + this._maskService.prefix.length\n                                : el.selectionStart;\n                    }\n                }\n                this._position.set(this._position() === 1 && this._inputValue().length === 1\n                    ? null\n                    : this._position());\n                let positionToApply = this._position()\n                    ? this._inputValue().length + position + caretShift\n                    : position +\n                        (this._code() === MaskExpression.BACKSPACE && !backspaceShift\n                            ? 0\n                            : caretShift);\n                if (positionToApply > this._getActualInputLength()) {\n                    positionToApply =\n                        el.value === this._maskService.decimalMarker && el.value.length === 1\n                            ? this._getActualInputLength() + 1\n                            : this._getActualInputLength();\n                }\n                if (positionToApply < 0) {\n                    positionToApply = 0;\n                }\n                el.setSelectionRange(positionToApply, positionToApply);\n                this._position.set(null);\n            }\n            else {\n                // eslint-disable-next-line no-console\n                console.warn('Ngx-mask writeValue work with string | number, your current value:', typeof transformedValue);\n            }\n        }\n        else {\n            if (!this._maskValue()) {\n                this.onChange(el.value);\n                return;\n            }\n            this._maskService.applyValueChanges(el.value.length, this._justPasted(), this._code() === MaskExpression.BACKSPACE || this._code() === MaskExpression.DELETE);\n        }\n    }\n    // IME starts\n    onCompositionStart() {\n        this._isComposing.set(true);\n    }\n    // IME completes\n    onCompositionEnd(e) {\n        this._isComposing.set(false);\n        this._justPasted.set(true);\n        this.onInput(e);\n    }\n    onBlur(e) {\n        if (this._maskValue()) {\n            const el = e.target;\n            if (this._maskService.leadZero &&\n                el.value.length > 0 &&\n                typeof this._maskService.decimalMarker === 'string') {\n                const maskExpression = this._maskService.maskExpression;\n                const decimalMarker = this._maskService.decimalMarker;\n                const suffix = this._maskService.suffix;\n                const precision = Number(this._maskService.maskExpression.slice(maskExpression.length - 1, maskExpression.length));\n                if (precision > 0) {\n                    el.value = suffix ? el.value.split(suffix).join('') : el.value;\n                    const decimalPart = el.value.split(decimalMarker)[1];\n                    el.value = el.value.includes(decimalMarker)\n                        ? el.value +\n                            MaskExpression.NUMBER_ZERO.repeat(precision - decimalPart.length) +\n                            suffix\n                        : el.value +\n                            decimalMarker +\n                            MaskExpression.NUMBER_ZERO.repeat(precision) +\n                            suffix;\n                    this._maskService.actualValue = el.value;\n                }\n            }\n            this._maskService.clearIfNotMatchFn();\n        }\n        this._isFocused.set(false);\n        this.onTouch();\n    }\n    onClick(e) {\n        if (!this._maskValue()) {\n            return;\n        }\n        const el = e.target;\n        const posStart = 0;\n        const posEnd = 0;\n        if (el !== null &&\n            el.selectionStart !== null &&\n            el.selectionStart === el.selectionEnd &&\n            el.selectionStart > this._maskService.prefix.length &&\n            e.keyCode !== 38) {\n            if (this._maskService.showMaskTyped && !this.keepCharacterPositions()) {\n                // We are showing the mask in the input\n                this._maskService.maskIsShown = this._maskService.showMaskInInput();\n                if (el.setSelectionRange &&\n                    this._maskService.prefix + this._maskService.maskIsShown === el.value) {\n                    // the input ONLY contains the mask, so position the cursor at the start\n                    el.focus();\n                    el.setSelectionRange(posStart, posEnd);\n                }\n                else {\n                    // the input contains some characters already\n                    if (el.selectionStart > this._maskService.actualValue.length) {\n                        // if the user clicked beyond our value's length, position the cursor at the end of our value\n                        el.setSelectionRange(this._maskService.actualValue.length, this._maskService.actualValue.length);\n                    }\n                }\n            }\n        }\n        const nextValue = el &&\n            (el.value === this._maskService.prefix\n                ? this._maskService.prefix + this._maskService.maskIsShown\n                : el.value);\n        /** Fix of cursor position jumping to end in most browsers no matter where cursor is inserted onFocus */\n        if (el && el.value !== nextValue) {\n            el.value = nextValue;\n        }\n        /** fix of cursor position with prefix when mouse click occur */\n        if (el &&\n            el.type !== 'number' &&\n            (el.selectionStart || el.selectionEnd) <=\n                this._maskService.prefix.length) {\n            const specialCharactersAtTheStart = this._maskService.maskExpression.match(new RegExp(`^[${this._maskService.specialCharacters.map((c) => `\\\\${c}`).join('')}]+`))?.[0].length || 0;\n            el.selectionStart = this._maskService.prefix.length + specialCharactersAtTheStart;\n            return;\n        }\n        /** select only inserted text */\n        if (el && el.selectionEnd > this._getActualInputLength()) {\n            el.selectionEnd = this._getActualInputLength();\n        }\n    }\n    onKeyDown(e) {\n        if (!this._maskValue()) {\n            return;\n        }\n        if (this._isComposing()) {\n            // User finalize their choice from IME composition, so trigger onInput() for the composed text.\n            if (e.key === 'Enter') {\n                this.onCompositionEnd(e);\n            }\n            return;\n        }\n        this._code.set(e.code ? e.code : e.key);\n        const el = e.target;\n        this._inputValue.set(el.value);\n        this._setMask();\n        if (el.type !== 'number') {\n            if (e.key === MaskExpression.ARROW_UP) {\n                e.preventDefault();\n            }\n            if (e.key === MaskExpression.ARROW_LEFT ||\n                e.key === MaskExpression.BACKSPACE ||\n                e.key === MaskExpression.DELETE) {\n                if (e.key === MaskExpression.BACKSPACE && el.value.length === 0) {\n                    el.selectionStart = el.selectionEnd;\n                }\n                if (e.key === MaskExpression.BACKSPACE && el.selectionStart !== 0) {\n                    const prefixLength = this.prefix().length;\n                    // If specialChars is false, (shouldn't ever happen) then set to the defaults\n                    const specialCharacters = this.specialCharacters().length\n                        ? this.specialCharacters()\n                        : this._config.specialCharacters;\n                    if (prefixLength > 1 && el.selectionStart <= prefixLength) {\n                        el.setSelectionRange(prefixLength, el.selectionEnd);\n                    }\n                    else {\n                        if (this._inputValue().length !== el.selectionStart &&\n                            el.selectionStart !== 1) {\n                            while (specialCharacters.includes((this._inputValue()[el.selectionStart - 1] ??\n                                MaskExpression.EMPTY_STRING).toString()) &&\n                                ((prefixLength >= 1 &&\n                                    el.selectionStart > prefixLength) ||\n                                    prefixLength === 0)) {\n                                el.setSelectionRange(el.selectionStart - 1, el.selectionEnd);\n                            }\n                        }\n                    }\n                }\n                this.checkSelectionOnDeletion(el);\n                if (this._maskService.prefix.length &&\n                    el.selectionStart <= this._maskService.prefix.length &&\n                    el.selectionEnd <= this._maskService.prefix.length) {\n                    e.preventDefault();\n                }\n                const cursorStart = el.selectionStart;\n                if (e.key === MaskExpression.BACKSPACE &&\n                    !el.readOnly &&\n                    cursorStart === 0 &&\n                    el.selectionEnd === el.value.length &&\n                    el.value.length !== 0) {\n                    this._position.set(this._maskService.prefix ? this._maskService.prefix.length : 0);\n                    this._maskService.applyMask(this._maskService.prefix, this._maskService.maskExpression, this._position());\n                }\n            }\n            if (!!this.suffix() &&\n                this.suffix().length > 1 &&\n                this._inputValue().length - this.suffix().length < el.selectionStart) {\n                el.setSelectionRange(this._inputValue().length - this.suffix().length, this._inputValue().length);\n            }\n            else if ((e.code === 'KeyA' && e.ctrlKey) ||\n                (e.code === 'KeyA' && e.metaKey) // Cmd + A (Mac)\n            ) {\n                el.setSelectionRange(0, this._getActualInputLength());\n                e.preventDefault();\n            }\n            this._maskService.selStart = el.selectionStart;\n            this._maskService.selEnd = el.selectionEnd;\n        }\n    }\n    /** It writes the value in the input */\n    async writeValue(controlValue) {\n        let value = controlValue;\n        const inputTransformFn = this._maskService.inputTransformFn;\n        if (typeof value === 'object' && value !== null && 'value' in value) {\n            if ('disable' in value) {\n                this.setDisabledState(Boolean(value.disable));\n            }\n            value = value.value;\n        }\n        if (value !== null) {\n            value = inputTransformFn ? inputTransformFn(value) : value;\n        }\n        if (typeof value === 'string' ||\n            typeof value === 'number' ||\n            value === null ||\n            typeof value === 'undefined') {\n            if (value === null || typeof value === 'undefined' || value === '') {\n                this._maskService.currentValue = '';\n                this._maskService.previousValue = '';\n            }\n            let inputValue = value;\n            if (typeof inputValue === 'number' ||\n                this._maskValue().startsWith(MaskExpression.SEPARATOR)) {\n                inputValue = String(inputValue);\n                const localeDecimalMarker = this._maskService.currentLocaleDecimalMarker();\n                if (!Array.isArray(this._maskService.decimalMarker)) {\n                    inputValue =\n                        this._maskService.decimalMarker !== localeDecimalMarker\n                            ? inputValue.replace(localeDecimalMarker, this._maskService.decimalMarker)\n                            : inputValue;\n                }\n                if (this._maskService.leadZero &&\n                    inputValue &&\n                    this.mask() &&\n                    this.dropSpecialCharacters() !== false) {\n                    inputValue = this._maskService._checkPrecision(this._maskService.maskExpression, inputValue);\n                }\n                if (this._maskService.decimalMarker === MaskExpression.COMMA ||\n                    (Array.isArray(this._maskService.decimalMarker) &&\n                        this._maskService.thousandSeparator === MaskExpression.DOT)) {\n                    inputValue = inputValue\n                        .toString()\n                        .replace(MaskExpression.DOT, MaskExpression.COMMA);\n                }\n                if (this.mask()?.startsWith(MaskExpression.SEPARATOR) && this.leadZero()) {\n                    requestAnimationFrame(() => {\n                        this._maskService.applyMask(inputValue?.toString() ?? '', this._maskService.maskExpression);\n                    });\n                }\n                this._maskService.isNumberValue = true;\n            }\n            if (typeof inputValue !== 'string' || value === null || typeof value === 'undefined') {\n                inputValue = '';\n            }\n            this._inputValue.set(inputValue);\n            this._setMask();\n            if ((inputValue && this._maskService.maskExpression) ||\n                (this._maskService.maskExpression &&\n                    (this._maskService.prefix || this._maskService.showMaskTyped))) {\n                // Let the service we know we are writing value so that triggering onChange function won't happen during applyMask\n                this._maskService.writingValue = true;\n                this._maskService.formElementProperty = [\n                    'value',\n                    this._maskService.applyMask(inputValue, this._maskService.maskExpression),\n                ];\n                // Let the service know we've finished writing value\n                this._maskService.writingValue = false;\n            }\n            else {\n                this._maskService.formElementProperty = ['value', inputValue];\n            }\n            this._inputValue.set(inputValue);\n        }\n        else {\n            // eslint-disable-next-line no-console\n            console.warn('Ngx-mask writeValue work with string | number, your current value:', typeof value);\n        }\n    }\n    registerOnChange(fn) {\n        this._maskService.onChange = this.onChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onTouch = fn;\n    }\n    _getActiveElement(document = this.document) {\n        const shadowRootEl = document?.activeElement?.shadowRoot;\n        if (!shadowRootEl?.activeElement) {\n            return document.activeElement;\n        }\n        else {\n            return this._getActiveElement(shadowRootEl);\n        }\n    }\n    checkSelectionOnDeletion(el) {\n        const prefixLength = this.prefix().length;\n        const suffixLength = this.suffix().length;\n        const inputValueLength = this._inputValue().length;\n        el.selectionStart = Math.min(Math.max(prefixLength, el.selectionStart), inputValueLength - suffixLength);\n        el.selectionEnd = Math.min(Math.max(prefixLength, el.selectionEnd), inputValueLength - suffixLength);\n    }\n    /** It disables the input element */\n    setDisabledState(isDisabled) {\n        this._maskService.formElementProperty = ['disabled', isDisabled];\n    }\n    _applyMask() {\n        this._maskService.maskExpression = this._maskService._repeatPatternSymbols(this._maskValue() || '');\n        this._maskService.formElementProperty = [\n            'value',\n            this._maskService.applyMask(this._inputValue(), this._maskService.maskExpression),\n        ];\n    }\n    _validateTime(value) {\n        const rowMaskLen = this._maskValue()\n            .split(MaskExpression.EMPTY_STRING)\n            .filter((s) => s !== ':').length;\n        if (!value) {\n            return null; // Don't validate empty values to allow for optional form control\n        }\n        if ((+(value[value.length - 1] ?? -1) === 0 && value.length < rowMaskLen) ||\n            value.length <= rowMaskLen - 2) {\n            return this._createValidationError(value);\n        }\n        return null;\n    }\n    _getActualInputLength() {\n        return (this._maskService.actualValue.length ||\n            this._maskService.actualValue.length + this._maskService.prefix.length);\n    }\n    _createValidationError(actualValue) {\n        return {\n            mask: {\n                requiredMask: this._maskValue(),\n                actualValue,\n            },\n        };\n    }\n    _setMask() {\n        this._maskExpressionArray().some((mask) => {\n            const specialChart = mask\n                .split(MaskExpression.EMPTY_STRING)\n                .some((char) => this._maskService.specialCharacters.includes(char));\n            if ((specialChart &&\n                this._inputValue() &&\n                this._areAllCharactersInEachStringSame(this._maskExpressionArray())) ||\n                mask.includes(MaskExpression.CURLY_BRACKETS_LEFT)) {\n                const test = this._maskService.removeMask(this._inputValue())?.length <=\n                    this._maskService.removeMask(mask)?.length;\n                if (test) {\n                    const maskValue = mask.includes(MaskExpression.CURLY_BRACKETS_LEFT)\n                        ? this._maskService._repeatPatternSymbols(mask)\n                        : mask;\n                    this._maskValue.set(maskValue);\n                    this._maskService.maskExpression = maskValue;\n                    return test;\n                }\n                else {\n                    const expression = this._maskExpressionArray()[this._maskExpressionArray().length - 1] ??\n                        MaskExpression.EMPTY_STRING;\n                    const maskValue = expression.includes(MaskExpression.CURLY_BRACKETS_LEFT)\n                        ? this._maskService._repeatPatternSymbols(expression)\n                        : expression;\n                    this._maskValue.set(maskValue);\n                    this._maskService.maskExpression = maskValue;\n                }\n            }\n            else {\n                const cleanMask = this._maskService.removeMask(mask);\n                const check = this._maskService\n                    .removeMask(this._inputValue())\n                    ?.split(MaskExpression.EMPTY_STRING)\n                    .every((character, index) => {\n                    const indexMask = cleanMask.charAt(index);\n                    return this._maskService._checkSymbolMask(character, indexMask);\n                });\n                if (check || this._justPasted()) {\n                    this._maskValue.set(mask);\n                    this._maskService.maskExpression = mask;\n                    return check;\n                }\n            }\n        });\n    }\n    _areAllCharactersInEachStringSame(array) {\n        const specialCharacters = this._maskService.specialCharacters;\n        function removeSpecialCharacters(str) {\n            const regex = new RegExp(`[${specialCharacters.map((ch) => `\\\\${ch}`).join('')}]`, 'g');\n            return str.replace(regex, '');\n        }\n        const processedArr = array.map(removeSpecialCharacters);\n        return processedArr.every((str) => {\n            const uniqueCharacters = new Set(str);\n            return uniqueCharacters.size === 1;\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: NgxMaskDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"17.1.0\", version: \"19.2.9\", type: NgxMaskDirective, isStandalone: true, selector: \"input[mask], textarea[mask]\", inputs: { mask: { classPropertyName: \"mask\", publicName: \"mask\", isSignal: true, isRequired: false, transformFunction: null }, specialCharacters: { classPropertyName: \"specialCharacters\", publicName: \"specialCharacters\", isSignal: true, isRequired: false, transformFunction: null }, patterns: { classPropertyName: \"patterns\", publicName: \"patterns\", isSignal: true, isRequired: false, transformFunction: null }, prefix: { classPropertyName: \"prefix\", publicName: \"prefix\", isSignal: true, isRequired: false, transformFunction: null }, suffix: { classPropertyName: \"suffix\", publicName: \"suffix\", isSignal: true, isRequired: false, transformFunction: null }, thousandSeparator: { classPropertyName: \"thousandSeparator\", publicName: \"thousandSeparator\", isSignal: true, isRequired: false, transformFunction: null }, decimalMarker: { classPropertyName: \"decimalMarker\", publicName: \"decimalMarker\", isSignal: true, isRequired: false, transformFunction: null }, dropSpecialCharacters: { classPropertyName: \"dropSpecialCharacters\", publicName: \"dropSpecialCharacters\", isSignal: true, isRequired: false, transformFunction: null }, hiddenInput: { classPropertyName: \"hiddenInput\", publicName: \"hiddenInput\", isSignal: true, isRequired: false, transformFunction: null }, showMaskTyped: { classPropertyName: \"showMaskTyped\", publicName: \"showMaskTyped\", isSignal: true, isRequired: false, transformFunction: null }, placeHolderCharacter: { classPropertyName: \"placeHolderCharacter\", publicName: \"placeHolderCharacter\", isSignal: true, isRequired: false, transformFunction: null }, shownMaskExpression: { classPropertyName: \"shownMaskExpression\", publicName: \"shownMaskExpression\", isSignal: true, isRequired: false, transformFunction: null }, clearIfNotMatch: { classPropertyName: \"clearIfNotMatch\", publicName: \"clearIfNotMatch\", isSignal: true, isRequired: false, transformFunction: null }, validation: { classPropertyName: \"validation\", publicName: \"validation\", isSignal: true, isRequired: false, transformFunction: null }, separatorLimit: { classPropertyName: \"separatorLimit\", publicName: \"separatorLimit\", isSignal: true, isRequired: false, transformFunction: null }, allowNegativeNumbers: { classPropertyName: \"allowNegativeNumbers\", publicName: \"allowNegativeNumbers\", isSignal: true, isRequired: false, transformFunction: null }, leadZeroDateTime: { classPropertyName: \"leadZeroDateTime\", publicName: \"leadZeroDateTime\", isSignal: true, isRequired: false, transformFunction: null }, leadZero: { classPropertyName: \"leadZero\", publicName: \"leadZero\", isSignal: true, isRequired: false, transformFunction: null }, triggerOnMaskChange: { classPropertyName: \"triggerOnMaskChange\", publicName: \"triggerOnMaskChange\", isSignal: true, isRequired: false, transformFunction: null }, apm: { classPropertyName: \"apm\", publicName: \"apm\", isSignal: true, isRequired: false, transformFunction: null }, inputTransformFn: { classPropertyName: \"inputTransformFn\", publicName: \"inputTransformFn\", isSignal: true, isRequired: false, transformFunction: null }, outputTransformFn: { classPropertyName: \"outputTransformFn\", publicName: \"outputTransformFn\", isSignal: true, isRequired: false, transformFunction: null }, keepCharacterPositions: { classPropertyName: \"keepCharacterPositions\", publicName: \"keepCharacterPositions\", isSignal: true, isRequired: false, transformFunction: null }, instantPrefix: { classPropertyName: \"instantPrefix\", publicName: \"instantPrefix\", isSignal: true, isRequired: false, transformFunction: null } }, outputs: { maskFilled: \"maskFilled\" }, host: { listeners: { \"paste\": \"onPaste()\", \"focus\": \"onFocus($event)\", \"ngModelChange\": \"onModelChange($event)\", \"input\": \"onInput($event)\", \"compositionstart\": \"onCompositionStart($event)\", \"compositionend\": \"onCompositionEnd($event)\", \"blur\": \"onBlur($event)\", \"click\": \"onClick($event)\", \"keydown\": \"onKeyDown($event)\" } }, providers: [\n            {\n                provide: NG_VALUE_ACCESSOR,\n                useExisting: NgxMaskDirective,\n                multi: true,\n            },\n            {\n                provide: NG_VALIDATORS,\n                useExisting: NgxMaskDirective,\n                multi: true,\n            },\n            NgxMaskService,\n        ], exportAs: [\"mask\", \"ngxMask\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: NgxMaskDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'input[mask], textarea[mask]',\n                    standalone: true,\n                    providers: [\n                        {\n                            provide: NG_VALUE_ACCESSOR,\n                            useExisting: NgxMaskDirective,\n                            multi: true,\n                        },\n                        {\n                            provide: NG_VALIDATORS,\n                            useExisting: NgxMaskDirective,\n                            multi: true,\n                        },\n                        NgxMaskService,\n                    ],\n                    exportAs: 'mask,ngxMask',\n                }]\n        }], propDecorators: { onPaste: [{\n                type: HostListener,\n                args: ['paste']\n            }], onFocus: [{\n                type: HostListener,\n                args: ['focus', ['$event']]\n            }], onModelChange: [{\n                type: HostListener,\n                args: ['ngModelChange', ['$event']]\n            }], onInput: [{\n                type: HostListener,\n                args: ['input', ['$event']]\n            }], onCompositionStart: [{\n                type: HostListener,\n                args: ['compositionstart', ['$event']]\n            }], onCompositionEnd: [{\n                type: HostListener,\n                args: ['compositionend', ['$event']]\n            }], onBlur: [{\n                type: HostListener,\n                args: ['blur', ['$event']]\n            }], onClick: [{\n                type: HostListener,\n                args: ['click', ['$event']]\n            }], onKeyDown: [{\n                type: HostListener,\n                args: ['keydown', ['$event']]\n            }] } });\n\nclass NgxMaskPipe {\n    defaultOptions = inject(NGX_MASK_CONFIG);\n    _maskService = inject(NgxMaskService);\n    _maskExpressionArray = [];\n    mask = '';\n    transform(value, mask, { patterns, ...config } = {}) {\n        let processedValue = value;\n        const currentConfig = {\n            maskExpression: mask,\n            ...this.defaultOptions,\n            ...config,\n            patterns: {\n                ...this._maskService.patterns,\n                ...patterns,\n            },\n        };\n        Object.entries(currentConfig).forEach(([key, val]) => {\n            this._maskService[key] = val;\n        });\n        if (mask.includes('||')) {\n            const maskParts = mask.split('||');\n            if (maskParts.length > 1) {\n                this._maskExpressionArray = maskParts.sort((a, b) => a.length - b.length);\n                this._setMask(`${processedValue}`);\n                return this._maskService.applyMask(`${processedValue}`, this.mask);\n            }\n            else {\n                this._maskExpressionArray = [];\n                return this._maskService.applyMask(`${processedValue}`, this.mask);\n            }\n        }\n        if (mask.includes(MaskExpression.CURLY_BRACKETS_LEFT)) {\n            return this._maskService.applyMask(`${processedValue}`, this._maskService._repeatPatternSymbols(mask));\n        }\n        if (mask.startsWith(MaskExpression.SEPARATOR)) {\n            if (config.decimalMarker) {\n                this._maskService.decimalMarker = config.decimalMarker;\n            }\n            if (config.thousandSeparator) {\n                this._maskService.thousandSeparator = config.thousandSeparator;\n            }\n            if (config.leadZero) {\n                this._maskService.leadZero = config.leadZero;\n            }\n            processedValue = String(processedValue);\n            const localeDecimalMarker = this._maskService.currentLocaleDecimalMarker();\n            if (!Array.isArray(this._maskService.decimalMarker)) {\n                processedValue =\n                    this._maskService.decimalMarker !== localeDecimalMarker\n                        ? processedValue.replace(localeDecimalMarker, this._maskService.decimalMarker)\n                        : processedValue;\n            }\n            if (this._maskService.leadZero &&\n                processedValue &&\n                this._maskService.dropSpecialCharacters !== false) {\n                processedValue = this._maskService._checkPrecision(mask, processedValue);\n            }\n            if (this._maskService.decimalMarker === MaskExpression.COMMA) {\n                processedValue = processedValue.replace(MaskExpression.DOT, MaskExpression.COMMA);\n            }\n            this._maskService.isNumberValue = true;\n        }\n        if (processedValue === null || typeof processedValue === 'undefined') {\n            return this._maskService.applyMask('', mask);\n        }\n        return this._maskService.applyMask(`${processedValue}`, mask);\n    }\n    _setMask(value) {\n        if (this._maskExpressionArray.length > 0) {\n            this._maskExpressionArray.some((mask) => {\n                const test = this._maskService.removeMask(value)?.length <=\n                    this._maskService.removeMask(mask)?.length;\n                if (value && test) {\n                    this.mask = mask;\n                    return test;\n                }\n                else {\n                    this.mask =\n                        this._maskExpressionArray[this._maskExpressionArray.length - 1] ??\n                            MaskExpression.EMPTY_STRING;\n                }\n            });\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: NgxMaskPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe });\n    static ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"19.2.9\", ngImport: i0, type: NgxMaskPipe, isStandalone: true, name: \"mask\" });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: NgxMaskPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'mask',\n                    pure: true,\n                    standalone: true,\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INITIAL_CONFIG, NEW_CONFIG, NGX_MASK_CONFIG, NgxMaskDirective, NgxMaskPipe, NgxMaskService, initialConfig, provideEnvironmentNgxMask, provideNgxMask, timeMasks, withoutValidation };\n"], "mappings": ";AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,YAAY,EAAEC,MAAM,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAEC,wBAAwB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAEC,IAAI,QAAQ,eAAe;AACvL,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,iBAAiB,EAAEC,aAAa,QAAQ,gBAAgB;AAEjE,IAAIC,cAAc;AAClB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAAC,WAAW,CAAC,GAAG,WAAW;EACzCA,cAAc,CAAC,SAAS,CAAC,GAAG,SAAS;EACrCA,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;EAC3BA,cAAc,CAAC,UAAU,CAAC,GAAG,UAAU;EACvCA,cAAc,CAAC,OAAO,CAAC,GAAG,GAAG;EAC7BA,cAAc,CAAC,QAAQ,CAAC,GAAG,IAAI;EAC/BA,cAAc,CAAC,QAAQ,CAAC,GAAG,GAAG;EAC9BA,cAAc,CAAC,MAAM,CAAC,GAAG,GAAG;EAC5BA,cAAc,CAAC,OAAO,CAAC,GAAG,GAAG;EAC7BA,cAAc,CAAC,SAAS,CAAC,GAAG,IAAI;EAChCA,cAAc,CAAC,YAAY,CAAC,GAAG,IAAI;EACnCA,cAAc,CAAC,SAAS,CAAC,GAAG,IAAI;EAChCA,cAAc,CAAC,uBAAuB,CAAC,GAAG,UAAU;EACpDA,cAAc,CAAC,YAAY,CAAC,GAAG,UAAU;EACzCA,cAAc,CAAC,eAAe,CAAC,GAAG,OAAO;EACzCA,cAAc,CAAC,iBAAiB,CAAC,GAAG,OAAO;EAC3CA,cAAc,CAAC,mBAAmB,CAAC,GAAG,YAAY;EAClDA,cAAc,CAAC,aAAa,CAAC,GAAG,OAAO;EACvCA,cAAc,CAAC,MAAM,CAAC,GAAG,IAAI;EAC7BA,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG;EAC3BA,cAAc,CAAC,QAAQ,CAAC,GAAG,GAAG;EAC9BA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG;EAChCA,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG;EAC3BA,cAAc,CAAC,OAAO,CAAC,GAAG,GAAG;EAC7BA,cAAc,CAAC,qBAAqB,CAAC,GAAG,GAAG;EAC3CA,cAAc,CAAC,sBAAsB,CAAC,GAAG,GAAG;EAC5CA,cAAc,CAAC,OAAO,CAAC,GAAG,GAAG;EAC7BA,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;EAC3BA,cAAc,CAAC,MAAM,CAAC,GAAG,GAAG;EAC5BA,cAAc,CAAC,cAAc,CAAC,GAAG,EAAE;EACnCA,cAAc,CAAC,aAAa,CAAC,GAAG,GAAG;EACnCA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG;EACvCA,cAAc,CAAC,OAAO,CAAC,GAAG,GAAG;EAC7BA,cAAc,CAAC,aAAa,CAAC,GAAG,GAAG;EACnCA,cAAc,CAAC,aAAa,CAAC,GAAG,GAAG;EACnCA,cAAc,CAAC,aAAa,CAAC,GAAG,GAAG;EACnCA,cAAc,CAAC,WAAW,CAAC,GAAG,WAAW;EACzCA,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACnCA,cAAc,CAAC,YAAY,CAAC,GAAG,WAAW;EAC1CA,cAAc,CAAC,UAAU,CAAC,GAAG,SAAS;EACtCA,cAAc,CAAC,aAAa,CAAC,GAAG,IAAI;AACxC,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAE3C,MAAMC,eAAe,GAAG,IAAIjB,cAAc,CAAC,iBAAiB,CAAC;AAC7D,MAAMkB,UAAU,GAAG,IAAIlB,cAAc,CAAC,qBAAqB,CAAC;AAC5D,MAAMmB,cAAc,GAAG,IAAInB,cAAc,CAAC,yBAAyB,CAAC;AACpE,MAAMoB,aAAa,GAAG;EAClBC,MAAM,EAAE,EAAE;EACVC,MAAM,EAAE,EAAE;EACVC,iBAAiB,EAAE,GAAG;EACtBC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EACzBC,eAAe,EAAE,KAAK;EACtBC,aAAa,EAAE,KAAK;EACpBC,aAAa,EAAE,KAAK;EACpBC,oBAAoB,EAAE,GAAG;EACzBC,qBAAqB,EAAE,IAAI;EAC3BC,WAAW,EAAE,KAAK;EAClBC,mBAAmB,EAAE,EAAE;EACvBC,cAAc,EAAE,EAAE;EAClBC,oBAAoB,EAAE,KAAK;EAC3BC,UAAU,EAAE,IAAI;EAChBC,iBAAiB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACzFC,gBAAgB,EAAE,KAAK;EACvBC,GAAG,EAAE,KAAK;EACVC,QAAQ,EAAE,KAAK;EACfC,sBAAsB,EAAE,KAAK;EAC7BC,mBAAmB,EAAE,KAAK;EAC1BC,gBAAgB,EAAGC,KAAK,IAAKA,KAAK;EAClCC,iBAAiB,EAAGD,KAAK,IAAKA,KAAK;EACnCE,UAAU,EAAE,IAAI3C,YAAY,CAAC,CAAC;EAC9B4C,QAAQ,EAAE;IACN,GAAG,EAAE;MACDC,OAAO,EAAE,IAAIC,MAAM,CAAC,KAAK;IAC7B,CAAC;IACD,GAAG,EAAE;MACDD,OAAO,EAAE,IAAIC,MAAM,CAAC,KAAK,CAAC;MAC1BC,QAAQ,EAAE;IACd,CAAC;IACDC,CAAC,EAAE;MACCH,OAAO,EAAE,IAAIC,MAAM,CAAC,KAAK,CAAC;MAC1BG,MAAM,EAAE;IACZ,CAAC;IACDC,CAAC,EAAE;MACCL,OAAO,EAAE,IAAIC,MAAM,CAAC,aAAa;IACrC,CAAC;IACDK,CAAC,EAAE;MACCN,OAAO,EAAE,IAAIC,MAAM,CAAC,UAAU;IAClC,CAAC;IACDM,CAAC,EAAE;MACCP,OAAO,EAAE,IAAIC,MAAM,CAAC,OAAO;IAC/B,CAAC;IACDO,CAAC,EAAE;MACCR,OAAO,EAAE,IAAIC,MAAM,CAAC,OAAO;IAC/B,CAAC;IACDQ,CAAC,EAAE;MACCT,OAAO,EAAE,IAAIC,MAAM,CAAC,KAAK;IAC7B,CAAC;IACDS,CAAC,EAAE;MACCV,OAAO,EAAE,IAAIC,MAAM,CAAC,KAAK;IAC7B,CAAC;IACDU,CAAC,EAAE;MACCX,OAAO,EAAE,IAAIC,MAAM,CAAC,KAAK;IAC7B,CAAC;IACDW,CAAC,EAAE;MACCZ,OAAO,EAAE,IAAIC,MAAM,CAAC,KAAK;IAC7B,CAAC;IACDY,CAAC,EAAE;MACCb,OAAO,EAAE,IAAIC,MAAM,CAAC,KAAK;IAC7B,CAAC;IACDa,CAAC,EAAE;MACCd,OAAO,EAAE,IAAIC,MAAM,CAAC,KAAK;IAC7B;EACJ;AACJ,CAAC;AACD,MAAMc,SAAS,GAAG,CACd7C,cAAc,CAAC8C,qBAAqB,EACpC9C,cAAc,CAAC+C,aAAa,EAC5B/C,cAAc,CAACgD,eAAe,CACjC;AACD,MAAMC,iBAAiB,GAAG,CACtBjD,cAAc,CAACkD,OAAO,EACtBlD,cAAc,CAACmD,UAAU,EACzBnD,cAAc,CAACoD,OAAO,EACtBpD,cAAc,CAACqD,OAAO,EACtBrD,cAAc,CAACsD,SAAS,EACxBtD,cAAc,CAACuD,iBAAiB,EAChCvD,cAAc,CAACwD,WAAW,EAC1BxD,cAAc,CAACyD,IAAI,EACnBzD,cAAc,CAAC0D,MAAM,CACxB;AAED,MAAMC,qBAAqB,CAAC;EACxBC,OAAO,GAAG1E,MAAM,CAACe,eAAe,CAAC;EACjCY,qBAAqB,GAAG,IAAI,CAAC+C,OAAO,CAAC/C,qBAAqB;EAC1DC,WAAW,GAAG,IAAI,CAAC8C,OAAO,CAAC9C,WAAW;EACtCL,eAAe,GAAG,IAAI,CAACmD,OAAO,CAACnD,eAAe;EAC9CU,iBAAiB,GAAG,IAAI,CAACyC,OAAO,CAACzC,iBAAiB;EAClDU,QAAQ,GAAG,IAAI,CAAC+B,OAAO,CAAC/B,QAAQ;EAChCvB,MAAM,GAAG,IAAI,CAACsD,OAAO,CAACtD,MAAM;EAC5BD,MAAM,GAAG,IAAI,CAACuD,OAAO,CAACvD,MAAM;EAC5BE,iBAAiB,GAAG,IAAI,CAACqD,OAAO,CAACrD,iBAAiB;EAClDC,aAAa,GAAG,IAAI,CAACoD,OAAO,CAACpD,aAAa;EAC1CqD,aAAa;EACbnD,aAAa,GAAG,IAAI,CAACkD,OAAO,CAAClD,aAAa;EAC1CE,oBAAoB,GAAG,IAAI,CAACgD,OAAO,CAAChD,oBAAoB;EACxDM,UAAU,GAAG,IAAI,CAAC0C,OAAO,CAAC1C,UAAU;EACpCF,cAAc,GAAG,IAAI,CAAC4C,OAAO,CAAC5C,cAAc;EAC5CC,oBAAoB,GAAG,IAAI,CAAC2C,OAAO,CAAC3C,oBAAoB;EACxDG,gBAAgB,GAAG,IAAI,CAACwC,OAAO,CAACxC,gBAAgB;EAChDE,QAAQ,GAAG,IAAI,CAACsC,OAAO,CAACtC,QAAQ;EAChCD,GAAG,GAAG,IAAI,CAACuC,OAAO,CAACvC,GAAG;EACtBI,gBAAgB,GAAG,IAAI,CAACmC,OAAO,CAACnC,gBAAgB;EAChDE,iBAAiB,GAAG,IAAI,CAACiC,OAAO,CAACjC,iBAAiB;EAClDJ,sBAAsB,GAAG,IAAI,CAACqC,OAAO,CAACrC,sBAAsB;EAC5DZ,aAAa,GAAG,IAAI,CAACiD,OAAO,CAACjD,aAAa;EAC1Ca,mBAAmB,GAAG,IAAI,CAACoC,OAAO,CAACpC,mBAAmB;EACtDsC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;EAClBC,eAAe,GAAG,KAAK;EACvBC,cAAc,GAAG,EAAE;EACnBC,WAAW,GAAG,EAAE;EAChBC,oBAAoB,GAAG,EAAE;EACzBpD,mBAAmB,GAAG,IAAI,CAAC6C,OAAO,CAAC7C,mBAAmB;EACtDqD,uBAAuB,GAAG,KAAK;EAC/BC,OAAO;EACPC,YAAY;EACZC,SAASA,CAACC,UAAU,EAAEP,cAAc,EAAEQ,QAAQ,GAAG,CAAC,EAAEC,UAAU,GAAG,KAAK,EAAEC,UAAU,GAAG,KAAK;EAC1F;EACAC,EAAE,GAAGA,CAAA,KAAM,CAAE,CAAC,EAAE;IACZ,IAAI,CAACX,cAAc,IAAI,OAAOO,UAAU,KAAK,QAAQ,EAAE;MACnD,OAAOxE,cAAc,CAAC6E,YAAY;IACtC;IACA,IAAIC,MAAM,GAAG,CAAC;IACd,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIC,KAAK,GAAG,KAAK;IACjB,IAAIC,cAAc,GAAG,KAAK;IAC1B,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,cAAc,GAAGZ,UAAU;IAC/B,IAAIa,iBAAiB,GAAGZ,QAAQ;IAChC,IAAIW,cAAc,CAACE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAChF,MAAM,CAACiF,MAAM,CAAC,KAAK,IAAI,CAACjF,MAAM,EAAE;MAC7D8E,cAAc,GAAGA,cAAc,CAACE,KAAK,CAAC,IAAI,CAAChF,MAAM,CAACiF,MAAM,EAAEH,cAAc,CAACG,MAAM,CAAC;IACpF;IACA,IAAI,CAAC,CAAC,IAAI,CAAClF,MAAM,IAAI+E,cAAc,CAACG,MAAM,GAAG,CAAC,EAAE;MAC5CH,cAAc,GAAG,IAAI,CAACI,oBAAoB,CAACJ,cAAc,CAAC;IAC9D;IACA,IAAIA,cAAc,KAAK,GAAG,IAAI,IAAI,CAAC9E,MAAM,EAAE;MACvC8E,cAAc,GAAG,EAAE;IACvB;IACA,MAAMK,UAAU,GAAGL,cAAc,CAACM,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC3F,cAAc,CAAC6E,YAAY,CAAC;IAC/E,IAAI,IAAI,CAAC5D,oBAAoB,IACzBmE,cAAc,CAACE,KAAK,CAACR,MAAM,EAAEA,MAAM,GAAG,CAAC,CAAC,KAAK9E,cAAc,CAAC4F,KAAK,EAAE;MACnEb,MAAM,IAAIK,cAAc,CAACE,KAAK,CAACR,MAAM,EAAEA,MAAM,GAAG,CAAC,CAAC;IACtD;IACA,IAAIb,cAAc,KAAKjE,cAAc,CAAC6F,EAAE,EAAE;MACtC,MAAMC,QAAQ,GAAGV,cAAc,CAACO,KAAK,CAAC3F,cAAc,CAAC+F,GAAG,CAAC;MACzD,IAAI,CAAC1B,OAAO,GAAG,IAAI,CAAC2B,QAAQ,CAACF,QAAQ,CAAC;MACtC;MACA7B,cAAc,GAAG,iBAAiB;IACtC;IACA,MAAMgC,GAAG,GAAG,EAAE;IACd;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,cAAc,CAACG,MAAM,EAAEW,CAAC,EAAE,EAAE;MAC5C,IAAId,cAAc,CAACc,CAAC,CAAC,EAAEC,KAAK,CAAC,KAAK,CAAC,EAAE;QACjCF,GAAG,CAACG,IAAI,CAAChB,cAAc,CAACc,CAAC,CAAC,IAAIlG,cAAc,CAAC6E,YAAY,CAAC;MAC9D;IACJ;IACA,IAAIZ,cAAc,KAAKjE,cAAc,CAACqG,QAAQ,EAAE;MAC5C,IAAI,CAAC/B,YAAY,GAAG2B,GAAG,CAACV,MAAM,KAAK,EAAE,IAAIU,GAAG,CAACV,MAAM,KAAK,EAAE;MAC1D,IAAIU,GAAG,CAACV,MAAM,GAAG,EAAE,EAAE;QACjB;QACAtB,cAAc,GAAG,oBAAoB;MACzC,CAAC,MACI;QACD;QACAA,cAAc,GAAG,gBAAgB;MACrC;IACJ;IACA,IAAIA,cAAc,CAACqC,UAAU,CAACtG,cAAc,CAACkD,OAAO,CAAC,EAAE;MACnD,IAAIkC,cAAc,CAACe,KAAK,CAAC,aAAa,CAAC;MACnC;MACCf,cAAc,CAACe,KAAK,CAAC,oCAAoC,CAAC,IAAI,CAACxB,UAAW,EAAE;QAC7ES,cAAc,GAAG,IAAI,CAACmB,eAAe,CAACnB,cAAc,CAAC;QACrD,MAAMoB,SAAS,GAAG,IAAI,CAACC,YAAY,CAACxC,cAAc,CAAC;QACnDmB,cAAc,GAAG,IAAI,CAACsB,mBAAmB,CAACtB,cAAc,EAAEoB,SAAS,EAAE,IAAI,CAAChG,aAAa,CAAC;MAC5F;MACA,MAAMA,aAAa,GAAG,OAAO,IAAI,CAACA,aAAa,KAAK,QAAQ,GAAG,IAAI,CAACA,aAAa,GAAGR,cAAc,CAAC+F,GAAG;MACtG,IAAIX,cAAc,CAACuB,OAAO,CAACnG,aAAa,CAAC,GAAG,CAAC,IACzC,CAAC,IAAI,CAACoG,UAAU,CAACxB,cAAc,CAACyB,SAAS,CAAC,CAAC,EAAEzB,cAAc,CAACuB,OAAO,CAACnG,aAAa,CAAC,CAAC,CAAC,EAAE;QACtF,IAAIsG,IAAI,GAAG1B,cAAc,CAACyB,SAAS,CAAC,CAAC,EAAEzB,cAAc,CAACuB,OAAO,CAACnG,aAAa,CAAC,GAAG,CAAC,CAAC;QACjF,IAAI,IAAI,CAACS,oBAAoB,IACzBmE,cAAc,CAACE,KAAK,CAACR,MAAM,EAAEA,MAAM,GAAG,CAAC,CAAC,KAAK9E,cAAc,CAAC4F,KAAK,IACjE,CAACjB,UAAU,EAAE;UACbmC,IAAI,GAAG1B,cAAc,CAACyB,SAAS,CAAC,CAAC,EAAEzB,cAAc,CAACuB,OAAO,CAACnG,aAAa,CAAC,CAAC;QAC7E;QACA4E,cAAc,GAAI,GAAE0B,IAAK,GAAE1B,cAAc,CAACyB,SAAS,CAACzB,cAAc,CAACuB,OAAO,CAACnG,aAAa,CAAC,EAAE4E,cAAc,CAACG,MAAM,CAAE,EAAC;MACvH;MACA,IAAI7D,KAAK,GAAG,EAAE;MACd;MACA,IAAI,CAACT,oBAAoB,IACrBmE,cAAc,CAACE,KAAK,CAACR,MAAM,EAAEA,MAAM,GAAG,CAAC,CAAC,KAAK9E,cAAc,CAAC4F,KAAK,GAC9DlE,KAAK,GAAI,GAAE1B,cAAc,CAAC4F,KAAM,GAAER,cAAc,CAACE,KAAK,CAACR,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGM,cAAc,CAACG,MAAM,CAAE,EAAC,GACpG7D,KAAK,GAAG0D,cAAe;MAC9B,IAAI,IAAI,CAACwB,UAAU,CAAClF,KAAK,CAAC,EAAE;QACxBqD,MAAM,GAAG,IAAI,CAACgC,iBAAiB,CAAC3B,cAAc,CAAC;MACnD,CAAC,MACI;QACDL,MAAM,GAAG,IAAI,CAACgC,iBAAiB,CAAC3B,cAAc,CAACyB,SAAS,CAAC,CAAC,EAAEzB,cAAc,CAACG,MAAM,GAAG,CAAC,CAAC,CAAC;MAC3F;IACJ,CAAC,MACI,IAAItB,cAAc,CAACqC,UAAU,CAACtG,cAAc,CAACsD,SAAS,CAAC,EAAE;MAC1D,IAAI8B,cAAc,CAACe,KAAK,CAAC,WAAW,CAAC,IACjCf,cAAc,CAACe,KAAK,CAAC,SAAS,CAAC,IAC/Bf,cAAc,CAACe,KAAK,CAAC,aAAa,CAAC,IACnCf,cAAc,CAACe,KAAK,CAAC,sCAAsC,CAAC,IAC5Df,cAAc,CAACe,KAAK,CAAC,eAAe,CAAC,EAAE;QACvCf,cAAc,GAAG,IAAI,CAACmB,eAAe,CAACnB,cAAc,CAAC;MACzD;MACA,MAAMoB,SAAS,GAAG,IAAI,CAACC,YAAY,CAACxC,cAAc,CAAC;MACnD,IAAIzD,aAAa,GAAG,IAAI,CAACA,aAAa;MACtC,IAAIwG,KAAK,CAACC,OAAO,CAAC,IAAI,CAACzG,aAAa,CAAC,EAAE;QACnC,IAAI,IAAI,CAAC0D,WAAW,CAACgD,QAAQ,CAAC,IAAI,CAAC1G,aAAa,CAAC,CAAC,CAAC,CAAC,IAChD,IAAI,CAAC0D,WAAW,CAACgD,QAAQ,CAAC,IAAI,CAAC1G,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;UAClDA,aAAa,GAAG,IAAI,CAAC0D,WAAW,CAACgD,QAAQ,CAAC,IAAI,CAAC1G,aAAa,CAAC,CAAC,CAAC,CAAC,GAC1D,IAAI,CAACA,aAAa,CAAC,CAAC,CAAC,GACrB,IAAI,CAACA,aAAa,CAAC,CAAC,CAAC;QAC/B,CAAC,MACI;UACDA,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC2G,IAAI,CAAEC,EAAE,IAAKA,EAAE,KAAK,IAAI,CAAC7G,iBAAiB,CAAC;QAClF;MACJ;MACA,IAAIoE,UAAU,EAAE;QACZ,MAAM;UAAE0C,kBAAkB;UAAEC;QAAa,CAAC,GAAG,IAAI,CAACC,gCAAgC,CAACnC,cAAc,EAAE5E,aAAa,CAAC;QACjH,MAAMgH,cAAc,GAAGpC,cAAc,CAAC,CAAC,CAAC,KAAKpF,cAAc,CAAC4F,KAAK;QACjE,MAAM6B,mBAAmB,GAAGrC,cAAc,CAAC,CAAC,CAAC,KAAKpF,cAAc,CAAC0H,WAAW;QAC5E,MAAMC,sBAAsB,GAAGvC,cAAc,CAAC,CAAC,CAAC,KAAK5E,aAAa;QAClE,MAAMoH,uBAAuB,GAAGxC,cAAc,CAAC,CAAC,CAAC,KAAK5E,aAAa;QACnE,IAAKmH,sBAAsB,IAAI,CAACL,YAAY,IACvCE,cAAc,IAAII,uBAAuB,IAAI,CAACN,YAAa,IAC3DG,mBAAmB,IAAI,CAACJ,kBAAkB,IAAI,CAACC,YAAa,EAAE;UAC/DlC,cAAc,GAAGpF,cAAc,CAAC0H,WAAW;QAC/C;QACA,IAAIL,kBAAkB,IAClBC,YAAY,IACZE,cAAc,IACdnC,iBAAiB,KAAK,CAAC,EAAE;UACzB,IAAIgC,kBAAkB,GAAGC,YAAY,IAAID,kBAAkB,GAAGC,YAAY,EAAE;YACxElC,cAAc,GAAGpF,cAAc,CAAC4F,KAAK,GAAGR,cAAc,CAACE,KAAK,CAACgC,YAAY,CAAC;UAC9E;QACJ;QACA,IAAI,CAACD,kBAAkB,IAAIC,YAAY,IAAIlC,cAAc,CAACG,MAAM,GAAG+B,YAAY,EAAE;UAC7ElC,cAAc,GAAGoC,cAAc,GACzBxH,cAAc,CAAC4F,KAAK,GAAGR,cAAc,CAACE,KAAK,CAACgC,YAAY,CAAC,GACzDlC,cAAc,CAACE,KAAK,CAACgC,YAAY,CAAC;QAC5C;QACA,IAAID,kBAAkB,IAAIC,YAAY,IAAIjC,iBAAiB,KAAK,CAAC,EAAE;UAC/D,IAAIgC,kBAAkB,GAAGC,YAAY,EAAE;YACnClC,cAAc,GAAGA,cAAc,CAACE,KAAK,CAAC+B,kBAAkB,GAAG,CAAC,CAAC;UACjE;UACA,IAAIA,kBAAkB,GAAGC,YAAY,EAAE;YACnClC,cAAc,GAAGA,cAAc,CAACE,KAAK,CAACgC,YAAY,CAAC;UACvD;QACJ;MACJ;MACA,IAAId,SAAS,KAAK,CAAC,EAAE;QACjBpB,cAAc,GAAG,IAAI,CAACnE,oBAAoB,GACpCmE,cAAc,CAACG,MAAM,GAAG,CAAC,IACvBH,cAAc,CAAC,CAAC,CAAC,KAAKpF,cAAc,CAAC4F,KAAK,IAC1CR,cAAc,CAAC,CAAC,CAAC,KAAKpF,cAAc,CAAC0H,WAAW,IAChDtC,cAAc,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC7E,iBAAiB,IAC5C6E,cAAc,CAAC,CAAC,CAAC,KAAKpF,cAAc,CAAC6H,KAAK,IAC1CzC,cAAc,CAAC,CAAC,CAAC,KAAKpF,cAAc,CAAC+F,GAAG,GACtC,GAAG,GAAGX,cAAc,CAACE,KAAK,CAAC,CAAC,EAAEF,cAAc,CAACG,MAAM,CAAC,GACpDH,cAAc,CAAC,CAAC,CAAC,KAAKpF,cAAc,CAAC0H,WAAW,IAC9CtC,cAAc,CAACG,MAAM,GAAG,CAAC,IACzBH,cAAc,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC7E,iBAAiB,IAC5C6E,cAAc,CAAC,CAAC,CAAC,KAAKpF,cAAc,CAAC6H,KAAK,IAC1CzC,cAAc,CAAC,CAAC,CAAC,KAAKpF,cAAc,CAAC+F,GAAG,GACtCX,cAAc,CAACE,KAAK,CAAC,CAAC,EAAEF,cAAc,CAACG,MAAM,CAAC,GAC9CH,cAAc,GACtBA,cAAc,CAACG,MAAM,GAAG,CAAC,IACvBH,cAAc,CAAC,CAAC,CAAC,KAAKpF,cAAc,CAAC0H,WAAW,IAChDtC,cAAc,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC7E,iBAAiB,IAC5C6E,cAAc,CAAC,CAAC,CAAC,KAAKpF,cAAc,CAAC6H,KAAK,IAC1CzC,cAAc,CAAC,CAAC,CAAC,KAAKpF,cAAc,CAAC+F,GAAG,GACtCX,cAAc,CAACE,KAAK,CAAC,CAAC,EAAEF,cAAc,CAACG,MAAM,CAAC,GAC9CH,cAAc;MAC5B,CAAC,MACI;QACD,IAAIA,cAAc,CAAC,CAAC,CAAC,KAAK5E,aAAa,IACnC4E,cAAc,CAACG,MAAM,GAAG,CAAC,IACzB,CAACZ,UAAU,EAAE;UACbS,cAAc,GACVpF,cAAc,CAAC0H,WAAW,GACtBtC,cAAc,CAACE,KAAK,CAAC,CAAC,EAAEF,cAAc,CAACG,MAAM,GAAG,CAAC,CAAC;UAC1D,IAAI,CAACvB,eAAe,GAAG,IAAI;QAC/B;QACA,IAAIoB,cAAc,CAAC,CAAC,CAAC,KAAKpF,cAAc,CAAC0H,WAAW,IAChDtC,cAAc,CAAC,CAAC,CAAC,KAAK5E,aAAa,IACnC4E,cAAc,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC7E,iBAAiB,IAC5C,CAACoE,UAAU,EAAE;UACbS,cAAc,GACVA,cAAc,CAACG,MAAM,GAAG,CAAC,GACnBH,cAAc,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GACxB9E,aAAa,GACb4E,cAAc,CAACE,KAAK,CAAC,CAAC,EAAEF,cAAc,CAACG,MAAM,GAAG,CAAC,CAAC,GACpDH,cAAc;UACxB,IAAI,CAACpB,eAAe,GAAG,IAAI;QAC/B;QACA,IAAI,IAAI,CAAC/C,oBAAoB,IACzB,CAAC0D,UAAU,IACXS,cAAc,CAAC,CAAC,CAAC,KAAKpF,cAAc,CAAC4F,KAAK,KACzCR,cAAc,CAAC,CAAC,CAAC,KAAK5E,aAAa,IAChC4E,cAAc,CAAC,CAAC,CAAC,KAAKpF,cAAc,CAAC0H,WAAW,CAAC,EAAE;UACvDtC,cAAc,GACVA,cAAc,CAAC,CAAC,CAAC,KAAK5E,aAAa,IAAI4E,cAAc,CAACG,MAAM,GAAG,CAAC,GAC1DH,cAAc,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GACxBtF,cAAc,CAAC0H,WAAW,GAC1BtC,cAAc,CAACE,KAAK,CAAC,CAAC,EAAEF,cAAc,CAACG,MAAM,CAAC,GAChDH,cAAc,CAAC,CAAC,CAAC,KAAKpF,cAAc,CAAC0H,WAAW,IAC9CtC,cAAc,CAACG,MAAM,GAAG,CAAC,IACzBH,cAAc,CAAC,CAAC,CAAC,KAAK5E,aAAa,GACjC4E,cAAc,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GACxB9E,aAAa,GACb4E,cAAc,CAACE,KAAK,CAAC,CAAC,EAAEF,cAAc,CAACG,MAAM,CAAC,GAChDH,cAAc;UAC5B,IAAI,CAACpB,eAAe,GAAG,IAAI;QAC/B;MACJ;MACA;MACA;MACA,MAAM8D,4BAA4B,GAAG,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAACxH,iBAAiB,CAAC;MACzF,IAAIyH,YAAY,GAAG,0CAA0C,CAACC,OAAO,CAACH,4BAA4B,EAAE,EAAE,CAAC;MACvG;MACA,IAAId,KAAK,CAACC,OAAO,CAAC,IAAI,CAACzG,aAAa,CAAC,EAAE;QACnC,KAAK,MAAM0H,MAAM,IAAI,IAAI,CAAC1H,aAAa,EAAE;UACrCwH,YAAY,GAAGA,YAAY,CAACC,OAAO,CAAC,IAAI,CAACF,uBAAuB,CAACG,MAAM,CAAC,EAAElI,cAAc,CAAC6E,YAAY,CAAC;QAC1G;MACJ,CAAC,MACI;QACDmD,YAAY,GAAGA,YAAY,CAACC,OAAO,CAAC,IAAI,CAACF,uBAAuB,CAAC,IAAI,CAACvH,aAAa,CAAC,EAAE,EAAE,CAAC;MAC7F;MACA,MAAM2H,iBAAiB,GAAG,IAAIpG,MAAM,CAAC,GAAG,GAAGiG,YAAY,GAAG,GAAG,CAAC;MAC9D,IAAI5C,cAAc,CAACe,KAAK,CAACgC,iBAAiB,CAAC,EAAE;QACzC/C,cAAc,GAAGA,cAAc,CAACyB,SAAS,CAAC,CAAC,EAAEzB,cAAc,CAACG,MAAM,GAAG,CAAC,CAAC;MAC3E;MACAH,cAAc,GAAG,IAAI,CAACsB,mBAAmB,CAACtB,cAAc,EAAEoB,SAAS,EAAE,IAAI,CAAChG,aAAa,CAAC;MACxF,MAAM4H,SAAS,GAAGhD,cAAc,CAAC6C,OAAO,CAAC,IAAIlG,MAAM,CAAC+F,4BAA4B,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC;MAC3F/C,MAAM,GAAG,IAAI,CAACsD,qBAAqB,CAACD,SAAS,EAAE,IAAI,CAAC7H,iBAAiB,EAAE,IAAI,CAACC,aAAa,EAAEgG,SAAS,CAAC;MACrG,MAAM8B,UAAU,GAAGvD,MAAM,CAAC4B,OAAO,CAAC3G,cAAc,CAAC6H,KAAK,CAAC,GAAGzC,cAAc,CAACuB,OAAO,CAAC3G,cAAc,CAAC6H,KAAK,CAAC;MACtG,MAAMU,SAAS,GAAGxD,MAAM,CAACQ,MAAM,GAAGH,cAAc,CAACG,MAAM;MACvD,MAAMiD,yCAAyC,GAAG7D,UAAU,IAAII,MAAM,CAACQ,MAAM,GAAGf,UAAU,CAACe,MAAM,IAAI,IAAI,CAACvE,cAAc;MACxH,IAAI,CAAC+D,MAAM,CAACM,iBAAiB,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC9E,iBAAiB,IACzDwE,MAAM,CAACM,iBAAiB,GAAG,IAAI,CAAC/E,MAAM,CAACiF,MAAM,CAAC,KAC9C,IAAI,CAACjF,MAAM,IACXqE,UAAU,EAAE;QACZU,iBAAiB,GAAGA,iBAAiB,GAAG,CAAC;MAC7C,CAAC,MACI,IAAKkD,SAAS,GAAG,CAAC,IAAIxD,MAAM,CAACM,iBAAiB,CAAC,KAAK,IAAI,CAAC9E,iBAAiB,IAC3EiI,yCAAyC,EAAE;QAC3CvD,cAAc,GAAG,IAAI;QACrB,IAAInB,MAAM,GAAG,CAAC;QACd,GAAG;UACC,IAAI,CAACA,MAAM,CAAC2E,GAAG,CAACpD,iBAAiB,GAAGvB,MAAM,CAAC;UAC3CA,MAAM,EAAE;QACZ,CAAC,QAAQA,MAAM,GAAGyE,SAAS;MAC/B,CAAC,MACI,IAAIxD,MAAM,CAACM,iBAAiB,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC9E,iBAAiB,IAC7DgI,SAAS,KAAK,CAAC,CAAC,IAChBA,SAAS,KAAK,CAAC,CAAC,IAChBxD,MAAM,CAACM,iBAAiB,CAAC,KAAK,IAAI,CAAC9E,iBAAiB,EAAE;QACtD,IAAI,CAACuD,MAAM,CAAC4E,KAAK,CAAC,CAAC;QACnB,IAAI,CAAC5E,MAAM,CAAC2E,GAAG,CAACpD,iBAAiB,GAAG,CAAC,CAAC;MAC1C,CAAC,MACI,IAAKiD,UAAU,KAAK,CAAC,IACtBjD,iBAAiB,GAAG,CAAC,IACrB,EAAEN,MAAM,CAAC4B,OAAO,CAAC3G,cAAc,CAAC6H,KAAK,CAAC,IAAIxC,iBAAiB,IACvDA,iBAAiB,GAAG,CAAC,CAAC,IACzB,EAAEN,MAAM,CAAC4B,OAAO,CAAC3G,cAAc,CAAC+F,GAAG,CAAC,IAAIV,iBAAiB,IAAIA,iBAAiB,GAAG,CAAC,CAAC,IAChFkD,SAAS,IAAI,CAAE,EAAE;QACrB,IAAI,CAACzE,MAAM,CAAC4E,KAAK,CAAC,CAAC;QACnBzD,cAAc,GAAG,IAAI;QACrBC,KAAK,GAAGqD,SAAS;QACjBlD,iBAAiB,IAAIkD,SAAS;QAC9B,IAAI,CAACzE,MAAM,CAAC2E,GAAG,CAACpD,iBAAiB,CAAC;MACtC,CAAC,MACI;QACD,IAAI,CAACvB,MAAM,CAAC4E,KAAK,CAAC,CAAC;MACvB;IACJ,CAAC,MACI;MACD;MACA;MACA,IAAIxC,CAAC,GAAG,CAAC,EAAEyC,WAAW,GAAGlD,UAAU,CAAC,CAAC,CAAC,EAAES,CAAC,GAAGT,UAAU,CAACF,MAAM,EAAEW,CAAC,EAAE,EAAEyC,WAAW,GAAGlD,UAAU,CAACS,CAAC,CAAC,IAAIlG,cAAc,CAAC6E,YAAY,EAAE;QAC5H,IAAIC,MAAM,KAAKb,cAAc,CAACsB,MAAM,EAAE;UAClC;QACJ;QACA,MAAMqD,mBAAmB,IAAG5I,cAAc,CAAC6I,WAAW,IAAI,IAAI,CAAChH,QAAQ;QACvE,IAAI,IAAI,CAACiH,gBAAgB,CAACH,WAAW,EAAE1E,cAAc,CAACa,MAAM,CAAC,IAAI9E,cAAc,CAAC6E,YAAY,CAAC,IACzFZ,cAAc,CAACa,MAAM,GAAG,CAAC,CAAC,KAAK9E,cAAc,CAAC+I,eAAe,EAAE;UAC/DhE,MAAM,IAAI4D,WAAW;UACrB7D,MAAM,IAAI,CAAC;QACf,CAAC,MACI,IAAIb,cAAc,CAACa,MAAM,GAAG,CAAC,CAAC,KAAK9E,cAAc,CAAC6I,WAAW,IAC9D7D,KAAK,IACL,IAAI,CAAC8D,gBAAgB,CAACH,WAAW,EAAE1E,cAAc,CAACa,MAAM,GAAG,CAAC,CAAC,IAAI9E,cAAc,CAAC6E,YAAY,CAAC,EAAE;UAC/FE,MAAM,IAAI4D,WAAW;UACrB7D,MAAM,IAAI,CAAC;UACXE,KAAK,GAAG,KAAK;QACjB,CAAC,MACI,IAAI,IAAI,CAAC8D,gBAAgB,CAACH,WAAW,EAAE1E,cAAc,CAACa,MAAM,CAAC,IAAI9E,cAAc,CAAC6E,YAAY,CAAC,IAC9FZ,cAAc,CAACa,MAAM,GAAG,CAAC,CAAC,KAAK9E,cAAc,CAAC6I,WAAW,IACzD,CAACD,mBAAmB,EAAE;UACtB7D,MAAM,IAAI4D,WAAW;UACrB3D,KAAK,GAAG,IAAI;QAChB,CAAC,MACI,IAAIf,cAAc,CAACa,MAAM,GAAG,CAAC,CAAC,KAAK9E,cAAc,CAAC+I,eAAe,IAClE,IAAI,CAACD,gBAAgB,CAACH,WAAW,EAAE1E,cAAc,CAACa,MAAM,GAAG,CAAC,CAAC,IAAI9E,cAAc,CAAC6E,YAAY,CAAC,EAAE;UAC/FE,MAAM,IAAI4D,WAAW;UACrB7D,MAAM,IAAI,CAAC;QACf,CAAC,MACI,IAAI,IAAI,CAACgE,gBAAgB,CAACH,WAAW,EAAE1E,cAAc,CAACa,MAAM,CAAC,IAAI9E,cAAc,CAAC6E,YAAY,CAAC,EAAE;UAChG,IAAIZ,cAAc,CAACa,MAAM,CAAC,KAAK9E,cAAc,CAACgJ,KAAK,EAAE;YACjD,IAAI,IAAI,CAAC3H,GAAG,GAAG4H,MAAM,CAACN,WAAW,CAAC,GAAG,CAAC,GAAGM,MAAM,CAACN,WAAW,CAAC,GAAG,CAAC,EAAE;cAC9DtD,iBAAiB,GAAG,CAAC,IAAI,CAACjE,gBAAgB,GACpCiE,iBAAiB,GAAG,CAAC,GACrBA,iBAAiB;cACvBP,MAAM,IAAI,CAAC;cACX,IAAI,CAACoE,UAAU,CAACpE,MAAM,CAAC;cACvBoB,CAAC,EAAE;cACH,IAAI,IAAI,CAAC9E,gBAAgB,EAAE;gBACvB2D,MAAM,IAAI,GAAG;cACjB;cACA;YACJ;UACJ;UACA,IAAId,cAAc,CAACa,MAAM,CAAC,KAAK9E,cAAc,CAACmJ,IAAI,EAAE;YAChD,IAAI,IAAI,CAAC9H,GAAG,GACL0D,MAAM,CAACQ,MAAM,KAAK,CAAC,IAAI0D,MAAM,CAAClE,MAAM,CAAC,GAAG,CAAC,IACvCA,MAAM,KAAK,GAAG,IAAIkE,MAAM,CAACN,WAAW,CAAC,GAAG,CAAE,IAC1CvD,cAAc,CAACE,KAAK,CAACR,MAAM,GAAG,CAAC,EAAEA,MAAM,CAAC,CAACS,MAAM,KAAK,CAAC,IAClD0D,MAAM,CAAC7D,cAAc,CAACE,KAAK,CAACR,MAAM,GAAG,CAAC,EAAEA,MAAM,CAAC,CAAC,GAAG,CAAE,IACxDM,cAAc,CAACE,KAAK,CAACR,MAAM,GAAG,CAAC,EAAEA,MAAM,CAAC,KAAK,GAAG,IAC7CmE,MAAM,CAACN,WAAW,CAAC,GAAG,CAAE,GAC7B5D,MAAM,KAAK,GAAG,IAAIkE,MAAM,CAACN,WAAW,CAAC,GAAG,CAAC,IACvC,CAAC5D,MAAM,CAACO,KAAK,CAACR,MAAM,GAAG,CAAC,EAAEA,MAAM,CAAC,KAAK,GAAG,IACtCC,MAAM,CAACO,KAAK,CAACR,MAAM,GAAG,CAAC,EAAEA,MAAM,CAAC,KAAK,GAAG,IACxCC,MAAM,CAACO,KAAK,CAACR,MAAM,GAAG,CAAC,EAAEA,MAAM,CAAC,KAAK,GAAG,IACxCC,MAAM,CAACO,KAAK,CAACR,MAAM,GAAG,CAAC,EAAEA,MAAM,CAAC,KAAK,GAAG,KACxCmE,MAAM,CAACN,WAAW,CAAC,GAAG,CAAC,IACvB7D,MAAM,GAAG,EAAG,EAAE;cACtBO,iBAAiB,GAAGA,iBAAiB,GAAG,CAAC;cACzCP,MAAM,IAAI,CAAC;cACXoB,CAAC,EAAE;cACH;YACJ;UACJ;UACA,IAAIjC,cAAc,CAACa,MAAM,CAAC,KAAK9E,cAAc,CAACoJ,MAAM,IAChDnF,cAAc,CAACa,MAAM,CAAC,KAAK9E,cAAc,CAACqJ,MAAM,EAAE;YAClD,IAAIJ,MAAM,CAACN,WAAW,CAAC,GAAG,CAAC,EAAE;cACzBtD,iBAAiB,GAAG,CAAC,IAAI,CAACjE,gBAAgB,GACpCiE,iBAAiB,GAAG,CAAC,GACrBA,iBAAiB;cACvBP,MAAM,IAAI,CAAC;cACX,IAAI,CAACoE,UAAU,CAACpE,MAAM,CAAC;cACvBoB,CAAC,EAAE;cACH,IAAI,IAAI,CAAC9E,gBAAgB,EAAE;gBACvB2D,MAAM,IAAI,GAAG;cACjB;cACA;YACJ;UACJ;UACA,MAAMuE,SAAS,GAAG,EAAE;UACpB,MAAMC,gBAAgB,GAAGnE,cAAc,CAACN,MAAM,CAAC;UAC/C,MAAM0E,uBAAuB,GAAGpE,cAAc,CAACN,MAAM,GAAG,CAAC,CAAC;UAC1D,MAAM2E,uBAAuB,GAAGrE,cAAc,CAACN,MAAM,GAAG,CAAC,CAAC;UAC1D,MAAM4E,wBAAwB,GAAGtE,cAAc,CAACN,MAAM,GAAG,CAAC,CAAC;UAC3D,MAAM6E,wBAAwB,GAAGvE,cAAc,CAACN,MAAM,GAAG,CAAC,CAAC;UAC3D,MAAM8E,iCAAiC,GAAGxE,cAAc,CAACE,KAAK,CAACR,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG,CAAC,CAAC;UACtF,MAAM+E,8BAA8B,GAAGzE,cAAc,CAACE,KAAK,CAACR,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG,CAAC,CAAC;UACnF,MAAMgF,4BAA4B,GAAG1E,cAAc,CAACE,KAAK,CAACR,MAAM,EAAEA,MAAM,GAAG,CAAC,CAAC;UAC7E,MAAMiF,6BAA6B,GAAG3E,cAAc,CAACE,KAAK,CAACR,MAAM,GAAG,CAAC,EAAEA,MAAM,CAAC;UAC9E,IAAIb,cAAc,CAACa,MAAM,CAAC,KAAK9E,cAAc,CAACgK,GAAG,EAAE;YAC/C,MAAMC,kBAAkB,GAAGhG,cAAc,CAACqB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKtF,cAAc,CAAC0D,MAAM;YAC/E,MAAMwG,mBAAmB,GAAGjG,cAAc,CAACqB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKtF,cAAc,CAAC0D,MAAM,IAC5E,IAAI,CAACvC,iBAAiB,CAAC+F,QAAQ,CAACyC,wBAAwB,CAAC;YAC7D,IAAKV,MAAM,CAACN,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAACvH,gBAAgB,IAChD,CAAC6I,kBAAkB,KACfhB,MAAM,CAACa,4BAA4B,CAAC,GAAGR,SAAS,IAC7CL,MAAM,CAACY,8BAA8B,CAAC,GAAGP,SAAS,IAClD,IAAI,CAACnI,iBAAiB,CAAC+F,QAAQ,CAACsC,uBAAuB,CAAC,CAAE,KACjEU,mBAAmB,GACdjB,MAAM,CAACY,8BAA8B,CAAC,GAAGP,SAAS,IAC/C,CAAC,IAAI,CAACnI,iBAAiB,CAAC+F,QAAQ,CAACqC,gBAAgB,CAAC,IAC/C,IAAI,CAACpI,iBAAiB,CAAC+F,QAAQ,CAACuC,uBAAuB,CAAE,IAC7D,IAAI,CAACtI,iBAAiB,CAAC+F,QAAQ,CAACqC,gBAAgB,CAAC,GACnDN,MAAM,CAACa,4BAA4B,CAAC,GAAGR,SAAS,IAC7C,IAAI,CAACnI,iBAAiB,CAAC+F,QAAQ,CAACsC,uBAAuB,CAAC,IACrD,CAAC7E,UAAW,CAAC,EAAE;cAC3BU,iBAAiB,GAAG,CAAC,IAAI,CAACjE,gBAAgB,GACpCiE,iBAAiB,GAAG,CAAC,GACrBA,iBAAiB;cACvBP,MAAM,IAAI,CAAC;cACX,IAAI,CAACoE,UAAU,CAACpE,MAAM,CAAC;cACvBoB,CAAC,EAAE;cACH,IAAI,IAAI,CAAC9E,gBAAgB,EAAE;gBACvB2D,MAAM,IAAI,GAAG;cACjB;cACA;YACJ;UACJ;UACA,IAAId,cAAc,CAACa,MAAM,CAAC,KAAK9E,cAAc,CAACmK,KAAK,EAAE;YACjD,MAAMC,WAAW,GAAG,EAAE;YACtB;YACA,MAAMC,WAAW,GAAGvF,MAAM,KAAK,CAAC,KAC3BmE,MAAM,CAACN,WAAW,CAAC,GAAG,CAAC,IACpBM,MAAM,CAACa,4BAA4B,CAAC,GAAGM,WAAW,IACjD,IAAI,CAACjJ,iBAAiB,CAAC+F,QAAQ,CAACsC,uBAAuB,CAAC,IACrD,CAAC7E,UAAW,CAAC;YACzB;YACA,MAAM2F,YAAY,GAAGrG,cAAc,CAACqB,KAAK,CAACR,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG,CAAC,CAAC;YACjE,MAAMyF,cAAc,GAAGX,iCAAiC,CAAC1C,QAAQ,CAACoD,YAAY,CAAC,IAC3ErG,cAAc,CAACiD,QAAQ,CAAC,IAAI,CAAC,KAC3B,IAAI,CAAC/F,iBAAiB,CAAC+F,QAAQ,CAACyC,wBAAwB,CAAC,IACvDV,MAAM,CAACY,8BAA8B,CAAC,GAAGO,WAAW,IACpD,CAAC,IAAI,CAACjJ,iBAAiB,CAAC+F,QAAQ,CAACqC,gBAAgB,CAAC,IAClD,IAAI,CAACpI,iBAAiB,CAAC+F,QAAQ,CAACqC,gBAAgB,CAAC,CAAC;YAC1D;YACA,MAAMiB,cAAc,GAAGvB,MAAM,CAACW,iCAAiC,CAAC,IAAIN,SAAS,IACzE,CAAC,IAAI,CAACnI,iBAAiB,CAAC+F,QAAQ,CAAC0C,iCAAiC,CAAC,IACnE,IAAI,CAACzI,iBAAiB,CAAC+F,QAAQ,CAACwC,wBAAwB,CAAC,KACxDT,MAAM,CAACa,4BAA4B,CAAC,GAAGM,WAAW,IAC/C,IAAI,CAACjJ,iBAAiB,CAAC+F,QAAQ,CAACsC,uBAAuB,CAAC,CAAC;YACjE;YACA,MAAMiB,iBAAiB,GAAIxB,MAAM,CAACa,4BAA4B,CAAC,GAAGM,WAAW,IAAItF,MAAM,KAAK,CAAC,IACxF,IAAI,CAAC3D,iBAAiB,CAAC+F,QAAQ,CAACsC,uBAAuB,CAAC,IACrD1E,MAAM,KAAK,CAAE;YACrB;YACA,MAAM4F,cAAc,GAAGzB,MAAM,CAACW,iCAAiC,CAAC,GAAGN,SAAS,IACxE,CAAC,IAAI,CAACnI,iBAAiB,CAAC+F,QAAQ,CAAC0C,iCAAiC,CAAC,IACnE,CAAC,IAAI,CAACzI,iBAAiB,CAAC+F,QAAQ,CAAC6C,6BAA6B,CAAC,IAC/Dd,MAAM,CAACc,6BAA6B,CAAC,GAAGK,WAAW,IACnDnG,cAAc,CAACiD,QAAQ,CAAC,IAAI,CAAC;YACjC;YACA,MAAMyD,cAAc,GAAG1B,MAAM,CAACW,iCAAiC,CAAC,IAAIN,SAAS,IACzE,CAAC,IAAI,CAACnI,iBAAiB,CAAC+F,QAAQ,CAAC0C,iCAAiC,CAAC,IACnE,CAAC,IAAI,CAACzI,iBAAiB,CAAC+F,QAAQ,CAACwC,wBAAwB,CAAC,IAC1DT,MAAM,CAACY,8BAA8B,CAAC,GAAGO,WAAW;YACxD,IAAKnB,MAAM,CAACN,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAACvH,gBAAgB,IACjDiJ,WAAW,IACXE,cAAc,IACdI,cAAc,IACdD,cAAc,IACdF,cAAc,IACbC,iBAAiB,IAAI,CAAC,IAAI,CAACrJ,gBAAiB,EAAE;cAC/CiE,iBAAiB,GAAG,CAAC,IAAI,CAACjE,gBAAgB,GACpCiE,iBAAiB,GAAG,CAAC,GACrBA,iBAAiB;cACvBP,MAAM,IAAI,CAAC;cACX,IAAI,CAACoE,UAAU,CAACpE,MAAM,CAAC;cACvBoB,CAAC,EAAE;cACH,IAAI,IAAI,CAAC9E,gBAAgB,EAAE;gBACvB2D,MAAM,IAAI,GAAG;cACjB;cACA;YACJ;UACJ;UACAA,MAAM,IAAI4D,WAAW;UACrB7D,MAAM,EAAE;QACZ,CAAC,MACI,IAAI,IAAI,CAAC3D,iBAAiB,CAAC+F,QAAQ,CAACyB,WAAW,CAAC,IACjD1E,cAAc,CAACa,MAAM,CAAC,KAAK6D,WAAW,EAAE;UACxC5D,MAAM,IAAI4D,WAAW;UACrB7D,MAAM,EAAE;QACZ,CAAC,MACI,IAAI,IAAI,CAAC3D,iBAAiB,CAACwF,OAAO,CAAC1C,cAAc,CAACa,MAAM,CAAC,IAAI9E,cAAc,CAAC6E,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;UACnGE,MAAM,IAAId,cAAc,CAACa,MAAM,CAAC;UAChCA,MAAM,EAAE;UACR,IAAI,CAACoE,UAAU,CAACpE,MAAM,CAAC;UACvBoB,CAAC,EAAE;QACP,CAAC,MACI,IAAIjC,cAAc,CAACa,MAAM,CAAC,KAAK9E,cAAc,CAAC4K,WAAW,IAC1D,IAAI,CAAClK,aAAa,EAAE;UACpB,IAAI,CAACwI,UAAU,CAACpE,MAAM,CAAC;QAC3B,CAAC,MACI,IAAI,IAAI,CAACjD,QAAQ,CAACoC,cAAc,CAACa,MAAM,CAAC,IAAI9E,cAAc,CAAC6E,YAAY,CAAC,IACzE,IAAI,CAAChD,QAAQ,CAACoC,cAAc,CAACa,MAAM,CAAC,IAAI9E,cAAc,CAAC6E,YAAY,CAAC,EAAE7C,QAAQ,EAAE;UAChF,IAAI,CAAC,CAACyD,UAAU,CAACX,MAAM,CAAC,IACpBb,cAAc,KAAK,iBAAiB,IACpCA,cAAc,KAAK,gBAAgB,IACnCA,cAAc,KAAK,oBAAoB,IACvC,CAACA,cAAc,CAACkC,KAAK,CAAC,UAAU,CAAC,IACjC,CAAC,IAAI,CAACtE,QAAQ,CAACoC,cAAc,CAACa,MAAM,CAAC,IAAI9E,cAAc,CAAC6E,YAAY,CAAC,EAC/D7C,QAAQ,EAAE;YAChB+C,MAAM,IAAIU,UAAU,CAACX,MAAM,CAAC;UAChC;UACA,IAAIb,cAAc,CAACiD,QAAQ,CAAClH,cAAc,CAAC4K,WAAW,GAAG5K,cAAc,CAAC6I,WAAW,CAAC,IAChF5E,cAAc,CAACiD,QAAQ,CAAClH,cAAc,CAAC0H,WAAW,GAAG1H,cAAc,CAAC6I,WAAW,CAAC,EAAE;YAClF/D,MAAM,EAAE;UACZ;UACAA,MAAM,EAAE;UACRoB,CAAC,EAAE;QACP,CAAC,MACI,IAAI,IAAI,CAACjC,cAAc,CAACa,MAAM,GAAG,CAAC,CAAC,KAAK9E,cAAc,CAAC6I,WAAW,IACnE,IAAI,CAACgC,gBAAgB,CAAC,IAAI,CAAC5G,cAAc,CAACa,MAAM,GAAG,CAAC,CAAC,IAAI9E,cAAc,CAAC6E,YAAY,CAAC,IACrF,IAAI,CAACgG,gBAAgB,CAAClC,WAAW,CAAC,KAAK,IAAI,CAAC1E,cAAc,CAACa,MAAM,GAAG,CAAC,CAAC,IACtEE,KAAK,EAAE;UACPF,MAAM,IAAI,CAAC;UACXC,MAAM,IAAI4D,WAAW;QACzB,CAAC,MACI,IAAI,IAAI,CAAC1E,cAAc,CAACa,MAAM,GAAG,CAAC,CAAC,KAAK9E,cAAc,CAAC+I,eAAe,IACvE,IAAI,CAAC8B,gBAAgB,CAAC,IAAI,CAAC5G,cAAc,CAACa,MAAM,GAAG,CAAC,CAAC,IAAI9E,cAAc,CAAC6E,YAAY,CAAC,IACrF,IAAI,CAACgG,gBAAgB,CAAClC,WAAW,CAAC,KAAK,IAAI,CAAC1E,cAAc,CAACa,MAAM,GAAG,CAAC,CAAC,IACtEE,KAAK,EAAE;UACPF,MAAM,IAAI,CAAC;UACXC,MAAM,IAAI4D,WAAW;QACzB,CAAC,MACI,IAAI,IAAI,CAACjI,aAAa,IACvB,IAAI,CAACS,iBAAiB,CAACwF,OAAO,CAACgC,WAAW,CAAC,GAAG,CAAC,IAC/CA,WAAW,KAAK,IAAI,CAAC/H,oBAAoB,IACzC,IAAI,CAACA,oBAAoB,CAAC2E,MAAM,KAAK,CAAC,EAAE;UACxCJ,QAAQ,GAAG,IAAI;QACnB;MACJ;IACJ;IACA,IAAIJ,MAAM,CAACM,iBAAiB,GAAG,CAAC,CAAC,IAC7BN,MAAM,CAACQ,MAAM,GAAG,CAAC,KAAKtB,cAAc,CAACsB,MAAM,IAC3C,IAAI,CAACpE,iBAAiB,CAACwF,OAAO,CAAC1C,cAAc,CAACA,cAAc,CAACsB,MAAM,GAAG,CAAC,CAAC,IAAIvF,cAAc,CAAC6E,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;MACjHE,MAAM,IAAId,cAAc,CAACA,cAAc,CAACsB,MAAM,GAAG,CAAC,CAAC;IACvD;IACA,IAAIuF,WAAW,GAAGzF,iBAAiB,GAAG,CAAC;IACvC,OAAO,IAAI,CAACvB,MAAM,CAACiH,GAAG,CAACD,WAAW,CAAC,EAAE;MACjC5F,KAAK,EAAE;MACP4F,WAAW,EAAE;IACjB;IACA,IAAIE,WAAW,GAAGtG,UAAU,IAAI,CAACT,cAAc,CAACqC,UAAU,CAACtG,cAAc,CAACsD,SAAS,CAAC,GAC9EwB,MAAM,GACN,IAAI,CAAChB,MAAM,CAACiH,GAAG,CAAC1F,iBAAiB,CAAC,GAC9BH,KAAK,GACL,CAAC;IACX,IAAIC,QAAQ,EAAE;MACV6F,WAAW,EAAE;IACjB;IACApG,EAAE,CAACoG,WAAW,EAAE/F,cAAc,CAAC;IAC/B,IAAIC,KAAK,GAAG,CAAC,EAAE;MACX,IAAI,CAACpB,MAAM,CAAC4E,KAAK,CAAC,CAAC;IACvB;IACA,IAAIuC,WAAW,GAAG,KAAK;IACvB,IAAItG,UAAU,EAAE;MACZsG,WAAW,GAAGxF,UAAU,CAACyF,KAAK,CAAEC,IAAI,IAAK,IAAI,CAAChK,iBAAiB,CAAC+F,QAAQ,CAACiE,IAAI,CAAC,CAAC;IACnF;IACA,IAAIC,GAAG,GAAI,GAAE,IAAI,CAAC9K,MAAO,GAAE2K,WAAW,GAAGjL,cAAc,CAAC6E,YAAY,GAAGE,MAAO,GAAE,IAAI,CAACrE,aAAa,GAAG,EAAE,GAAG,IAAI,CAACL,MAAO,EAAC;IACvH,IAAI0E,MAAM,CAACQ,MAAM,KAAK,CAAC,EAAE;MACrB6F,GAAG,GAAG,IAAI,CAACzK,aAAa,GAAI,GAAE,IAAI,CAACL,MAAO,GAAEyE,MAAO,EAAC,GAAI,GAAEA,MAAO,EAAC;IACtE;IACA,MAAMsG,iCAAiC,GAAGjG,cAAc,CAACG,MAAM,KAAK,CAAC,IACjE,IAAI,CAACpE,iBAAiB,CAAC+F,QAAQ,CAACjD,cAAc,CAAC,CAAC,CAAC,CAAC,IAClDmB,cAAc,KAAKnB,cAAc,CAAC,CAAC,CAAC;IACxC,IAAI,CAAC,IAAI,CAAC6E,gBAAgB,CAAC1D,cAAc,EAAEnB,cAAc,CAAC,CAAC,CAAC,CAAC,IACzDoH,iCAAiC,EAAE;MACnC,OAAO,EAAE;IACb;IACA,IAAItG,MAAM,CAACmC,QAAQ,CAAClH,cAAc,CAAC4F,KAAK,CAAC,IAAI,IAAI,CAACtF,MAAM,IAAI,IAAI,CAACW,oBAAoB,EAAE;MACnF,IAAI0D,UAAU,IAAII,MAAM,KAAK/E,cAAc,CAAC4F,KAAK,EAAE;QAC/C,OAAO,EAAE;MACb;MACAwF,GAAG,GAAI,GAAEpL,cAAc,CAAC4F,KAAM,GAAE,IAAI,CAACtF,MAAO,GAAEyE,MAAM,CAC/CY,KAAK,CAAC3F,cAAc,CAAC4F,KAAK,CAAC,CAC3B0F,IAAI,CAACtL,cAAc,CAAC6E,YAAY,CAAE,GAAE,IAAI,CAACxE,MAAO,EAAC;IAC1D;IACA,OAAO+K,GAAG;EACd;EACAG,oBAAoBA,CAAC5C,WAAW,EAAE;IAC9B,IAAI3B,KAAK,CAACC,OAAO,CAAC,IAAI,CAACpG,qBAAqB,CAAC,EAAE;MAC3C,OAAO,IAAI,CAACA,qBAAqB,CAACsG,IAAI,CAAEqE,GAAG,IAAKA,GAAG,KAAK7C,WAAW,CAAC;IACxE;IACA,OAAO,IAAI,CAACkC,gBAAgB,CAAClC,WAAW,CAAC;EAC7C;EACAkC,gBAAgBA,CAAClC,WAAW,EAAE;IAC1B,OAAO,IAAI,CAACxH,iBAAiB,CAACgG,IAAI,CAAEqE,GAAG,IAAKA,GAAG,KAAK7C,WAAW,CAAC;EACpE;EACAG,gBAAgBA,CAACH,WAAW,EAAE8C,UAAU,EAAE;IACtC,IAAI,CAAC5J,QAAQ,GAAG,IAAI,CAACgC,aAAa,GAAG,IAAI,CAACA,aAAa,GAAG,IAAI,CAAChC,QAAQ;IACvE,OAAQ,CAAC,IAAI,CAACA,QAAQ,CAAC4J,UAAU,CAAC,EAAE3J,OAAO,IACvC,IAAI,CAACD,QAAQ,CAAC4J,UAAU,CAAC,EAAE3J,OAAO,CAAC4J,IAAI,CAAC/C,WAAW,CAAC,KACpD,KAAK;EACb;EACAN,qBAAqB,GAAGA,CAACsD,GAAG,EAAEC,qBAAqB,EAAEC,YAAY,EAAErF,SAAS,KAAK;IAC7E,IAAIsF,CAAC,GAAG,EAAE;IACV,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAI/E,KAAK,CAACC,OAAO,CAAC4E,YAAY,CAAC,EAAE;MAC7B,MAAMG,MAAM,GAAG,IAAIjK,MAAM,CAAC8J,YAAY,CAACI,GAAG,CAAEC,CAAC,IAAM,cAAc,CAACvF,OAAO,CAACuF,CAAC,CAAC,IAAI,CAAC,GAAI,KAAIA,CAAE,EAAC,GAAGA,CAAE,CAAC,CAACZ,IAAI,CAAC,GAAG,CAAC,CAAC;MAC7GQ,CAAC,GAAGH,GAAG,CAAChG,KAAK,CAACqG,MAAM,CAAC;MACrBD,WAAW,GAAGJ,GAAG,CAACxF,KAAK,CAAC6F,MAAM,CAAC,GAAG,CAAC,CAAC,IAAIhM,cAAc,CAAC6E,YAAY;IACvE,CAAC,MACI;MACDiH,CAAC,GAAGH,GAAG,CAAChG,KAAK,CAACkG,YAAY,CAAC;MAC3BE,WAAW,GAAGF,YAAY;IAC9B;IACA,MAAMM,QAAQ,GAAGL,CAAC,CAACvG,MAAM,GAAG,CAAC,GAAI,GAAEwG,WAAY,GAAED,CAAC,CAAC,CAAC,CAAE,EAAC,GAAG9L,cAAc,CAAC6E,YAAY;IACrF,IAAIuG,GAAG,GAAGU,CAAC,CAAC,CAAC,CAAC,IAAI9L,cAAc,CAAC6E,YAAY;IAC7C,MAAM7D,cAAc,GAAG,IAAI,CAACA,cAAc,CAACiH,OAAO,CAAC,KAAK,EAAEjI,cAAc,CAAC6E,YAAY,CAAC;IACtF,IAAI7D,cAAc,IAAI,CAACA,cAAc,EAAE;MACnC,IAAIoK,GAAG,CAAC,CAAC,CAAC,KAAKpL,cAAc,CAAC4F,KAAK,EAAE;QACjCwF,GAAG,GAAI,IAAGA,GAAG,CAAC9F,KAAK,CAAC,CAAC,EAAE8F,GAAG,CAAC7F,MAAM,CAAC,CAACD,KAAK,CAAC,CAAC,EAAEtE,cAAc,CAACuE,MAAM,CAAE,EAAC;MACxE,CAAC,MACI;QACD6F,GAAG,GAAGA,GAAG,CAAC9F,KAAK,CAAC,CAAC,EAAEtE,cAAc,CAACuE,MAAM,CAAC;MAC7C;IACJ;IACA,MAAM6G,GAAG,GAAG,cAAc;IAC1B,OAAOR,qBAAqB,IAAIQ,GAAG,CAACV,IAAI,CAACN,GAAG,CAAC,EAAE;MAC3CA,GAAG,GAAGA,GAAG,CAACnD,OAAO,CAACmE,GAAG,EAAE,IAAI,GAAGR,qBAAqB,GAAG,IAAI,CAAC;IAC/D;IACA,IAAI,OAAOpF,SAAS,KAAK,WAAW,EAAE;MAClC,OAAO4E,GAAG,GAAGe,QAAQ;IACzB,CAAC,MACI,IAAI3F,SAAS,KAAK,CAAC,EAAE;MACtB,OAAO4E,GAAG;IACd;IACA,OAAOA,GAAG,GAAGe,QAAQ,CAACtF,SAAS,CAAC,CAAC,EAAEL,SAAS,GAAG,CAAC,CAAC;EACrD,CAAC;EACDI,UAAU,GAAI+E,GAAG,IAAK;IAClB,MAAMU,YAAY,GAAGV,GAAG,CAAC1D,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAC1C,MAAMvG,KAAK,GAAGuH,MAAM,CAAC,IAAI,CAAChI,oBAAoB,IAAI0K,GAAG,CAACzE,QAAQ,CAAClH,cAAc,CAAC4F,KAAK,CAAC,GAC9EyG,YAAY,CAAC/G,KAAK,CAAC,CAAC,EAAEqG,GAAG,CAACpG,MAAM,CAAC,GACjC8G,YAAY,CAAC;IACnB,OAAO,CAACC,KAAK,CAAC5K,KAAK,CAAC,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,GAAG;EACtD,CAAC;EACD+E,YAAY,GAAIxC,cAAc,IAAK;IAC/B,MAAM6H,CAAC,GAAG7H,cAAc,CAAC0B,KAAK,CAAC3F,cAAc,CAAC+F,GAAG,CAAC;IAClD,IAAI+F,CAAC,CAACvG,MAAM,GAAG,CAAC,EAAE;MACd,OAAO0D,MAAM,CAAC6C,CAAC,CAACA,CAAC,CAACvG,MAAM,GAAG,CAAC,CAAC,CAAC;IAClC;IACA,OAAOgH,QAAQ;EACnB,CAAC;EACD/G,oBAAoB,GAAIhB,UAAU,IAAK;IACnC,KAAK,IAAI0B,CAAC,GAAG,IAAI,CAAC7F,MAAM,EAAEkF,MAAM,GAAG,CAAC,EAAEW,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC/C,MAAMsG,MAAM,GAAG,IAAI,CAACnM,MAAM,CAACwG,SAAS,CAACX,CAAC,EAAE,IAAI,CAAC7F,MAAM,EAAEkF,MAAM,CAAC;MAC5D,IAAIf,UAAU,CAAC0C,QAAQ,CAACsF,MAAM,CAAC,IAC3BtG,CAAC,KAAK,IAAI,CAAC7F,MAAM,EAAEkF,MAAM,GAAG,CAAC,KAC5BW,CAAC,GAAG,CAAC,GAAG,CAAC,IACN,CAAC1B,UAAU,CAAC0C,QAAQ,CAAC,IAAI,CAAC7G,MAAM,CAACwG,SAAS,CAACX,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC7F,MAAM,EAAEkF,MAAM,CAAC,CAAC,CAAC,EAAE;QAC9E,OAAOf,UAAU,CAACyD,OAAO,CAACuE,MAAM,EAAExM,cAAc,CAAC6E,YAAY,CAAC;MAClE;IACJ;IACA,OAAOL,UAAU;EACrB,CAAC;EACDkC,mBAAmB,GAAGA,CAAClC,UAAU,EAAEgC,SAAS,EAAEhG,aAAa,KAAK;IAC5D,IAAIiM,mBAAmB,GAAGjI,UAAU;IACpC,IAAIkI,sBAAsB,GAAGlM,aAAa;IAC1C,IAAIgG,SAAS,GAAG+F,QAAQ,EAAE;MACtB;MACA,IAAIvF,KAAK,CAACC,OAAO,CAACyF,sBAAsB,CAAC,EAAE;QACvC,MAAMxE,MAAM,GAAGwE,sBAAsB,CAACvF,IAAI,CAAEC,EAAE,IAAKA,EAAE,KAAK,IAAI,CAAC7G,iBAAiB,CAAC;QACjFmM,sBAAsB,GAAGxE,MAAM,GAAGA,MAAM,GAAGwE,sBAAsB,CAAC,CAAC,CAAC;MACxE;MACA,MAAMC,cAAc,GAAG,IAAI5K,MAAM,CAAC,IAAI,CAACgG,uBAAuB,CAAC2E,sBAAsB,CAAC,GAAI,OAAMlG,SAAU,MAAK,CAAC;MAChH,MAAMoG,cAAc,GAAGH,mBAAmB,CAACtG,KAAK,CAACwG,cAAc,CAAC;MAChE,MAAME,oBAAoB,GAAG,CAACD,cAAc,IAAIA,cAAc,CAAC,CAAC,CAAC,EAAErH,MAAM,KAAK,CAAC;MAC/E,IAAIsH,oBAAoB,GAAG,CAAC,GAAGrG,SAAS,EAAE;QACtC,MAAMsG,IAAI,GAAGD,oBAAoB,GAAG,CAAC,GAAGrG,SAAS;QACjDiG,mBAAmB,GAAGA,mBAAmB,CAAC5F,SAAS,CAAC,CAAC,EAAE4F,mBAAmB,CAAClH,MAAM,GAAGuH,IAAI,CAAC;MAC7F;MACA,IAAItG,SAAS,KAAK,CAAC,IACf,IAAI,CAACuG,kBAAkB,CAACN,mBAAmB,CAACA,mBAAmB,CAAClH,MAAM,GAAG,CAAC,CAAC,EAAEmH,sBAAsB,EAAE,IAAI,CAACnM,iBAAiB,CAAC,EAAE;QAC9HkM,mBAAmB,GAAGA,mBAAmB,CAAC5F,SAAS,CAAC,CAAC,EAAE4F,mBAAmB,CAAClH,MAAM,GAAG,CAAC,CAAC;MAC1F;IACJ;IACA,OAAOkH,mBAAmB;EAC9B,CAAC;EACDlG,eAAeA,CAACoF,GAAG,EAAE;IACjB,OAAOA,GAAG,CACLhG,KAAK,CAAC3F,cAAc,CAAC6E,YAAY,CAAC,CAClCmI,MAAM,CAAC,CAAC9G,CAAC,EAAE+G,GAAG,KAAK;MACpB,MAAMC,eAAe,GAAG,OAAO,IAAI,CAAC1M,aAAa,KAAK,QAAQ,GACxD0F,CAAC,KAAK,IAAI,CAAC1F,aAAa;MACxB;MACE,IAAI,CAACA,aAAa,CAAC0G,QAAQ,CAAChB,CAAC,CAAC;MACtC,OAAQA,CAAC,CAACC,KAAK,CAAC,QAAQ,CAAC,IACrBD,CAAC,KAAK,IAAI,CAAC3F,iBAAiB,IAC5B2M,eAAe,IACdhH,CAAC,KAAKlG,cAAc,CAAC4F,KAAK,IAAIqH,GAAG,KAAK,CAAC,IAAI,IAAI,CAAChM,oBAAqB;IAC9E,CAAC,CAAC,CACGqK,IAAI,CAACtL,cAAc,CAAC6E,YAAY,CAAC;EAC1C;EACAkD,uBAAuBA,CAACoD,IAAI,EAAE;IAC1B;IACA;IACA;IACA,IAAIA,IAAI,EAAE;MACN,MAAMgC,aAAa,GAAG,cAAc;MACpC,OAAOhC,IAAI,KAAK,GAAG,GAAG,KAAK,GAAGgC,aAAa,CAACxG,OAAO,CAACwE,IAAI,CAAC,IAAI,CAAC,GAAI,KAAIA,IAAK,EAAC,GAAGA,IAAI;IACvF;IACA,OAAOA,IAAI;EACf;EACAjC,UAAUA,CAACpE,MAAM,EAAE;IACf,IAAI,CAAChB,MAAM,CAAC2E,GAAG,CAAC3D,MAAM,GAAG,IAAI,CAACxE,MAAM,CAACiF,MAAM,IAAI,CAAC,CAAC;EACrD;EACAwH,kBAAkBA,CAACrL,KAAK,EAAE0L,aAAa,EAAEC,aAAa,EAAE;IACpD,OAAOrG,KAAK,CAACC,OAAO,CAACmG,aAAa,CAAC,GAC7BA,aAAa,CAACJ,MAAM,CAAEd,CAAC,IAAKA,CAAC,KAAKmB,aAAa,CAAC,CAACnG,QAAQ,CAACxF,KAAK,CAAC,GAChEA,KAAK,KAAK0L,aAAa;EACjC;EACApH,QAAQA,CAACF,QAAQ,EAAE;IACf,OAAO,EAAEA,QAAQ,CAACP,MAAM,KAAK,CAAC,IAC1B,CAACO,QAAQ,CAACwH,IAAI,CAAC,CAAC5L,KAAK,EAAE6L,KAAK,KAAK;MAC7B,IAAIzH,QAAQ,CAACP,MAAM,KAAKgI,KAAK,GAAG,CAAC,EAAE;QAC/B,OAAO7L,KAAK,KAAK1B,cAAc,CAAC6E,YAAY,IAAIoE,MAAM,CAACvH,KAAK,CAAC,GAAG,GAAG;MACvE;MACA,OAAOA,KAAK,KAAK1B,cAAc,CAAC6E,YAAY,IAAIoE,MAAM,CAACvH,KAAK,CAACmF,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG;IACvF,CAAC,CAAC,CAAC;EACX;EACAE,iBAAiBA,CAACrF,KAAK,EAAE;IACrB,IAAIA,KAAK,KAAK1B,cAAc,CAAC4F,KAAK,IAAI,IAAI,CAAC3E,oBAAoB,EAAE;MAC7D,OAAOS,KAAK;IAChB;IACA,MAAM8L,YAAY,GAAG,OAAO,IAAI,CAAChN,aAAa,KAAK,QAAQ,GACrDkB,KAAK,CAACiF,OAAO,CAAC,IAAI,CAACnG,aAAa,CAAC,GACjCkB,KAAK,CAACiF,OAAO,CAAC3G,cAAc,CAAC+F,GAAG,CAAC;IACvC,MAAM0H,YAAY,GAAG,IAAI,CAACxM,oBAAoB,IAAIS,KAAK,CAACwF,QAAQ,CAAClH,cAAc,CAAC4F,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;IACjG,IAAI4H,YAAY,KAAK,CAAC,CAAC,EAAE;MACrB,MAAME,WAAW,GAAGC,QAAQ,CAACF,YAAY,GAAG/L,KAAK,CAAC4D,KAAK,CAAC,CAAC,EAAE5D,KAAK,CAAC6D,MAAM,CAAC,GAAG7D,KAAK,EAAE,EAAE,CAAC;MACrF,OAAO4K,KAAK,CAACoB,WAAW,CAAC,GACnB1N,cAAc,CAAC6E,YAAY,GAC1B,GAAE4I,YAAa,GAAEC,WAAY,EAAC;IACzC,CAAC,MACI;MACD,MAAME,WAAW,GAAGD,QAAQ,CAACjM,KAAK,CAACuG,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACpB,SAAS,CAAC,CAAC,EAAE2G,YAAY,CAAC,EAAE,EAAE,CAAC;MACnF,MAAMK,WAAW,GAAGnM,KAAK,CAACmF,SAAS,CAAC2G,YAAY,GAAG,CAAC,CAAC;MACrD,MAAMM,aAAa,GAAGxB,KAAK,CAACsB,WAAW,CAAC,GAAG,EAAE,GAAGA,WAAW,CAAClI,QAAQ,CAAC,CAAC;MACtE,MAAMqI,OAAO,GAAG,OAAO,IAAI,CAACvN,aAAa,KAAK,QAAQ,GAAG,IAAI,CAACA,aAAa,GAAGR,cAAc,CAAC+F,GAAG;MAChG,OAAO+H,aAAa,KAAK9N,cAAc,CAAC6E,YAAY,GAC9C7E,cAAc,CAAC6E,YAAY,GAC1B,GAAE4I,YAAa,GAAEK,aAAc,GAAEC,OAAQ,GAAEF,WAAY,EAAC;IACnE;EACJ;EACAtG,gCAAgCA,CAACyG,WAAW,EAAExN,aAAa,EAAE;IACzD,IAAI6G,kBAAkB,GAAG,IAAI;IAC7B,IAAIC,YAAY,GAAG,IAAI;IACvB,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8H,WAAW,CAACzI,MAAM,EAAEW,CAAC,EAAE,EAAE;MACzC,MAAMiF,IAAI,GAAG6C,WAAW,CAAC9H,CAAC,CAAC;MAC3B,IAAIiF,IAAI,KAAK3K,aAAa,IAAI6G,kBAAkB,KAAK,IAAI,EAAE;QACvDA,kBAAkB,GAAGnB,CAAC;MAC1B;MACA,IAAIiF,IAAI,IAAIA,IAAI,IAAI,GAAG,IAAIA,IAAI,IAAI,GAAG,IAAI7D,YAAY,KAAK,IAAI,EAAE;QAC7DA,YAAY,GAAGpB,CAAC;MACpB;MACA,IAAImB,kBAAkB,KAAK,IAAI,IAAIC,YAAY,KAAK,IAAI,EAAE;QACtD;MACJ;IACJ;IACA,OAAO;MACHD,kBAAkB;MAClBC;IACJ,CAAC;EACL;EACA,OAAO2G,IAAI,YAAAC,8BAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFxK,qBAAqB;EAAA;EACxH,OAAOyK,KAAK,kBAD6ErP,EAAE,CAAAsP,kBAAA;IAAAC,KAAA,EACY3K,qBAAqB;IAAA4K,OAAA,EAArB5K,qBAAqB,CAAAsK;EAAA;AAChI;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAH6FzP,EAAE,CAAA0P,iBAAA,CAGJ9K,qBAAqB,EAAc,CAAC;IACnH+K,IAAI,EAAEvP;EACV,CAAC,CAAC;AAAA;AAEV,MAAMwP,cAAc,SAAShL,qBAAqB,CAAC;EAC/CiL,aAAa,GAAG,KAAK;EACrBC,WAAW,GAAG,EAAE;EAChBC,QAAQ,GAAG,IAAI;EACfC,MAAM,GAAG,IAAI;EACbC,WAAW,GAAG,KAAK;EACnBC,mBAAmB,GAAG,EAAE;EACxBC,aAAa,GAAG,EAAE;EAClBC,YAAY,GAAG,EAAE;EACjB;AACJ;AACA;AACA;EACIC,YAAY,GAAG,KAAK;EACpBC,UAAU,GAAG,KAAK;EAClBC,MAAM;EACNC,IAAI;EACJ;EACAC,QAAQ,GAAIC,CAAC,IAAK,CAAE,CAAC;EACrBC,WAAW,GAAGxQ,MAAM,CAACE,UAAU,EAAE;IAAE4C,QAAQ,EAAE;EAAK,CAAC,CAAC;EACpD2N,QAAQ,GAAGzQ,MAAM,CAACW,QAAQ,CAAC;EAC3B+D,OAAO,GAAG1E,MAAM,CAACe,eAAe,CAAC;EACjC2P,SAAS,GAAG1Q,MAAM,CAACG,SAAS,EAAE;IAAE2C,QAAQ,EAAE;EAAK,CAAC,CAAC;EACjD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIuC,SAASA,CAACC,UAAU,EAAEP,cAAc,EAAEQ,QAAQ,GAAG,CAAC,EAAEC,UAAU,GAAG,KAAK,EAAEC,UAAU,GAAG,KAAK;EAC1F;EACAC,EAAE,GAAGA,CAAA,KAAM,CAAE,CAAC,EAAE;IACZ;IACA,IAAI,CAACX,cAAc,EAAE;MACjB,OAAOO,UAAU,KAAK,IAAI,CAACN,WAAW,GAAG,IAAI,CAACA,WAAW,GAAGM,UAAU;IAC1E;IACA;IACA,IAAI,CAACqK,WAAW,GAAG,IAAI,CAACnO,aAAa,GAC/B,IAAI,CAACmP,eAAe,CAAC,CAAC,GACtB7P,cAAc,CAAC6E,YAAY;IACjC;IACA,IAAI,IAAI,CAACZ,cAAc,KAAKjE,cAAc,CAAC6F,EAAE,IAAI,IAAI,CAACnF,aAAa,EAAE;MACjE,IAAI,CAACmO,WAAW,GAAG,IAAI,CAACgB,eAAe,CAACrL,UAAU,IAAIxE,cAAc,CAAC8P,IAAI,CAAC;IAC9E;IACA,IAAI,IAAI,CAAC7L,cAAc,KAAKjE,cAAc,CAACqG,QAAQ,IAAI,IAAI,CAAC3F,aAAa,EAAE;MACvE,IAAI,CAACmO,WAAW,GAAG,IAAI,CAACgB,eAAe,CAACrL,UAAU,IAAIxE,cAAc,CAAC8P,IAAI,CAAC;IAC9E;IACA;IACA,IAAI,CAACtL,UAAU,IAAI,IAAI,CAAC9D,aAAa,EAAE;MACnC,IAAI,CAACqP,iBAAiB,CAAC,IAAI,CAACzP,MAAM,CAAC;MACnC,OAAQ,GAAE,IAAI,CAACA,MAAO,GAAE,IAAI,CAACuO,WAAY,GAAE,IAAI,CAACxO,MAAO,EAAC;IAC5D;IACA,MAAM2P,SAAS,GAAG,CAAC,CAACxL,UAAU,IAAI,OAAO,IAAI,CAACsK,QAAQ,KAAK,QAAQ,GAC5DtK,UAAU,CAAC,IAAI,CAACsK,QAAQ,CAAC,IAAI9O,cAAc,CAAC6E,YAAY,GACzD7E,cAAc,CAAC6E,YAAY;IACjC,IAAIoL,aAAa,GAAG,EAAE;IACtB,IAAInF,WAAW,GAAGrG,QAAQ;IAC1B;IACA,IAAI,CAAC,IAAI,CAAC3D,WAAW,IAChB0D,UAAU,IAAIA,UAAU,CAACmC,OAAO,CAAC3G,cAAc,CAAC6I,WAAW,CAAC,IAAI,CAAE,KACnE,CAAC,IAAI,CAACuG,YAAY,EAAE;MACpB,IAAIc,YAAY,GAAG1L,UAAU,IAAIA,UAAU,CAACe,MAAM,KAAK,CAAC,GAClDf,UAAU,CAACmB,KAAK,CAAC3F,cAAc,CAAC6E,YAAY,CAAC,GAC7C,IAAI,CAACX,WAAW,CAACyB,KAAK,CAAC3F,cAAc,CAAC6E,YAAY,CAAC;MACzD;MACA,IAAIF,UAAU,EAAE;QACZuL,YAAY,GAAGA,YAAY,CACtB5K,KAAK,CAAC,CAAC,EAAEb,QAAQ,CAAC,CAClB0L,MAAM,CAACD,YAAY,CAAC5K,KAAK,CAACb,QAAQ,GAAG,CAAC,CAAC,CAAC;MACjD;MACA;MACA,IAAI,IAAI,CAAC/D,aAAa,EAAE;QACpB;QACA8D,UAAU,GAAG,IAAI,CAAC4L,UAAU,CAAC5L,UAAU,CAAC;QACxC0L,YAAY,GAAG,IAAI,CAACE,UAAU,CAACF,YAAY,CAAC5E,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC3F,KAAK,CAAC3F,cAAc,CAAC6E,YAAY,CAAC;MAC5F;MACA;MACA,IAAI,OAAO,IAAI,CAACiK,QAAQ,KAAK,QAAQ,IAAI,OAAO,IAAI,CAACC,MAAM,KAAK,QAAQ,EAAE;QACtE,IAAI,CAACD,QAAQ,GAAG7F,MAAM,CAAC,IAAI,CAAC6F,QAAQ,CAAC;QACrC,IAAI,CAACC,MAAM,GAAG9F,MAAM,CAAC,IAAI,CAAC8F,MAAM,CAAC;MACrC,CAAC,MACI;QACD,IAAIvK,UAAU,KAAKxE,cAAc,CAAC6E,YAAY,IAAIqL,YAAY,CAAC3K,MAAM,EAAE;UACnE,IAAI,OAAO,IAAI,CAACuJ,QAAQ,KAAK,QAAQ,IAAI,OAAO,IAAI,CAACC,MAAM,KAAK,QAAQ,EAAE;YACtE,IAAIvK,UAAU,CAACe,MAAM,GAAG2K,YAAY,CAAC3K,MAAM,EAAE;cACzC2K,YAAY,CAACG,MAAM,CAAC,IAAI,CAACvB,QAAQ,EAAE,CAAC,EAAEkB,SAAS,CAAC;YACpD,CAAC,MACI,IAAIxL,UAAU,CAACe,MAAM,GAAG2K,YAAY,CAAC3K,MAAM,EAAE;cAC9C,IAAI2K,YAAY,CAAC3K,MAAM,GAAGf,UAAU,CAACe,MAAM,KAAK,CAAC,EAAE;gBAC/C,IAAIZ,UAAU,EAAE;kBACZuL,YAAY,CAACG,MAAM,CAAC,IAAI,CAACvB,QAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;gBAC7C,CAAC,MACI;kBACDoB,YAAY,CAACG,MAAM,CAAC7L,UAAU,CAACe,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;gBACjD;cACJ,CAAC,MACI;gBACD2K,YAAY,CAACG,MAAM,CAAC,IAAI,CAACvB,QAAQ,EAAE,IAAI,CAACC,MAAM,GAAG,IAAI,CAACD,QAAQ,CAAC;cACnE;YACJ;UACJ;QACJ,CAAC,MACI;UACDoB,YAAY,GAAG,EAAE;QACrB;MACJ;MACA;MACA,IAAI,IAAI,CAACxP,aAAa,IAAI,CAAC,IAAI,CAACI,WAAW,EAAE;QACzCmP,aAAa,GAAG,IAAI,CAACG,UAAU,CAAC5L,UAAU,CAAC;MAC/C;MACA;MACA,IAAI,IAAI,CAACN,WAAW,CAACqB,MAAM,EAAE;QACzB,IAAI2K,YAAY,CAAC3K,MAAM,GAAGf,UAAU,CAACe,MAAM,EAAE;UACzC0K,aAAa,GAAG,IAAI,CAACK,iBAAiB,CAACJ,YAAY,CAAC5E,IAAI,CAACtL,cAAc,CAAC6E,YAAY,CAAC,CAAC;QAC1F,CAAC,MACI,IAAIqL,YAAY,CAAC3K,MAAM,KAAKf,UAAU,CAACe,MAAM,EAAE;UAChD0K,aAAa,GAAGC,YAAY,CAAC5E,IAAI,CAACtL,cAAc,CAAC6E,YAAY,CAAC;QAClE,CAAC,MACI;UACDoL,aAAa,GAAGzL,UAAU;QAC9B;MACJ,CAAC,MACI;QACDyL,aAAa,GAAGzL,UAAU;MAC9B;IACJ;IACA;IACA,IAAIE,UAAU,KAAK,IAAI,CAAC5D,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAAC,EAAE;MACvDmP,aAAa,GAAGzL,UAAU;IAC9B;IACA;IACA,IAAIG,UAAU,IACV,IAAI,CAACxD,iBAAiB,CAACwF,OAAO,CAAC,IAAI,CAAC1C,cAAc,CAAC6G,WAAW,CAAC,IAAI9K,cAAc,CAAC6E,YAAY,CAAC,KAAK,CAAC,CAAC,IACtG,IAAI,CAACnE,aAAa,IAClB,CAAC,IAAI,CAACJ,MAAM,EAAE;MACd2P,aAAa,GAAG,IAAI,CAACd,YAAY;IACrC;IACA;IACA,IAAI,IAAI,CAAC/K,uBAAuB,IAAI0G,WAAW,EAAE;MAC7C,IAAI,IAAI,CAAC3J,iBAAiB,CAAC+F,QAAQ,CAAC,IAAI,CAAChD,WAAW,CAACoB,KAAK,CAACwF,WAAW,EAAEA,WAAW,GAAG,CAAC,CAAC,CAAC,EAAE;QACvFA,WAAW,GAAGA,WAAW,GAAG,CAAC;MACjC,CAAC,MACI,IAAI7G,cAAc,CAACqB,KAAK,CAACwF,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAG,CAAC,CAAC,KAAK9K,cAAc,CAAC0D,MAAM,EAAE;QACvFoH,WAAW,GAAGA,WAAW,GAAG,CAAC;MACjC;MACA,IAAI,CAAC1G,uBAAuB,GAAG,KAAK;IACxC;IACA;IACA,IAAI,IAAI,CAAC1D,aAAa,IAClB,IAAI,CAACE,oBAAoB,CAAC2E,MAAM,KAAK,CAAC,IACtC,CAAC,IAAI,CAACnE,gBAAgB,EAAE;MACxB6O,aAAa,GAAG,IAAI,CAACG,UAAU,CAACH,aAAa,CAAC;IAClD;IACA;IACA,IAAI,IAAI,CAACjB,WAAW,EAAE;MAClBiB,aAAa,GAAGzL,UAAU;IAC9B,CAAC,MACI;MACDyL,aAAa,GACTM,OAAO,CAACN,aAAa,CAAC,IAAIA,aAAa,CAAC1K,MAAM,GAAG0K,aAAa,GAAGzL,UAAU;IACnF;IACA;IACA,IAAI,IAAI,CAAC9D,aAAa,IAClB,IAAI,CAACa,sBAAsB,IAC3B,IAAI,CAAC2C,WAAW,IAChB,CAACQ,UAAU,IACX,CAAC,IAAI,CAAC0K,YAAY,EAAE;MACpB,MAAM1N,KAAK,GAAG,IAAI,CAACb,qBAAqB,GAClC,IAAI,CAACuP,UAAU,CAAC,IAAI,CAAClM,WAAW,CAAC,GACjC,IAAI,CAACA,WAAW;MACtB,IAAI,CAAC6L,iBAAiB,CAACrO,KAAK,CAAC;MAC7B,OAAO,IAAI,CAACwC,WAAW,GACjB,IAAI,CAACA,WAAW,GACf,GAAE,IAAI,CAAC5D,MAAO,GAAE,IAAI,CAACuO,WAAY,GAAE,IAAI,CAACxO,MAAO,EAAC;IAC3D;IACA;IACA,MAAM0E,MAAM,GAAG,KAAK,CAACR,SAAS,CAAC0L,aAAa,EAAEhM,cAAc,EAAE6G,WAAW,EAAEpG,UAAU,EAAEC,UAAU,EAAEC,EAAE,CAAC;IACtG,IAAI,CAACV,WAAW,GAAG,IAAI,CAACsM,cAAc,CAACzL,MAAM,CAAC;IAC9C;IACA;IACA,IAAI,IAAI,CAACxE,iBAAiB,KAAKP,cAAc,CAAC+F,GAAG,IAC7C,IAAI,CAACvF,aAAa,KAAKR,cAAc,CAAC+F,GAAG,EAAE;MAC3C,IAAI,CAACvF,aAAa,GAAGR,cAAc,CAAC6H,KAAK;IAC7C;IACA;IACA,IAAI,IAAI,CAAC5D,cAAc,CAACqC,UAAU,CAACtG,cAAc,CAACsD,SAAS,CAAC,IACxD,IAAI,CAACzC,qBAAqB,KAAK,IAAI,EAAE;MACrC,IAAI,CAACM,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAC6L,MAAM,CAAEyD,IAAI,IAAK,CAAC,IAAI,CAAC1D,kBAAkB,CAAC0D,IAAI,EAAE,IAAI,CAACjQ,aAAa,EAAE,IAAI,CAACD,iBAAiB,CAAC,CAAC;MAC5I,CAAC;IACL;IACA;IACA,IAAIwE,MAAM,IAAIA,MAAM,KAAK,EAAE,EAAE;MACzB,IAAI,CAACmK,aAAa,GAAG,IAAI,CAACC,YAAY;MACtC,IAAI,CAACA,YAAY,GAAGpK,MAAM;MAC1B,IAAI,CAACsK,UAAU,GACX,IAAI,CAACH,aAAa,KAAK,IAAI,CAACC,YAAY,IACnCc,aAAa,KAAK,IAAI,CAACd,YAAY,IAAI,IAAI,CAACC,YAAa,IACzD,IAAI,CAACF,aAAa,KAAK,IAAI,CAACC,YAAY,IAAIzK,UAAW;IACpE;IACA;IACA;IACA,IAAI,CAAC2K,UAAU,GAAG,IAAI,CAACU,iBAAiB,CAAChL,MAAM,CAAC,GAAG,EAAE;IACrD;IACA,IAAI,CAAC,IAAI,CAACrE,aAAa,IAAK,IAAI,CAACA,aAAa,IAAI,IAAI,CAACI,WAAY,EAAE;MACjE,IAAI,IAAI,CAACA,WAAW,EAAE;QAClB,OAAQ,GAAE,IAAI,CAAC4P,SAAS,CAAC3L,MAAM,EAAE,IAAI,CAACd,cAAc,CAAE,GAAE,IAAI,CAAC4K,WAAW,CAACvJ,KAAK,CAACP,MAAM,CAACQ,MAAM,CAAE,EAAC;MACnG;MACA,OAAOR,MAAM;IACjB;IACA,MAAM4L,MAAM,GAAG5L,MAAM,CAACQ,MAAM;IAC5B,MAAMqL,SAAS,GAAI,GAAE,IAAI,CAACtQ,MAAO,GAAE,IAAI,CAACuO,WAAY,GAAE,IAAI,CAACxO,MAAO,EAAC;IACnE;IACA,IAAI,IAAI,CAAC4D,cAAc,CAACiD,QAAQ,CAAClH,cAAc,CAACgJ,KAAK,CAAC,EAAE;MACpD,MAAM6H,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,CAAC/L,MAAM,CAAC;MAC3D,OAAQ,GAAEA,MAAO,GAAE6L,SAAS,CAACtL,KAAK,CAACqL,MAAM,GAAGE,iBAAiB,CAAE,EAAC;IACpE,CAAC,MACI,IAAI,IAAI,CAAC5M,cAAc,KAAKjE,cAAc,CAAC6F,EAAE,IAC9C,IAAI,CAAC5B,cAAc,KAAKjE,cAAc,CAACqG,QAAQ,EAAE;MACjD,OAAQ,GAAEtB,MAAO,GAAE6L,SAAU,EAAC;IAClC;IACA,OAAQ,GAAE7L,MAAO,GAAE6L,SAAS,CAACtL,KAAK,CAACqL,MAAM,CAAE,EAAC;EAChD;EACA;EACAG,oBAAoBA,CAACpP,KAAK,EAAE;IACxB,MAAMqP,KAAK,GAAG,eAAe;IAC7B,IAAI5K,KAAK,GAAG4K,KAAK,CAACC,IAAI,CAACtP,KAAK,CAAC;IAC7B,IAAImP,iBAAiB,GAAG,CAAC;IACzB,OAAO1K,KAAK,IAAI,IAAI,EAAE;MAClB0K,iBAAiB,IAAI,CAAC;MACtB1K,KAAK,GAAG4K,KAAK,CAACC,IAAI,CAACtP,KAAK,CAAC;IAC7B;IACA,OAAOmP,iBAAiB;EAC5B;EACAI,iBAAiBA,CAACxM,QAAQ,EAAEC,UAAU,EAAEC,UAAU;EAClD;EACAC,EAAE,GAAGA,CAAA,KAAM,CAAE,CAAC,EAAE;IACZ,MAAMsM,WAAW,GAAG,IAAI,CAACxB,WAAW,EAAEyB,aAAa;IACnD,IAAI,CAACD,WAAW,EAAE;MACd;IACJ;IACAA,WAAW,CAACxP,KAAK,GAAG,IAAI,CAAC6C,SAAS,CAAC2M,WAAW,CAACxP,KAAK,EAAE,IAAI,CAACuC,cAAc,EAAEQ,QAAQ,EAAEC,UAAU,EAAEC,UAAU,EAAEC,EAAE,CAAC;IAChH,IAAIsM,WAAW,KAAK,IAAI,CAACE,iBAAiB,CAAC,CAAC,EAAE;MAC1C;IACJ;IACA,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC5B;EACAX,SAASA,CAAClM,UAAU,EAAEP,cAAc,EAAE;IAClC,OAAOO,UAAU,CACZmB,KAAK,CAAC3F,cAAc,CAAC6E,YAAY,CAAC,CAClCoH,GAAG,CAAC,CAACqF,IAAI,EAAE/D,KAAK,KAAK;MACtB,IAAI,IAAI,CAAC1L,QAAQ,IACb,IAAI,CAACA,QAAQ,CAACoC,cAAc,CAACsJ,KAAK,CAAC,IAAIvN,cAAc,CAAC6E,YAAY,CAAC,IACnE,IAAI,CAAChD,QAAQ,CAACoC,cAAc,CAACsJ,KAAK,CAAC,IAAIvN,cAAc,CAAC6E,YAAY,CAAC,EAAE3C,MAAM,EAAE;QAC7E,OAAO,IAAI,CAACL,QAAQ,CAACoC,cAAc,CAACsJ,KAAK,CAAC,IAAIvN,cAAc,CAAC6E,YAAY,CAAC,EACpE3C,MAAM;MAChB;MACA,OAAOoP,IAAI;IACf,CAAC,CAAC,CACGhG,IAAI,CAACtL,cAAc,CAAC6E,YAAY,CAAC;EAC1C;EACA;EACA2L,cAAcA,CAACpF,GAAG,EAAE;IAChB,MAAMmG,OAAO,GAAGnG,GAAG,CACdzF,KAAK,CAAC3F,cAAc,CAAC6E,YAAY,CAAC,CAClCmI,MAAM,CAAC,CAAC9K,MAAM,EAAEgE,CAAC,KAAK;MACvB,MAAMsL,QAAQ,GAAG,IAAI,CAACvN,cAAc,CAACiC,CAAC,CAAC,IAAIlG,cAAc,CAAC6E,YAAY;MACtE,OAAQ,IAAI,CAACiE,gBAAgB,CAAC5G,MAAM,EAAEsP,QAAQ,CAAC,IAC1C,IAAI,CAACrQ,iBAAiB,CAAC+F,QAAQ,CAACsK,QAAQ,CAAC,IAAItP,MAAM,KAAKsP,QAAS;IAC1E,CAAC,CAAC;IACF,IAAID,OAAO,CAACjG,IAAI,CAACtL,cAAc,CAAC6E,YAAY,CAAC,KAAKuG,GAAG,EAAE;MACnD,OAAOmG,OAAO,CAACjG,IAAI,CAACtL,cAAc,CAAC6E,YAAY,CAAC;IACpD;IACA,OAAOuG,GAAG;EACd;EACAkF,iBAAiBA,CAAC9L,UAAU,EAAE;IAC1B,IAAIiN,eAAe,GAAG,EAAE;IACxB,MAAMxB,aAAa,GAAIzL,UAAU,IAC7BA,UAAU,CACLmB,KAAK,CAAC3F,cAAc,CAAC6E,YAAY,CAAC,CAClCoH,GAAG,CAAC,CAACyF,UAAU,EAAEnE,KAAK,KAAK;MAC5B,IAAI,IAAI,CAACpM,iBAAiB,CAAC+F,QAAQ,CAAC1C,UAAU,CAAC+I,KAAK,GAAG,CAAC,CAAC,IAAIvN,cAAc,CAAC6E,YAAY,CAAC,IACrFL,UAAU,CAAC+I,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,CAACtJ,cAAc,CAACsJ,KAAK,GAAG,CAAC,CAAC,EAAE;QAC1DkE,eAAe,GAAGC,UAAU;QAC5B,OAAOlN,UAAU,CAAC+I,KAAK,GAAG,CAAC,CAAC;MAChC;MACA,IAAIkE,eAAe,CAAClM,MAAM,EAAE;QACxB,MAAMoM,aAAa,GAAGF,eAAe;QACrCA,eAAe,GAAGzR,cAAc,CAAC6E,YAAY;QAC7C,OAAO8M,aAAa;MACxB;MACA,OAAOD,UAAU;IACrB,CAAC,CAAC,IACF,EAAE;IACN,OAAOzB,aAAa,CAAC3E,IAAI,CAACtL,cAAc,CAAC6E,YAAY,CAAC;EAC1D;EACA;AACJ;AACA;AACA;AACA;EACI+M,cAAcA,CAAClQ,KAAK,EAAE;IAClB,IAAK,CAACA,KAAK,IAAIA,KAAK,KAAK,CAAC,IACrB,IAAI,CAACuC,cAAc,CAACqC,UAAU,CAACtG,cAAc,CAACsD,SAAS,CAAC,KACpD,IAAI,CAAChC,QAAQ,IAAI,CAAC,IAAI,CAACT,qBAAqB,CAAE,IAClD,IAAI,CAACoD,cAAc,CAACqC,UAAU,CAACtG,cAAc,CAACsD,SAAS,CAAC,IACrD,IAAI,CAACtC,cAAc,CAACuE,MAAM,GAAG,EAAE,IAC/BsM,MAAM,CAACnQ,KAAK,CAAC,CAAC6D,MAAM,GAAG,EAAG,EAAE;MAChC,OAAOsM,MAAM,CAACnQ,KAAK,CAAC;IACxB;IACA,OAAOuH,MAAM,CAACvH,KAAK,CAAC,CACfoQ,cAAc,CAAC,UAAU,EAAE;MAC5BC,WAAW,EAAE,KAAK;MAClBC,qBAAqB,EAAE;IAC3B,CAAC,CAAC,CACG/J,OAAO,CAAE,IAAGjI,cAAc,CAAC4F,KAAM,GAAE,EAAE5F,cAAc,CAAC4F,KAAK,CAAC;EACnE;EACAiK,eAAeA,CAACoC,QAAQ,EAAE;IACtB,IAAI,IAAI,CAACvR,aAAa,IAAI,CAAC,CAAC,IAAI,CAACK,mBAAmB,EAAE;MAClD,IAAI,IAAI,CAACkD,cAAc,CAACsB,MAAM,KAAK,IAAI,CAACxE,mBAAmB,CAACwE,MAAM,EAAE;QAChE,MAAM,IAAI2M,KAAK,CAAC,oDAAoD,CAAC;MACzE,CAAC,MACI;QACD,OAAO,IAAI,CAACnR,mBAAmB;MACnC;IACJ,CAAC,MACI,IAAI,IAAI,CAACL,aAAa,EAAE;MACzB,IAAIuR,QAAQ,EAAE;QACV,IAAI,IAAI,CAAChO,cAAc,KAAKjE,cAAc,CAAC6F,EAAE,EAAE;UAC3C,OAAO,IAAI,CAACsM,WAAW,CAACF,QAAQ,CAAC;QACrC;QACA,IAAI,IAAI,CAAChO,cAAc,KAAKjE,cAAc,CAACqG,QAAQ,EAAE;UACjD,OAAO,IAAI,CAAC+L,gBAAgB,CAACH,QAAQ,CAAC;QAC1C;MACJ;MACA,IAAI,IAAI,CAACrR,oBAAoB,CAAC2E,MAAM,KAAK,IAAI,CAACtB,cAAc,CAACsB,MAAM,EAAE;QACjE,OAAO,IAAI,CAAC3E,oBAAoB;MACpC;MACA,OAAO,IAAI,CAACqD,cAAc,CAACgE,OAAO,CAAC,KAAK,EAAE,IAAI,CAACrH,oBAAoB,CAAC;IACxE;IACA,OAAO,EAAE;EACb;EACAyQ,iBAAiBA,CAAA,EAAG;IAChB,MAAMH,WAAW,GAAG,IAAI,CAACxB,WAAW,EAAEyB,aAAa;IACnD,IAAI,CAACD,WAAW,EAAE;MACd;IACJ;IACA,IAAI,IAAI,CAACzQ,eAAe,IACpB,IAAI,CAACH,MAAM,CAACiF,MAAM,GAAG,IAAI,CAACtB,cAAc,CAACsB,MAAM,GAAG,IAAI,CAAClF,MAAM,CAACkF,MAAM,KAChE2L,WAAW,CAACxP,KAAK,CAACuG,OAAO,CAAC,IAAI,CAACrH,oBAAoB,EAAEZ,cAAc,CAAC6E,YAAY,CAAC,CAC5EU,MAAM,EAAE;MACjB,IAAI,CAAC8M,mBAAmB,GAAG,CAAC,OAAO,EAAErS,cAAc,CAAC6E,YAAY,CAAC;MACjE,IAAI,CAACN,SAAS,CAAC,EAAE,EAAE,IAAI,CAACN,cAAc,CAAC;IAC3C;EACJ;EACA,IAAIoO,mBAAmBA,CAAC,CAACC,IAAI,EAAE5Q,KAAK,CAAC,EAAE;IACnC,IAAI,CAAC,IAAI,CAACkO,SAAS,IAAI,CAAC,IAAI,CAACF,WAAW,EAAE;MACtC;IACJ;IACA;IACA6C,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAAC7C,SAAS,EAAE8C,WAAW,CAAC,IAAI,CAAChD,WAAW,EAAEyB,aAAa,EAAEmB,IAAI,EAAE5Q,KAAK,CAAC,CAAC;EAC3G;EACAiR,0BAA0BA,CAACC,IAAI,EAAE;IAC7B,MAAMC,KAAK,GAAGD,IAAI,CACbjN,KAAK,CAAC3F,cAAc,CAAC6E,YAAY,CAAC,CAClCmI,MAAM,CAAEyD,IAAI,IAAK,IAAI,CAAClF,oBAAoB,CAACkF,IAAI,CAAC,CAAC;IACtD,OAAOoC,KAAK,CAACtN,MAAM;EACvB;EACA6K,UAAUA,CAAC5L,UAAU,EAAE;IACnB,OAAO,IAAI,CAACsO,WAAW,CAAC,IAAI,CAACC,aAAa,CAAC,IAAI,CAACC,aAAa,CAACxO,UAAU,CAAC,CAAC,EAAE,IAAI,CAACrD,iBAAiB,CAACgP,MAAM,CAAC,GAAG,CAAC,CAACA,MAAM,CAAC,IAAI,CAACvP,oBAAoB,CAAC,CAAC;EACrJ;EACAuR,WAAWA,CAACF,QAAQ,EAAE;IAClB,IAAIA,QAAQ,KAAKjS,cAAc,CAAC8P,IAAI,EAAE;MAClC,OAAQ,GAAE,IAAI,CAAClP,oBAAqB,IAAG,IAAI,CAACA,oBAAqB,IAAG,IAAI,CAACA,oBAAqB,IAAG,IAAI,CAACA,oBAAqB,EAAC;IAChI;IACA,MAAMqF,GAAG,GAAG,EAAE;IACd;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+L,QAAQ,CAAC1M,MAAM,EAAEW,CAAC,EAAE,EAAE;MACtC,MAAMxE,KAAK,GAAGuQ,QAAQ,CAAC/L,CAAC,CAAC,IAAIlG,cAAc,CAAC6E,YAAY;MACxD,IAAI,CAACnD,KAAK,EAAE;QACR;MACJ;MACA,IAAIA,KAAK,CAACyE,KAAK,CAAC,KAAK,CAAC,EAAE;QACpBF,GAAG,CAACG,IAAI,CAAC1E,KAAK,CAAC;MACnB;IACJ;IACA,IAAIuE,GAAG,CAACV,MAAM,IAAI,CAAC,EAAE;MACjB,OAAQ,GAAE,IAAI,CAAC3E,oBAAqB,IAAG,IAAI,CAACA,oBAAqB,IAAG,IAAI,CAACA,oBAAqB,EAAC;IACnG;IACA,IAAIqF,GAAG,CAACV,MAAM,GAAG,CAAC,IAAIU,GAAG,CAACV,MAAM,IAAI,CAAC,EAAE;MACnC,OAAQ,GAAE,IAAI,CAAC3E,oBAAqB,IAAG,IAAI,CAACA,oBAAqB,EAAC;IACtE;IACA,IAAIqF,GAAG,CAACV,MAAM,GAAG,CAAC,IAAIU,GAAG,CAACV,MAAM,IAAI,CAAC,EAAE;MACnC,OAAO,IAAI,CAAC3E,oBAAoB;IACpC;IACA,IAAIqF,GAAG,CAACV,MAAM,GAAG,CAAC,IAAIU,GAAG,CAACV,MAAM,IAAI,EAAE,EAAE;MACpC,OAAO,EAAE;IACb;IACA,OAAO,EAAE;EACb;EACA6M,gBAAgBA,CAACH,QAAQ,EAAE;IACvB,MAAMgB,GAAG,GAAI,GAAE,IAAI,CAACrS,oBAAqB,GAAE,IAAI,CAACA,oBAAqB,GAAE,IAAI,CAACA,oBAAqB,EAAC,GAC7F,IAAG,IAAI,CAACA,oBAAqB,GAAE,IAAI,CAACA,oBAAqB,GAAE,IAAI,CAACA,oBAAqB,EAAC,GACtF,IAAG,IAAI,CAACA,oBAAqB,GAAE,IAAI,CAACA,oBAAqB,GAAE,IAAI,CAACA,oBAAqB,EAAC,GACtF,IAAG,IAAI,CAACA,oBAAqB,GAAE,IAAI,CAACA,oBAAqB,EAAC;IAC/D,MAAMsS,IAAI,GAAI,GAAE,IAAI,CAACtS,oBAAqB,GAAE,IAAI,CAACA,oBAAqB,EAAC,GAClE,IAAG,IAAI,CAACA,oBAAqB,GAAE,IAAI,CAACA,oBAAqB,GAAE,IAAI,CAACA,oBAAqB,EAAC,GACtF,IAAG,IAAI,CAACA,oBAAqB,GAAE,IAAI,CAACA,oBAAqB,GAAE,IAAI,CAACA,oBAAqB,EAAC,GACtF,IAAG,IAAI,CAACA,oBAAqB,GAAE,IAAI,CAACA,oBAAqB,GAAE,IAAI,CAACA,oBAAqB,GAAE,IAAI,CAACA,oBAAqB,EAAC,GAClH,IAAG,IAAI,CAACA,oBAAqB,GAAE,IAAI,CAACA,oBAAqB,EAAC;IAC/D,IAAIqR,QAAQ,KAAKjS,cAAc,CAAC8P,IAAI,EAAE;MAClC,OAAOmD,GAAG;IACd;IACA,MAAMhN,GAAG,GAAG,EAAE;IACd;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+L,QAAQ,CAAC1M,MAAM,EAAEW,CAAC,EAAE,EAAE;MACtC,MAAMxE,KAAK,GAAGuQ,QAAQ,CAAC/L,CAAC,CAAC,IAAIlG,cAAc,CAAC6E,YAAY;MACxD,IAAI,CAACnD,KAAK,EAAE;QACR;MACJ;MACA,IAAIA,KAAK,CAACyE,KAAK,CAAC,KAAK,CAAC,EAAE;QACpBF,GAAG,CAACG,IAAI,CAAC1E,KAAK,CAAC;MACnB;IACJ;IACA,IAAIuE,GAAG,CAACV,MAAM,IAAI,CAAC,EAAE;MACjB,OAAO0N,GAAG,CAAC3N,KAAK,CAACW,GAAG,CAACV,MAAM,EAAE0N,GAAG,CAAC1N,MAAM,CAAC;IAC5C;IACA,IAAIU,GAAG,CAACV,MAAM,GAAG,CAAC,IAAIU,GAAG,CAACV,MAAM,IAAI,CAAC,EAAE;MACnC,OAAO0N,GAAG,CAAC3N,KAAK,CAACW,GAAG,CAACV,MAAM,GAAG,CAAC,EAAE0N,GAAG,CAAC1N,MAAM,CAAC;IAChD;IACA,IAAIU,GAAG,CAACV,MAAM,GAAG,CAAC,IAAIU,GAAG,CAACV,MAAM,IAAI,CAAC,EAAE;MACnC,OAAO0N,GAAG,CAAC3N,KAAK,CAACW,GAAG,CAACV,MAAM,GAAG,CAAC,EAAE0N,GAAG,CAAC1N,MAAM,CAAC;IAChD;IACA,IAAIU,GAAG,CAACV,MAAM,GAAG,CAAC,IAAIU,GAAG,CAACV,MAAM,GAAG,EAAE,EAAE;MACnC,OAAO0N,GAAG,CAAC3N,KAAK,CAACW,GAAG,CAACV,MAAM,GAAG,CAAC,EAAE0N,GAAG,CAAC1N,MAAM,CAAC;IAChD;IACA,IAAIU,GAAG,CAACV,MAAM,KAAK,EAAE,EAAE;MACnB,OAAO,EAAE;IACb;IACA,IAAIU,GAAG,CAACV,MAAM,KAAK,EAAE,EAAE;MACnB,IAAI0M,QAAQ,CAAC1M,MAAM,KAAK,EAAE,EAAE;QACxB,OAAO2N,IAAI,CAAC5N,KAAK,CAAC,EAAE,EAAE4N,IAAI,CAAC3N,MAAM,CAAC;MACtC;MACA,OAAO2N,IAAI,CAAC5N,KAAK,CAAC,EAAE,EAAE4N,IAAI,CAAC3N,MAAM,CAAC;IACtC;IACA,IAAIU,GAAG,CAACV,MAAM,GAAG,EAAE,IAAIU,GAAG,CAACV,MAAM,IAAI,EAAE,EAAE;MACrC,OAAO2N,IAAI,CAAC5N,KAAK,CAACW,GAAG,CAACV,MAAM,GAAG,CAAC,EAAE2N,IAAI,CAAC3N,MAAM,CAAC;IAClD;IACA,OAAO,EAAE;EACb;EACA;AACJ;AACA;EACI6L,iBAAiBA,CAACzB,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IACxC,MAAMwD,YAAY,GAAGxD,QAAQ,EAAEyD,aAAa,EAAEC,UAAU;IACxD,IAAI,CAACF,YAAY,EAAEC,aAAa,EAAE;MAC9B,OAAOzD,QAAQ,CAACyD,aAAa;IACjC,CAAC,MACI;MACD,OAAO,IAAI,CAAChC,iBAAiB,CAAC+B,YAAY,CAAC;IAC/C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIpD,iBAAiBA,CAACvL,UAAU,EAAE;IAC1B,MAAM7C,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,GAC1C,IAAI,CAACA,iBAAiB,GACrBuK,CAAC,IAAKA,CAAC;IACd,IAAI,CAACkD,YAAY,GAAG,KAAK;IACzB,IAAI,CAACJ,WAAW,GAAG,KAAK;IACxB,IAAIhI,KAAK,CAACC,OAAO,CAAC,IAAI,CAACpG,qBAAqB,CAAC,EAAE;MAC3C,IAAI,CAAC2O,QAAQ,CAAC7N,iBAAiB,CAAC,IAAI,CAAC2R,SAAS,CAAC,IAAI,CAACC,aAAa,CAAC,IAAI,CAACT,WAAW,CAAC,IAAI,CAACC,aAAa,CAAC,IAAI,CAACC,aAAa,CAACxO,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC3D,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1K,CAAC,MACI,IAAI,IAAI,CAACA,qBAAqB,IAC9B,CAAC,IAAI,CAACA,qBAAqB,IAAI,IAAI,CAACP,MAAM,KAAKkE,UAAW,EAAE;MAC7D,IAAI,CAACgL,QAAQ,CAAC7N,iBAAiB,CAAC,IAAI,CAAC2R,SAAS,CAAC,IAAI,CAACC,aAAa,CAAC,IAAI,CAACR,aAAa,CAAC,IAAI,CAACC,aAAa,CAACxO,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5H,CAAC,MACI;MACD,IAAI,CAACgL,QAAQ,CAAC7N,iBAAiB,CAAC,IAAI,CAAC2R,SAAS,CAAC9O,UAAU,CAAC,CAAC,CAAC;IAChE;EACJ;EACA8O,SAASA,CAAC5R,KAAK,EAAE;IACb,IAAI,CAAC,IAAI,CAACkN,aAAa,IAAIlN,KAAK,KAAK1B,cAAc,CAAC6E,YAAY,EAAE;MAC9D,OAAOnD,KAAK;IAChB;IACA,IAAI,IAAI,CAACuC,cAAc,CAACqC,UAAU,CAACtG,cAAc,CAACsD,SAAS,CAAC,KACvD,IAAI,CAAChC,QAAQ,IAAI,CAAC,IAAI,CAACT,qBAAqB,CAAC,EAAE;MAChD,OAAOa,KAAK;IAChB;IACA,IAAImQ,MAAM,CAACnQ,KAAK,CAAC,CAAC6D,MAAM,GAAG,EAAE,IAAI,IAAI,CAACtB,cAAc,CAACqC,UAAU,CAACtG,cAAc,CAACsD,SAAS,CAAC,EAAE;MACvF,OAAOuO,MAAM,CAACnQ,KAAK,CAAC;IACxB;IACA,MAAM8R,GAAG,GAAGvK,MAAM,CAACvH,KAAK,CAAC;IACzB,IAAI,IAAI,CAACuC,cAAc,CAACqC,UAAU,CAACtG,cAAc,CAACsD,SAAS,CAAC,IAAI2F,MAAM,CAACqD,KAAK,CAACkH,GAAG,CAAC,EAAE;MAC/E,MAAMhI,GAAG,GAAGqG,MAAM,CAACnQ,KAAK,CAAC,CAACuG,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;MAC3C,OAAOgB,MAAM,CAACuC,GAAG,CAAC;IACtB;IACA,OAAOvC,MAAM,CAACqD,KAAK,CAACkH,GAAG,CAAC,GAAG9R,KAAK,GAAG8R,GAAG;EAC1C;EACAV,WAAWA,CAACpR,KAAK,EAAE+R,0BAA0B,EAAE;IAC3C,IAAI,IAAI,CAACxP,cAAc,CAACqC,UAAU,CAACtG,cAAc,CAACkD,OAAO,CAAC,IACtDxB,KAAK,CAACwF,QAAQ,CAAClH,cAAc,CAAC+F,GAAG,CAAC,EAAE;MACpC,OAAOrE,KAAK;IAChB;IACA,OAAOA,KAAK,GACNA,KAAK,CAACuG,OAAO,CAAC,IAAI,CAACyL,gBAAgB,CAACD,0BAA0B,CAAC,EAAEzT,cAAc,CAAC6E,YAAY,CAAC,GAC7FnD,KAAK;EACf;EACAsR,aAAaA,CAACtR,KAAK,EAAE;IACjB,IAAI,CAAC,IAAI,CAACpB,MAAM,EAAE;MACd,OAAOoB,KAAK;IAChB;IACA,OAAOA,KAAK,GAAGA,KAAK,CAACuG,OAAO,CAAC,IAAI,CAAC3H,MAAM,EAAEN,cAAc,CAAC6E,YAAY,CAAC,GAAGnD,KAAK;EAClF;EACAqR,aAAaA,CAACrR,KAAK,EAAE;IACjB,IAAI,CAAC,IAAI,CAACrB,MAAM,EAAE;MACd,OAAOqB,KAAK;IAChB;IACA,OAAOA,KAAK,GAAGA,KAAK,CAACuG,OAAO,CAAC,IAAI,CAAC5H,MAAM,EAAEL,cAAc,CAAC6E,YAAY,CAAC,GAAGnD,KAAK;EAClF;EACAiS,uBAAuBA,CAAC5O,MAAM,EAAE;IAC5B,IAAI5D,iBAAiB,GAAG6F,KAAK,CAACC,OAAO,CAAC,IAAI,CAACpG,qBAAqB,CAAC,GAC3D,IAAI,CAACM,iBAAiB,CAAC6L,MAAM,CAAEd,CAAC,IAAK;MACnC,OAAO,IAAI,CAACrL,qBAAqB,CAACqG,QAAQ,CAACgF,CAAC,CAAC;IACjD,CAAC,CAAC,GACA,IAAI,CAAC/K,iBAAiB;IAC5B,IAAI,CAAC,IAAI,CAACiD,uBAAuB,IAC7B,IAAI,CAACwP,qBAAqB,CAAC,CAAC,IAC5B7O,MAAM,CAACmC,QAAQ,CAAClH,cAAc,CAAC6T,WAAW,CAAC,IAC3C,IAAI,CAAC5P,cAAc,CAACiD,QAAQ,CAAClH,cAAc,CAAC6I,WAAW,CAAC,EAAE;MAC1D1H,iBAAiB,GAAGA,iBAAiB,CAAC6L,MAAM,CAAE7B,IAAI,IAAKA,IAAI,KAAKnL,cAAc,CAAC6T,WAAW,CAAC;IAC/F;IACA,OAAO,IAAI,CAACf,WAAW,CAAC/N,MAAM,EAAE5D,iBAAiB,CAAC;EACtD;EACAuS,gBAAgBA,CAACD,0BAA0B,EAAE;IACzC,OAAO,IAAI1R,MAAM,CAAC0R,0BAA0B,CAACxH,GAAG,CAAEwE,IAAI,IAAM,KAAIA,IAAK,EAAC,CAAC,CAACnF,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC;EAC5F;EACAwI,0BAA0BA,CAACpS,KAAK,EAAE;IAC9B,MAAMqS,OAAO,GAAG/M,KAAK,CAACC,OAAO,CAAC,IAAI,CAACzG,aAAa,CAAC,GAC3C,IAAI,CAACA,aAAa,GAClB,CAAC,IAAI,CAACA,aAAa,CAAC;IAC1B,OAAOkB,KAAK,CAACuG,OAAO,CAAC,IAAI,CAACyL,gBAAgB,CAACK,OAAO,CAAC,EAAE/T,cAAc,CAAC+F,GAAG,CAAC;EAC5E;EACAwN,aAAaA,CAACxO,MAAM,EAAE;IAClB,IAAIiP,eAAe,GAAGjP,MAAM;IAC5B,IAAIiP,eAAe,KAAKhU,cAAc,CAAC6E,YAAY,EAAE;MACjD,OAAOmP,eAAe;IAC1B;IACA,IAAI,IAAI,CAAC/P,cAAc,CAACqC,UAAU,CAACtG,cAAc,CAACkD,OAAO,CAAC,IACtD,IAAI,CAAC1C,aAAa,KAAKR,cAAc,CAAC6H,KAAK,EAAE;MAC7CmM,eAAe,GAAGA,eAAe,CAAC/L,OAAO,CAACjI,cAAc,CAAC6H,KAAK,EAAE7H,cAAc,CAAC+F,GAAG,CAAC;IACvF;IACA,MAAMkO,kBAAkB,GAAG,IAAI,CAACC,2BAA2B,CAAC,IAAI,CAACjQ,cAAc,CAAC;IAChF,MAAMkQ,cAAc,GAAG,IAAI,CAAChT,iBAAiB,CAACoE,MAAM,KAAK,CAAC,GACpD,IAAI,CAACoO,uBAAuB,CAACK,eAAe,CAAC,GAC7C,IAAI,CAACF,0BAA0B,CAAC,IAAI,CAACH,uBAAuB,CAACK,eAAe,CAAC,CAAC;IACpF,IAAI,CAAC,IAAI,CAACpF,aAAa,EAAE;MACrB,OAAOuF,cAAc;IACzB;IACA,IAAIF,kBAAkB,EAAE;MACpB,IAAID,eAAe,KAAK,IAAI,CAACxT,aAAa,EAAE;QACxC,OAAO,IAAI;MACf;MACA,IAAI2T,cAAc,CAAC5O,MAAM,GAAG,EAAE,EAAE;QAC5B,OAAOsM,MAAM,CAACsC,cAAc,CAAC;MACjC;MACA,OAAO,IAAI,CAACC,eAAe,CAAC,IAAI,CAACnQ,cAAc,EAAEkQ,cAAc,CAAC;IACpE,CAAC,MACI;MACD,OAAOA,cAAc;IACzB;EACJ;EACAP,qBAAqBA,CAAA,EAAG;IACpB,KAAK,MAAMS,GAAG,IAAI,IAAI,CAACxS,QAAQ,EAAE;MAC7B;MACA,IAAI,IAAI,CAACA,QAAQ,CAACwS,GAAG,CAAC,IAAI,IAAI,CAACxS,QAAQ,CAACwS,GAAG,CAAC,EAAEC,cAAc,CAAC,SAAS,CAAC,EAAE;QACrE,MAAMC,aAAa,GAAG,IAAI,CAAC1S,QAAQ,CAACwS,GAAG,CAAC,EAAEvS,OAAO,CAAC4D,QAAQ,CAAC,CAAC;QAC5D,MAAM5D,OAAO,GAAG,IAAI,CAACD,QAAQ,CAACwS,GAAG,CAAC,EAAEvS,OAAO;QAC3C,IAAIyS,aAAa,EAAErN,QAAQ,CAAClH,cAAc,CAAC6T,WAAW,CAAC,IACnD/R,OAAO,EAAE4J,IAAI,CAAC,IAAI,CAACzH,cAAc,CAAC,EAAE;UACpC,OAAO,IAAI;QACf;MACJ;IACJ;IACA,OAAO,KAAK;EAChB;EACA;EACAiQ,2BAA2BA,CAACM,aAAa,EAAE;IACvC,MAAMC,OAAO,GAAGD,aAAa,CAACrO,KAAK,CAAC,IAAIpE,MAAM,CAAE,sBAAqB,CAAC,CAAC;IACvE,OAAO0S,OAAO,GAAGxL,MAAM,CAACwL,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;EAC9C;EACAL,eAAeA,CAACM,mBAAmB,EAAEP,cAAc,EAAE;IACjD,MAAMF,kBAAkB,GAAG,IAAI,CAACxN,YAAY,CAACiO,mBAAmB,CAAC;IACjE,IAAIhT,KAAK,GAAGyS,cAAc;IAC1B,IAAIO,mBAAmB,CAAC/N,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IACnC,IAAI,CAACrF,QAAQ,IAAI2H,MAAM,CAACgL,kBAAkB,CAAC,GAAG,CAAE,EAAE;MACnD,IAAI,IAAI,CAACzT,aAAa,KAAKR,cAAc,CAAC6H,KAAK,IAAI,IAAI,CAACvG,QAAQ,EAAE;QAC9DI,KAAK,GAAGA,KAAK,CAACuG,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;MACnC;MACA,OAAO,IAAI,CAAC3G,QAAQ,GACd2H,MAAM,CAACvH,KAAK,CAAC,CAACiT,OAAO,CAAC1L,MAAM,CAACgL,kBAAkB,CAAC,CAAC,GACjDhL,MAAM,CAACvH,KAAK,CAAC,CAACiT,OAAO,CAAC,CAAC,CAAC;IAClC;IACA,OAAO,IAAI,CAAC/C,cAAc,CAAClQ,KAAK,CAAC;EACrC;EACAkT,qBAAqBA,CAACC,OAAO,EAAE;IAC3B,OAASA,OAAO,CAAC1O,KAAK,CAAC,UAAU,CAAC,IAC9B0O,OAAO,CACFlP,KAAK,CAAC3F,cAAc,CAAC6E,YAAY,CAAC,CAClCiQ,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,EAAEzH,KAAK,KAAK;MACnC,IAAI,CAAC+B,MAAM,GACP0F,OAAO,KAAKhV,cAAc,CAACiV,mBAAmB,GAAG1H,KAAK,GAAG,IAAI,CAAC+B,MAAM;MACxE,IAAI0F,OAAO,KAAKhV,cAAc,CAACkV,oBAAoB,EAAE;QACjD,OAAO,IAAI,CAACrK,gBAAgB,CAACmK,OAAO,CAAC,GAAGD,KAAK,GAAGC,OAAO,GAAGD,KAAK;MACnE;MACA,IAAI,CAACxF,IAAI,GAAGhC,KAAK;MACjB,MAAM4H,YAAY,GAAGlM,MAAM,CAAC4L,OAAO,CAACvP,KAAK,CAAC,IAAI,CAACgK,MAAM,GAAG,CAAC,EAAE,IAAI,CAACC,IAAI,CAAC,CAAC;MACtE,MAAM6F,WAAW,GAAG,IAAIpO,KAAK,CAACmO,YAAY,GAAG,CAAC,CAAC,CAAC7J,IAAI,CAACuJ,OAAO,CAAC,IAAI,CAACvF,MAAM,GAAG,CAAC,CAAC,CAAC;MAC9E,IAAIuF,OAAO,CAACvP,KAAK,CAAC,CAAC,EAAE,IAAI,CAACgK,MAAM,CAAC,CAAC/J,MAAM,GAAG,CAAC,IACxCsP,OAAO,CAAC3N,QAAQ,CAAClH,cAAc,CAACqV,QAAQ,CAAC,EAAE;QAC3C,MAAMC,OAAO,GAAGT,OAAO,CAACvP,KAAK,CAAC,CAAC,EAAE,IAAI,CAACgK,MAAM,GAAG,CAAC,CAAC;QACjD,OAAOgG,OAAO,CAACpO,QAAQ,CAAClH,cAAc,CAACiV,mBAAmB,CAAC,GACrDF,KAAK,GAAGK,WAAW,GACnBE,OAAO,GAAGP,KAAK,GAAGK,WAAW;MACvC,CAAC,MACI;QACD,OAAOL,KAAK,GAAGK,WAAW;MAC9B;IACJ,CAAC,EAAE,EAAE,CAAC,IACNP,OAAO;EACf;EACAU,0BAA0BA,CAAA,EAAG;IACzB,OAAQ,GAAG,CAAEzD,cAAc,CAAC,CAAC,CAACjL,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EACjD;EACA,OAAOoH,IAAI;IAAA,IAAAuH,2BAAA;IAAA,gBAAAC,uBAAAtH,CAAA;MAAA,QAAAqH,2BAAA,KAAAA,2BAAA,GAzoB8EzW,EAAE,CAAA2W,qBAAA,CAyoBQ/G,cAAc,IAAAR,CAAA,IAAdQ,cAAc;IAAA;EAAA;EACjH,OAAOP,KAAK,kBA1oB6ErP,EAAE,CAAAsP,kBAAA;IAAAC,KAAA,EA0oBYK,cAAc;IAAAJ,OAAA,EAAdI,cAAc,CAAAV;EAAA;AACzH;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KA5oB6FzP,EAAE,CAAA0P,iBAAA,CA4oBJE,cAAc,EAAc,CAAC;IAC5GD,IAAI,EAAEvP;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,SAASwW,cAAcA,CAAA,EAAG;EACtB,MAAMC,UAAU,GAAG1W,MAAM,CAACiB,cAAc,CAAC;EACzC,MAAM0V,WAAW,GAAG3W,MAAM,CAACgB,UAAU,CAAC;EACtC,OAAO2V,WAAW,YAAYC,QAAQ,GAChC;IAAE,GAAGF,UAAU;IAAE,GAAGC,WAAW,CAAC;EAAE,CAAC,GACnC;IAAE,GAAGD,UAAU;IAAE,GAAGC;EAAY,CAAC;AAC3C;AACA,SAASE,cAAcA,CAACF,WAAW,EAAE;EACjC,OAAO,CACH;IACIG,OAAO,EAAE9V,UAAU;IACnB+V,QAAQ,EAAEJ;EACd,CAAC,EACD;IACIG,OAAO,EAAE7V,cAAc;IACvB8V,QAAQ,EAAE7V;EACd,CAAC,EACD;IACI4V,OAAO,EAAE/V,eAAe;IACxBiW,UAAU,EAAEP;EAChB,CAAC,EACDhH,cAAc,CACjB;AACL;AACA,SAASwH,yBAAyBA,CAACN,WAAW,EAAE;EAC5C,OAAOvW,wBAAwB,CAACyW,cAAc,CAACF,WAAW,CAAC,CAAC;AAChE;AAEA,MAAMO,gBAAgB,CAAC;EACnBxD,IAAI,GAAGrT,KAAK,CAAC,EAAE,CAAC;EAChB4B,iBAAiB,GAAG5B,KAAK,CAAC,EAAE,CAAC;EAC7BsC,QAAQ,GAAGtC,KAAK,CAAC,CAAC,CAAC,CAAC;EACpBe,MAAM,GAAGf,KAAK,CAAC,EAAE,CAAC;EAClBc,MAAM,GAAGd,KAAK,CAAC,EAAE,CAAC;EAClBgB,iBAAiB,GAAGhB,KAAK,CAAC,GAAG,CAAC;EAC9BiB,aAAa,GAAGjB,KAAK,CAAC,GAAG,CAAC;EAC1BsB,qBAAqB,GAAGtB,KAAK,CAAC,IAAI,CAAC;EACnCuB,WAAW,GAAGvB,KAAK,CAAC,IAAI,CAAC;EACzBmB,aAAa,GAAGnB,KAAK,CAAC,IAAI,CAAC;EAC3BqB,oBAAoB,GAAGrB,KAAK,CAAC,IAAI,CAAC;EAClCwB,mBAAmB,GAAGxB,KAAK,CAAC,IAAI,CAAC;EACjCkB,eAAe,GAAGlB,KAAK,CAAC,IAAI,CAAC;EAC7B2B,UAAU,GAAG3B,KAAK,CAAC,IAAI,CAAC;EACxByB,cAAc,GAAGzB,KAAK,CAAC,EAAE,CAAC;EAC1B0B,oBAAoB,GAAG1B,KAAK,CAAC,IAAI,CAAC;EAClC6B,gBAAgB,GAAG7B,KAAK,CAAC,IAAI,CAAC;EAC9B+B,QAAQ,GAAG/B,KAAK,CAAC,IAAI,CAAC;EACtBiC,mBAAmB,GAAGjC,KAAK,CAAC,IAAI,CAAC;EACjC8B,GAAG,GAAG9B,KAAK,CAAC,IAAI,CAAC;EACjBkC,gBAAgB,GAAGlC,KAAK,CAAC,IAAI,CAAC;EAC9BoC,iBAAiB,GAAGpC,KAAK,CAAC,IAAI,CAAC;EAC/BgC,sBAAsB,GAAGhC,KAAK,CAAC,IAAI,CAAC;EACpCoB,aAAa,GAAGpB,KAAK,CAAC,IAAI,CAAC;EAC3BqC,UAAU,GAAGpC,MAAM,CAAC,CAAC;EACrB6W,UAAU,GAAG5W,MAAM,CAAC,EAAE,CAAC;EACvB6W,WAAW,GAAG7W,MAAM,CAAC,EAAE,CAAC;EACxB8W,SAAS,GAAG9W,MAAM,CAAC,IAAI,CAAC;EACxB+W,KAAK,GAAG/W,MAAM,CAAC,EAAE,CAAC;EAClBgX,oBAAoB,GAAGhX,MAAM,CAAC,EAAE,CAAC;EACjCiX,WAAW,GAAGjX,MAAM,CAAC,KAAK,CAAC;EAC3BkX,UAAU,GAAGlX,MAAM,CAAC,KAAK,CAAC;EAC1B;EACAmX,YAAY,GAAGnX,MAAM,CAAC,KAAK,CAAC;EAC5BoX,YAAY,GAAG3X,MAAM,CAACyP,cAAc,EAAE;IAAEmI,IAAI,EAAE;EAAK,CAAC,CAAC;EACrDnH,QAAQ,GAAGzQ,MAAM,CAACW,QAAQ,CAAC;EAC3B+D,OAAO,GAAG1E,MAAM,CAACe,eAAe,CAAC;EACjC;EACAuP,QAAQ,GAAIC,CAAC,IAAK,CAAE,CAAC;EACrB;EACAsH,OAAO,GAAGA,CAAA,KAAM,CAAE,CAAC;EACnBC,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAM;MAAErE,IAAI;MAAEzR,iBAAiB;MAAEU,QAAQ;MAAEvB,MAAM;MAAED,MAAM;MAAEE,iBAAiB;MAAEC,aAAa;MAAEK,qBAAqB;MAAEC,WAAW;MAAEJ,aAAa;MAAEE,oBAAoB;MAAEG,mBAAmB;MAAEN,eAAe;MAAES,UAAU;MAAEF,cAAc;MAAEC,oBAAoB;MAAEG,gBAAgB;MAAEE,QAAQ;MAAEE,mBAAmB;MAAEH,GAAG;MAAEI,gBAAgB;MAAEE,iBAAiB;MAAEJ,sBAAsB;MAAEZ;IAAe,CAAC,GAAGsW,OAAO;IAC3Y,IAAIrE,IAAI,EAAE;MACN,IAAIA,IAAI,CAACzD,YAAY,KAAKyD,IAAI,CAAC1D,aAAa,IAAI,CAAC0D,IAAI,CAACsE,WAAW,EAAE;QAC/D,IAAI,CAACL,YAAY,CAAC7H,WAAW,GAAG,IAAI;MACxC;MACA,IAAI4D,IAAI,CAACzD,YAAY,IAAIyD,IAAI,CAACzD,YAAY,CAACxJ,KAAK,CAAC3F,cAAc,CAACmX,EAAE,CAAC,CAAC5R,MAAM,GAAG,CAAC,EAAE;QAC5E,IAAI,CAACkR,oBAAoB,CAACW,GAAG,CAACxE,IAAI,CAACzD,YAAY,CAACxJ,KAAK,CAAC3F,cAAc,CAACmX,EAAE,CAAC,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UACpF,OAAOD,CAAC,CAAC/R,MAAM,GAAGgS,CAAC,CAAChS,MAAM;QAC9B,CAAC,CAAC,CAAC;QACH,IAAI,CAACiS,QAAQ,CAAC,CAAC;MACnB,CAAC,MACI;QACD,IAAI,CAACf,oBAAoB,CAACW,GAAG,CAAC,EAAE,CAAC;QACjC,IAAI,CAACf,UAAU,CAACe,GAAG,CAACxE,IAAI,CAACzD,YAAY,IAAInP,cAAc,CAAC6E,YAAY,CAAC;QACrE,IAAI,CAACgS,YAAY,CAAC5S,cAAc,GAAG,IAAI,CAACoS,UAAU,CAAC,CAAC;MACxD;IACJ;IACA,IAAIlV,iBAAiB,EAAE;MACnB,IAAI,CAACA,iBAAiB,CAACgO,YAAY,IAAI,CAACnI,KAAK,CAACC,OAAO,CAAC9F,iBAAiB,CAACgO,YAAY,CAAC,EAAE;QACnF;MACJ,CAAC,MACI;QACD,IAAI,CAAC0H,YAAY,CAAC1V,iBAAiB,GAAGA,iBAAiB,CAACgO,YAAY,IAAI,EAAE;MAC9E;IACJ;IACA,IAAIlO,oBAAoB,EAAE;MACtB,IAAI,CAAC4V,YAAY,CAAC5V,oBAAoB,GAAGA,oBAAoB,CAACkO,YAAY;MAC1E,IAAI,IAAI,CAAC0H,YAAY,CAAC5V,oBAAoB,EAAE;QACxC,IAAI,CAAC4V,YAAY,CAAC1V,iBAAiB,GAAG,IAAI,CAAC0V,YAAY,CAAC1V,iBAAiB,CAAC6L,MAAM,CAAEyK,CAAC,IAAKA,CAAC,KAAKzX,cAAc,CAAC4F,KAAK,CAAC;MACvH;IACJ;IACA;IACA,IAAI/D,QAAQ,IAAIA,QAAQ,CAACsN,YAAY,EAAE;MACnC,IAAI,CAAC0H,YAAY,CAAChV,QAAQ,GAAGA,QAAQ,CAACsN,YAAY;IACtD;IACA,IAAI9N,GAAG,IAAIA,GAAG,CAAC8N,YAAY,EAAE;MACzB,IAAI,CAAC0H,YAAY,CAACxV,GAAG,GAAGA,GAAG,CAAC8N,YAAY;IAC5C;IACA,IAAIxO,aAAa,EAAE;MACf,IAAI,CAACkW,YAAY,CAAClW,aAAa,GAAGA,aAAa,CAACwO,YAAY;IAChE;IACA,IAAI7O,MAAM,EAAE;MACR,IAAI,CAACuW,YAAY,CAACvW,MAAM,GAAGA,MAAM,CAAC6O,YAAY;IAClD;IACA,IAAI9O,MAAM,EAAE;MACR,IAAI,CAACwW,YAAY,CAACxW,MAAM,GAAGA,MAAM,CAAC8O,YAAY;IAClD;IACA,IAAI5O,iBAAiB,EAAE;MACnB,IAAI,CAACsW,YAAY,CAACtW,iBAAiB,GAAGA,iBAAiB,CAAC4O,YAAY;MACpE,IAAI5O,iBAAiB,CAAC2O,aAAa,IAAI3O,iBAAiB,CAAC4O,YAAY,EAAE;QACnE,MAAMuI,qBAAqB,GAAG,IAAI,CAACb,YAAY,CAACrW,aAAa;QAC7D,IAAID,iBAAiB,CAAC4O,YAAY,KAAK,IAAI,CAAC0H,YAAY,CAACrW,aAAa,EAAE;UACpE,IAAI,CAACqW,YAAY,CAACrW,aAAa,GAC3BD,iBAAiB,CAAC4O,YAAY,KAAKnP,cAAc,CAAC6H,KAAK,GACjD7H,cAAc,CAAC+F,GAAG,GAClB/F,cAAc,CAAC6H,KAAK;QAClC;QACA,IAAI,IAAI,CAACgP,YAAY,CAAChW,qBAAqB,KAAK,IAAI,EAAE;UAClD,IAAI,CAACgW,YAAY,CAAC1V,iBAAiB,GAAG,IAAI,CAACyC,OAAO,CAACzC,iBAAiB;QACxE;QACA,IAAI,OAAOuW,qBAAqB,KAAK,QAAQ,IACzC,OAAO,IAAI,CAACb,YAAY,CAACrW,aAAa,KAAK,QAAQ,EAAE;UACrD,IAAI,CAAC8V,WAAW,CAACc,GAAG,CAAC,IAAI,CAACd,WAAW,CAAC,CAAC,CAClC3Q,KAAK,CAACpF,iBAAiB,CAAC2O,aAAa,CAAC,CACtC5D,IAAI,CAAC,EAAE,CAAC,CACRrD,OAAO,CAACyP,qBAAqB,EAAE,IAAI,CAACb,YAAY,CAACrW,aAAa,CAAC,CAAC;UACrE,IAAI,CAACqW,YAAY,CAAC3S,WAAW,GAAG,IAAI,CAACoS,WAAW,CAAC,CAAC;QACtD;QACA,IAAI,CAACO,YAAY,CAACzH,YAAY,GAAG,IAAI;MACzC;IACJ;IACA,IAAI5O,aAAa,EAAE;MACf,IAAI,CAACqW,YAAY,CAACrW,aAAa,GAAGA,aAAa,CAAC2O,YAAY;IAChE;IACA,IAAItO,qBAAqB,EAAE;MACvB,IAAI,CAACgW,YAAY,CAAChW,qBAAqB,GAAGA,qBAAqB,CAACsO,YAAY;IAChF;IACA,IAAIrO,WAAW,EAAE;MACb,IAAI,CAAC+V,YAAY,CAAC/V,WAAW,GAAGA,WAAW,CAACqO,YAAY;MACxD,IAAIrO,WAAW,CAACoO,aAAa,KAAK,IAAI,IAAIpO,WAAW,CAACqO,YAAY,KAAK,KAAK,EAAE;QAC1E,IAAI,CAACmH,WAAW,CAACc,GAAG,CAAC,IAAI,CAACP,YAAY,CAAC3S,WAAW,CAAC;MACvD;IACJ;IACA,IAAIxD,aAAa,EAAE;MACf,IAAI,CAACmW,YAAY,CAACnW,aAAa,GAAGA,aAAa,CAACyO,YAAY;MAC5D,IAAIzO,aAAa,CAACwO,aAAa,KAAK,KAAK,IACrCxO,aAAa,CAACyO,YAAY,KAAK,IAAI,IACnC,IAAI,CAACwH,UAAU,CAAC,CAAC,EAAE;QACnBgB,qBAAqB,CAAC,MAAM;UACxB,IAAI,CAACd,YAAY,CAACnH,WAAW,EAAEyB,aAAa,CAACyG,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC;MACN;IACJ;IACA,IAAIhX,oBAAoB,EAAE;MACtB,IAAI,CAACiW,YAAY,CAACjW,oBAAoB,GAAGA,oBAAoB,CAACuO,YAAY;IAC9E;IACA,IAAIpO,mBAAmB,EAAE;MACrB,IAAI,CAAC8V,YAAY,CAAC9V,mBAAmB,GAAGA,mBAAmB,CAACoO,YAAY;IAC5E;IACA,IAAI1O,eAAe,EAAE;MACjB,IAAI,CAACoW,YAAY,CAACpW,eAAe,GAAGA,eAAe,CAAC0O,YAAY;IACpE;IACA,IAAIjO,UAAU,EAAE;MACZ,IAAI,CAAC2V,YAAY,CAAC3V,UAAU,GAAGA,UAAU,CAACiO,YAAY;IAC1D;IACA,IAAInO,cAAc,EAAE;MAChB,IAAI,CAAC6V,YAAY,CAAC7V,cAAc,GAAGA,cAAc,CAACmO,YAAY;IAClE;IACA,IAAI/N,gBAAgB,EAAE;MAClB,IAAI,CAACyV,YAAY,CAACzV,gBAAgB,GAAGA,gBAAgB,CAAC+N,YAAY;IACtE;IACA,IAAI7N,QAAQ,EAAE;MACV,IAAI,CAACuV,YAAY,CAACvV,QAAQ,GAAGA,QAAQ,CAAC6N,YAAY;IACtD;IACA,IAAI3N,mBAAmB,EAAE;MACrB,IAAI,CAACqV,YAAY,CAACrV,mBAAmB,GAAGA,mBAAmB,CAAC2N,YAAY;IAC5E;IACA,IAAI1N,gBAAgB,EAAE;MAClB,IAAI,CAACoV,YAAY,CAACpV,gBAAgB,GAAGA,gBAAgB,CAAC0N,YAAY;IACtE;IACA,IAAIxN,iBAAiB,EAAE;MACnB,IAAI,CAACkV,YAAY,CAAClV,iBAAiB,GAAGA,iBAAiB,CAACwN,YAAY;IACxE;IACA,IAAI5N,sBAAsB,EAAE;MACxB,IAAI,CAACsV,YAAY,CAACtV,sBAAsB,GAAGA,sBAAsB,CAAC4N,YAAY;IAClF;IACA,IAAI,CAAC0I,UAAU,CAAC,CAAC;EACrB;EACAC,QAAQA,CAAC;IAAEpW;EAAM,CAAC,EAAE;IAChB,MAAM0D,cAAc,GAAG,OAAO1D,KAAK,KAAK,QAAQ,GAAGmQ,MAAM,CAACnQ,KAAK,CAAC,GAAGA,KAAK;IACxE,MAAMqW,SAAS,GAAG,IAAI,CAAC1B,UAAU,CAAC,CAAC;IACnC,IAAI,CAAC,IAAI,CAACQ,YAAY,CAAC3V,UAAU,IAAI,CAAC6W,SAAS,EAAE;MAC7C,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAAClB,YAAY,CAACxS,OAAO,EAAE;MAC3B,OAAO,IAAI,CAAC2T,sBAAsB,CAAC5S,cAAc,CAAC;IACtD;IACA,IAAI,IAAI,CAACyR,YAAY,CAACvS,YAAY,EAAE;MAChC,OAAO,IAAI,CAAC0T,sBAAsB,CAAC5S,cAAc,CAAC;IACtD;IACA,IAAI2S,SAAS,CAACzR,UAAU,CAACtG,cAAc,CAACsD,SAAS,CAAC,EAAE;MAChD,OAAO,IAAI;IACf;IACA,IAAIL,iBAAiB,CAACiE,QAAQ,CAAC6Q,SAAS,CAAC,EAAE;MACvC,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAAClB,YAAY,CAACpW,eAAe,EAAE;MACnC,OAAO,IAAI;IACf;IACA,IAAIoC,SAAS,CAACqE,QAAQ,CAAC6Q,SAAS,CAAC,EAAE;MAC/B,OAAO,IAAI,CAACE,aAAa,CAAC7S,cAAc,CAAC;IAC7C;IACA,IAAI2S,SAAS,KAAK/X,cAAc,CAACkY,UAAU,EAAE;MACzC,MAAMC,YAAY,GAAG,sBAAsB;MAC3C,IAAI,CAACA,YAAY,CAACzM,IAAI,CAACtG,cAAc,CAAC,IAAIA,cAAc,EAAE;QACtD,OAAO,IAAI,CAAC4S,sBAAsB,CAAC5S,cAAc,CAAC;MACtD,CAAC,MACI;QACD,OAAO,IAAI;MACf;IACJ;IACA,IAAIA,cAAc,IAAIA,cAAc,CAACG,MAAM,IAAI,CAAC,EAAE;MAC9C,IAAI6S,YAAY,GAAG,CAAC;MACpB,IAAIL,SAAS,CAAC7Q,QAAQ,CAAClH,cAAc,CAACiV,mBAAmB,CAAC,IACtD8C,SAAS,CAAC7Q,QAAQ,CAAClH,cAAc,CAACkV,oBAAoB,CAAC,EAAE;QACzD,MAAMmD,yBAAyB,GAAGN,SAAS,CAACzS,KAAK,CAACyS,SAAS,CAACpR,OAAO,CAAC3G,cAAc,CAACiV,mBAAmB,CAAC,GAAG,CAAC,EAAE8C,SAAS,CAACpR,OAAO,CAAC3G,cAAc,CAACkV,oBAAoB,CAAC,CAAC;QACpK,OAAOmD,yBAAyB,KAAKxG,MAAM,CAACzM,cAAc,CAACG,MAAM,CAAC,GAC5D,IAAI,GACJ,IAAI,CAACyS,sBAAsB,CAAC5S,cAAc,CAAC;MACrD;MACA,IAAI2S,SAAS,CAACzR,UAAU,CAACtG,cAAc,CAACkD,OAAO,CAAC,EAAE;QAC9C,OAAO,IAAI;MACf;MACA,KAAK,MAAMmR,GAAG,IAAI,IAAI,CAACwC,YAAY,CAAChV,QAAQ,EAAE;QAC1C,IAAI,IAAI,CAACgV,YAAY,CAAChV,QAAQ,CAACwS,GAAG,CAAC,EAAErS,QAAQ,EAAE;UAC3C,IAAI+V,SAAS,CAACpR,OAAO,CAAC0N,GAAG,CAAC,KAAK0D,SAAS,CAACO,WAAW,CAACjE,GAAG,CAAC,EAAE;YACvD,MAAMkE,GAAG,GAAGR,SAAS,CAChBpS,KAAK,CAAC3F,cAAc,CAAC6E,YAAY,CAAC,CAClCmI,MAAM,CAAE9G,CAAC,IAAKA,CAAC,KAAKmO,GAAG,CAAC,CACxB/I,IAAI,CAACtL,cAAc,CAAC6E,YAAY,CAAC;YACtCuT,YAAY,IAAIG,GAAG,CAAChT,MAAM;UAC9B,CAAC,MACI,IAAIwS,SAAS,CAACpR,OAAO,CAAC0N,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;YACpC+D,YAAY,EAAE;UAClB;UACA,IAAIL,SAAS,CAACpR,OAAO,CAAC0N,GAAG,CAAC,KAAK,CAAC,CAAC,IAC7BjP,cAAc,CAACG,MAAM,IAAIwS,SAAS,CAACpR,OAAO,CAAC0N,GAAG,CAAC,EAAE;YACjD,OAAO,IAAI;UACf;UACA,IAAI+D,YAAY,KAAKL,SAAS,CAACxS,MAAM,EAAE;YACnC,OAAO,IAAI;UACf;QACJ;MACJ;MACA,IAAKwS,SAAS,CAACpR,OAAO,CAAC3G,cAAc,CAAC6I,WAAW,CAAC,GAAG,CAAC,IAClDzD,cAAc,CAACG,MAAM,GAAGwS,SAAS,CAACpR,OAAO,CAAC3G,cAAc,CAAC6I,WAAW,CAAC,IACpEkP,SAAS,CAACpR,OAAO,CAAC3G,cAAc,CAAC+I,eAAe,CAAC,GAAG,CAAC,IAClD3D,cAAc,CAACG,MAAM,GAAGwS,SAAS,CAACpR,OAAO,CAAC3G,cAAc,CAAC+I,eAAe,CAAE,EAAE;QAChF,OAAO,IAAI,CAACiP,sBAAsB,CAAC5S,cAAc,CAAC;MACtD;MACA,IAAI2S,SAAS,CAACpR,OAAO,CAAC3G,cAAc,CAAC6I,WAAW,CAAC,KAAK,CAAC,CAAC,IACpDkP,SAAS,CAACpR,OAAO,CAAC3G,cAAc,CAAC+I,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE;QAC1D,MAAMyP,KAAK,GAAGT,SAAS,CAACpS,KAAK,CAAC,GAAG,CAAC;QAClC,MAAMJ,MAAM,GAAG,IAAI,CAACsR,YAAY,CAAChW,qBAAqB,GAChDkX,SAAS,CAACxS,MAAM,GACd,IAAI,CAACsR,YAAY,CAAClE,0BAA0B,CAACoF,SAAS,CAAC,GACvDK,YAAY,GACd,IAAI,CAAC9X,MAAM,CAAC,CAAC,GACTyX,SAAS,CAACxS,MAAM,GAAG,IAAI,CAACjF,MAAM,CAAC,CAAC,CAACiF,MAAM,GAAG6S,YAAY,GACtDL,SAAS,CAACxS,MAAM,GAAG6S,YAAY;QACzC,IAAII,KAAK,CAACjT,MAAM,KAAK,CAAC,EAAE;UACpB,IAAIH,cAAc,CAACG,MAAM,GAAGA,MAAM,EAAE;YAChC,OAAO,IAAI,CAACyS,sBAAsB,CAAC5S,cAAc,CAAC;UACtD;QACJ;QACA,IAAIoT,KAAK,CAACjT,MAAM,GAAG,CAAC,EAAE;UAClB,MAAMkT,cAAc,GAAGD,KAAK,CAACA,KAAK,CAACjT,MAAM,GAAG,CAAC,CAAC;UAC9C,IAAIkT,cAAc,IACd,IAAI,CAAC5B,YAAY,CAAC1V,iBAAiB,CAAC+F,QAAQ,CAACuR,cAAc,CAAC,CAAC,CAAC,CAAC,IAC/D5G,MAAM,CAACzM,cAAc,CAAC,CAAC8B,QAAQ,CAACuR,cAAc,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IACxD,CAAC,IAAI,CAAC5X,qBAAqB,CAAC,CAAC,EAAE;YAC/B,MAAM6X,OAAO,GAAGhX,KAAK,CAACiE,KAAK,CAAC8S,cAAc,CAAC,CAAC,CAAC,CAAC;YAC9C,OAAOC,OAAO,CAACA,OAAO,CAACnT,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM,KAAKkT,cAAc,CAAClT,MAAM,GAAG,CAAC,GACjE,IAAI,GACJ,IAAI,CAACyS,sBAAsB,CAAC5S,cAAc,CAAC;UACrD,CAAC,MACI,IAAI,CAAEqT,cAAc,IACrB,CAAC,IAAI,CAAC5B,YAAY,CAAC1V,iBAAiB,CAAC+F,QAAQ,CAACuR,cAAc,CAAC,CAAC,CAAC,CAAC,IAChE,CAACA,cAAc,IACf,IAAI,CAAC5B,YAAY,CAAChW,qBAAqB,KACvCuE,cAAc,CAACG,MAAM,IAAIA,MAAM,GAAG,CAAC,EAAE;YACrC,OAAO,IAAI;UACf,CAAC,MACI;YACD,OAAO,IAAI,CAACyS,sBAAsB,CAAC5S,cAAc,CAAC;UACtD;QACJ;MACJ;MACA,IAAI2S,SAAS,CAACpR,OAAO,CAAC3G,cAAc,CAAC6I,WAAW,CAAC,KAAK,CAAC,IACnDkP,SAAS,CAACpR,OAAO,CAAC3G,cAAc,CAAC+I,eAAe,CAAC,KAAK,CAAC,EAAE;QACzD,OAAO,IAAI;MACf;IACJ;IACA,IAAIrH,KAAK,EAAE;MACP,IAAI,CAACE,UAAU,CAAC+W,IAAI,CAAC,CAAC;MACtB,OAAO,IAAI;IACf;IACA,OAAO,IAAI;EACf;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAAClC,WAAW,CAACU,GAAG,CAAC,IAAI,CAAC;EAC9B;EACAyB,OAAOA,CAAA,EAAG;IACN,IAAI,CAAClC,UAAU,CAACS,GAAG,CAAC,IAAI,CAAC;EAC7B;EACA0B,aAAaA,CAACpX,KAAK,EAAE;IACjB;IACA,IAAI,CAACA,KAAK,KAAK1B,cAAc,CAAC6E,YAAY,IACtCnD,KAAK,KAAK,IAAI,IACd,OAAOA,KAAK,KAAK,WAAW,KAC5B,IAAI,CAACmV,YAAY,CAAC3S,WAAW,EAAE;MAC/B,IAAI,CAAC2S,YAAY,CAAC3S,WAAW,GAAG,IAAI,CAAC2S,YAAY,CAACrG,cAAc,CAACxQ,cAAc,CAAC6E,YAAY,CAAC;IACjG;EACJ;EACAkU,OAAOA,CAACC,CAAC,EAAE;IACP;IACA,IAAI,IAAI,CAACpC,YAAY,CAAC,CAAC,EAAE;MACrB;IACJ;IACA,MAAMqC,EAAE,GAAGD,CAAC,CAACE,MAAM;IACnB,MAAMC,gBAAgB,GAAG,IAAI,CAACtC,YAAY,CAACpV,gBAAgB,GACrD,IAAI,CAACoV,YAAY,CAACpV,gBAAgB,CAACwX,EAAE,CAACvX,KAAK,CAAC,GAC5CuX,EAAE,CAACvX,KAAK;IACd,IAAIuX,EAAE,CAACvK,IAAI,KAAK,QAAQ,EAAE;MACtB,IAAI,OAAOyK,gBAAgB,KAAK,QAAQ,IAAI,OAAOA,gBAAgB,KAAK,QAAQ,EAAE;QAC9EF,EAAE,CAACvX,KAAK,GAAGyX,gBAAgB,CAACzT,QAAQ,CAAC,CAAC;QACtC,IAAI,CAAC4Q,WAAW,CAACc,GAAG,CAAC6B,EAAE,CAACvX,KAAK,CAAC;QAC9B,IAAI,CAAC8V,QAAQ,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,CAACnB,UAAU,CAAC,CAAC,EAAE;UACpB,IAAI,CAAC7G,QAAQ,CAACyJ,EAAE,CAACvX,KAAK,CAAC;UACvB;QACJ;QACA,IAAI+C,QAAQ,GAAGwU,EAAE,CAACG,cAAc,KAAK,CAAC,GAChCH,EAAE,CAACG,cAAc,GAAG,IAAI,CAACvC,YAAY,CAACvW,MAAM,CAACiF,MAAM,GACnD0T,EAAE,CAACG,cAAc;QACvB,IAAI,IAAI,CAAC1Y,aAAa,CAAC,CAAC,IACpB,IAAI,CAACa,sBAAsB,CAAC,CAAC,IAC7B,IAAI,CAACsV,YAAY,CAACjW,oBAAoB,CAAC2E,MAAM,KAAK,CAAC,EAAE;UACrD,MAAMlF,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;UAC5B,MAAMC,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;UAC5B,MAAMqI,WAAW,GAAGsQ,EAAE,CAACvX,KAAK,CAAC4D,KAAK,CAACb,QAAQ,GAAG,CAAC,EAAEA,QAAQ,CAAC;UAC1D,MAAM4U,YAAY,GAAG/Y,MAAM,CAACiF,MAAM;UAClC,MAAM+T,YAAY,GAAG,IAAI,CAACzC,YAAY,CAAC/N,gBAAgB,CAACH,WAAW,EAAE,IAAI,CAACkO,YAAY,CAAC5S,cAAc,CAACQ,QAAQ,GAAG,CAAC,GAAG4U,YAAY,CAAC,IAC9HrZ,cAAc,CAAC6E,YAAY,CAAC;UAChC,MAAM0U,qBAAqB,GAAG,IAAI,CAAC1C,YAAY,CAAC/N,gBAAgB,CAACH,WAAW,EAAE,IAAI,CAACkO,YAAY,CAAC5S,cAAc,CAACQ,QAAQ,GAAG,CAAC,GAAG4U,YAAY,CAAC,IACvIrZ,cAAc,CAAC6E,YAAY,CAAC;UAChC,MAAM2U,oBAAoB,GAAG,IAAI,CAAC3C,YAAY,CAAC/H,QAAQ,KAAK,IAAI,CAAC+H,YAAY,CAAC9H,MAAM;UACpF,MAAMD,QAAQ,GAAG7F,MAAM,CAAC,IAAI,CAAC4N,YAAY,CAAC/H,QAAQ,CAAC,GAAGuK,YAAY;UAClE,MAAMtK,MAAM,GAAG9F,MAAM,CAAC,IAAI,CAAC4N,YAAY,CAAC9H,MAAM,CAAC,GAAGsK,YAAY;UAC9D,MAAMI,iBAAiB,GAAG,IAAI,CAACjD,KAAK,CAAC,CAAC,KAAKxW,cAAc,CAAC0Z,SAAS,IAC/D,IAAI,CAAClD,KAAK,CAAC,CAAC,KAAKxW,cAAc,CAAC2Z,MAAM;UAC1C,IAAIF,iBAAiB,EAAE;YACnB,IAAI,CAACD,oBAAoB,EAAE;cACvB,IAAI,IAAI,CAAC3C,YAAY,CAAC/H,QAAQ,KAAKuK,YAAY,EAAE;gBAC7C,IAAI,CAACxC,YAAY,CAAC3S,WAAW,GAAI,GAAE5D,MAAO,GAAE,IAAI,CAACuW,YAAY,CAAChI,WAAW,CAACvJ,KAAK,CAAC,CAAC,EAAEyJ,MAAM,CAAE,GAAE,IAAI,CAACuH,WAAW,CAAC,CAAC,CAAC3Q,KAAK,CAACrF,MAAM,CAAC,CAACgL,IAAI,CAAC,EAAE,CAAE,EAAC;cAC5I,CAAC,MACI,IAAI,IAAI,CAACuL,YAAY,CAAC/H,QAAQ,KAC/B,IAAI,CAAC+H,YAAY,CAAChI,WAAW,CAACtJ,MAAM,GAAG8T,YAAY,EAAE;gBACrD,IAAI,CAACxC,YAAY,CAAC3S,WAAW,GAAI,GAAE,IAAI,CAACoS,WAAW,CAAC,CAAE,GAAE,IAAI,CAACO,YAAY,CAAChI,WAAW,CAACvJ,KAAK,CAACwJ,QAAQ,EAAEC,MAAM,CAAE,EAAC;cACnH,CAAC,MACI;gBACD,IAAI,CAAC8H,YAAY,CAAC3S,WAAW,GAAI,GAAE5D,MAAO,GAAE,IAAI,CAACgW,WAAW,CAAC,CAAC,CACzD3Q,KAAK,CAACrF,MAAM,CAAC,CACbgL,IAAI,CAAC,EAAE,CAAC,CACRhG,KAAK,CAAC,CAAC,EAAEwJ,QAAQ,CAAE,GAAE,IAAI,CAAC+H,YAAY,CAAChI,WAAW,CAACvJ,KAAK,CAACwJ,QAAQ,EAAEC,MAAM,CAAE,GAAE,IAAI,CAAC8H,YAAY,CAAC3S,WAAW,CAACoB,KAAK,CAACyJ,MAAM,GAAGsK,YAAY,EAAE,IAAI,CAACxC,YAAY,CAAChI,WAAW,CAACtJ,MAAM,GAAG8T,YAAY,CAAE,GAAEhZ,MAAO,EAAC;cAChN;YACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAACwW,YAAY,CAAC1V,iBAAiB,CAAC+F,QAAQ,CAAC,IAAI,CAAC2P,YAAY,CAAC5S,cAAc,CAACqB,KAAK,CAACb,QAAQ,GAAG4U,YAAY,EAAE5U,QAAQ,GAAG,CAAC,GAAG4U,YAAY,CAAC,CAAC,IAChJG,oBAAoB,EAAE;cACtB,IAAI1K,QAAQ,KAAK,CAAC,IAAIxO,MAAM,EAAE;gBAC1B,IAAI,CAACuW,YAAY,CAAC3S,WAAW,GAAI,GAAE5D,MAAO,GAAE,IAAI,CAACuW,YAAY,CAACjW,oBAAqB,GAAEqY,EAAE,CAACvX,KAAK,CACxFiE,KAAK,CAACrF,MAAM,CAAC,CACbgL,IAAI,CAAC,EAAE,CAAC,CACR3F,KAAK,CAACtF,MAAM,CAAC,CACbiL,IAAI,CAAC,EAAE,CAAE,GAAEjL,MAAO,EAAC;gBACxBoE,QAAQ,GAAGA,QAAQ,GAAG,CAAC;cAC3B,CAAC,MACI;gBACD,MAAMmV,KAAK,GAAGX,EAAE,CAACvX,KAAK,CAACmF,SAAS,CAAC,CAAC,EAAEpC,QAAQ,CAAC;gBAC7C,MAAMoV,KAAK,GAAGZ,EAAE,CAACvX,KAAK,CAACmF,SAAS,CAACpC,QAAQ,CAAC;gBAC1C,IAAI,CAACoS,YAAY,CAAC3S,WAAW,GAAI,GAAE0V,KAAM,GAAE,IAAI,CAAC/C,YAAY,CAACjW,oBAAqB,GAAEiZ,KAAM,EAAC;cAC/F;YACJ;YACApV,QAAQ,GAAG,IAAI,CAAC+R,KAAK,CAAC,CAAC,KAAKxW,cAAc,CAAC2Z,MAAM,GAAGlV,QAAQ,GAAG,CAAC,GAAGA,QAAQ;UAC/E;UACA,IAAI,CAACgV,iBAAiB,EAAE;YACpB,IAAI,CAACH,YAAY,IAAI,CAACC,qBAAqB,IAAIC,oBAAoB,EAAE;cACjE/U,QAAQ,GAAGwE,MAAM,CAACgQ,EAAE,CAACG,cAAc,CAAC,GAAG,CAAC;YAC5C,CAAC,MACI,IAAI,IAAI,CAACvC,YAAY,CAAC1V,iBAAiB,CAAC+F,QAAQ,CAAC+R,EAAE,CAACvX,KAAK,CAAC4D,KAAK,CAACb,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,IACzF8U,qBAAqB,IACrB,CAAC,IAAI,CAAC1C,YAAY,CAAC1V,iBAAiB,CAAC+F,QAAQ,CAAC+R,EAAE,CAACvX,KAAK,CAAC4D,KAAK,CAACb,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE;cAC3F,IAAI,CAACoS,YAAY,CAAC3S,WAAW,GAAI,GAAE+U,EAAE,CAACvX,KAAK,CAAC4D,KAAK,CAAC,CAAC,EAAEb,QAAQ,GAAG,CAAC,CAAE,GAAEwU,EAAE,CAACvX,KAAK,CAAC4D,KAAK,CAACb,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAE,GAAEkE,WAAY,GAAEsQ,EAAE,CAACvX,KAAK,CAAC4D,KAAK,CAACb,QAAQ,GAAG,CAAC,CAAE,EAAC;cAC1JA,QAAQ,GAAGA,QAAQ,GAAG,CAAC;YAC3B,CAAC,MACI,IAAI6U,YAAY,EAAE;cACnB,IAAIL,EAAE,CAACvX,KAAK,CAAC6D,MAAM,KAAK,CAAC,IAAId,QAAQ,KAAK,CAAC,EAAE;gBACzC,IAAI,CAACoS,YAAY,CAAC3S,WAAW,GAAI,GAAE5D,MAAO,GAAEqI,WAAY,GAAE,IAAI,CAACkO,YAAY,CAAChI,WAAW,CAACvJ,KAAK,CAAC,CAAC,EAAE,IAAI,CAACuR,YAAY,CAAChI,WAAW,CAACtJ,MAAM,CAAE,GAAElF,MAAO,EAAC;cACrJ,CAAC,MACI;gBACD,IAAI,CAACwW,YAAY,CAAC3S,WAAW,GAAI,GAAE+U,EAAE,CAACvX,KAAK,CAAC4D,KAAK,CAAC,CAAC,EAAEb,QAAQ,GAAG,CAAC,CAAE,GAAEkE,WAAY,GAAEsQ,EAAE,CAACvX,KAAK,CACtF4D,KAAK,CAACb,QAAQ,GAAG,CAAC,CAAC,CACnBkB,KAAK,CAACtF,MAAM,CAAC,CACbiL,IAAI,CAAC,EAAE,CAAE,GAAEjL,MAAO,EAAC;cAC5B;YACJ,CAAC,MACI,IAAIC,MAAM,IACX2Y,EAAE,CAACvX,KAAK,CAAC6D,MAAM,KAAK,CAAC,IACrBd,QAAQ,GAAG4U,YAAY,KAAK,CAAC,IAC7B,IAAI,CAACxC,YAAY,CAAC/N,gBAAgB,CAACmQ,EAAE,CAACvX,KAAK,EAAE,IAAI,CAACmV,YAAY,CAAC5S,cAAc,CAACQ,QAAQ,GAAG,CAAC,GAAG4U,YAAY,CAAC,IACtGrZ,cAAc,CAAC6E,YAAY,CAAC,EAAE;cAClC,IAAI,CAACgS,YAAY,CAAC3S,WAAW,GAAI,GAAE5D,MAAO,GAAE2Y,EAAE,CAACvX,KAAM,GAAE,IAAI,CAACmV,YAAY,CAAChI,WAAW,CAACvJ,KAAK,CAAC,CAAC,EAAE,IAAI,CAACuR,YAAY,CAAChI,WAAW,CAACtJ,MAAM,CAAE,GAAElF,MAAO,EAAC;YAClJ;UACJ;QACJ;QACA,IAAIyZ,UAAU,GAAG,CAAC;QAClB,IAAI7U,cAAc,GAAG,KAAK;QAC1B,IAAI,IAAI,CAACuR,KAAK,CAAC,CAAC,KAAKxW,cAAc,CAAC2Z,MAAM,IAAI3Z,cAAc,CAACsD,SAAS,EAAE;UACpE,IAAI,CAACuT,YAAY,CAACzS,uBAAuB,GAAG,IAAI;QACpD;QACA,IAAI,IAAI,CAACkS,WAAW,CAAC,CAAC,CAAC/Q,MAAM,IAAI,IAAI,CAACsR,YAAY,CAAC5S,cAAc,CAACsB,MAAM,GAAG,CAAC,IACxE,IAAI,CAACiR,KAAK,CAAC,CAAC,KAAKxW,cAAc,CAAC0Z,SAAS,IACzC,IAAI,CAAC7C,YAAY,CAAC5S,cAAc,KAAKjE,cAAc,CAACuD,iBAAiB,IACrEkB,QAAQ,GAAG,EAAE,EAAE;UACf,MAAMkE,WAAW,GAAG,IAAI,CAAC2N,WAAW,CAAC,CAAC,CAAChR,KAAK,CAACb,QAAQ,GAAG,CAAC,EAAEA,QAAQ,CAAC;UACpEwU,EAAE,CAACvX,KAAK,GACJ,IAAI,CAAC4U,WAAW,CAAC,CAAC,CAAChR,KAAK,CAAC,CAAC,EAAEb,QAAQ,GAAG,CAAC,CAAC,GACrCkE,WAAW,GACX,IAAI,CAAC2N,WAAW,CAAC,CAAC,CAAChR,KAAK,CAACb,QAAQ,GAAG,CAAC,CAAC;QAClD;QACA,IAAI,IAAI,CAACoS,YAAY,CAAC5S,cAAc,KAAKjE,cAAc,CAACuD,iBAAiB,IACrE,IAAI,CAACnC,gBAAgB,CAAC,CAAC,EAAE;UACzB,IAAKqD,QAAQ,GAAG,CAAC,IAAIwE,MAAM,CAACgQ,EAAE,CAACvX,KAAK,CAAC,GAAG,EAAE,IAAIuH,MAAM,CAACgQ,EAAE,CAACvX,KAAK,CAAC,GAAG,EAAE,IAC9D+C,QAAQ,KAAK,CAAC,IAAIwE,MAAM,CAACgQ,EAAE,CAACvX,KAAK,CAAC4D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAG,EAAE;YACvDb,QAAQ,GAAGA,QAAQ,GAAG,CAAC;UAC3B;QACJ;QACA,IAAI,IAAI,CAACoS,YAAY,CAAC5S,cAAc,KAAKjE,cAAc,CAAC8C,qBAAqB,IACzE,IAAI,CAACzB,GAAG,CAAC,CAAC,EAAE;UACZ,IAAI,IAAI,CAACqV,WAAW,CAAC,CAAC,IAAIuC,EAAE,CAACvX,KAAK,CAAC4D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKtF,cAAc,CAAC+Z,WAAW,EAAE;YAC3Ed,EAAE,CAACvX,KAAK,GAAGuX,EAAE,CAACvX,KAAK,CAAC4D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG2T,EAAE,CAACvX,KAAK,CAAC4D,KAAK,CAAC,CAAC,EAAE2T,EAAE,CAACvX,KAAK,CAAC6D,MAAM,CAAC;UACxE;UACA0T,EAAE,CAACvX,KAAK,GACJuX,EAAE,CAACvX,KAAK,KAAK1B,cAAc,CAAC+Z,WAAW,GACjC/Z,cAAc,CAAC0H,WAAW,GAC1BuR,EAAE,CAACvX,KAAK;QACtB;QACA,IAAI,CAACmV,YAAY,CAAC5F,iBAAiB,CAACxM,QAAQ,EAAE,IAAI,CAACiS,WAAW,CAAC,CAAC,EAAE,IAAI,CAACF,KAAK,CAAC,CAAC,KAAKxW,cAAc,CAAC0Z,SAAS,IACvG,IAAI,CAAClD,KAAK,CAAC,CAAC,KAAKxW,cAAc,CAAC2Z,MAAM,EAAE,CAACzU,KAAK,EAAE8U,eAAe,KAAK;UACpE,IAAI,CAACtD,WAAW,CAACU,GAAG,CAAC,KAAK,CAAC;UAC3B0C,UAAU,GAAG5U,KAAK;UAClBD,cAAc,GAAG+U,eAAe;QACpC,CAAC,CAAC;QACF;QACA,IAAI,IAAI,CAAC5I,iBAAiB,CAAC,CAAC,KAAK6H,EAAE,EAAE;UACjC;QACJ;QACA,IAAI,IAAI,CAACpC,YAAY,CAAC7S,eAAe,EAAE;UACnCS,QAAQ,GAAGA,QAAQ,GAAG,CAAC;UACvB,IAAI,CAACoS,YAAY,CAAC7S,eAAe,GAAG,KAAK;QAC7C;QACA;QACA,IAAI,IAAI,CAACyS,oBAAoB,CAAC,CAAC,CAAClR,MAAM,EAAE;UACpC,IAAI,IAAI,CAACiR,KAAK,CAAC,CAAC,KAAKxW,cAAc,CAAC0Z,SAAS,EAAE;YAC3C,MAAMO,oBAAoB,GAAG,IAAI,CAAC9Y,iBAAiB,CAAC,CAAC,CAAC+F,QAAQ,CAAC,IAAI,CAAC2P,YAAY,CAAC3S,WAAW,CAACoB,KAAK,CAACb,QAAQ,GAAG,CAAC,EAAEA,QAAQ,CAAC,CAAC;YAC3H,MAAMyV,sBAAsB,GAAG,IAAI,CAACrD,YAAY,CAACzG,UAAU,CAAC,IAAI,CAACkG,WAAW,CAAC,CAAC,CAAC,EAAE/Q,MAAM,KACnF,IAAI,CAACsR,YAAY,CAACzG,UAAU,CAAC,IAAI,CAACyG,YAAY,CAAC5S,cAAc,CAAC,EAAEsB,MAAM;YAC1E,MAAM4U,mBAAmB,GAAG,IAAI,CAAChZ,iBAAiB,CAAC,CAAC,CAAC+F,QAAQ,CAAC,IAAI,CAAC2P,YAAY,CAAC3S,WAAW,CAACoB,KAAK,CAACb,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAC,CAAC;YAC1H,IAAIyV,sBAAsB,IAAI,CAACC,mBAAmB,EAAE;cAChD1V,QAAQ,GAAGwU,EAAE,CAACG,cAAc,GAAG,CAAC;YACpC,CAAC,MACI;cACD3U,QAAQ,GAAGwV,oBAAoB,GAAGxV,QAAQ,GAAG,CAAC,GAAGA,QAAQ;YAC7D;UACJ,CAAC,MACI;YACDA,QAAQ,GACJwU,EAAE,CAACG,cAAc,KAAK,CAAC,GACjBH,EAAE,CAACG,cAAc,GAAG,IAAI,CAACvC,YAAY,CAACvW,MAAM,CAACiF,MAAM,GACnD0T,EAAE,CAACG,cAAc;UAC/B;QACJ;QACA,IAAI,CAAC7C,SAAS,CAACa,GAAG,CAAC,IAAI,CAACb,SAAS,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAACD,WAAW,CAAC,CAAC,CAAC/Q,MAAM,KAAK,CAAC,GACtE,IAAI,GACJ,IAAI,CAACgR,SAAS,CAAC,CAAC,CAAC;QACvB,IAAI6D,eAAe,GAAG,IAAI,CAAC7D,SAAS,CAAC,CAAC,GAChC,IAAI,CAACD,WAAW,CAAC,CAAC,CAAC/Q,MAAM,GAAGd,QAAQ,GAAGqV,UAAU,GACjDrV,QAAQ,IACL,IAAI,CAAC+R,KAAK,CAAC,CAAC,KAAKxW,cAAc,CAAC0Z,SAAS,IAAI,CAACzU,cAAc,GACvD,CAAC,GACD6U,UAAU,CAAC;QACzB,IAAIM,eAAe,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC,EAAE;UAChDD,eAAe,GACXnB,EAAE,CAACvX,KAAK,KAAK,IAAI,CAACmV,YAAY,CAACrW,aAAa,IAAIyY,EAAE,CAACvX,KAAK,CAAC6D,MAAM,KAAK,CAAC,GAC/D,IAAI,CAAC8U,qBAAqB,CAAC,CAAC,GAAG,CAAC,GAChC,IAAI,CAACA,qBAAqB,CAAC,CAAC;QAC1C;QACA,IAAID,eAAe,GAAG,CAAC,EAAE;UACrBA,eAAe,GAAG,CAAC;QACvB;QACAnB,EAAE,CAACqB,iBAAiB,CAACF,eAAe,EAAEA,eAAe,CAAC;QACtD,IAAI,CAAC7D,SAAS,CAACa,GAAG,CAAC,IAAI,CAAC;MAC5B,CAAC,MACI;QACD;QACAmD,OAAO,CAACC,IAAI,CAAC,oEAAoE,EAAE,OAAOrB,gBAAgB,CAAC;MAC/G;IACJ,CAAC,MACI;MACD,IAAI,CAAC,IAAI,CAAC9C,UAAU,CAAC,CAAC,EAAE;QACpB,IAAI,CAAC7G,QAAQ,CAACyJ,EAAE,CAACvX,KAAK,CAAC;QACvB;MACJ;MACA,IAAI,CAACmV,YAAY,CAAC5F,iBAAiB,CAACgI,EAAE,CAACvX,KAAK,CAAC6D,MAAM,EAAE,IAAI,CAACmR,WAAW,CAAC,CAAC,EAAE,IAAI,CAACF,KAAK,CAAC,CAAC,KAAKxW,cAAc,CAAC0Z,SAAS,IAAI,IAAI,CAAClD,KAAK,CAAC,CAAC,KAAKxW,cAAc,CAAC2Z,MAAM,CAAC;IACjK;EACJ;EACA;EACAc,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC7D,YAAY,CAACQ,GAAG,CAAC,IAAI,CAAC;EAC/B;EACA;EACAsD,gBAAgBA,CAAC1B,CAAC,EAAE;IAChB,IAAI,CAACpC,YAAY,CAACQ,GAAG,CAAC,KAAK,CAAC;IAC5B,IAAI,CAACV,WAAW,CAACU,GAAG,CAAC,IAAI,CAAC;IAC1B,IAAI,CAAC2B,OAAO,CAACC,CAAC,CAAC;EACnB;EACA2B,MAAMA,CAAC3B,CAAC,EAAE;IACN,IAAI,IAAI,CAAC3C,UAAU,CAAC,CAAC,EAAE;MACnB,MAAM4C,EAAE,GAAGD,CAAC,CAACE,MAAM;MACnB,IAAI,IAAI,CAACrC,YAAY,CAACvV,QAAQ,IAC1B2X,EAAE,CAACvX,KAAK,CAAC6D,MAAM,GAAG,CAAC,IACnB,OAAO,IAAI,CAACsR,YAAY,CAACrW,aAAa,KAAK,QAAQ,EAAE;QACrD,MAAMyD,cAAc,GAAG,IAAI,CAAC4S,YAAY,CAAC5S,cAAc;QACvD,MAAMzD,aAAa,GAAG,IAAI,CAACqW,YAAY,CAACrW,aAAa;QACrD,MAAMH,MAAM,GAAG,IAAI,CAACwW,YAAY,CAACxW,MAAM;QACvC,MAAMmG,SAAS,GAAGyC,MAAM,CAAC,IAAI,CAAC4N,YAAY,CAAC5S,cAAc,CAACqB,KAAK,CAACrB,cAAc,CAACsB,MAAM,GAAG,CAAC,EAAEtB,cAAc,CAACsB,MAAM,CAAC,CAAC;QAClH,IAAIiB,SAAS,GAAG,CAAC,EAAE;UACfyS,EAAE,CAACvX,KAAK,GAAGrB,MAAM,GAAG4Y,EAAE,CAACvX,KAAK,CAACiE,KAAK,CAACtF,MAAM,CAAC,CAACiL,IAAI,CAAC,EAAE,CAAC,GAAG2N,EAAE,CAACvX,KAAK;UAC9D,MAAMmM,WAAW,GAAGoL,EAAE,CAACvX,KAAK,CAACiE,KAAK,CAACnF,aAAa,CAAC,CAAC,CAAC,CAAC;UACpDyY,EAAE,CAACvX,KAAK,GAAGuX,EAAE,CAACvX,KAAK,CAACwF,QAAQ,CAAC1G,aAAa,CAAC,GACrCyY,EAAE,CAACvX,KAAK,GACN1B,cAAc,CAAC0H,WAAW,CAACkT,MAAM,CAACpU,SAAS,GAAGqH,WAAW,CAACtI,MAAM,CAAC,GACjElF,MAAM,GACR4Y,EAAE,CAACvX,KAAK,GACNlB,aAAa,GACbR,cAAc,CAAC0H,WAAW,CAACkT,MAAM,CAACpU,SAAS,CAAC,GAC5CnG,MAAM;UACd,IAAI,CAACwW,YAAY,CAAC3S,WAAW,GAAG+U,EAAE,CAACvX,KAAK;QAC5C;MACJ;MACA,IAAI,CAACmV,YAAY,CAACxF,iBAAiB,CAAC,CAAC;IACzC;IACA,IAAI,CAACsF,UAAU,CAACS,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAI,CAACL,OAAO,CAAC,CAAC;EAClB;EACA8D,OAAOA,CAAC7B,CAAC,EAAE;IACP,IAAI,CAAC,IAAI,CAAC3C,UAAU,CAAC,CAAC,EAAE;MACpB;IACJ;IACA,MAAM4C,EAAE,GAAGD,CAAC,CAACE,MAAM;IACnB,MAAM4B,QAAQ,GAAG,CAAC;IAClB,MAAMC,MAAM,GAAG,CAAC;IAChB,IAAI9B,EAAE,KAAK,IAAI,IACXA,EAAE,CAACG,cAAc,KAAK,IAAI,IAC1BH,EAAE,CAACG,cAAc,KAAKH,EAAE,CAAC+B,YAAY,IACrC/B,EAAE,CAACG,cAAc,GAAG,IAAI,CAACvC,YAAY,CAACvW,MAAM,CAACiF,MAAM,IACnDyT,CAAC,CAACiC,OAAO,KAAK,EAAE,EAAE;MAClB,IAAI,IAAI,CAACpE,YAAY,CAACnW,aAAa,IAAI,CAAC,IAAI,CAACa,sBAAsB,CAAC,CAAC,EAAE;QACnE;QACA,IAAI,CAACsV,YAAY,CAAChI,WAAW,GAAG,IAAI,CAACgI,YAAY,CAAChH,eAAe,CAAC,CAAC;QACnE,IAAIoJ,EAAE,CAACqB,iBAAiB,IACpB,IAAI,CAACzD,YAAY,CAACvW,MAAM,GAAG,IAAI,CAACuW,YAAY,CAAChI,WAAW,KAAKoK,EAAE,CAACvX,KAAK,EAAE;UACvE;UACAuX,EAAE,CAACiC,KAAK,CAAC,CAAC;UACVjC,EAAE,CAACqB,iBAAiB,CAACQ,QAAQ,EAAEC,MAAM,CAAC;QAC1C,CAAC,MACI;UACD;UACA,IAAI9B,EAAE,CAACG,cAAc,GAAG,IAAI,CAACvC,YAAY,CAAC3S,WAAW,CAACqB,MAAM,EAAE;YAC1D;YACA0T,EAAE,CAACqB,iBAAiB,CAAC,IAAI,CAACzD,YAAY,CAAC3S,WAAW,CAACqB,MAAM,EAAE,IAAI,CAACsR,YAAY,CAAC3S,WAAW,CAACqB,MAAM,CAAC;UACpG;QACJ;MACJ;IACJ;IACA,MAAM4V,SAAS,GAAGlC,EAAE,KACfA,EAAE,CAACvX,KAAK,KAAK,IAAI,CAACmV,YAAY,CAACvW,MAAM,GAChC,IAAI,CAACuW,YAAY,CAACvW,MAAM,GAAG,IAAI,CAACuW,YAAY,CAAChI,WAAW,GACxDoK,EAAE,CAACvX,KAAK,CAAC;IACnB;IACA,IAAIuX,EAAE,IAAIA,EAAE,CAACvX,KAAK,KAAKyZ,SAAS,EAAE;MAC9BlC,EAAE,CAACvX,KAAK,GAAGyZ,SAAS;IACxB;IACA;IACA,IAAIlC,EAAE,IACFA,EAAE,CAACvK,IAAI,KAAK,QAAQ,IACpB,CAACuK,EAAE,CAACG,cAAc,IAAIH,EAAE,CAAC+B,YAAY,KACjC,IAAI,CAACnE,YAAY,CAACvW,MAAM,CAACiF,MAAM,EAAE;MACrC,MAAM6V,2BAA2B,GAAG,IAAI,CAACvE,YAAY,CAAC5S,cAAc,CAACkC,KAAK,CAAC,IAAIpE,MAAM,CAAE,KAAI,IAAI,CAAC8U,YAAY,CAAC1V,iBAAiB,CAAC8K,GAAG,CAAEwL,CAAC,IAAM,KAAIA,CAAE,EAAC,CAAC,CAACnM,IAAI,CAAC,EAAE,CAAE,IAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC/F,MAAM,IAAI,CAAC;MACnL0T,EAAE,CAACG,cAAc,GAAG,IAAI,CAACvC,YAAY,CAACvW,MAAM,CAACiF,MAAM,GAAG6V,2BAA2B;MACjF;IACJ;IACA;IACA,IAAInC,EAAE,IAAIA,EAAE,CAAC+B,YAAY,GAAG,IAAI,CAACX,qBAAqB,CAAC,CAAC,EAAE;MACtDpB,EAAE,CAAC+B,YAAY,GAAG,IAAI,CAACX,qBAAqB,CAAC,CAAC;IAClD;EACJ;EACAgB,SAASA,CAACrC,CAAC,EAAE;IACT,IAAI,CAAC,IAAI,CAAC3C,UAAU,CAAC,CAAC,EAAE;MACpB;IACJ;IACA,IAAI,IAAI,CAACO,YAAY,CAAC,CAAC,EAAE;MACrB;MACA,IAAIoC,CAAC,CAAC3E,GAAG,KAAK,OAAO,EAAE;QACnB,IAAI,CAACqG,gBAAgB,CAAC1B,CAAC,CAAC;MAC5B;MACA;IACJ;IACA,IAAI,CAACxC,KAAK,CAACY,GAAG,CAAC4B,CAAC,CAACsC,IAAI,GAAGtC,CAAC,CAACsC,IAAI,GAAGtC,CAAC,CAAC3E,GAAG,CAAC;IACvC,MAAM4E,EAAE,GAAGD,CAAC,CAACE,MAAM;IACnB,IAAI,CAAC5C,WAAW,CAACc,GAAG,CAAC6B,EAAE,CAACvX,KAAK,CAAC;IAC9B,IAAI,CAAC8V,QAAQ,CAAC,CAAC;IACf,IAAIyB,EAAE,CAACvK,IAAI,KAAK,QAAQ,EAAE;MACtB,IAAIsK,CAAC,CAAC3E,GAAG,KAAKrU,cAAc,CAACub,QAAQ,EAAE;QACnCvC,CAAC,CAACwC,cAAc,CAAC,CAAC;MACtB;MACA,IAAIxC,CAAC,CAAC3E,GAAG,KAAKrU,cAAc,CAACyb,UAAU,IACnCzC,CAAC,CAAC3E,GAAG,KAAKrU,cAAc,CAAC0Z,SAAS,IAClCV,CAAC,CAAC3E,GAAG,KAAKrU,cAAc,CAAC2Z,MAAM,EAAE;QACjC,IAAIX,CAAC,CAAC3E,GAAG,KAAKrU,cAAc,CAAC0Z,SAAS,IAAIT,EAAE,CAACvX,KAAK,CAAC6D,MAAM,KAAK,CAAC,EAAE;UAC7D0T,EAAE,CAACG,cAAc,GAAGH,EAAE,CAAC+B,YAAY;QACvC;QACA,IAAIhC,CAAC,CAAC3E,GAAG,KAAKrU,cAAc,CAAC0Z,SAAS,IAAIT,EAAE,CAACG,cAAc,KAAK,CAAC,EAAE;UAC/D,MAAMC,YAAY,GAAG,IAAI,CAAC/Y,MAAM,CAAC,CAAC,CAACiF,MAAM;UACzC;UACA,MAAMpE,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAC,CAAC,CAACoE,MAAM,GACnD,IAAI,CAACpE,iBAAiB,CAAC,CAAC,GACxB,IAAI,CAACyC,OAAO,CAACzC,iBAAiB;UACpC,IAAIkY,YAAY,GAAG,CAAC,IAAIJ,EAAE,CAACG,cAAc,IAAIC,YAAY,EAAE;YACvDJ,EAAE,CAACqB,iBAAiB,CAACjB,YAAY,EAAEJ,EAAE,CAAC+B,YAAY,CAAC;UACvD,CAAC,MACI;YACD,IAAI,IAAI,CAAC1E,WAAW,CAAC,CAAC,CAAC/Q,MAAM,KAAK0T,EAAE,CAACG,cAAc,IAC/CH,EAAE,CAACG,cAAc,KAAK,CAAC,EAAE;cACzB,OAAOjY,iBAAiB,CAAC+F,QAAQ,CAAC,CAAC,IAAI,CAACoP,WAAW,CAAC,CAAC,CAAC2C,EAAE,CAACG,cAAc,GAAG,CAAC,CAAC,IACxEpZ,cAAc,CAAC6E,YAAY,EAAEa,QAAQ,CAAC,CAAC,CAAC,KACtC2T,YAAY,IAAI,CAAC,IACfJ,EAAE,CAACG,cAAc,GAAGC,YAAY,IAChCA,YAAY,KAAK,CAAC,CAAC,EAAE;gBACzBJ,EAAE,CAACqB,iBAAiB,CAACrB,EAAE,CAACG,cAAc,GAAG,CAAC,EAAEH,EAAE,CAAC+B,YAAY,CAAC;cAChE;YACJ;UACJ;QACJ;QACA,IAAI,CAACU,wBAAwB,CAACzC,EAAE,CAAC;QACjC,IAAI,IAAI,CAACpC,YAAY,CAACvW,MAAM,CAACiF,MAAM,IAC/B0T,EAAE,CAACG,cAAc,IAAI,IAAI,CAACvC,YAAY,CAACvW,MAAM,CAACiF,MAAM,IACpD0T,EAAE,CAAC+B,YAAY,IAAI,IAAI,CAACnE,YAAY,CAACvW,MAAM,CAACiF,MAAM,EAAE;UACpDyT,CAAC,CAACwC,cAAc,CAAC,CAAC;QACtB;QACA,MAAMG,WAAW,GAAG1C,EAAE,CAACG,cAAc;QACrC,IAAIJ,CAAC,CAAC3E,GAAG,KAAKrU,cAAc,CAAC0Z,SAAS,IAClC,CAACT,EAAE,CAAC2C,QAAQ,IACZD,WAAW,KAAK,CAAC,IACjB1C,EAAE,CAAC+B,YAAY,KAAK/B,EAAE,CAACvX,KAAK,CAAC6D,MAAM,IACnC0T,EAAE,CAACvX,KAAK,CAAC6D,MAAM,KAAK,CAAC,EAAE;UACvB,IAAI,CAACgR,SAAS,CAACa,GAAG,CAAC,IAAI,CAACP,YAAY,CAACvW,MAAM,GAAG,IAAI,CAACuW,YAAY,CAACvW,MAAM,CAACiF,MAAM,GAAG,CAAC,CAAC;UAClF,IAAI,CAACsR,YAAY,CAACtS,SAAS,CAAC,IAAI,CAACsS,YAAY,CAACvW,MAAM,EAAE,IAAI,CAACuW,YAAY,CAAC5S,cAAc,EAAE,IAAI,CAACsS,SAAS,CAAC,CAAC,CAAC;QAC7G;MACJ;MACA,IAAI,CAAC,CAAC,IAAI,CAAClW,MAAM,CAAC,CAAC,IACf,IAAI,CAACA,MAAM,CAAC,CAAC,CAACkF,MAAM,GAAG,CAAC,IACxB,IAAI,CAAC+Q,WAAW,CAAC,CAAC,CAAC/Q,MAAM,GAAG,IAAI,CAAClF,MAAM,CAAC,CAAC,CAACkF,MAAM,GAAG0T,EAAE,CAACG,cAAc,EAAE;QACtEH,EAAE,CAACqB,iBAAiB,CAAC,IAAI,CAAChE,WAAW,CAAC,CAAC,CAAC/Q,MAAM,GAAG,IAAI,CAAClF,MAAM,CAAC,CAAC,CAACkF,MAAM,EAAE,IAAI,CAAC+Q,WAAW,CAAC,CAAC,CAAC/Q,MAAM,CAAC;MACrG,CAAC,MACI,IAAKyT,CAAC,CAACsC,IAAI,KAAK,MAAM,IAAItC,CAAC,CAAC6C,OAAO,IACnC7C,CAAC,CAACsC,IAAI,KAAK,MAAM,IAAItC,CAAC,CAAC8C,OAAQ,CAAC;MAAA,EACnC;QACE7C,EAAE,CAACqB,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAACD,qBAAqB,CAAC,CAAC,CAAC;QACrDrB,CAAC,CAACwC,cAAc,CAAC,CAAC;MACtB;MACA,IAAI,CAAC3E,YAAY,CAAC/H,QAAQ,GAAGmK,EAAE,CAACG,cAAc;MAC9C,IAAI,CAACvC,YAAY,CAAC9H,MAAM,GAAGkK,EAAE,CAAC+B,YAAY;IAC9C;EACJ;EACA;EACMe,UAAUA,CAACC,YAAY,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC3B,IAAIxa,KAAK,GAAGsa,YAAY;MACxB,MAAMva,gBAAgB,GAAGwa,KAAI,CAACpF,YAAY,CAACpV,gBAAgB;MAC3D,IAAI,OAAOC,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAO,IAAIA,KAAK,EAAE;QACjE,IAAI,SAAS,IAAIA,KAAK,EAAE;UACpBua,KAAI,CAACE,gBAAgB,CAAC5L,OAAO,CAAC7O,KAAK,CAAC0a,OAAO,CAAC,CAAC;QACjD;QACA1a,KAAK,GAAGA,KAAK,CAACA,KAAK;MACvB;MACA,IAAIA,KAAK,KAAK,IAAI,EAAE;QAChBA,KAAK,GAAGD,gBAAgB,GAAGA,gBAAgB,CAACC,KAAK,CAAC,GAAGA,KAAK;MAC9D;MACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IACzB,OAAOA,KAAK,KAAK,QAAQ,IACzBA,KAAK,KAAK,IAAI,IACd,OAAOA,KAAK,KAAK,WAAW,EAAE;QAC9B,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,EAAE,EAAE;UAChEua,KAAI,CAACpF,YAAY,CAAC1H,YAAY,GAAG,EAAE;UACnC8M,KAAI,CAACpF,YAAY,CAAC3H,aAAa,GAAG,EAAE;QACxC;QACA,IAAI1K,UAAU,GAAG9C,KAAK;QACtB,IAAI,OAAO8C,UAAU,KAAK,QAAQ,IAC9ByX,KAAI,CAAC5F,UAAU,CAAC,CAAC,CAAC/P,UAAU,CAACtG,cAAc,CAACsD,SAAS,CAAC,EAAE;UACxDkB,UAAU,GAAGqN,MAAM,CAACrN,UAAU,CAAC;UAC/B,MAAM6X,mBAAmB,GAAGJ,KAAI,CAACpF,YAAY,CAACtB,0BAA0B,CAAC,CAAC;UAC1E,IAAI,CAACvO,KAAK,CAACC,OAAO,CAACgV,KAAI,CAACpF,YAAY,CAACrW,aAAa,CAAC,EAAE;YACjDgE,UAAU,GACNyX,KAAI,CAACpF,YAAY,CAACrW,aAAa,KAAK6b,mBAAmB,GACjD7X,UAAU,CAACyD,OAAO,CAACoU,mBAAmB,EAAEJ,KAAI,CAACpF,YAAY,CAACrW,aAAa,CAAC,GACxEgE,UAAU;UACxB;UACA,IAAIyX,KAAI,CAACpF,YAAY,CAACvV,QAAQ,IAC1BkD,UAAU,IACVyX,KAAI,CAACrJ,IAAI,CAAC,CAAC,IACXqJ,KAAI,CAACpb,qBAAqB,CAAC,CAAC,KAAK,KAAK,EAAE;YACxC2D,UAAU,GAAGyX,KAAI,CAACpF,YAAY,CAACzC,eAAe,CAAC6H,KAAI,CAACpF,YAAY,CAAC5S,cAAc,EAAEO,UAAU,CAAC;UAChG;UACA,IAAIyX,KAAI,CAACpF,YAAY,CAACrW,aAAa,KAAKR,cAAc,CAAC6H,KAAK,IACvDb,KAAK,CAACC,OAAO,CAACgV,KAAI,CAACpF,YAAY,CAACrW,aAAa,CAAC,IAC3Cyb,KAAI,CAACpF,YAAY,CAACtW,iBAAiB,KAAKP,cAAc,CAAC+F,GAAI,EAAE;YACjEvB,UAAU,GAAGA,UAAU,CAClBkB,QAAQ,CAAC,CAAC,CACVuC,OAAO,CAACjI,cAAc,CAAC+F,GAAG,EAAE/F,cAAc,CAAC6H,KAAK,CAAC;UAC1D;UACA,IAAIoU,KAAI,CAACrJ,IAAI,CAAC,CAAC,EAAEtM,UAAU,CAACtG,cAAc,CAACsD,SAAS,CAAC,IAAI2Y,KAAI,CAAC3a,QAAQ,CAAC,CAAC,EAAE;YACtEqW,qBAAqB,CAAC,MAAM;cACxBsE,KAAI,CAACpF,YAAY,CAACtS,SAAS,CAACC,UAAU,EAAEkB,QAAQ,CAAC,CAAC,IAAI,EAAE,EAAEuW,KAAI,CAACpF,YAAY,CAAC5S,cAAc,CAAC;YAC/F,CAAC,CAAC;UACN;UACAgY,KAAI,CAACpF,YAAY,CAACjI,aAAa,GAAG,IAAI;QAC1C;QACA,IAAI,OAAOpK,UAAU,KAAK,QAAQ,IAAI9C,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;UAClF8C,UAAU,GAAG,EAAE;QACnB;QACAyX,KAAI,CAAC3F,WAAW,CAACc,GAAG,CAAC5S,UAAU,CAAC;QAChCyX,KAAI,CAACzE,QAAQ,CAAC,CAAC;QACf,IAAKhT,UAAU,IAAIyX,KAAI,CAACpF,YAAY,CAAC5S,cAAc,IAC9CgY,KAAI,CAACpF,YAAY,CAAC5S,cAAc,KAC5BgY,KAAI,CAACpF,YAAY,CAACvW,MAAM,IAAI2b,KAAI,CAACpF,YAAY,CAACnW,aAAa,CAAE,EAAE;UACpE;UACAub,KAAI,CAACpF,YAAY,CAACzH,YAAY,GAAG,IAAI;UACrC6M,KAAI,CAACpF,YAAY,CAACxE,mBAAmB,GAAG,CACpC,OAAO,EACP4J,KAAI,CAACpF,YAAY,CAACtS,SAAS,CAACC,UAAU,EAAEyX,KAAI,CAACpF,YAAY,CAAC5S,cAAc,CAAC,CAC5E;UACD;UACAgY,KAAI,CAACpF,YAAY,CAACzH,YAAY,GAAG,KAAK;QAC1C,CAAC,MACI;UACD6M,KAAI,CAACpF,YAAY,CAACxE,mBAAmB,GAAG,CAAC,OAAO,EAAE7N,UAAU,CAAC;QACjE;QACAyX,KAAI,CAAC3F,WAAW,CAACc,GAAG,CAAC5S,UAAU,CAAC;MACpC,CAAC,MACI;QACD;QACA+V,OAAO,CAACC,IAAI,CAAC,oEAAoE,EAAE,OAAO9Y,KAAK,CAAC;MACpG;IAAC;EACL;EACA4a,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAAC1F,YAAY,CAACrH,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG+M,EAAE;EACnD;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACxF,OAAO,GAAGwF,EAAE;EACrB;EACAnL,iBAAiBA,CAACzB,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IACxC,MAAMwD,YAAY,GAAGxD,QAAQ,EAAEyD,aAAa,EAAEC,UAAU;IACxD,IAAI,CAACF,YAAY,EAAEC,aAAa,EAAE;MAC9B,OAAOzD,QAAQ,CAACyD,aAAa;IACjC,CAAC,MACI;MACD,OAAO,IAAI,CAAChC,iBAAiB,CAAC+B,YAAY,CAAC;IAC/C;EACJ;EACAuI,wBAAwBA,CAACzC,EAAE,EAAE;IACzB,MAAMI,YAAY,GAAG,IAAI,CAAC/Y,MAAM,CAAC,CAAC,CAACiF,MAAM;IACzC,MAAMkX,YAAY,GAAG,IAAI,CAACpc,MAAM,CAAC,CAAC,CAACkF,MAAM;IACzC,MAAMmX,gBAAgB,GAAG,IAAI,CAACpG,WAAW,CAAC,CAAC,CAAC/Q,MAAM;IAClD0T,EAAE,CAACG,cAAc,GAAGuD,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACxD,YAAY,EAAEJ,EAAE,CAACG,cAAc,CAAC,EAAEsD,gBAAgB,GAAGD,YAAY,CAAC;IACxGxD,EAAE,CAAC+B,YAAY,GAAG2B,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACxD,YAAY,EAAEJ,EAAE,CAAC+B,YAAY,CAAC,EAAE0B,gBAAgB,GAAGD,YAAY,CAAC;EACxG;EACA;EACAN,gBAAgBA,CAACW,UAAU,EAAE;IACzB,IAAI,CAACjG,YAAY,CAACxE,mBAAmB,GAAG,CAAC,UAAU,EAAEyK,UAAU,CAAC;EACpE;EACAjF,UAAUA,CAAA,EAAG;IACT,IAAI,CAAChB,YAAY,CAAC5S,cAAc,GAAG,IAAI,CAAC4S,YAAY,CAACjC,qBAAqB,CAAC,IAAI,CAACyB,UAAU,CAAC,CAAC,IAAI,EAAE,CAAC;IACnG,IAAI,CAACQ,YAAY,CAACxE,mBAAmB,GAAG,CACpC,OAAO,EACP,IAAI,CAACwE,YAAY,CAACtS,SAAS,CAAC,IAAI,CAAC+R,WAAW,CAAC,CAAC,EAAE,IAAI,CAACO,YAAY,CAAC5S,cAAc,CAAC,CACpF;EACL;EACAgU,aAAaA,CAACvW,KAAK,EAAE;IACjB,MAAMqb,UAAU,GAAG,IAAI,CAAC1G,UAAU,CAAC,CAAC,CAC/B1Q,KAAK,CAAC3F,cAAc,CAAC6E,YAAY,CAAC,CAClCmI,MAAM,CAAEpK,CAAC,IAAKA,CAAC,KAAK,GAAG,CAAC,CAAC2C,MAAM;IACpC,IAAI,CAAC7D,KAAK,EAAE;MACR,OAAO,IAAI,CAAC,CAAC;IACjB;IACA,IAAK,EAAEA,KAAK,CAACA,KAAK,CAAC6D,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI7D,KAAK,CAAC6D,MAAM,GAAGwX,UAAU,IACpErb,KAAK,CAAC6D,MAAM,IAAIwX,UAAU,GAAG,CAAC,EAAE;MAChC,OAAO,IAAI,CAAC/E,sBAAsB,CAACtW,KAAK,CAAC;IAC7C;IACA,OAAO,IAAI;EACf;EACA2Y,qBAAqBA,CAAA,EAAG;IACpB,OAAQ,IAAI,CAACxD,YAAY,CAAC3S,WAAW,CAACqB,MAAM,IACxC,IAAI,CAACsR,YAAY,CAAC3S,WAAW,CAACqB,MAAM,GAAG,IAAI,CAACsR,YAAY,CAACvW,MAAM,CAACiF,MAAM;EAC9E;EACAyS,sBAAsBA,CAAC9T,WAAW,EAAE;IAChC,OAAO;MACH0O,IAAI,EAAE;QACFoK,YAAY,EAAE,IAAI,CAAC3G,UAAU,CAAC,CAAC;QAC/BnS;MACJ;IACJ,CAAC;EACL;EACAsT,QAAQA,CAAA,EAAG;IACP,IAAI,CAACf,oBAAoB,CAAC,CAAC,CAACnJ,IAAI,CAAEsF,IAAI,IAAK;MACvC,MAAMtI,YAAY,GAAGsI,IAAI,CACpBjN,KAAK,CAAC3F,cAAc,CAAC6E,YAAY,CAAC,CAClCyI,IAAI,CAAEnC,IAAI,IAAK,IAAI,CAAC0L,YAAY,CAAC1V,iBAAiB,CAAC+F,QAAQ,CAACiE,IAAI,CAAC,CAAC;MACvE,IAAKb,YAAY,IACb,IAAI,CAACgM,WAAW,CAAC,CAAC,IAClB,IAAI,CAAC2G,iCAAiC,CAAC,IAAI,CAACxG,oBAAoB,CAAC,CAAC,CAAC,IACnE7D,IAAI,CAAC1L,QAAQ,CAAClH,cAAc,CAACiV,mBAAmB,CAAC,EAAE;QACnD,MAAMvJ,IAAI,GAAG,IAAI,CAACmL,YAAY,CAACzG,UAAU,CAAC,IAAI,CAACkG,WAAW,CAAC,CAAC,CAAC,EAAE/Q,MAAM,IACjE,IAAI,CAACsR,YAAY,CAACzG,UAAU,CAACwC,IAAI,CAAC,EAAErN,MAAM;QAC9C,IAAImG,IAAI,EAAE;UACN,MAAMqM,SAAS,GAAGnF,IAAI,CAAC1L,QAAQ,CAAClH,cAAc,CAACiV,mBAAmB,CAAC,GAC7D,IAAI,CAAC4B,YAAY,CAACjC,qBAAqB,CAAChC,IAAI,CAAC,GAC7CA,IAAI;UACV,IAAI,CAACyD,UAAU,CAACe,GAAG,CAACW,SAAS,CAAC;UAC9B,IAAI,CAAClB,YAAY,CAAC5S,cAAc,GAAG8T,SAAS;UAC5C,OAAOrM,IAAI;QACf,CAAC,MACI;UACD,MAAMwR,UAAU,GAAG,IAAI,CAACzG,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAACA,oBAAoB,CAAC,CAAC,CAAClR,MAAM,GAAG,CAAC,CAAC,IAClFvF,cAAc,CAAC6E,YAAY;UAC/B,MAAMkT,SAAS,GAAGmF,UAAU,CAAChW,QAAQ,CAAClH,cAAc,CAACiV,mBAAmB,CAAC,GACnE,IAAI,CAAC4B,YAAY,CAACjC,qBAAqB,CAACsI,UAAU,CAAC,GACnDA,UAAU;UAChB,IAAI,CAAC7G,UAAU,CAACe,GAAG,CAACW,SAAS,CAAC;UAC9B,IAAI,CAAClB,YAAY,CAAC5S,cAAc,GAAG8T,SAAS;QAChD;MACJ,CAAC,MACI;QACD,MAAMoF,SAAS,GAAG,IAAI,CAACtG,YAAY,CAACzG,UAAU,CAACwC,IAAI,CAAC;QACpD,MAAMwK,KAAK,GAAG,IAAI,CAACvG,YAAY,CAC1BzG,UAAU,CAAC,IAAI,CAACkG,WAAW,CAAC,CAAC,CAAC,EAC7B3Q,KAAK,CAAC3F,cAAc,CAAC6E,YAAY,CAAC,CACnCqG,KAAK,CAAC,CAACmS,SAAS,EAAE9P,KAAK,KAAK;UAC7B,MAAM+P,SAAS,GAAGH,SAAS,CAACI,MAAM,CAAChQ,KAAK,CAAC;UACzC,OAAO,IAAI,CAACsJ,YAAY,CAAC/N,gBAAgB,CAACuU,SAAS,EAAEC,SAAS,CAAC;QACnE,CAAC,CAAC;QACF,IAAIF,KAAK,IAAI,IAAI,CAAC1G,WAAW,CAAC,CAAC,EAAE;UAC7B,IAAI,CAACL,UAAU,CAACe,GAAG,CAACxE,IAAI,CAAC;UACzB,IAAI,CAACiE,YAAY,CAAC5S,cAAc,GAAG2O,IAAI;UACvC,OAAOwK,KAAK;QAChB;MACJ;IACJ,CAAC,CAAC;EACN;EACAH,iCAAiCA,CAACzE,KAAK,EAAE;IACrC,MAAMrX,iBAAiB,GAAG,IAAI,CAAC0V,YAAY,CAAC1V,iBAAiB;IAC7D,SAASqc,uBAAuBA,CAAC7R,GAAG,EAAE;MAClC,MAAMoF,KAAK,GAAG,IAAIhP,MAAM,CAAE,IAAGZ,iBAAiB,CAAC8K,GAAG,CAAEwR,EAAE,IAAM,KAAIA,EAAG,EAAC,CAAC,CAACnS,IAAI,CAAC,EAAE,CAAE,GAAE,EAAE,GAAG,CAAC;MACvF,OAAOK,GAAG,CAAC1D,OAAO,CAAC8I,KAAK,EAAE,EAAE,CAAC;IACjC;IACA,MAAM2M,YAAY,GAAGlF,KAAK,CAACvM,GAAG,CAACuR,uBAAuB,CAAC;IACvD,OAAOE,YAAY,CAACxS,KAAK,CAAES,GAAG,IAAK;MAC/B,MAAMgS,gBAAgB,GAAG,IAAI5Z,GAAG,CAAC4H,GAAG,CAAC;MACrC,OAAOgS,gBAAgB,CAACC,IAAI,KAAK,CAAC;IACtC,CAAC,CAAC;EACN;EACA,OAAO3P,IAAI,YAAA4P,yBAAA1P,CAAA;IAAA,YAAAA,CAAA,IAAwFiI,gBAAgB;EAAA;EACnH,OAAO0H,IAAI,kBAzhD8E/e,EAAE,CAAAgf,iBAAA;IAAArP,IAAA,EAyhDJ0H,gBAAgB;IAAA4H,SAAA;IAAAC,YAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAzhDdpf,EAAE,CAAAsf,UAAA,mBAAAC,0CAAA;UAAA,OAyhDJF,GAAA,CAAAxF,OAAA,CAAQ,CAAC;QAAA,CAAM,CAAC,mBAAA2F,0CAAAC,MAAA;UAAA,OAAhBJ,GAAA,CAAAvF,OAAA,CAAA2F,MAAc,CAAC;QAAA,EAAC,2BAAAC,kDAAAD,MAAA;UAAA,OAAhBJ,GAAA,CAAAtF,aAAA,CAAA0F,MAAoB,CAAC;QAAA,CAAN,CAAC,mBAAAE,0CAAAF,MAAA;UAAA,OAAhBJ,GAAA,CAAArF,OAAA,CAAAyF,MAAc,CAAC;QAAA,EAAC,8BAAAG,qDAAAH,MAAA;UAAA,OAAhBJ,GAAA,CAAA3D,kBAAA,CAAA+D,MAAyB,CAAC;QAAA,CAAX,CAAC,4BAAAI,mDAAAJ,MAAA;UAAA,OAAhBJ,GAAA,CAAA1D,gBAAA,CAAA8D,MAAuB,CAAC;QAAA,CAAT,CAAC,kBAAAK,yCAAAL,MAAA;UAAA,OAAhBJ,GAAA,CAAAzD,MAAA,CAAA6D,MAAa,CAAC;QAAA,CAAC,CAAC,mBAAAM,0CAAAN,MAAA;UAAA,OAAhBJ,GAAA,CAAAvD,OAAA,CAAA2D,MAAc,CAAC;QAAA,EAAC,qBAAAO,4CAAAP,MAAA;UAAA,OAAhBJ,GAAA,CAAA/C,SAAA,CAAAmD,MAAgB,CAAC;QAAA,CAAF,CAAC;MAAA;IAAA;IAAAQ,MAAA;MAAApM,IAAA,GAzhDd7T,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAA/d,iBAAA,GAAFpC,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAArd,QAAA,GAAF9C,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAA5e,MAAA,GAAFvB,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAA7e,MAAA,GAAFtB,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAA3e,iBAAA,GAAFxB,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAA1e,aAAA,GAAFzB,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAAre,qBAAA,GAAF9B,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAApe,WAAA,GAAF/B,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAAxe,aAAA,GAAF3B,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAAte,oBAAA,GAAF7B,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAAne,mBAAA,GAAFhC,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAAze,eAAA,GAAF1B,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAAhe,UAAA,GAAFnC,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAAle,cAAA,GAAFjC,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAAje,oBAAA,GAAFlC,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAA9d,gBAAA,GAAFrC,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAA5d,QAAA,GAAFvC,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAA1d,mBAAA,GAAFzC,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAA7d,GAAA,GAAFtC,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAAzd,gBAAA,GAAF1C,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAAvd,iBAAA,GAAF5C,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAA3d,sBAAA,GAAFxC,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;MAAAve,aAAA,GAAF5B,EAAE,CAAAkgB,YAAA,CAAAC,WAAA;IAAA;IAAAC,OAAA;MAAAvd,UAAA;IAAA;IAAAwd,QAAA;IAAAC,UAAA;IAAAC,QAAA,GAAFvgB,EAAE,CAAAwgB,kBAAA,CAyhDg4H,CACn9H;MACIvJ,OAAO,EAAElW,iBAAiB;MAC1B0f,WAAW,EAAEpJ,gBAAgB;MAC7BpR,KAAK,EAAE;IACX,CAAC,EACD;MACIgR,OAAO,EAAEjW,aAAa;MACtByf,WAAW,EAAEpJ,gBAAgB;MAC7BpR,KAAK,EAAE;IACX,CAAC,EACD2J,cAAc,CACjB,GAriDoF5P,EAAE,CAAA0gB,oBAAA;EAAA;AAsiD/F;AACA;EAAA,QAAAjR,SAAA,oBAAAA,SAAA,KAviD6FzP,EAAE,CAAA0P,iBAAA,CAuiDJ2H,gBAAgB,EAAc,CAAC;IAC9G1H,IAAI,EAAE/O,SAAS;IACf+f,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,6BAA6B;MACvCN,UAAU,EAAE,IAAI;MAChBO,SAAS,EAAE,CACP;QACI5J,OAAO,EAAElW,iBAAiB;QAC1B0f,WAAW,EAAEpJ,gBAAgB;QAC7BpR,KAAK,EAAE;MACX,CAAC,EACD;QACIgR,OAAO,EAAEjW,aAAa;QACtByf,WAAW,EAAEpJ,gBAAgB;QAC7BpR,KAAK,EAAE;MACX,CAAC,EACD2J,cAAc,CACjB;MACDyQ,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,QAAkB;IAAExG,OAAO,EAAE,CAAC;MACxBlK,IAAI,EAAEhP,YAAY;MAClBggB,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAE7G,OAAO,EAAE,CAAC;MACVnK,IAAI,EAAEhP,YAAY;MAClBggB,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC,CAAC;IAAE5G,aAAa,EAAE,CAAC;MAChBpK,IAAI,EAAEhP,YAAY;MAClBggB,IAAI,EAAE,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC;IACtC,CAAC,CAAC;IAAE3G,OAAO,EAAE,CAAC;MACVrK,IAAI,EAAEhP,YAAY;MAClBggB,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC,CAAC;IAAEjF,kBAAkB,EAAE,CAAC;MACrB/L,IAAI,EAAEhP,YAAY;MAClBggB,IAAI,EAAE,CAAC,kBAAkB,EAAE,CAAC,QAAQ,CAAC;IACzC,CAAC,CAAC;IAAEhF,gBAAgB,EAAE,CAAC;MACnBhM,IAAI,EAAEhP,YAAY;MAClBggB,IAAI,EAAE,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC;IACvC,CAAC,CAAC;IAAE/E,MAAM,EAAE,CAAC;MACTjM,IAAI,EAAEhP,YAAY;MAClBggB,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC;IAC7B,CAAC,CAAC;IAAE7E,OAAO,EAAE,CAAC;MACVnM,IAAI,EAAEhP,YAAY;MAClBggB,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC,CAAC;IAAErE,SAAS,EAAE,CAAC;MACZ3M,IAAI,EAAEhP,YAAY;MAClBggB,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC;IAChC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMG,WAAW,CAAC;EACdC,cAAc,GAAG5gB,MAAM,CAACe,eAAe,CAAC;EACxC4W,YAAY,GAAG3X,MAAM,CAACyP,cAAc,CAAC;EACrC8H,oBAAoB,GAAG,EAAE;EACzB7D,IAAI,GAAG,EAAE;EACTmN,SAASA,CAACre,KAAK,EAAEkR,IAAI,EAAE;IAAE/Q,QAAQ;IAAE,GAAGme;EAAO,CAAC,GAAG,CAAC,CAAC,EAAE;IACjD,IAAI5a,cAAc,GAAG1D,KAAK;IAC1B,MAAMue,aAAa,GAAG;MAClBhc,cAAc,EAAE2O,IAAI;MACpB,GAAG,IAAI,CAACkN,cAAc;MACtB,GAAGE,MAAM;MACTne,QAAQ,EAAE;QACN,GAAG,IAAI,CAACgV,YAAY,CAAChV,QAAQ;QAC7B,GAAGA;MACP;IACJ,CAAC;IACDqe,MAAM,CAACC,OAAO,CAACF,aAAa,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC/L,GAAG,EAAE7I,GAAG,CAAC,KAAK;MAClD,IAAI,CAACqL,YAAY,CAACxC,GAAG,CAAC,GAAG7I,GAAG;IAChC,CAAC,CAAC;IACF,IAAIoH,IAAI,CAAC1L,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrB,MAAMmZ,SAAS,GAAGzN,IAAI,CAACjN,KAAK,CAAC,IAAI,CAAC;MAClC,IAAI0a,SAAS,CAAC9a,MAAM,GAAG,CAAC,EAAE;QACtB,IAAI,CAACkR,oBAAoB,GAAG4J,SAAS,CAAChJ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC/R,MAAM,GAAGgS,CAAC,CAAChS,MAAM,CAAC;QACzE,IAAI,CAACiS,QAAQ,CAAE,GAAEpS,cAAe,EAAC,CAAC;QAClC,OAAO,IAAI,CAACyR,YAAY,CAACtS,SAAS,CAAE,GAAEa,cAAe,EAAC,EAAE,IAAI,CAACwN,IAAI,CAAC;MACtE,CAAC,MACI;QACD,IAAI,CAAC6D,oBAAoB,GAAG,EAAE;QAC9B,OAAO,IAAI,CAACI,YAAY,CAACtS,SAAS,CAAE,GAAEa,cAAe,EAAC,EAAE,IAAI,CAACwN,IAAI,CAAC;MACtE;IACJ;IACA,IAAIA,IAAI,CAAC1L,QAAQ,CAAClH,cAAc,CAACiV,mBAAmB,CAAC,EAAE;MACnD,OAAO,IAAI,CAAC4B,YAAY,CAACtS,SAAS,CAAE,GAAEa,cAAe,EAAC,EAAE,IAAI,CAACyR,YAAY,CAACjC,qBAAqB,CAAChC,IAAI,CAAC,CAAC;IAC1G;IACA,IAAIA,IAAI,CAACtM,UAAU,CAACtG,cAAc,CAACsD,SAAS,CAAC,EAAE;MAC3C,IAAI0c,MAAM,CAACxf,aAAa,EAAE;QACtB,IAAI,CAACqW,YAAY,CAACrW,aAAa,GAAGwf,MAAM,CAACxf,aAAa;MAC1D;MACA,IAAIwf,MAAM,CAACzf,iBAAiB,EAAE;QAC1B,IAAI,CAACsW,YAAY,CAACtW,iBAAiB,GAAGyf,MAAM,CAACzf,iBAAiB;MAClE;MACA,IAAIyf,MAAM,CAAC1e,QAAQ,EAAE;QACjB,IAAI,CAACuV,YAAY,CAACvV,QAAQ,GAAG0e,MAAM,CAAC1e,QAAQ;MAChD;MACA8D,cAAc,GAAGyM,MAAM,CAACzM,cAAc,CAAC;MACvC,MAAMiX,mBAAmB,GAAG,IAAI,CAACxF,YAAY,CAACtB,0BAA0B,CAAC,CAAC;MAC1E,IAAI,CAACvO,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC4P,YAAY,CAACrW,aAAa,CAAC,EAAE;QACjD4E,cAAc,GACV,IAAI,CAACyR,YAAY,CAACrW,aAAa,KAAK6b,mBAAmB,GACjDjX,cAAc,CAAC6C,OAAO,CAACoU,mBAAmB,EAAE,IAAI,CAACxF,YAAY,CAACrW,aAAa,CAAC,GAC5E4E,cAAc;MAC5B;MACA,IAAI,IAAI,CAACyR,YAAY,CAACvV,QAAQ,IAC1B8D,cAAc,IACd,IAAI,CAACyR,YAAY,CAAChW,qBAAqB,KAAK,KAAK,EAAE;QACnDuE,cAAc,GAAG,IAAI,CAACyR,YAAY,CAACzC,eAAe,CAACxB,IAAI,EAAExN,cAAc,CAAC;MAC5E;MACA,IAAI,IAAI,CAACyR,YAAY,CAACrW,aAAa,KAAKR,cAAc,CAAC6H,KAAK,EAAE;QAC1DzC,cAAc,GAAGA,cAAc,CAAC6C,OAAO,CAACjI,cAAc,CAAC+F,GAAG,EAAE/F,cAAc,CAAC6H,KAAK,CAAC;MACrF;MACA,IAAI,CAACgP,YAAY,CAACjI,aAAa,GAAG,IAAI;IAC1C;IACA,IAAIxJ,cAAc,KAAK,IAAI,IAAI,OAAOA,cAAc,KAAK,WAAW,EAAE;MAClE,OAAO,IAAI,CAACyR,YAAY,CAACtS,SAAS,CAAC,EAAE,EAAEqO,IAAI,CAAC;IAChD;IACA,OAAO,IAAI,CAACiE,YAAY,CAACtS,SAAS,CAAE,GAAEa,cAAe,EAAC,EAAEwN,IAAI,CAAC;EACjE;EACA4E,QAAQA,CAAC9V,KAAK,EAAE;IACZ,IAAI,IAAI,CAAC+U,oBAAoB,CAAClR,MAAM,GAAG,CAAC,EAAE;MACtC,IAAI,CAACkR,oBAAoB,CAACnJ,IAAI,CAAEsF,IAAI,IAAK;QACrC,MAAMlH,IAAI,GAAG,IAAI,CAACmL,YAAY,CAACzG,UAAU,CAAC1O,KAAK,CAAC,EAAE6D,MAAM,IACpD,IAAI,CAACsR,YAAY,CAACzG,UAAU,CAACwC,IAAI,CAAC,EAAErN,MAAM;QAC9C,IAAI7D,KAAK,IAAIgK,IAAI,EAAE;UACf,IAAI,CAACkH,IAAI,GAAGA,IAAI;UAChB,OAAOlH,IAAI;QACf,CAAC,MACI;UACD,IAAI,CAACkH,IAAI,GACL,IAAI,CAAC6D,oBAAoB,CAAC,IAAI,CAACA,oBAAoB,CAAClR,MAAM,GAAG,CAAC,CAAC,IAC3DvF,cAAc,CAAC6E,YAAY;QACvC;MACJ,CAAC,CAAC;IACN;EACJ;EACA,OAAOoJ,IAAI,YAAAqS,oBAAAnS,CAAA;IAAA,YAAAA,CAAA,IAAwF0R,WAAW;EAAA;EAC9G,OAAOU,KAAK,kBA7qD6ExhB,EAAE,CAAAyhB,YAAA;IAAAlO,IAAA;IAAA5D,IAAA,EA6qDMmR,WAAW;IAAAY,IAAA;IAAApB,UAAA;EAAA;AAChH;AACA;EAAA,QAAA7Q,SAAA,oBAAAA,SAAA,KA/qD6FzP,EAAE,CAAA0P,iBAAA,CA+qDJoR,WAAW,EAAc,CAAC;IACzGnR,IAAI,EAAE9O,IAAI;IACV8f,IAAI,EAAE,CAAC;MACCpN,IAAI,EAAE,MAAM;MACZmO,IAAI,EAAE,IAAI;MACVpB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASlf,cAAc,EAAED,UAAU,EAAED,eAAe,EAAEmW,gBAAgB,EAAEyJ,WAAW,EAAElR,cAAc,EAAEvO,aAAa,EAAE+V,yBAAyB,EAAEJ,cAAc,EAAElT,SAAS,EAAEI,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}