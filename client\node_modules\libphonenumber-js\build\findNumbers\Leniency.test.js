/*
import { containsMoreThanOneSlashInNationalNumber } from './Leniency.js'

describe('Leniency', () => {
	it('testContainsMoreThanOneSlashInNationalNumber', () => {
		// A date should return true.
		number.setCountryCode(1)
		number.setCountryCodeSource(CountryCodeSource.FROM_DEFAULT_COUNTRY)
		containsMoreThanOneSlashInNationalNumber(number, '1/05/2013').should.equal(true)

		// Here, the country code source thinks it started with a country calling code, but this is not
		// the same as the part before the slash, so it's still true.
		number.setCountryCode(274)
		number.setCountryCodeSource(CountryCodeSource.FROM_NUMBER_WITHOUT_PLUS_SIGN)
		containsMoreThanOneSlashInNationalNumber(number, '27/4/2013').should.equal(true)

		// Now it should be false, because the first slash is after the country calling code.
		number.setCountryCode(49)
		number.setCountryCodeSource(CountryCodeSource.FROM_NUMBER_WITH_PLUS_SIGN)
		containsMoreThanOneSlashInNationalNumber(number, '49/69/2013').should.equal(false)

		number.setCountryCode(49)
		number.setCountryCodeSource(CountryCodeSource.FROM_NUMBER_WITHOUT_PLUS_SIGN)
		containsMoreThanOneSlashInNationalNumber(number, '+49/69/2013').should.equal(false)
		containsMoreThanOneSlashInNationalNumber(number, '+ 49/69/2013').should.equal(false)
		containsMoreThanOneSlashInNationalNumber(number, '+ 49/69/20/13').should.equal(true)

		// Here, the first group is not assumed to be the country calling code, even though it is the
		// same as it, so this should return true.
		number.setCountryCode(49)
		number.setCountryCodeSource(CountryCodeSource.FROM_DEFAULT_COUNTRY)
		containsMoreThanOneSlashInNationalNumber(number, '49/69/2013').should.equal(true)
	})
})
*/
"use strict";
//# sourceMappingURL=Leniency.test.js.map