import type { NgxMaskConfig } from './ngx-mask.config';
import * as i0 from "@angular/core";
export declare class NgxMaskApplierService {
    protected _config: NgxMaskConfig;
    dropSpecialCharacters: NgxMaskConfig['dropSpecialCharacters'];
    hiddenInput: NgxMaskConfig['hiddenInput'];
    clearIfNotMatch: NgxMaskConfig['clearIfNotMatch'];
    specialCharacters: NgxMaskConfig['specialCharacters'];
    patterns: NgxMaskConfig['patterns'];
    prefix: NgxMaskConfig['prefix'];
    suffix: NgxMaskConfig['suffix'];
    thousandSeparator: NgxMaskConfig['thousandSeparator'];
    decimalMarker: NgxMaskConfig['decimalMarker'];
    customPattern: NgxMaskConfig['patterns'];
    showMaskTyped: NgxMaskConfig['showMaskTyped'];
    placeHolderCharacter: NgxMaskConfig['placeHolderCharacter'];
    validation: NgxMaskConfig['validation'];
    separatorLimit: NgxMaskConfig['separatorLimit'];
    allowNegativeNumbers: NgxMaskConfig['allowNegativeNumbers'];
    leadZeroDateTime: NgxMaskConfig['leadZeroDateTime'];
    leadZero: NgxMaskConfig['leadZero'];
    apm: NgxMaskConfig['apm'];
    inputTransformFn: NgxMaskConfig['inputTransformFn'] | null;
    outputTransformFn: NgxMaskConfig['outputTransformFn'] | null;
    keepCharacterPositions: NgxMaskConfig['keepCharacterPositions'];
    instantPrefix: NgxMaskConfig['instantPrefix'];
    triggerOnMaskChange: NgxMaskConfig['triggerOnMaskChange'];
    private _shift;
    plusOnePosition: boolean;
    maskExpression: string;
    actualValue: string;
    showKeepCharacterExp: string;
    shownMaskExpression: NgxMaskConfig['shownMaskExpression'];
    deletedSpecialCharacter: boolean;
    ipError?: boolean;
    cpfCnpjError?: boolean;
    applyMask(inputValue: string | object | boolean | null | undefined, maskExpression: string, position?: number, justPasted?: boolean, backspaced?: boolean, cb?: (...args: any[]) => any): string;
    _findDropSpecialChar(inputSymbol: string): undefined | string;
    _findSpecialChar(inputSymbol: string): undefined | string;
    _checkSymbolMask(inputSymbol: string, maskSymbol: string): boolean;
    private _formatWithSeparators;
    private percentage;
    getPrecision: (maskExpression: string) => number;
    private checkAndRemoveSuffix;
    private checkInputPrecision;
    private _stripToDecimal;
    private _charToRegExpExpression;
    private _shiftStep;
    protected _compareOrIncludes<T>(value: T, comparedValue: T | T[], excludedValue: T): boolean;
    private _validIP;
    private _splitPercentZero;
    private _findFirstNonZeroAndDecimalIndex;
    static ɵfac: i0.ɵɵFactoryDeclaration<NgxMaskApplierService, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<NgxMaskApplierService>;
}
