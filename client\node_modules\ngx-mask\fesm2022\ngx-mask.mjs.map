{"version": 3, "file": "ngx-mask.mjs", "sources": ["../../../projects/ngx-mask-lib/src/lib/ngx-mask-expression.enum.ts", "../../../projects/ngx-mask-lib/src/lib/ngx-mask.config.ts", "../../../projects/ngx-mask-lib/src/lib/ngx-mask-applier.service.ts", "../../../projects/ngx-mask-lib/src/lib/ngx-mask.service.ts", "../../../projects/ngx-mask-lib/src/lib/ngx-mask.providers.ts", "../../../projects/ngx-mask-lib/src/lib/ngx-mask.directive.ts", "../../../projects/ngx-mask-lib/src/lib/ngx-mask.pipe.ts", "../../../projects/ngx-mask-lib/src/ngx-mask.ts"], "sourcesContent": ["export const enum MaskExpression {\n    SEPARATOR = 'separator',\n    PERCENT = 'percent',\n    IP = 'IP',\n    CPF_CNPJ = 'CPF_CNPJ',\n    MONTH = 'M',\n    MONTHS = 'M0',\n    MINUTE = 'm',\n    HOUR = 'h',\n    HOURS = 'H',\n    MINUTES = 'm0',\n    HOURS_HOUR = 'Hh',\n    SECONDS = 's0',\n    HOURS_MINUTES_SECONDS = 'Hh:m0:s0',\n    EMAIL_MASK = 'A*@A*.A*',\n    HOURS_MINUTES = 'Hh:m0',\n    MINUTES_SECONDS = 'm0:s0',\n    DAYS_MONTHS_YEARS = 'd0/M0/0000',\n    DAYS_MONTHS = 'd0/M0',\n    DAYS = 'd0',\n    DAY = 'd',\n    SECOND = 's',\n    LETTER_S = 'S',\n    DOT = '.',\n    COMMA = ',',\n    CURLY_BRACKETS_LEFT = '{',\n    CURLY_BRACKETS_RIGHT = '}',\n    MINUS = '-',\n    OR = '||',\n    HASH = '#',\n    EMPTY_STRING = '',\n    SYMBOL_STAR = '*',\n    SYMBOL_QUESTION = '?',\n    SLASH = '/',\n    WHITE_SPACE = ' ',\n    NUMBER_ZERO = '0',\n    NUMBER_NINE = '9',\n    BACKSPACE = 'Backspace',\n    DELETE = 'Delete',\n    ARROW_LEFT = 'ArrowLeft',\n    ARROW_UP = 'ArrowUp',\n    DOUBLE_ZERO = '00',\n}\n", "import { EventEmitter, InjectionToken } from '@angular/core';\nimport { MaskExpression } from './ngx-mask-expression.enum';\n\nexport type InputTransformFn = (value: unknown) => string | number;\n\nexport type OutputTransformFn = (value: string | number | undefined | null) => unknown;\n\nexport type NgxMaskConfig = {\n    suffix: string;\n    prefix: string;\n    thousandSeparator: string;\n    decimalMarker: '.' | ',' | ['.', ','];\n    clearIfNotMatch: boolean;\n    showMaskTyped: boolean;\n    placeHolderCharacter: string;\n    shownMaskExpression: string;\n    specialCharacters: string[] | readonly string[];\n    dropSpecialCharacters: boolean | string[] | readonly string[];\n    hiddenInput: boolean;\n    validation: boolean;\n    instantPrefix: boolean;\n    separatorLimit: string;\n    apm: boolean;\n    allowNegativeNumbers: boolean;\n    leadZeroDateTime: boolean;\n    leadZero: boolean;\n    triggerOnMaskChange: boolean;\n    keepCharacterPositions: boolean;\n    inputTransformFn: InputTransformFn;\n    outputTransformFn: OutputTransformFn;\n    maskFilled: EventEmitter<void>;\n    patterns: Record<\n        string,\n        {\n            pattern: RegExp;\n            optional?: boolean;\n            symbol?: string;\n        }\n    >;\n};\n\nexport type NgxMaskOptions = Partial<NgxMaskConfig>;\nexport const NGX_MASK_CONFIG = new InjectionToken<NgxMaskConfig>('ngx-mask config');\nexport const NEW_CONFIG = new InjectionToken<NgxMaskConfig>('new ngx-mask config');\nexport const INITIAL_CONFIG = new InjectionToken<NgxMaskConfig>('initial ngx-mask config');\n\nexport const initialConfig: NgxMaskConfig = {\n    suffix: '',\n    prefix: '',\n    thousandSeparator: ' ',\n    decimalMarker: ['.', ','],\n    clearIfNotMatch: false,\n    showMaskTyped: false,\n    instantPrefix: false,\n    placeHolderCharacter: '_',\n    dropSpecialCharacters: true,\n    hiddenInput: false,\n    shownMaskExpression: '',\n    separatorLimit: '',\n    allowNegativeNumbers: false,\n    validation: true,\n    specialCharacters: ['-', '/', '(', ')', '.', ':', ' ', '+', ',', '@', '[', ']', '\"', \"'\"],\n    leadZeroDateTime: false,\n    apm: false,\n    leadZero: false,\n    keepCharacterPositions: false,\n    triggerOnMaskChange: false,\n    inputTransformFn: (value: unknown) => value as string | number,\n    outputTransformFn: (value: string | number | undefined | null) => value,\n    maskFilled: new EventEmitter<void>(),\n    patterns: {\n        '0': {\n            pattern: new RegExp('\\\\d'),\n        },\n        '9': {\n            pattern: new RegExp('\\\\d'),\n            optional: true,\n        },\n        X: {\n            pattern: new RegExp('\\\\d'),\n            symbol: '*',\n        },\n        A: {\n            pattern: new RegExp('[a-zA-Z0-9]'),\n        },\n        S: {\n            pattern: new RegExp('[a-zA-Z]'),\n        },\n        U: {\n            pattern: new RegExp('[A-Z]'),\n        },\n        L: {\n            pattern: new RegExp('[a-z]'),\n        },\n        d: {\n            pattern: new RegExp('\\\\d'),\n        },\n        m: {\n            pattern: new RegExp('\\\\d'),\n        },\n        M: {\n            pattern: new RegExp('\\\\d'),\n        },\n        H: {\n            pattern: new RegExp('\\\\d'),\n        },\n        h: {\n            pattern: new RegExp('\\\\d'),\n        },\n        s: {\n            pattern: new RegExp('\\\\d'),\n        },\n    },\n};\n\nexport const timeMasks: string[] = [\n    MaskExpression.HOURS_MINUTES_SECONDS,\n    MaskExpression.HOURS_MINUTES,\n    MaskExpression.MINUTES_SECONDS,\n];\n\nexport const withoutValidation: string[] = [\n    MaskExpression.PERCENT,\n    MaskExpression.HOURS_HOUR,\n    MaskExpression.SECONDS,\n    MaskExpression.MINUTES,\n    MaskExpression.SEPARATOR,\n    MaskExpression.DAYS_MONTHS_YEARS,\n    MaskExpression.DAYS_MONTHS,\n    MaskExpression.DAYS,\n    MaskExpression.MONTHS,\n];\n", "import { inject, Injectable } from '@angular/core';\nimport type { NgxMaskConfig } from './ngx-mask.config';\nimport { NGX_MASK_CONFIG } from './ngx-mask.config';\nimport { MaskExpression } from './ngx-mask-expression.enum';\n\n@Injectable()\nexport class NgxMaskApplierService {\n    protected _config = inject<NgxMaskConfig>(NGX_MASK_CONFIG);\n\n    public dropSpecialCharacters: NgxMaskConfig['dropSpecialCharacters'] =\n        this._config.dropSpecialCharacters;\n\n    public hiddenInput: NgxMaskConfig['hiddenInput'] = this._config.hiddenInput;\n\n    public clearIfNotMatch: NgxMaskConfig['clearIfNotMatch'] = this._config.clearIfNotMatch;\n\n    public specialCharacters: NgxMaskConfig['specialCharacters'] = this._config.specialCharacters;\n\n    public patterns: NgxMaskConfig['patterns'] = this._config.patterns;\n\n    public prefix: NgxMaskConfig['prefix'] = this._config.prefix;\n\n    public suffix: NgxMaskConfig['suffix'] = this._config.suffix;\n\n    public thousandSeparator: NgxMaskConfig['thousandSeparator'] = this._config.thousandSeparator;\n\n    public decimalMarker: NgxMaskConfig['decimalMarker'] = this._config.decimalMarker;\n\n    public customPattern!: NgxMaskConfig['patterns'];\n\n    public showMaskTyped: NgxMaskConfig['showMaskTyped'] = this._config.showMaskTyped;\n\n    public placeHolderCharacter: NgxMaskConfig['placeHolderCharacter'] =\n        this._config.placeHolderCharacter;\n\n    public validation: NgxMaskConfig['validation'] = this._config.validation;\n\n    public separatorLimit: NgxMaskConfig['separatorLimit'] = this._config.separatorLimit;\n\n    public allowNegativeNumbers: NgxMaskConfig['allowNegativeNumbers'] =\n        this._config.allowNegativeNumbers;\n\n    public leadZeroDateTime: NgxMaskConfig['leadZeroDateTime'] = this._config.leadZeroDateTime;\n\n    public leadZero: NgxMaskConfig['leadZero'] = this._config.leadZero;\n\n    public apm: NgxMaskConfig['apm'] = this._config.apm;\n\n    public inputTransformFn: NgxMaskConfig['inputTransformFn'] | null =\n        this._config.inputTransformFn;\n\n    public outputTransformFn: NgxMaskConfig['outputTransformFn'] | null =\n        this._config.outputTransformFn;\n\n    public keepCharacterPositions: NgxMaskConfig['keepCharacterPositions'] =\n        this._config.keepCharacterPositions;\n\n    public instantPrefix: NgxMaskConfig['instantPrefix'] = this._config.instantPrefix;\n\n    public triggerOnMaskChange: NgxMaskConfig['triggerOnMaskChange'] =\n        this._config.triggerOnMaskChange;\n\n    private _shift = new Set<number>();\n\n    public plusOnePosition = false;\n\n    public maskExpression = '';\n\n    public actualValue = '';\n\n    public showKeepCharacterExp = '';\n\n    public shownMaskExpression: NgxMaskConfig['shownMaskExpression'] =\n        this._config.shownMaskExpression;\n\n    public deletedSpecialCharacter = false;\n\n    public ipError?: boolean;\n\n    public cpfCnpjError?: boolean;\n\n    public applyMask(\n        inputValue: string | object | boolean | null | undefined,\n        maskExpression: string,\n        position = 0,\n        justPasted = false,\n        backspaced = false,\n        // eslint-disable-next-line @typescript-eslint/no-empty-function\n        cb: (...args: any[]) => any = () => {}\n    ): string {\n        if (!maskExpression || typeof inputValue !== 'string') {\n            return MaskExpression.EMPTY_STRING;\n        }\n        let cursor = 0;\n        let result = '';\n        let multi = false;\n        let backspaceShift = false;\n        let shift = 1;\n        let stepBack = false;\n        let processedValue = inputValue;\n        let processedPosition = position;\n\n        if (processedValue.slice(0, this.prefix.length) === this.prefix) {\n            processedValue = processedValue.slice(this.prefix.length, processedValue.length);\n        }\n        if (!!this.suffix && processedValue.length > 0) {\n            processedValue = this.checkAndRemoveSuffix(processedValue);\n        }\n        if (processedValue === '(' && this.prefix) {\n            processedValue = '';\n        }\n        const inputArray: string[] = processedValue.toString().split(MaskExpression.EMPTY_STRING);\n        if (\n            this.allowNegativeNumbers &&\n            processedValue.slice(cursor, cursor + 1) === MaskExpression.MINUS\n        ) {\n            result += processedValue.slice(cursor, cursor + 1);\n        }\n        if (maskExpression === MaskExpression.IP) {\n            const valuesIP = processedValue.split(MaskExpression.DOT);\n            this.ipError = this._validIP(valuesIP);\n\n            // eslint-disable-next-line no-param-reassign\n            maskExpression = '***************';\n        }\n        const arr: string[] = [];\n        // eslint-disable-next-line @typescript-eslint/prefer-for-of\n        for (let i = 0; i < processedValue.length; i++) {\n            if (processedValue[i]?.match('\\\\d')) {\n                arr.push(processedValue[i] ?? MaskExpression.EMPTY_STRING);\n            }\n        }\n        if (maskExpression === MaskExpression.CPF_CNPJ) {\n            this.cpfCnpjError = arr.length !== 11 && arr.length !== 14;\n            if (arr.length > 11) {\n                // eslint-disable-next-line no-param-reassign\n                maskExpression = '00.000.000/0000-00';\n            } else {\n                // eslint-disable-next-line no-param-reassign\n                maskExpression = '000.000.000-00';\n            }\n        }\n        if (maskExpression.startsWith(MaskExpression.PERCENT)) {\n            if (\n                processedValue.match('[a-z]|[A-Z]') ||\n                // eslint-disable-next-line no-useless-escape\n                (processedValue.match(/[-!$%^&*()_+|~=`{}\\[\\]:\";'<>?,\\/.]/) && !backspaced)\n            ) {\n                processedValue = this._stripToDecimal(processedValue);\n                const precision: number = this.getPrecision(maskExpression);\n\n                processedValue = this.checkInputPrecision(\n                    processedValue,\n                    precision,\n                    this.decimalMarker\n                );\n            }\n            const decimalMarker =\n                typeof this.decimalMarker === 'string' ? this.decimalMarker : MaskExpression.DOT;\n            if (\n                processedValue.indexOf(decimalMarker) > 0 &&\n                !this.percentage(processedValue.substring(0, processedValue.indexOf(decimalMarker)))\n            ) {\n                let base: string = processedValue.substring(\n                    0,\n                    processedValue.indexOf(decimalMarker) - 1\n                );\n                if (\n                    this.allowNegativeNumbers &&\n                    processedValue.slice(cursor, cursor + 1) === MaskExpression.MINUS &&\n                    !backspaced\n                ) {\n                    base = processedValue.substring(0, processedValue.indexOf(decimalMarker));\n                }\n\n                processedValue = `${base}${processedValue.substring(\n                    processedValue.indexOf(decimalMarker),\n                    processedValue.length\n                )}`;\n            }\n            let value = '';\n            // eslint-disable-next-line @typescript-eslint/no-unused-expressions\n            this.allowNegativeNumbers &&\n            processedValue.slice(cursor, cursor + 1) === MaskExpression.MINUS\n                ? (value = `${MaskExpression.MINUS}${processedValue.slice(cursor + 1, cursor + processedValue.length)}`)\n                : (value = processedValue);\n            if (this.percentage(value)) {\n                result = this._splitPercentZero(processedValue);\n            } else {\n                result = this._splitPercentZero(\n                    processedValue.substring(0, processedValue.length - 1)\n                );\n            }\n        } else if (maskExpression.startsWith(MaskExpression.SEPARATOR)) {\n            if (\n                processedValue.match('[wа-яА-Я]') ||\n                processedValue.match('[ЁёА-я]') ||\n                processedValue.match('[a-z]|[A-Z]') ||\n                processedValue.match(/[-@#!$%\\\\^&*()_£¬'+|~=`{}\\]:\";<>.?/]/) ||\n                processedValue.match('[^A-Za-z0-9,]')\n            ) {\n                processedValue = this._stripToDecimal(processedValue);\n            }\n\n            const precision: number = this.getPrecision(maskExpression);\n            let decimalMarker = this.decimalMarker;\n\n            if (Array.isArray(this.decimalMarker)) {\n                if (\n                    this.actualValue.includes(this.decimalMarker[0]) ||\n                    this.actualValue.includes(this.decimalMarker[1])\n                ) {\n                    decimalMarker = this.actualValue.includes(this.decimalMarker[0])\n                        ? this.decimalMarker[0]\n                        : this.decimalMarker[1];\n                } else {\n                    decimalMarker = this.decimalMarker.find(\n                        (dm) => dm !== this.thousandSeparator\n                    ) as '.' | ',';\n                }\n            }\n\n            if (backspaced) {\n                const { decimalMarkerIndex, nonZeroIndex } = this._findFirstNonZeroAndDecimalIndex(\n                    processedValue,\n                    decimalMarker as '.' | ','\n                );\n                const zeroIndexMinus = processedValue[0] === MaskExpression.MINUS;\n                const zeroIndexNumberZero = processedValue[0] === MaskExpression.NUMBER_ZERO;\n                const zeroIndexDecimalMarker = processedValue[0] === decimalMarker;\n                const firstIndexDecimalMarker = processedValue[1] === decimalMarker;\n\n                if (\n                    (zeroIndexDecimalMarker && !nonZeroIndex) ||\n                    (zeroIndexMinus && firstIndexDecimalMarker && !nonZeroIndex) ||\n                    (zeroIndexNumberZero && !decimalMarkerIndex && !nonZeroIndex)\n                ) {\n                    processedValue = MaskExpression.NUMBER_ZERO;\n                }\n\n                if (\n                    decimalMarkerIndex &&\n                    nonZeroIndex &&\n                    zeroIndexMinus &&\n                    processedPosition === 1\n                ) {\n                    if (decimalMarkerIndex < nonZeroIndex || decimalMarkerIndex > nonZeroIndex) {\n                        processedValue = MaskExpression.MINUS + processedValue.slice(nonZeroIndex);\n                    }\n                }\n\n                if (!decimalMarkerIndex && nonZeroIndex && processedValue.length > nonZeroIndex) {\n                    processedValue = zeroIndexMinus\n                        ? MaskExpression.MINUS + processedValue.slice(nonZeroIndex)\n                        : processedValue.slice(nonZeroIndex);\n                }\n\n                if (decimalMarkerIndex && nonZeroIndex && processedPosition === 0) {\n                    if (decimalMarkerIndex < nonZeroIndex) {\n                        processedValue = processedValue.slice(decimalMarkerIndex - 1);\n                    }\n                    if (decimalMarkerIndex > nonZeroIndex) {\n                        processedValue = processedValue.slice(nonZeroIndex);\n                    }\n                }\n            }\n\n            if (precision === 0) {\n                processedValue = this.allowNegativeNumbers\n                    ? processedValue.length > 2 &&\n                      processedValue[0] === MaskExpression.MINUS &&\n                      processedValue[1] === MaskExpression.NUMBER_ZERO &&\n                      processedValue[2] !== this.thousandSeparator &&\n                      processedValue[2] !== MaskExpression.COMMA &&\n                      processedValue[2] !== MaskExpression.DOT\n                        ? '-' + processedValue.slice(2, processedValue.length)\n                        : processedValue[0] === MaskExpression.NUMBER_ZERO &&\n                            processedValue.length > 1 &&\n                            processedValue[1] !== this.thousandSeparator &&\n                            processedValue[1] !== MaskExpression.COMMA &&\n                            processedValue[1] !== MaskExpression.DOT\n                          ? processedValue.slice(1, processedValue.length)\n                          : processedValue\n                    : processedValue.length > 1 &&\n                        processedValue[0] === MaskExpression.NUMBER_ZERO &&\n                        processedValue[1] !== this.thousandSeparator &&\n                        processedValue[1] !== MaskExpression.COMMA &&\n                        processedValue[1] !== MaskExpression.DOT\n                      ? processedValue.slice(1, processedValue.length)\n                      : processedValue;\n            } else {\n                if (\n                    processedValue[0] === decimalMarker &&\n                    processedValue.length > 1 &&\n                    !backspaced\n                ) {\n                    processedValue =\n                        MaskExpression.NUMBER_ZERO +\n                        processedValue.slice(0, processedValue.length + 1);\n                    this.plusOnePosition = true;\n                }\n                if (\n                    processedValue[0] === MaskExpression.NUMBER_ZERO &&\n                    processedValue[1] !== decimalMarker &&\n                    processedValue[1] !== this.thousandSeparator &&\n                    !backspaced\n                ) {\n                    processedValue =\n                        processedValue.length > 1\n                            ? processedValue.slice(0, 1) +\n                              decimalMarker +\n                              processedValue.slice(1, processedValue.length + 1)\n                            : processedValue;\n                    this.plusOnePosition = true;\n                }\n                if (\n                    this.allowNegativeNumbers &&\n                    !backspaced &&\n                    processedValue[0] === MaskExpression.MINUS &&\n                    (processedValue[1] === decimalMarker ||\n                        processedValue[1] === MaskExpression.NUMBER_ZERO)\n                ) {\n                    processedValue =\n                        processedValue[1] === decimalMarker && processedValue.length > 2\n                            ? processedValue.slice(0, 1) +\n                              MaskExpression.NUMBER_ZERO +\n                              processedValue.slice(1, processedValue.length)\n                            : processedValue[1] === MaskExpression.NUMBER_ZERO &&\n                                processedValue.length > 2 &&\n                                processedValue[2] !== decimalMarker\n                              ? processedValue.slice(0, 2) +\n                                decimalMarker +\n                                processedValue.slice(2, processedValue.length)\n                              : processedValue;\n                    this.plusOnePosition = true;\n                }\n            }\n\n            // TODO: we had different rexexps here for the different cases... but tests dont seam to bother - check this\n            //  separator: no COMMA, dot-sep: no SPACE, COMMA OK, comma-sep: no SPACE, COMMA OK\n\n            const thousandSeparatorCharEscaped: string = this._charToRegExpExpression(\n                this.thousandSeparator\n            );\n            let invalidChars: string = '@#!$%^&*()_+|~=`{}\\\\[\\\\]:\\\\s,\\\\.\";<>?\\\\/'.replace(\n                thousandSeparatorCharEscaped,\n                ''\n            );\n            //.replace(decimalMarkerEscaped, '');\n            if (Array.isArray(this.decimalMarker)) {\n                for (const marker of this.decimalMarker) {\n                    invalidChars = invalidChars.replace(\n                        this._charToRegExpExpression(marker),\n                        MaskExpression.EMPTY_STRING\n                    );\n                }\n            } else {\n                invalidChars = invalidChars.replace(\n                    this._charToRegExpExpression(this.decimalMarker),\n                    ''\n                );\n            }\n\n            const invalidCharRegexp = new RegExp('[' + invalidChars + ']');\n            if (processedValue.match(invalidCharRegexp)) {\n                processedValue = processedValue.substring(0, processedValue.length - 1);\n            }\n\n            processedValue = this.checkInputPrecision(\n                processedValue,\n                precision,\n                this.decimalMarker\n            );\n            const strForSep: string = processedValue.replace(\n                new RegExp(thousandSeparatorCharEscaped, 'g'),\n                ''\n            );\n\n            result = this._formatWithSeparators(\n                strForSep,\n                this.thousandSeparator,\n                this.decimalMarker,\n                precision\n            );\n\n            const commaShift: number =\n                result.indexOf(MaskExpression.COMMA) - processedValue.indexOf(MaskExpression.COMMA);\n            const shiftStep: number = result.length - processedValue.length;\n            const backspacedDecimalMarkerWithSeparatorLimit =\n                backspaced && result.length < inputValue.length && this.separatorLimit;\n\n            if (\n                (result[processedPosition - 1] === this.thousandSeparator ||\n                    result[processedPosition - this.prefix.length]) &&\n                this.prefix &&\n                backspaced\n            ) {\n                processedPosition = processedPosition - 1;\n            } else if (\n                (shiftStep > 0 && result[processedPosition] !== this.thousandSeparator) ||\n                backspacedDecimalMarkerWithSeparatorLimit\n            ) {\n                backspaceShift = true;\n                let _shift = 0;\n                do {\n                    this._shift.add(processedPosition + _shift);\n                    _shift++;\n                } while (_shift < shiftStep);\n            } else if (\n                result[processedPosition - 1] === this.thousandSeparator ||\n                shiftStep === -4 ||\n                shiftStep === -3 ||\n                result[processedPosition] === this.thousandSeparator\n            ) {\n                this._shift.clear();\n                this._shift.add(processedPosition - 1);\n            } else if (\n                (commaShift !== 0 &&\n                    processedPosition > 0 &&\n                    !(\n                        result.indexOf(MaskExpression.COMMA) >= processedPosition &&\n                        processedPosition > 3\n                    )) ||\n                (!(\n                    result.indexOf(MaskExpression.DOT) >= processedPosition && processedPosition > 3\n                ) &&\n                    shiftStep <= 0)\n            ) {\n                this._shift.clear();\n                backspaceShift = true;\n                shift = shiftStep;\n\n                processedPosition += shiftStep;\n                this._shift.add(processedPosition);\n            } else {\n                this._shift.clear();\n            }\n        } else {\n            for (\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                let i = 0, inputSymbol: string = inputArray[0]!;\n                i < inputArray.length;\n                i++, inputSymbol = inputArray[i] ?? MaskExpression.EMPTY_STRING\n            ) {\n                if (cursor === maskExpression.length) {\n                    break;\n                }\n\n                const symbolStarInPattern: boolean = MaskExpression.SYMBOL_STAR in this.patterns;\n                if (\n                    this._checkSymbolMask(\n                        inputSymbol,\n                        maskExpression[cursor] ?? MaskExpression.EMPTY_STRING\n                    ) &&\n                    maskExpression[cursor + 1] === MaskExpression.SYMBOL_QUESTION\n                ) {\n                    result += inputSymbol;\n                    cursor += 2;\n                } else if (\n                    maskExpression[cursor + 1] === MaskExpression.SYMBOL_STAR &&\n                    multi &&\n                    this._checkSymbolMask(\n                        inputSymbol,\n                        maskExpression[cursor + 2] ?? MaskExpression.EMPTY_STRING\n                    )\n                ) {\n                    result += inputSymbol;\n                    cursor += 3;\n                    multi = false;\n                } else if (\n                    this._checkSymbolMask(\n                        inputSymbol,\n                        maskExpression[cursor] ?? MaskExpression.EMPTY_STRING\n                    ) &&\n                    maskExpression[cursor + 1] === MaskExpression.SYMBOL_STAR &&\n                    !symbolStarInPattern\n                ) {\n                    result += inputSymbol;\n                    multi = true;\n                } else if (\n                    maskExpression[cursor + 1] === MaskExpression.SYMBOL_QUESTION &&\n                    this._checkSymbolMask(\n                        inputSymbol,\n                        maskExpression[cursor + 2] ?? MaskExpression.EMPTY_STRING\n                    )\n                ) {\n                    result += inputSymbol;\n                    cursor += 3;\n                } else if (\n                    this._checkSymbolMask(\n                        inputSymbol,\n                        maskExpression[cursor] ?? MaskExpression.EMPTY_STRING\n                    )\n                ) {\n                    if (maskExpression[cursor] === MaskExpression.HOURS) {\n                        if (this.apm ? Number(inputSymbol) > 9 : Number(inputSymbol) > 2) {\n                            processedPosition = !this.leadZeroDateTime\n                                ? processedPosition + 1\n                                : processedPosition;\n                            cursor += 1;\n                            this._shiftStep(cursor);\n                            i--;\n                            if (this.leadZeroDateTime) {\n                                result += '0';\n                            }\n                            continue;\n                        }\n                    }\n                    if (maskExpression[cursor] === MaskExpression.HOUR) {\n                        if (\n                            this.apm\n                                ? (result.length === 1 && Number(result) > 1) ||\n                                  (result === '1' && Number(inputSymbol) > 2) ||\n                                  (processedValue.slice(cursor - 1, cursor).length === 1 &&\n                                      Number(processedValue.slice(cursor - 1, cursor)) > 2) ||\n                                  (processedValue.slice(cursor - 1, cursor) === '1' &&\n                                      Number(inputSymbol) > 2)\n                                : (result === '2' && Number(inputSymbol) > 3) ||\n                                  ((result.slice(cursor - 2, cursor) === '2' ||\n                                      result.slice(cursor - 3, cursor) === '2' ||\n                                      result.slice(cursor - 4, cursor) === '2' ||\n                                      result.slice(cursor - 1, cursor) === '2') &&\n                                      Number(inputSymbol) > 3 &&\n                                      cursor > 10)\n                        ) {\n                            processedPosition = processedPosition + 1;\n                            cursor += 1;\n                            i--;\n                            continue;\n                        }\n                    }\n                    if (\n                        maskExpression[cursor] === MaskExpression.MINUTE ||\n                        maskExpression[cursor] === MaskExpression.SECOND\n                    ) {\n                        if (Number(inputSymbol) > 5) {\n                            processedPosition = !this.leadZeroDateTime\n                                ? processedPosition + 1\n                                : processedPosition;\n                            cursor += 1;\n                            this._shiftStep(cursor);\n                            i--;\n                            if (this.leadZeroDateTime) {\n                                result += '0';\n                            }\n                            continue;\n                        }\n                    }\n                    const daysCount = 31;\n                    const inputValueCursor = processedValue[cursor] as string;\n                    const inputValueCursorPlusOne = processedValue[cursor + 1] as string;\n                    const inputValueCursorPlusTwo = processedValue[cursor + 2] as string;\n                    const inputValueCursorMinusOne = processedValue[cursor - 1] as string;\n                    const inputValueCursorMinusTwo = processedValue[cursor - 2] as string;\n                    const inputValueSliceMinusThreeMinusOne = processedValue.slice(\n                        cursor - 3,\n                        cursor - 1\n                    );\n                    const inputValueSliceMinusOnePlusOne = processedValue.slice(\n                        cursor - 1,\n                        cursor + 1\n                    );\n                    const inputValueSliceCursorPlusTwo = processedValue.slice(cursor, cursor + 2);\n                    const inputValueSliceMinusTwoCursor = processedValue.slice(cursor - 2, cursor);\n                    if (maskExpression[cursor] === MaskExpression.DAY) {\n                        const maskStartWithMonth =\n                            maskExpression.slice(0, 2) === MaskExpression.MONTHS;\n                        const startWithMonthInput: boolean =\n                            maskExpression.slice(0, 2) === MaskExpression.MONTHS &&\n                            this.specialCharacters.includes(inputValueCursorMinusTwo);\n                        if (\n                            (Number(inputSymbol) > 3 && this.leadZeroDateTime) ||\n                            (!maskStartWithMonth &&\n                                (Number(inputValueSliceCursorPlusTwo) > daysCount ||\n                                    Number(inputValueSliceMinusOnePlusOne) > daysCount ||\n                                    this.specialCharacters.includes(inputValueCursorPlusOne))) ||\n                            (startWithMonthInput\n                                ? Number(inputValueSliceMinusOnePlusOne) > daysCount ||\n                                  (!this.specialCharacters.includes(inputValueCursor) &&\n                                      this.specialCharacters.includes(inputValueCursorPlusTwo)) ||\n                                  this.specialCharacters.includes(inputValueCursor)\n                                : Number(inputValueSliceCursorPlusTwo) > daysCount ||\n                                  (this.specialCharacters.includes(inputValueCursorPlusOne) &&\n                                      !backspaced))\n                        ) {\n                            processedPosition = !this.leadZeroDateTime\n                                ? processedPosition + 1\n                                : processedPosition;\n                            cursor += 1;\n                            this._shiftStep(cursor);\n                            i--;\n\n                            if (this.leadZeroDateTime) {\n                                result += '0';\n                            }\n                            continue;\n                        }\n                    }\n                    if (maskExpression[cursor] === MaskExpression.MONTH) {\n                        const monthsCount = 12;\n                        // mask without day\n                        const withoutDays: boolean =\n                            cursor === 0 &&\n                            (Number(inputSymbol) > 2 ||\n                                Number(inputValueSliceCursorPlusTwo) > monthsCount ||\n                                (this.specialCharacters.includes(inputValueCursorPlusOne) &&\n                                    !backspaced));\n                        // day<10 && month<12 for input\n                        const specialChart = maskExpression.slice(cursor + 2, cursor + 3);\n                        const day1monthInput: boolean =\n                            inputValueSliceMinusThreeMinusOne.includes(specialChart) &&\n                            maskExpression.includes('d0') &&\n                            ((this.specialCharacters.includes(inputValueCursorMinusTwo) &&\n                                Number(inputValueSliceMinusOnePlusOne) > monthsCount &&\n                                !this.specialCharacters.includes(inputValueCursor)) ||\n                                this.specialCharacters.includes(inputValueCursor));\n                        //  month<12 && day<10 for input\n                        const day2monthInput: boolean =\n                            Number(inputValueSliceMinusThreeMinusOne) <= daysCount &&\n                            !this.specialCharacters.includes(\n                                inputValueSliceMinusThreeMinusOne as string\n                            ) &&\n                            this.specialCharacters.includes(inputValueCursorMinusOne) &&\n                            (Number(inputValueSliceCursorPlusTwo) > monthsCount ||\n                                this.specialCharacters.includes(inputValueCursorPlusOne));\n                        // cursor === 5 && without days\n                        const day2monthInputDot: boolean =\n                            (Number(inputValueSliceCursorPlusTwo) > monthsCount && cursor === 5) ||\n                            (this.specialCharacters.includes(inputValueCursorPlusOne) &&\n                                cursor === 5);\n                        // // day<10 && month<12 for paste whole data\n                        const day1monthPaste: boolean =\n                            Number(inputValueSliceMinusThreeMinusOne) > daysCount &&\n                            !this.specialCharacters.includes(\n                                inputValueSliceMinusThreeMinusOne as string\n                            ) &&\n                            !this.specialCharacters.includes(\n                                inputValueSliceMinusTwoCursor as string\n                            ) &&\n                            Number(inputValueSliceMinusTwoCursor) > monthsCount &&\n                            maskExpression.includes('d0');\n                        // 10<day<31 && month<12 for paste whole data\n                        const day2monthPaste: boolean =\n                            Number(inputValueSliceMinusThreeMinusOne) <= daysCount &&\n                            !this.specialCharacters.includes(\n                                inputValueSliceMinusThreeMinusOne as string\n                            ) &&\n                            !this.specialCharacters.includes(inputValueCursorMinusOne) &&\n                            Number(inputValueSliceMinusOnePlusOne) > monthsCount;\n                        if (\n                            (Number(inputSymbol) > 1 && this.leadZeroDateTime) ||\n                            withoutDays ||\n                            day1monthInput ||\n                            day2monthPaste ||\n                            day1monthPaste ||\n                            day2monthInput ||\n                            (day2monthInputDot && !this.leadZeroDateTime)\n                        ) {\n                            processedPosition = !this.leadZeroDateTime\n                                ? processedPosition + 1\n                                : processedPosition;\n                            cursor += 1;\n                            this._shiftStep(cursor);\n                            i--;\n                            if (this.leadZeroDateTime) {\n                                result += '0';\n                            }\n                            continue;\n                        }\n                    }\n                    result += inputSymbol;\n                    cursor++;\n                } else if (\n                    this.specialCharacters.includes(inputSymbol) &&\n                    maskExpression[cursor] === inputSymbol\n                ) {\n                    result += inputSymbol;\n                    cursor++;\n                } else if (\n                    this.specialCharacters.indexOf(\n                        maskExpression[cursor] ?? MaskExpression.EMPTY_STRING\n                    ) !== -1\n                ) {\n                    result += maskExpression[cursor];\n                    cursor++;\n                    this._shiftStep(cursor);\n                    i--;\n                } else if (\n                    maskExpression[cursor] === MaskExpression.NUMBER_NINE &&\n                    this.showMaskTyped\n                ) {\n                    this._shiftStep(cursor);\n                } else if (\n                    this.patterns[maskExpression[cursor] ?? MaskExpression.EMPTY_STRING] &&\n                    this.patterns[maskExpression[cursor] ?? MaskExpression.EMPTY_STRING]?.optional\n                ) {\n                    if (\n                        !!inputArray[cursor] &&\n                        maskExpression !== '***************' &&\n                        maskExpression !== '000.000.000-00' &&\n                        maskExpression !== '00.000.000/0000-00' &&\n                        !maskExpression.match(/^9+\\.0+$/) &&\n                        !this.patterns[maskExpression[cursor] ?? MaskExpression.EMPTY_STRING]\n                            ?.optional\n                    ) {\n                        result += inputArray[cursor];\n                    }\n                    if (\n                        maskExpression.includes(\n                            MaskExpression.NUMBER_NINE + MaskExpression.SYMBOL_STAR\n                        ) &&\n                        maskExpression.includes(\n                            MaskExpression.NUMBER_ZERO + MaskExpression.SYMBOL_STAR\n                        )\n                    ) {\n                        cursor++;\n                    }\n\n                    cursor++;\n                    i--;\n                } else if (\n                    this.maskExpression[cursor + 1] === MaskExpression.SYMBOL_STAR &&\n                    this._findSpecialChar(\n                        this.maskExpression[cursor + 2] ?? MaskExpression.EMPTY_STRING\n                    ) &&\n                    this._findSpecialChar(inputSymbol) === this.maskExpression[cursor + 2] &&\n                    multi\n                ) {\n                    cursor += 3;\n                    result += inputSymbol;\n                } else if (\n                    this.maskExpression[cursor + 1] === MaskExpression.SYMBOL_QUESTION &&\n                    this._findSpecialChar(\n                        this.maskExpression[cursor + 2] ?? MaskExpression.EMPTY_STRING\n                    ) &&\n                    this._findSpecialChar(inputSymbol) === this.maskExpression[cursor + 2] &&\n                    multi\n                ) {\n                    cursor += 3;\n                    result += inputSymbol;\n                } else if (\n                    this.showMaskTyped &&\n                    this.specialCharacters.indexOf(inputSymbol) < 0 &&\n                    inputSymbol !== this.placeHolderCharacter &&\n                    this.placeHolderCharacter.length === 1\n                ) {\n                    stepBack = true;\n                }\n            }\n        }\n        if (\n            result[processedPosition - 1] &&\n            result.length + 1 === maskExpression.length &&\n            this.specialCharacters.indexOf(\n                maskExpression[maskExpression.length - 1] ?? MaskExpression.EMPTY_STRING\n            ) !== -1\n        ) {\n            result += maskExpression[maskExpression.length - 1];\n        }\n        let newPosition: number = processedPosition + 1;\n\n        while (this._shift.has(newPosition)) {\n            shift++;\n            newPosition++;\n        }\n\n        let actualShift: number =\n            justPasted && !maskExpression.startsWith(MaskExpression.SEPARATOR)\n                ? cursor\n                : this._shift.has(processedPosition)\n                  ? shift\n                  : 0;\n        if (stepBack) {\n            actualShift--;\n        }\n\n        cb(actualShift, backspaceShift);\n        if (shift < 0) {\n            this._shift.clear();\n        }\n        let onlySpecial = false;\n        if (backspaced) {\n            onlySpecial = inputArray.every((char) => this.specialCharacters.includes(char));\n        }\n\n        let res = `${this.prefix}${onlySpecial ? MaskExpression.EMPTY_STRING : result}${\n            this.showMaskTyped ? '' : this.suffix\n        }`;\n\n        if (result.length === 0) {\n            res = this.instantPrefix ? `${this.prefix}${result}` : `${result}`;\n        }\n\n        const isSpecialCharacterMaskFirstSymbol =\n            processedValue.length === 1 &&\n            this.specialCharacters.includes(maskExpression[0] as string) &&\n            processedValue !== maskExpression[0];\n\n        if (\n            !this._checkSymbolMask(processedValue, maskExpression[1] as string) &&\n            isSpecialCharacterMaskFirstSymbol\n        ) {\n            return '';\n        }\n\n        if (result.includes(MaskExpression.MINUS) && this.prefix && this.allowNegativeNumbers) {\n            if (backspaced && result === MaskExpression.MINUS) {\n                return '';\n            }\n            res = `${MaskExpression.MINUS}${this.prefix}${result\n                .split(MaskExpression.MINUS)\n                .join(MaskExpression.EMPTY_STRING)}${this.suffix}`;\n        }\n        return res;\n    }\n\n    public _findDropSpecialChar(inputSymbol: string): undefined | string {\n        if (Array.isArray(this.dropSpecialCharacters)) {\n            return this.dropSpecialCharacters.find((val: string) => val === inputSymbol);\n        }\n        return this._findSpecialChar(inputSymbol);\n    }\n\n    public _findSpecialChar(inputSymbol: string): undefined | string {\n        return this.specialCharacters.find((val: string) => val === inputSymbol);\n    }\n\n    public _checkSymbolMask(inputSymbol: string, maskSymbol: string): boolean {\n        this.patterns = this.customPattern ? this.customPattern : this.patterns;\n        return (\n            (this.patterns[maskSymbol]?.pattern &&\n                this.patterns[maskSymbol]?.pattern.test(inputSymbol)) ??\n            false\n        );\n    }\n\n    private _formatWithSeparators = (\n        str: string,\n        thousandSeparatorChar: string,\n        decimalChars: string | string[],\n        precision: number\n    ) => {\n        let x: string[] = [];\n        let decimalChar = '';\n\n        if (Array.isArray(decimalChars)) {\n            const regExp = new RegExp(\n                decimalChars.map((v) => ('[\\\\^$.|?*+()'.indexOf(v) >= 0 ? `\\\\${v}` : v)).join('|')\n            );\n            x = str.split(regExp);\n            decimalChar = str.match(regExp)?.[0] ?? MaskExpression.EMPTY_STRING;\n        } else {\n            x = str.split(decimalChars);\n            decimalChar = decimalChars;\n        }\n        const decimals: string =\n            x.length > 1 ? `${decimalChar}${x[1]}` : MaskExpression.EMPTY_STRING;\n        let res: string = x[0] ?? MaskExpression.EMPTY_STRING;\n        const separatorLimit: string = this.separatorLimit.replace(\n            /\\s/g,\n            MaskExpression.EMPTY_STRING\n        );\n        if (separatorLimit && +separatorLimit) {\n            if (res[0] === MaskExpression.MINUS) {\n                res = `-${res.slice(1, res.length).slice(0, separatorLimit.length)}`;\n            } else {\n                res = res.slice(0, separatorLimit.length);\n            }\n        }\n        const rgx = /(\\d+)(\\d{3})/;\n\n        while (thousandSeparatorChar && rgx.test(res)) {\n            res = res.replace(rgx, '$1' + thousandSeparatorChar + '$2');\n        }\n\n        if (typeof precision === 'undefined') {\n            return res + decimals;\n        } else if (precision === 0) {\n            return res;\n        }\n        return res + decimals.substring(0, precision + 1);\n    };\n\n    private percentage = (str: string): boolean => {\n        const sanitizedStr = str.replace(',', '.');\n        const value = Number(\n            this.allowNegativeNumbers && str.includes(MaskExpression.MINUS)\n                ? sanitizedStr.slice(1, str.length)\n                : sanitizedStr\n        );\n\n        return !isNaN(value) && value >= 0 && value <= 100;\n    };\n\n    public getPrecision = (maskExpression: string): number => {\n        const x: string[] = maskExpression.split(MaskExpression.DOT);\n        if (x.length > 1) {\n            return Number(x[x.length - 1]);\n        }\n\n        return Infinity;\n    };\n\n    private checkAndRemoveSuffix = (inputValue: string): string => {\n        for (let i = this.suffix?.length - 1; i >= 0; i--) {\n            const substr = this.suffix.substring(i, this.suffix?.length);\n            if (\n                inputValue.includes(substr) &&\n                i !== this.suffix?.length - 1 &&\n                (i - 1 < 0 ||\n                    !inputValue.includes(this.suffix.substring(i - 1, this.suffix?.length)))\n            ) {\n                return inputValue.replace(substr, MaskExpression.EMPTY_STRING);\n            }\n        }\n        return inputValue;\n    };\n\n    private checkInputPrecision = (\n        inputValue: string,\n        precision: number,\n        decimalMarker: NgxMaskConfig['decimalMarker']\n    ): string => {\n        let processedInputValue = inputValue;\n        let processedDecimalMarker = decimalMarker;\n\n        if (precision < Infinity) {\n            // TODO need think about decimalMarker\n            if (Array.isArray(processedDecimalMarker)) {\n                const marker = processedDecimalMarker.find((dm) => dm !== this.thousandSeparator);\n\n                processedDecimalMarker = marker ? marker : processedDecimalMarker[0];\n            }\n            const precisionRegEx = new RegExp(\n                this._charToRegExpExpression(processedDecimalMarker) + `\\\\d{${precision}}.*$`\n            );\n            const precisionMatch: RegExpMatchArray | null =\n                processedInputValue.match(precisionRegEx);\n            const precisionMatchLength: number = (precisionMatch && precisionMatch[0]?.length) ?? 0;\n            if (precisionMatchLength - 1 > precision) {\n                const diff = precisionMatchLength - 1 - precision;\n\n                processedInputValue = processedInputValue.substring(\n                    0,\n                    processedInputValue.length - diff\n                );\n            }\n            if (\n                precision === 0 &&\n                this._compareOrIncludes(\n                    processedInputValue[processedInputValue.length - 1],\n                    processedDecimalMarker,\n                    this.thousandSeparator\n                )\n            ) {\n                processedInputValue = processedInputValue.substring(\n                    0,\n                    processedInputValue.length - 1\n                );\n            }\n        }\n        return processedInputValue;\n    };\n\n    private _stripToDecimal(str: string): string {\n        return str\n            .split(MaskExpression.EMPTY_STRING)\n            .filter((i: string, idx: number) => {\n                const isDecimalMarker =\n                    typeof this.decimalMarker === 'string'\n                        ? i === this.decimalMarker\n                        : // TODO (inepipenko) use utility type\n                          this.decimalMarker.includes(\n                              i as MaskExpression.COMMA | MaskExpression.DOT\n                          );\n                return (\n                    i.match('^-?\\\\d') ||\n                    i === this.thousandSeparator ||\n                    isDecimalMarker ||\n                    (i === MaskExpression.MINUS && idx === 0 && this.allowNegativeNumbers)\n                );\n            })\n            .join(MaskExpression.EMPTY_STRING);\n    }\n\n    private _charToRegExpExpression(char: string): string {\n        // if (Array.isArray(char)) {\n        // \treturn char.map((v) => ('[\\\\^$.|?*+()'.indexOf(v) >= 0 ? `\\\\${v}` : v)).join('|');\n        // }\n        if (char) {\n            const charsToEscape = '[\\\\^$.|?*+()';\n            return char === ' ' ? '\\\\s' : charsToEscape.indexOf(char) >= 0 ? `\\\\${char}` : char;\n        }\n        return char;\n    }\n\n    private _shiftStep(cursor: number) {\n        this._shift.add(cursor + this.prefix.length || 0);\n    }\n\n    protected _compareOrIncludes<T>(value: T, comparedValue: T | T[], excludedValue: T): boolean {\n        return Array.isArray(comparedValue)\n            ? comparedValue.filter((v) => v !== excludedValue).includes(value)\n            : value === comparedValue;\n    }\n\n    private _validIP(valuesIP: string[]): boolean {\n        return !(\n            valuesIP.length === 4 &&\n            !valuesIP.some((value: string, index: number) => {\n                if (valuesIP.length !== index + 1) {\n                    return value === MaskExpression.EMPTY_STRING || Number(value) > 255;\n                }\n                return value === MaskExpression.EMPTY_STRING || Number(value.substring(0, 3)) > 255;\n            })\n        );\n    }\n\n    private _splitPercentZero(value: string): string {\n        if (value === MaskExpression.MINUS && this.allowNegativeNumbers) {\n            return value;\n        }\n        const decimalIndex =\n            typeof this.decimalMarker === 'string'\n                ? value.indexOf(this.decimalMarker)\n                : value.indexOf(MaskExpression.DOT);\n        const emptyOrMinus =\n            this.allowNegativeNumbers && value.includes(MaskExpression.MINUS) ? '-' : '';\n        if (decimalIndex === -1) {\n            const parsedValue = parseInt(emptyOrMinus ? value.slice(1, value.length) : value, 10);\n            return isNaN(parsedValue)\n                ? MaskExpression.EMPTY_STRING\n                : `${emptyOrMinus}${parsedValue}`;\n        } else {\n            const integerPart = parseInt(value.replace('-', '').substring(0, decimalIndex), 10);\n            const decimalPart = value.substring(decimalIndex + 1);\n            const integerString = isNaN(integerPart) ? '' : integerPart.toString();\n\n            const decimal =\n                typeof this.decimalMarker === 'string' ? this.decimalMarker : MaskExpression.DOT;\n\n            return integerString === MaskExpression.EMPTY_STRING\n                ? MaskExpression.EMPTY_STRING\n                : `${emptyOrMinus}${integerString}${decimal}${decimalPart}`;\n        }\n    }\n\n    private _findFirstNonZeroAndDecimalIndex(inputString: string, decimalMarker: '.' | ',') {\n        let decimalMarkerIndex: number | null = null;\n        let nonZeroIndex: number | null = null;\n\n        for (let i = 0; i < inputString.length; i++) {\n            const char = inputString[i];\n\n            if (char === decimalMarker && decimalMarkerIndex === null) {\n                decimalMarkerIndex = i;\n            }\n\n            if (char && char >= '1' && char <= '9' && nonZeroIndex === null) {\n                nonZeroIndex = i;\n            }\n\n            if (decimalMarkerIndex !== null && nonZeroIndex !== null) {\n                break;\n            }\n        }\n\n        return {\n            decimalMarkerIndex,\n            nonZeroIndex,\n        };\n    }\n}\n", "import { ElementRef, inject, Injectable, Renderer2 } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n\nimport type { NgxMaskConfig } from './ngx-mask.config';\nimport { NGX_MASK_CONFIG } from './ngx-mask.config';\nimport { NgxMaskApplierService } from './ngx-mask-applier.service';\nimport { MaskExpression } from './ngx-mask-expression.enum';\n\n@Injectable()\nexport class NgxMaskService extends NgxMaskApplierService {\n    public isNumberValue = false;\n    public maskIsShown = '';\n    public selStart: number | null = null;\n    public selEnd: number | null = null;\n    public maskChanged = false;\n    public maskExpressionArray: string[] = [];\n    public previousValue = '';\n    public currentValue = '';\n    /**\n     * Whether we are currently in writeValue function, in this case when applying the mask we don't want to trigger onChange function,\n     * since writeValue should be a one way only process of writing the DOM value based on the Angular model value.\n     */\n    public writingValue = false;\n\n    private _emitValue = false;\n    private _start!: number;\n    private _end!: number;\n\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    public onChange = (_: any) => {};\n\n    public readonly _elementRef = inject(ElementRef, { optional: true });\n\n    private readonly document = inject(DOCUMENT);\n\n    protected override _config = inject<NgxMaskConfig>(NGX_MASK_CONFIG);\n\n    private readonly _renderer = inject(Renderer2, { optional: true });\n\n    /**\n     * Applies the mask to the input value.\n     * @param inputValue The input value to be masked.\n     * @param maskExpression The mask expression to apply.\n     * @param position The position in the input value.\n     * @param justPasted Whether the value was just pasted.\n     * @param backspaced Whether the value was backspaced.\n     * @param cb Callback function.\n     * @returns The masked value.\n     */\n    public override applyMask(\n        inputValue: string,\n        maskExpression: string,\n        position = 0,\n        justPasted = false,\n        backspaced = false,\n        // eslint-disable-next-line @typescript-eslint/no-empty-function\n        cb: (...args: any[]) => any = () => {}\n    ): string {\n        // If no mask expression, return the input value or the actual value\n        if (!maskExpression) {\n            return inputValue !== this.actualValue ? this.actualValue : inputValue;\n        }\n\n        // Show mask in input if required\n        this.maskIsShown = this.showMaskTyped\n            ? this.showMaskInInput()\n            : MaskExpression.EMPTY_STRING;\n\n        // Handle specific mask expressions\n        if (this.maskExpression === MaskExpression.IP && this.showMaskTyped) {\n            this.maskIsShown = this.showMaskInInput(inputValue || MaskExpression.HASH);\n        }\n        if (this.maskExpression === MaskExpression.CPF_CNPJ && this.showMaskTyped) {\n            this.maskIsShown = this.showMaskInInput(inputValue || MaskExpression.HASH);\n        }\n\n        // Handle empty input value with mask typed\n        if (!inputValue && this.showMaskTyped) {\n            this.formControlResult(this.prefix);\n            return `${this.prefix}${this.maskIsShown}${this.suffix}`;\n        }\n\n        const getSymbol: string =\n            !!inputValue && typeof this.selStart === 'number'\n                ? (inputValue[this.selStart] ?? MaskExpression.EMPTY_STRING)\n                : MaskExpression.EMPTY_STRING;\n        let newInputValue = '';\n        let newPosition = position;\n\n        // Handle hidden input or input with asterisk symbol\n        if (\n            (this.hiddenInput ||\n                (inputValue && inputValue.indexOf(MaskExpression.SYMBOL_STAR) >= 0)) &&\n            !this.writingValue\n        ) {\n            let actualResult: string[] =\n                inputValue && inputValue.length === 1\n                    ? inputValue.split(MaskExpression.EMPTY_STRING)\n                    : this.actualValue.split(MaskExpression.EMPTY_STRING);\n\n            // Handle backspace\n            if (backspaced) {\n                actualResult = actualResult\n                    .slice(0, position)\n                    .concat(actualResult.slice(position + 1));\n            }\n\n            // Remove mask if showMaskTyped is true\n            if (this.showMaskTyped) {\n                // eslint-disable-next-line no-param-reassign\n                inputValue = this.removeMask(inputValue);\n                actualResult = this.removeMask(actualResult.join('')).split(\n                    MaskExpression.EMPTY_STRING\n                );\n            }\n\n            // Handle selection start and end\n            if (typeof this.selStart === 'object' && typeof this.selEnd === 'object') {\n                this.selStart = Number(this.selStart);\n                this.selEnd = Number(this.selEnd);\n            } else {\n                if (inputValue !== MaskExpression.EMPTY_STRING && actualResult.length) {\n                    if (typeof this.selStart === 'number' && typeof this.selEnd === 'number') {\n                        if (inputValue.length > actualResult.length) {\n                            actualResult.splice(this.selStart, 0, getSymbol);\n                        } else if (inputValue.length < actualResult.length) {\n                            if (actualResult.length - inputValue.length === 1) {\n                                if (backspaced) {\n                                    actualResult.splice(this.selStart - 1, 1);\n                                } else {\n                                    actualResult.splice(inputValue.length - 1, 1);\n                                }\n                            } else {\n                                actualResult.splice(this.selStart, this.selEnd - this.selStart);\n                            }\n                        }\n                    }\n                } else {\n                    actualResult = [];\n                }\n            }\n\n            // Remove mask if showMaskTyped is true and hiddenInput is false\n            if (this.showMaskTyped && !this.hiddenInput) {\n                newInputValue = this.removeMask(inputValue);\n            }\n\n            // Handle actual value length\n            if (this.actualValue.length) {\n                if (actualResult.length < inputValue.length) {\n                    newInputValue = this.shiftTypedSymbols(\n                        actualResult.join(MaskExpression.EMPTY_STRING)\n                    );\n                } else if (actualResult.length === inputValue.length) {\n                    newInputValue = actualResult.join(MaskExpression.EMPTY_STRING);\n                } else {\n                    newInputValue = inputValue;\n                }\n            } else {\n                newInputValue = inputValue;\n            }\n        }\n\n        // Handle just pasted input\n        if (justPasted && (this.hiddenInput || !this.hiddenInput)) {\n            newInputValue = inputValue;\n        }\n\n        // Handle backspace with special characters\n        if (\n            backspaced &&\n            this.specialCharacters.indexOf(\n                this.maskExpression[newPosition] ?? MaskExpression.EMPTY_STRING\n            ) !== -1 &&\n            this.showMaskTyped &&\n            !this.prefix\n        ) {\n            newInputValue = this.currentValue;\n        }\n\n        // Handle deleted special character\n        if (this.deletedSpecialCharacter && newPosition) {\n            if (\n                this.specialCharacters.includes(\n                    this.actualValue.slice(newPosition, newPosition + 1)\n                )\n            ) {\n                newPosition = newPosition + 1;\n            } else if (\n                maskExpression.slice(newPosition - 1, newPosition + 1) !== MaskExpression.MONTHS\n            ) {\n                newPosition = newPosition - 2;\n            }\n\n            this.deletedSpecialCharacter = false;\n        }\n\n        // Remove mask if showMaskTyped is true and placeHolderCharacter length is 1\n        if (\n            this.showMaskTyped &&\n            this.placeHolderCharacter.length === 1 &&\n            !this.leadZeroDateTime\n        ) {\n            newInputValue = this.removeMask(newInputValue);\n        }\n\n        // Handle mask changed\n        if (this.maskChanged) {\n            newInputValue = inputValue;\n        } else {\n            newInputValue =\n                Boolean(newInputValue) && newInputValue.length ? newInputValue : inputValue;\n        }\n\n        // Handle showMaskTyped and keepCharacterPositions\n        if (\n            this.showMaskTyped &&\n            this.keepCharacterPositions &&\n            this.actualValue &&\n            !justPasted &&\n            !this.writingValue\n        ) {\n            const value = this.dropSpecialCharacters\n                ? this.removeMask(this.actualValue)\n                : this.actualValue;\n            this.formControlResult(value);\n            return this.actualValue\n                ? this.actualValue\n                : `${this.prefix}${this.maskIsShown}${this.suffix}`;\n        }\n\n        // Apply the mask using the parent class method\n        const result: string = super.applyMask(\n            newInputValue,\n            maskExpression,\n            newPosition,\n            justPasted,\n            backspaced,\n            cb\n        );\n\n        this.actualValue = this.getActualValue(result);\n\n        // handle some separator implications:\n        // a.) adjust decimalMarker default (. -> ,) if thousandSeparator is a dot\n        if (\n            this.thousandSeparator === MaskExpression.DOT &&\n            this.decimalMarker === MaskExpression.DOT\n        ) {\n            this.decimalMarker = MaskExpression.COMMA;\n        }\n        // b) remove decimal marker from list of special characters to mask\n        if (\n            this.maskExpression.startsWith(MaskExpression.SEPARATOR) &&\n            this.dropSpecialCharacters === true\n        ) {\n            this.specialCharacters = this.specialCharacters.filter(\n                (item: string) =>\n                    !this._compareOrIncludes(item, this.decimalMarker, this.thousandSeparator) //item !== this.decimalMarker, // !\n            );\n        }\n\n        // Update previous and current values\n        if (result || result === '') {\n            this.previousValue = this.currentValue;\n            this.currentValue = result;\n\n            this._emitValue =\n                this.previousValue !== this.currentValue ||\n                (newInputValue !== this.currentValue && this.writingValue) ||\n                (this.previousValue === this.currentValue && justPasted);\n        }\n\n        // Propagate the input value back to the Angular model\n        // eslint-disable-next-line no-unused-expressions,@typescript-eslint/no-unused-expressions\n        this._emitValue ? this.formControlResult(result) : '';\n\n        // Handle hidden input and showMaskTyped\n        if (!this.showMaskTyped || (this.showMaskTyped && this.hiddenInput)) {\n            if (this.hiddenInput) {\n                return `${this.hideInput(result, this.maskExpression)}${this.maskIsShown.slice(result.length)}`;\n            }\n            return result;\n        }\n\n        const resLen: number = result.length;\n        const prefNmask = `${this.prefix}${this.maskIsShown}${this.suffix}`;\n\n        // Handle specific mask expressions\n        if (this.maskExpression.includes(MaskExpression.HOURS)) {\n            const countSkipedSymbol = this._numberSkipedSymbols(result);\n            return `${result}${prefNmask.slice(resLen + countSkipedSymbol)}`;\n        } else if (\n            this.maskExpression === MaskExpression.IP ||\n            this.maskExpression === MaskExpression.CPF_CNPJ\n        ) {\n            return `${result}${prefNmask}`;\n        }\n\n        return `${result}${prefNmask.slice(resLen)}`;\n    }\n\n    // get the number of characters that were shifted\n    private _numberSkipedSymbols(value: string): number {\n        const regex = /(^|\\D)(\\d\\D)/g;\n        let match = regex.exec(value);\n        let countSkipedSymbol = 0;\n        while (match != null) {\n            countSkipedSymbol += 1;\n            match = regex.exec(value);\n        }\n        return countSkipedSymbol;\n    }\n\n    public applyValueChanges(\n        position: number,\n        justPasted: boolean,\n        backspaced: boolean,\n        // eslint-disable-next-line @typescript-eslint/no-empty-function\n        cb: (...args: any[]) => any = () => {}\n    ): void {\n        const formElement = this._elementRef?.nativeElement;\n        if (!formElement) {\n            return;\n        }\n\n        formElement.value = this.applyMask(\n            formElement.value,\n            this.maskExpression,\n            position,\n            justPasted,\n            backspaced,\n            cb\n        );\n        if (formElement === this._getActiveElement()) {\n            return;\n        }\n        this.clearIfNotMatchFn();\n    }\n\n    public hideInput(inputValue: string, maskExpression: string): string {\n        return inputValue\n            .split(MaskExpression.EMPTY_STRING)\n            .map((curr: string, index: number) => {\n                if (\n                    this.patterns &&\n                    this.patterns[maskExpression[index] ?? MaskExpression.EMPTY_STRING] &&\n                    this.patterns[maskExpression[index] ?? MaskExpression.EMPTY_STRING]?.symbol\n                ) {\n                    return this.patterns[maskExpression[index] ?? MaskExpression.EMPTY_STRING]\n                        ?.symbol;\n                }\n                return curr;\n            })\n            .join(MaskExpression.EMPTY_STRING);\n    }\n\n    // this function is not necessary, it checks result against maskExpression\n    public getActualValue(res: string): string {\n        const compare: string[] = res\n            .split(MaskExpression.EMPTY_STRING)\n            .filter((symbol: string, i: number) => {\n                const maskChar = this.maskExpression[i] ?? MaskExpression.EMPTY_STRING;\n                return (\n                    this._checkSymbolMask(symbol, maskChar) ||\n                    (this.specialCharacters.includes(maskChar) && symbol === maskChar)\n                );\n            });\n        if (compare.join(MaskExpression.EMPTY_STRING) === res) {\n            return compare.join(MaskExpression.EMPTY_STRING);\n        }\n        return res;\n    }\n\n    public shiftTypedSymbols(inputValue: string): string {\n        let symbolToReplace = '';\n        const newInputValue: (string | undefined)[] =\n            (inputValue &&\n                inputValue\n                    .split(MaskExpression.EMPTY_STRING)\n                    .map((currSymbol: string, index: number) => {\n                        if (\n                            this.specialCharacters.includes(\n                                inputValue[index + 1] ?? MaskExpression.EMPTY_STRING\n                            ) &&\n                            inputValue[index + 1] !== this.maskExpression[index + 1]\n                        ) {\n                            symbolToReplace = currSymbol;\n                            return inputValue[index + 1];\n                        }\n                        if (symbolToReplace.length) {\n                            const replaceSymbol: string = symbolToReplace;\n                            symbolToReplace = MaskExpression.EMPTY_STRING;\n                            return replaceSymbol;\n                        }\n                        return currSymbol;\n                    })) ||\n            [];\n        return newInputValue.join(MaskExpression.EMPTY_STRING);\n    }\n\n    /**\n     * Convert number value to string\n     * 3.1415 -> '3.1415'\n     * 1e-7 -> '0.0000001'\n     */\n    public numberToString(value: number | string): string {\n        if (\n            (!value && value !== 0) ||\n            (this.maskExpression.startsWith(MaskExpression.SEPARATOR) &&\n                (this.leadZero || !this.dropSpecialCharacters)) ||\n            (this.maskExpression.startsWith(MaskExpression.SEPARATOR) &&\n                this.separatorLimit.length > 14 &&\n                String(value).length > 14)\n        ) {\n            return String(value);\n        }\n        return Number(value)\n            .toLocaleString('fullwide', {\n                useGrouping: false,\n                maximumFractionDigits: 20,\n            })\n            .replace(`/${MaskExpression.MINUS}/`, MaskExpression.MINUS);\n    }\n\n    public showMaskInInput(inputVal?: string): string {\n        if (this.showMaskTyped && !!this.shownMaskExpression) {\n            if (this.maskExpression.length !== this.shownMaskExpression.length) {\n                throw new Error('Mask expression must match mask placeholder length');\n            } else {\n                return this.shownMaskExpression;\n            }\n        } else if (this.showMaskTyped) {\n            if (inputVal) {\n                if (this.maskExpression === MaskExpression.IP) {\n                    return this._checkForIp(inputVal);\n                }\n                if (this.maskExpression === MaskExpression.CPF_CNPJ) {\n                    return this._checkForCpfCnpj(inputVal);\n                }\n            }\n            if (this.placeHolderCharacter.length === this.maskExpression.length) {\n                return this.placeHolderCharacter;\n            }\n            return this.maskExpression.replace(/\\w/g, this.placeHolderCharacter);\n        }\n        return '';\n    }\n\n    public clearIfNotMatchFn(): void {\n        const formElement = this._elementRef?.nativeElement;\n        if (!formElement) {\n            return;\n        }\n        if (\n            this.clearIfNotMatch &&\n            this.prefix.length + this.maskExpression.length + this.suffix.length !==\n                formElement.value.replace(this.placeHolderCharacter, MaskExpression.EMPTY_STRING)\n                    .length\n        ) {\n            this.formElementProperty = ['value', MaskExpression.EMPTY_STRING];\n            this.applyMask('', this.maskExpression);\n        }\n    }\n\n    public set formElementProperty([name, value]: [string, string | boolean]) {\n        if (!this._renderer || !this._elementRef) {\n            return;\n        }\n        //[TODO]: andriikamaldinov1 find better solution\n        Promise.resolve().then(() =>\n            this._renderer?.setProperty(this._elementRef?.nativeElement, name, value)\n        );\n    }\n\n    public checkDropSpecialCharAmount(mask: string): number {\n        const chars: string[] = mask\n            .split(MaskExpression.EMPTY_STRING)\n            .filter((item: string) => this._findDropSpecialChar(item));\n        return chars.length;\n    }\n\n    public removeMask(inputValue: string): string {\n        return this._removeMask(\n            this._removeSuffix(this._removePrefix(inputValue)),\n            this.specialCharacters.concat('_').concat(this.placeHolderCharacter)\n        );\n    }\n\n    private _checkForIp(inputVal: string): string {\n        if (inputVal === MaskExpression.HASH) {\n            return `${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n        }\n        const arr: string[] = [];\n        // eslint-disable-next-line @typescript-eslint/prefer-for-of\n        for (let i = 0; i < inputVal.length; i++) {\n            const value = inputVal[i] ?? MaskExpression.EMPTY_STRING;\n            if (!value) {\n                continue;\n            }\n            if (value.match('\\\\d')) {\n                arr.push(value);\n            }\n        }\n        if (arr.length <= 3) {\n            return `${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n        }\n        if (arr.length > 3 && arr.length <= 6) {\n            return `${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n        }\n        if (arr.length > 6 && arr.length <= 9) {\n            return this.placeHolderCharacter;\n        }\n        if (arr.length > 9 && arr.length <= 12) {\n            return '';\n        }\n        return '';\n    }\n\n    private _checkForCpfCnpj(inputVal: string): string {\n        const cpf =\n            `${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `-${this.placeHolderCharacter}${this.placeHolderCharacter}`;\n        const cnpj =\n            `${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `/${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `-${this.placeHolderCharacter}${this.placeHolderCharacter}`;\n\n        if (inputVal === MaskExpression.HASH) {\n            return cpf;\n        }\n        const arr: string[] = [];\n        // eslint-disable-next-line @typescript-eslint/prefer-for-of\n        for (let i = 0; i < inputVal.length; i++) {\n            const value = inputVal[i] ?? MaskExpression.EMPTY_STRING;\n            if (!value) {\n                continue;\n            }\n            if (value.match('\\\\d')) {\n                arr.push(value);\n            }\n        }\n        if (arr.length <= 3) {\n            return cpf.slice(arr.length, cpf.length);\n        }\n        if (arr.length > 3 && arr.length <= 6) {\n            return cpf.slice(arr.length + 1, cpf.length);\n        }\n        if (arr.length > 6 && arr.length <= 9) {\n            return cpf.slice(arr.length + 2, cpf.length);\n        }\n        if (arr.length > 9 && arr.length < 11) {\n            return cpf.slice(arr.length + 3, cpf.length);\n        }\n        if (arr.length === 11) {\n            return '';\n        }\n        if (arr.length === 12) {\n            if (inputVal.length === 17) {\n                return cnpj.slice(16, cnpj.length);\n            }\n            return cnpj.slice(15, cnpj.length);\n        }\n        if (arr.length > 12 && arr.length <= 14) {\n            return cnpj.slice(arr.length + 4, cnpj.length);\n        }\n        return '';\n    }\n\n    /**\n     * Recursively determine the current active element by navigating the Shadow DOM until the Active Element is found.\n     */\n    private _getActiveElement(document: DocumentOrShadowRoot = this.document): Element | null {\n        const shadowRootEl = document?.activeElement?.shadowRoot;\n        if (!shadowRootEl?.activeElement) {\n            return document.activeElement;\n        } else {\n            return this._getActiveElement(shadowRootEl);\n        }\n    }\n\n    /**\n     * Propogates the input value back to the Angular model by triggering the onChange function. It won't do this if writingValue\n     * is true. If that is true it means we are currently in the writeValue function, which is supposed to only update the actual\n     * DOM element based on the Angular model value. It should be a one way process, i.e. writeValue should not be modifying the Angular\n     * model value too. Therefore, we don't trigger onChange in this scenario.\n     * @param inputValue the current form input value\n     */\n    private formControlResult(inputValue: string): void {\n        const outputTransformFn = this.outputTransformFn\n            ? this.outputTransformFn\n            : (v: unknown) => v;\n        this.writingValue = false;\n        this.maskChanged = false;\n        if (Array.isArray(this.dropSpecialCharacters)) {\n            this.onChange(\n                outputTransformFn(\n                    this._toNumber(\n                        this._checkSymbols(\n                            this._removeMask(\n                                this._removeSuffix(this._removePrefix(inputValue)),\n                                this.dropSpecialCharacters\n                            )\n                        )\n                    )\n                )\n            );\n        } else if (\n            this.dropSpecialCharacters ||\n            (!this.dropSpecialCharacters && this.prefix === inputValue)\n        ) {\n            this.onChange(\n                outputTransformFn(\n                    this._toNumber(\n                        this._checkSymbols(this._removeSuffix(this._removePrefix(inputValue)))\n                    )\n                )\n            );\n        } else {\n            this.onChange(outputTransformFn(this._toNumber(inputValue)));\n        }\n    }\n\n    private _toNumber(value: string | number | undefined | null) {\n        if (!this.isNumberValue || value === MaskExpression.EMPTY_STRING) {\n            return value;\n        }\n        if (\n            this.maskExpression.startsWith(MaskExpression.SEPARATOR) &&\n            (this.leadZero || !this.dropSpecialCharacters)\n        ) {\n            return value;\n        }\n\n        if (String(value).length > 14 && this.maskExpression.startsWith(MaskExpression.SEPARATOR)) {\n            return String(value);\n        }\n\n        const num = Number(value);\n        if (this.maskExpression.startsWith(MaskExpression.SEPARATOR) && Number.isNaN(num)) {\n            const val = String(value).replace(',', '.');\n            return Number(val);\n        }\n\n        return Number.isNaN(num) ? value : num;\n    }\n\n    private _removeMask(value: string, specialCharactersForRemove: string[]): string {\n        if (\n            this.maskExpression.startsWith(MaskExpression.PERCENT) &&\n            value.includes(MaskExpression.DOT)\n        ) {\n            return value;\n        }\n\n        return value\n            ? value.replace(\n                  this._regExpForRemove(specialCharactersForRemove),\n                  MaskExpression.EMPTY_STRING\n              )\n            : value;\n    }\n\n    private _removePrefix(value: string): string {\n        if (!this.prefix) {\n            return value;\n        }\n        return value ? value.replace(this.prefix, MaskExpression.EMPTY_STRING) : value;\n    }\n\n    private _removeSuffix(value: string): string {\n        if (!this.suffix) {\n            return value;\n        }\n        return value ? value.replace(this.suffix, MaskExpression.EMPTY_STRING) : value;\n    }\n\n    private _retrieveSeparatorValue(result: string): string {\n        let specialCharacters = Array.isArray(this.dropSpecialCharacters)\n            ? this.specialCharacters.filter((v) => {\n                  return (this.dropSpecialCharacters as string[]).includes(v);\n              })\n            : this.specialCharacters;\n        if (\n            !this.deletedSpecialCharacter &&\n            this._checkPatternForSpace() &&\n            result.includes(MaskExpression.WHITE_SPACE) &&\n            this.maskExpression.includes(MaskExpression.SYMBOL_STAR)\n        ) {\n            specialCharacters = specialCharacters.filter(\n                (char) => char !== MaskExpression.WHITE_SPACE\n            );\n        }\n\n        return this._removeMask(result, specialCharacters as string[]);\n    }\n\n    private _regExpForRemove(specialCharactersForRemove: string[]): RegExp {\n        return new RegExp(\n            specialCharactersForRemove.map((item: string) => `\\\\${item}`).join('|'),\n            'gi'\n        );\n    }\n\n    private _replaceDecimalMarkerToDot(value: string): string {\n        const markers = Array.isArray(this.decimalMarker)\n            ? this.decimalMarker\n            : [this.decimalMarker];\n\n        return value.replace(this._regExpForRemove(markers), MaskExpression.DOT);\n    }\n\n    public _checkSymbols(result: string): string | number | undefined | null {\n        let processedResult = result;\n\n        if (processedResult === MaskExpression.EMPTY_STRING) {\n            return processedResult;\n        }\n\n        if (\n            this.maskExpression.startsWith(MaskExpression.PERCENT) &&\n            this.decimalMarker === MaskExpression.COMMA\n        ) {\n            processedResult = processedResult.replace(MaskExpression.COMMA, MaskExpression.DOT);\n        }\n        const separatorPrecision: number | null = this._retrieveSeparatorPrecision(\n            this.maskExpression\n        );\n\n        const separatorValue: string =\n            this.specialCharacters.length === 0\n                ? this._retrieveSeparatorValue(processedResult)\n                : this._replaceDecimalMarkerToDot(this._retrieveSeparatorValue(processedResult));\n\n        if (!this.isNumberValue) {\n            return separatorValue;\n        }\n        if (separatorPrecision) {\n            if (processedResult === this.decimalMarker) {\n                return null;\n            }\n            if (separatorValue.length > 14) {\n                return String(separatorValue);\n            }\n            return this._checkPrecision(this.maskExpression, separatorValue);\n        } else {\n            return separatorValue;\n        }\n    }\n\n    private _checkPatternForSpace(): boolean {\n        for (const key in this.patterns) {\n            // eslint-disable-next-line no-prototype-builtins\n            if (this.patterns[key] && this.patterns[key]?.hasOwnProperty('pattern')) {\n                const patternString = this.patterns[key]?.pattern.toString();\n                const pattern = this.patterns[key]?.pattern;\n                if (\n                    (patternString?.includes(MaskExpression.WHITE_SPACE) as boolean) &&\n                    pattern?.test(this.maskExpression)\n                ) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n    // TODO should think about helpers or separting decimal precision to own property\n    private _retrieveSeparatorPrecision(maskExpretion: string): number | null {\n        const matcher: RegExpMatchArray | null = maskExpretion.match(\n            new RegExp(`^separator\\\\.([^d]*)`)\n        );\n        return matcher ? Number(matcher[1]) : null;\n    }\n\n    public _checkPrecision(separatorExpression: string, separatorValue: string): number | string {\n        const separatorPrecision = this.getPrecision(separatorExpression);\n        let value = separatorValue;\n\n        if (\n            separatorExpression.indexOf('2') > 0 ||\n            (this.leadZero && Number(separatorPrecision) > 0)\n        ) {\n            if (this.decimalMarker === MaskExpression.COMMA && this.leadZero) {\n                value = value.replace(',', '.');\n            }\n            return this.leadZero\n                ? Number(value).toFixed(Number(separatorPrecision))\n                : Number(value).toFixed(2);\n        }\n        return this.numberToString(value);\n    }\n\n    public _repeatPatternSymbols(maskExp: string): string {\n        return (\n            (maskExp.match(/{[0-9]+}/) &&\n                maskExp\n                    .split(MaskExpression.EMPTY_STRING)\n                    .reduce((accum: string, currVal: string, index: number): string => {\n                        this._start =\n                            currVal === MaskExpression.CURLY_BRACKETS_LEFT ? index : this._start;\n                        if (currVal !== MaskExpression.CURLY_BRACKETS_RIGHT) {\n                            return this._findSpecialChar(currVal) ? accum + currVal : accum;\n                        }\n                        this._end = index;\n                        const repeatNumber = Number(maskExp.slice(this._start + 1, this._end));\n                        const replaceWith: string = new Array(repeatNumber + 1).join(\n                            maskExp[this._start - 1]\n                        );\n                        if (\n                            maskExp.slice(0, this._start).length > 1 &&\n                            maskExp.includes(MaskExpression.LETTER_S)\n                        ) {\n                            const symbols = maskExp.slice(0, this._start - 1);\n                            return symbols.includes(MaskExpression.CURLY_BRACKETS_LEFT)\n                                ? accum + replaceWith\n                                : symbols + accum + replaceWith;\n                        } else {\n                            return accum + replaceWith;\n                        }\n                    }, '')) ||\n            maskExp\n        );\n    }\n\n    public currentLocaleDecimalMarker(): string {\n        return (1.1).toLocaleString().substring(1, 2);\n    }\n}\n", "import type { EnvironmentProviders, Provider } from '@angular/core';\nimport { inject, makeEnvironmentProviders } from '@angular/core';\n\nimport type { NgxMaskOptions } from './ngx-mask.config';\nimport { NGX_MASK_CONFIG, INITIAL_CONFIG, initialConfig, NEW_CONFIG } from './ngx-mask.config';\nimport { NgxMaskService } from './ngx-mask.service';\n\n/**\n * @internal\n */\nfunction _configFactory(): NgxMaskOptions {\n    const initConfig = inject<NgxMaskOptions>(INITIAL_CONFIG);\n    const configValue = inject<NgxMaskOptions | (() => NgxMaskOptions)>(NEW_CONFIG);\n\n    return configValue instanceof Function\n        ? { ...initConfig, ...configValue() }\n        : { ...initConfig, ...configValue };\n}\n\nexport function provideNgxMask(configValue?: NgxMaskOptions | (() => NgxMaskOptions)): Provider[] {\n    return [\n        {\n            provide: NEW_CONFIG,\n            useValue: configValue,\n        },\n        {\n            provide: INITIAL_CONFIG,\n            useValue: initialConfig,\n        },\n        {\n            provide: NGX_MASK_CONFIG,\n            useFactory: _configFactory,\n        },\n        NgxMaskService,\n    ];\n}\n\nexport function provideEnvironmentNgxMask(\n    configValue?: NgxMaskOptions | (() => NgxMaskOptions)\n): EnvironmentProviders {\n    return makeEnvironmentProviders(provideNgxMask(configValue));\n}\n", "import { DOCUMENT } from '@angular/common';\nimport type { OnChanges, SimpleChanges } from '@angular/core';\nimport { signal, input, output, Directive, HostListener, inject } from '@angular/core';\nimport type {\n    ControlValueAccessor,\n    FormControl,\n    ValidationErrors,\n    Validator,\n} from '@angular/forms';\nimport { NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';\n\nimport type { CustomKeyboardEvent } from './custom-keyboard-event';\nimport type { NgxMaskConfig } from './ngx-mask.config';\nimport { NGX_MASK_CONFIG, timeMasks, withoutValidation } from './ngx-mask.config';\nimport { NgxMaskService } from './ngx-mask.service';\nimport { MaskExpression } from './ngx-mask-expression.enum';\n\n@Directive({\n    selector: 'input[mask], textarea[mask]',\n    standalone: true,\n    providers: [\n        {\n            provide: NG_VALUE_ACCESSOR,\n            useExisting: NgxMaskDirective,\n            multi: true,\n        },\n        {\n            provide: NG_VALIDATORS,\n            useExisting: NgxMaskDirective,\n            multi: true,\n        },\n        NgxMaskService,\n    ],\n    exportAs: 'mask,ngxMask',\n})\nexport class NgxMaskDirective implements ControlValueAccessor, OnChanges, Validator {\n    public mask = input<string | undefined | null>('');\n    public specialCharacters = input<NgxMaskConfig['specialCharacters']>([]);\n    public patterns = input<NgxMaskConfig['patterns']>({});\n    public prefix = input<NgxMaskConfig['prefix']>('');\n    public suffix = input<NgxMaskConfig['suffix']>('');\n    public thousandSeparator = input<NgxMaskConfig['thousandSeparator']>(' ');\n    public decimalMarker = input<NgxMaskConfig['decimalMarker']>('.');\n    public dropSpecialCharacters = input<NgxMaskConfig['dropSpecialCharacters'] | null>(null);\n    public hiddenInput = input<NgxMaskConfig['hiddenInput'] | null>(null);\n    public showMaskTyped = input<NgxMaskConfig['showMaskTyped'] | null>(null);\n    public placeHolderCharacter = input<NgxMaskConfig['placeHolderCharacter'] | null>(null);\n    public shownMaskExpression = input<NgxMaskConfig['shownMaskExpression'] | null>(null);\n    public clearIfNotMatch = input<NgxMaskConfig['clearIfNotMatch'] | null>(null);\n    public validation = input<NgxMaskConfig['validation'] | null>(null);\n    public separatorLimit = input<NgxMaskConfig['separatorLimit'] | null>('');\n    public allowNegativeNumbers = input<NgxMaskConfig['allowNegativeNumbers'] | null>(null);\n    public leadZeroDateTime = input<NgxMaskConfig['leadZeroDateTime'] | null>(null);\n    public leadZero = input<NgxMaskConfig['leadZero'] | null>(null);\n    public triggerOnMaskChange = input<NgxMaskConfig['triggerOnMaskChange'] | null>(null);\n    public apm = input<NgxMaskConfig['apm'] | null>(null);\n    public inputTransformFn = input<NgxMaskConfig['inputTransformFn'] | null>(null);\n    public outputTransformFn = input<NgxMaskConfig['outputTransformFn'] | null>(null);\n    public keepCharacterPositions = input<NgxMaskConfig['keepCharacterPositions'] | null>(null);\n    public instantPrefix = input<NgxMaskConfig['instantPrefix'] | null>(null);\n\n    public maskFilled = output<void>();\n\n    private _maskValue = signal<string>('');\n    private _inputValue = signal<string>('');\n    private _position = signal<number | null>(null);\n    private _code = signal<string>('');\n    private _maskExpressionArray = signal<string[]>([]);\n    private _justPasted = signal<boolean>(false);\n    private _isFocused = signal<boolean>(false);\n    /**For IME composition event */\n    private _isComposing = signal<boolean>(false);\n\n    public _maskService = inject(NgxMaskService, { self: true });\n\n    private readonly document = inject(DOCUMENT);\n\n    protected _config = inject<NgxMaskConfig>(NGX_MASK_CONFIG);\n\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    public onChange = (_: any) => {};\n\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    public onTouch = () => {};\n\n    public ngOnChanges(changes: SimpleChanges): void {\n        const {\n            mask,\n            specialCharacters,\n            patterns,\n            prefix,\n            suffix,\n            thousandSeparator,\n            decimalMarker,\n            dropSpecialCharacters,\n            hiddenInput,\n            showMaskTyped,\n            placeHolderCharacter,\n            shownMaskExpression,\n            clearIfNotMatch,\n            validation,\n            separatorLimit,\n            allowNegativeNumbers,\n            leadZeroDateTime,\n            leadZero,\n            triggerOnMaskChange,\n            apm,\n            inputTransformFn,\n            outputTransformFn,\n            keepCharacterPositions,\n            instantPrefix,\n        } = changes;\n        if (mask) {\n            if (mask.currentValue !== mask.previousValue && !mask.firstChange) {\n                this._maskService.maskChanged = true;\n            }\n            if (mask.currentValue && mask.currentValue.split(MaskExpression.OR).length > 1) {\n                this._maskExpressionArray.set(\n                    mask.currentValue.split(MaskExpression.OR).sort((a: string, b: string) => {\n                        return a.length - b.length;\n                    })\n                );\n                this._setMask();\n            } else {\n                this._maskExpressionArray.set([]);\n                this._maskValue.set(mask.currentValue || MaskExpression.EMPTY_STRING);\n                this._maskService.maskExpression = this._maskValue();\n            }\n        }\n        if (specialCharacters) {\n            if (!specialCharacters.currentValue || !Array.isArray(specialCharacters.currentValue)) {\n                return;\n            } else {\n                this._maskService.specialCharacters = specialCharacters.currentValue || [];\n            }\n        }\n        if (allowNegativeNumbers) {\n            this._maskService.allowNegativeNumbers = allowNegativeNumbers.currentValue;\n            if (this._maskService.allowNegativeNumbers) {\n                this._maskService.specialCharacters = this._maskService.specialCharacters.filter(\n                    (c: string) => c !== MaskExpression.MINUS\n                );\n            }\n        }\n        // Only overwrite the mask available patterns if a pattern has actually been passed in\n        if (patterns && patterns.currentValue) {\n            this._maskService.patterns = patterns.currentValue;\n        }\n        if (apm && apm.currentValue) {\n            this._maskService.apm = apm.currentValue;\n        }\n\n        if (instantPrefix) {\n            this._maskService.instantPrefix = instantPrefix.currentValue;\n        }\n        if (prefix) {\n            this._maskService.prefix = prefix.currentValue;\n        }\n        if (suffix) {\n            this._maskService.suffix = suffix.currentValue;\n        }\n        if (thousandSeparator) {\n            this._maskService.thousandSeparator = thousandSeparator.currentValue;\n            if (thousandSeparator.previousValue && thousandSeparator.currentValue) {\n                const previousDecimalMarker = this._maskService.decimalMarker;\n\n                if (thousandSeparator.currentValue === this._maskService.decimalMarker) {\n                    this._maskService.decimalMarker =\n                        thousandSeparator.currentValue === MaskExpression.COMMA\n                            ? MaskExpression.DOT\n                            : MaskExpression.COMMA;\n                }\n                if (this._maskService.dropSpecialCharacters === true) {\n                    this._maskService.specialCharacters = this._config.specialCharacters;\n                }\n                if (\n                    typeof previousDecimalMarker === 'string' &&\n                    typeof this._maskService.decimalMarker === 'string'\n                ) {\n                    this._inputValue.set(\n                        this._inputValue()\n                            .split(thousandSeparator.previousValue)\n                            .join('')\n                            .replace(previousDecimalMarker, this._maskService.decimalMarker)\n                    );\n                    this._maskService.actualValue = this._inputValue();\n                }\n                this._maskService.writingValue = true;\n            }\n        }\n        if (decimalMarker) {\n            this._maskService.decimalMarker = decimalMarker.currentValue;\n        }\n        if (dropSpecialCharacters) {\n            this._maskService.dropSpecialCharacters = dropSpecialCharacters.currentValue;\n        }\n        if (hiddenInput) {\n            this._maskService.hiddenInput = hiddenInput.currentValue;\n            if (hiddenInput.previousValue === true && hiddenInput.currentValue === false) {\n                this._inputValue.set(this._maskService.actualValue);\n            }\n        }\n        if (showMaskTyped) {\n            this._maskService.showMaskTyped = showMaskTyped.currentValue;\n            if (\n                showMaskTyped.previousValue === false &&\n                showMaskTyped.currentValue === true &&\n                this._isFocused()\n            ) {\n                requestAnimationFrame(() => {\n                    this._maskService._elementRef?.nativeElement.click();\n                });\n            }\n        }\n        if (placeHolderCharacter) {\n            this._maskService.placeHolderCharacter = placeHolderCharacter.currentValue;\n        }\n        if (shownMaskExpression) {\n            this._maskService.shownMaskExpression = shownMaskExpression.currentValue;\n        }\n        if (clearIfNotMatch) {\n            this._maskService.clearIfNotMatch = clearIfNotMatch.currentValue;\n        }\n        if (validation) {\n            this._maskService.validation = validation.currentValue;\n        }\n        if (separatorLimit) {\n            this._maskService.separatorLimit = separatorLimit.currentValue;\n        }\n        if (leadZeroDateTime) {\n            this._maskService.leadZeroDateTime = leadZeroDateTime.currentValue;\n        }\n        if (leadZero) {\n            this._maskService.leadZero = leadZero.currentValue;\n        }\n        if (triggerOnMaskChange) {\n            this._maskService.triggerOnMaskChange = triggerOnMaskChange.currentValue;\n        }\n        if (inputTransformFn) {\n            this._maskService.inputTransformFn = inputTransformFn.currentValue;\n        }\n        if (outputTransformFn) {\n            this._maskService.outputTransformFn = outputTransformFn.currentValue;\n        }\n        if (keepCharacterPositions) {\n            this._maskService.keepCharacterPositions = keepCharacterPositions.currentValue;\n        }\n        this._applyMask();\n    }\n\n    public validate({ value }: FormControl): ValidationErrors | null {\n        const processedValue: string = typeof value === 'number' ? String(value) : value;\n        const maskValue = this._maskValue();\n\n        if (!this._maskService.validation || !maskValue) {\n            return null;\n        }\n        if (this._maskService.ipError) {\n            return this._createValidationError(processedValue);\n        }\n        if (this._maskService.cpfCnpjError) {\n            return this._createValidationError(processedValue);\n        }\n        if (maskValue.startsWith(MaskExpression.SEPARATOR)) {\n            return null;\n        }\n        if (withoutValidation.includes(maskValue)) {\n            return null;\n        }\n        if (this._maskService.clearIfNotMatch) {\n            return null;\n        }\n        if (timeMasks.includes(maskValue)) {\n            return this._validateTime(processedValue);\n        }\n        if (maskValue === MaskExpression.EMAIL_MASK) {\n            const emailPattern = /^[^@]+@[^@]+\\.[^@]+$/;\n\n            if (!emailPattern.test(processedValue) && processedValue) {\n                return this._createValidationError(processedValue);\n            } else {\n                return null;\n            }\n        }\n        if (processedValue && processedValue.length >= 1) {\n            let counterOfOpt = 0;\n\n            if (\n                maskValue.includes(MaskExpression.CURLY_BRACKETS_LEFT) &&\n                maskValue.includes(MaskExpression.CURLY_BRACKETS_RIGHT)\n            ) {\n                const lengthInsideCurlyBrackets = maskValue.slice(\n                    maskValue.indexOf(MaskExpression.CURLY_BRACKETS_LEFT) + 1,\n                    maskValue.indexOf(MaskExpression.CURLY_BRACKETS_RIGHT)\n                );\n\n                return lengthInsideCurlyBrackets === String(processedValue.length)\n                    ? null\n                    : this._createValidationError(processedValue);\n            }\n            if (maskValue.startsWith(MaskExpression.PERCENT)) {\n                return null;\n            }\n            for (const key in this._maskService.patterns) {\n                if (this._maskService.patterns[key]?.optional) {\n                    if (maskValue.indexOf(key) !== maskValue.lastIndexOf(key)) {\n                        const opt: string = maskValue\n                            .split(MaskExpression.EMPTY_STRING)\n                            .filter((i: string) => i === key)\n                            .join(MaskExpression.EMPTY_STRING);\n                        counterOfOpt += opt.length;\n                    } else if (maskValue.indexOf(key) !== -1) {\n                        counterOfOpt++;\n                    }\n                    if (\n                        maskValue.indexOf(key) !== -1 &&\n                        processedValue.length >= maskValue.indexOf(key)\n                    ) {\n                        return null;\n                    }\n                    if (counterOfOpt === maskValue.length) {\n                        return null;\n                    }\n                }\n            }\n            if (\n                (maskValue.indexOf(MaskExpression.SYMBOL_STAR) > 1 &&\n                    processedValue.length < maskValue.indexOf(MaskExpression.SYMBOL_STAR)) ||\n                (maskValue.indexOf(MaskExpression.SYMBOL_QUESTION) > 1 &&\n                    processedValue.length < maskValue.indexOf(MaskExpression.SYMBOL_QUESTION))\n            ) {\n                return this._createValidationError(processedValue);\n            }\n            if (\n                maskValue.indexOf(MaskExpression.SYMBOL_STAR) === -1 ||\n                maskValue.indexOf(MaskExpression.SYMBOL_QUESTION) === -1\n            ) {\n                const array = maskValue.split('*');\n                const length: number = this._maskService.dropSpecialCharacters\n                    ? maskValue.length -\n                      this._maskService.checkDropSpecialCharAmount(maskValue) -\n                      counterOfOpt\n                    : this.prefix()\n                      ? maskValue.length + this.prefix().length - counterOfOpt\n                      : maskValue.length - counterOfOpt;\n\n                if (array.length === 1) {\n                    if (processedValue.length < length) {\n                        return this._createValidationError(processedValue);\n                    }\n                }\n                if (array.length > 1) {\n                    const lastIndexArray = array[array.length - 1];\n                    if (\n                        lastIndexArray &&\n                        this._maskService.specialCharacters.includes(lastIndexArray[0] as string) &&\n                        String(processedValue).includes(lastIndexArray[0] ?? '') &&\n                        !this.dropSpecialCharacters()\n                    ) {\n                        const special = value.split(lastIndexArray[0]);\n                        return special[special.length - 1].length === lastIndexArray.length - 1\n                            ? null\n                            : this._createValidationError(processedValue);\n                    } else if (\n                        ((lastIndexArray &&\n                            !this._maskService.specialCharacters.includes(\n                                lastIndexArray[0] as string\n                            )) ||\n                            !lastIndexArray ||\n                            this._maskService.dropSpecialCharacters) &&\n                        processedValue.length >= length - 1\n                    ) {\n                        return null;\n                    } else {\n                        return this._createValidationError(processedValue);\n                    }\n                }\n            }\n            if (\n                maskValue.indexOf(MaskExpression.SYMBOL_STAR) === 1 ||\n                maskValue.indexOf(MaskExpression.SYMBOL_QUESTION) === 1\n            ) {\n                return null;\n            }\n        }\n        if (value) {\n            this.maskFilled.emit();\n            return null;\n        }\n        return null;\n    }\n\n    @HostListener('paste')\n    public onPaste() {\n        this._justPasted.set(true);\n    }\n\n    @HostListener('focus', ['$event']) public onFocus() {\n        this._isFocused.set(true);\n    }\n\n    @HostListener('ngModelChange', ['$event'])\n    public onModelChange(value: string | undefined | null | number): void {\n        // on form reset we need to update the actualValue\n        if (\n            (value === MaskExpression.EMPTY_STRING ||\n                value === null ||\n                typeof value === 'undefined') &&\n            this._maskService.actualValue\n        ) {\n            this._maskService.actualValue = this._maskService.getActualValue(\n                MaskExpression.EMPTY_STRING\n            );\n        }\n    }\n\n    @HostListener('input', ['$event'])\n    public onInput(e: CustomKeyboardEvent): void {\n        // If IME is composing text, we wait for the composed text.\n        if (this._isComposing()) {\n            return;\n        }\n        const el: HTMLInputElement = e.target as HTMLInputElement;\n\n        const transformedValue = this._maskService.inputTransformFn\n            ? this._maskService.inputTransformFn(el.value)\n            : el.value;\n\n        if (el.type !== 'number') {\n            if (typeof transformedValue === 'string' || typeof transformedValue === 'number') {\n                el.value = transformedValue.toString();\n\n                this._inputValue.set(el.value);\n                this._setMask();\n\n                if (!this._maskValue()) {\n                    this.onChange(el.value);\n                    return;\n                }\n\n                let position: number =\n                    el.selectionStart === 1\n                        ? (el.selectionStart as number) + this._maskService.prefix.length\n                        : (el.selectionStart as number);\n\n                if (\n                    this.showMaskTyped() &&\n                    this.keepCharacterPositions() &&\n                    this._maskService.placeHolderCharacter.length === 1\n                ) {\n                    const suffix = this.suffix();\n                    const prefix = this.prefix();\n                    const inputSymbol = el.value.slice(position - 1, position);\n                    const prefixLength = prefix.length;\n                    const checkSymbols: boolean = this._maskService._checkSymbolMask(\n                        inputSymbol,\n                        this._maskService.maskExpression[position - 1 - prefixLength] ??\n                            MaskExpression.EMPTY_STRING\n                    );\n\n                    const checkSpecialCharacter: boolean = this._maskService._checkSymbolMask(\n                        inputSymbol,\n                        this._maskService.maskExpression[position + 1 - prefixLength] ??\n                            MaskExpression.EMPTY_STRING\n                    );\n                    const selectRangeBackspace: boolean =\n                        this._maskService.selStart === this._maskService.selEnd;\n                    const selStart = Number(this._maskService.selStart) - prefixLength;\n                    const selEnd = Number(this._maskService.selEnd) - prefixLength;\n\n                    const backspaceOrDelete =\n                        this._code() === MaskExpression.BACKSPACE ||\n                        this._code() === MaskExpression.DELETE;\n\n                    if (backspaceOrDelete) {\n                        if (!selectRangeBackspace) {\n                            if (this._maskService.selStart === prefixLength) {\n                                this._maskService.actualValue = `${prefix}${this._maskService.maskIsShown.slice(0, selEnd)}${this._inputValue().split(prefix).join('')}`;\n                            } else if (\n                                this._maskService.selStart ===\n                                this._maskService.maskIsShown.length + prefixLength\n                            ) {\n                                this._maskService.actualValue = `${this._inputValue()}${this._maskService.maskIsShown.slice(selStart, selEnd)}`;\n                            } else {\n                                this._maskService.actualValue = `${prefix}${this._inputValue()\n                                    .split(prefix)\n                                    .join('')\n                                    .slice(\n                                        0,\n                                        selStart\n                                    )}${this._maskService.maskIsShown.slice(selStart, selEnd)}${this._maskService.actualValue.slice(\n                                    selEnd + prefixLength,\n                                    this._maskService.maskIsShown.length + prefixLength\n                                )}${suffix}`;\n                            }\n                        } else if (\n                            !this._maskService.specialCharacters.includes(\n                                this._maskService.maskExpression.slice(\n                                    position - prefixLength,\n                                    position + 1 - prefixLength\n                                )\n                            ) &&\n                            selectRangeBackspace\n                        ) {\n                            if (selStart === 1 && prefix) {\n                                this._maskService.actualValue = `${prefix}${this._maskService.placeHolderCharacter}${el.value\n                                    .split(prefix)\n                                    .join('')\n                                    .split(suffix)\n                                    .join('')}${suffix}`;\n\n                                position = position - 1;\n                            } else {\n                                const part1 = el.value.substring(0, position);\n                                const part2 = el.value.substring(position);\n                                this._maskService.actualValue = `${part1}${this._maskService.placeHolderCharacter}${part2}`;\n                            }\n                        }\n                        position = this._code() === MaskExpression.DELETE ? position + 1 : position;\n                    }\n                    if (!backspaceOrDelete) {\n                        if (!checkSymbols && !checkSpecialCharacter && selectRangeBackspace) {\n                            position = Number(el.selectionStart) - 1;\n                        } else if (\n                            this._maskService.specialCharacters.includes(\n                                el.value.slice(position, position + 1)\n                            ) &&\n                            checkSpecialCharacter &&\n                            !this._maskService.specialCharacters.includes(\n                                el.value.slice(position + 1, position + 2)\n                            )\n                        ) {\n                            this._maskService.actualValue = `${el.value.slice(0, position - 1)}${el.value.slice(position, position + 1)}${inputSymbol}${el.value.slice(position + 2)}`;\n                            position = position + 1;\n                        } else if (checkSymbols) {\n                            if (el.value.length === 1 && position === 1) {\n                                this._maskService.actualValue = `${prefix}${inputSymbol}${this._maskService.maskIsShown.slice(\n                                    1,\n                                    this._maskService.maskIsShown.length\n                                )}${suffix}`;\n                            } else {\n                                this._maskService.actualValue = `${el.value.slice(0, position - 1)}${inputSymbol}${el.value\n                                    .slice(position + 1)\n                                    .split(suffix)\n                                    .join('')}${suffix}`;\n                            }\n                        } else if (\n                            prefix &&\n                            el.value.length === 1 &&\n                            position - prefixLength === 1 &&\n                            this._maskService._checkSymbolMask(\n                                el.value,\n                                this._maskService.maskExpression[position - 1 - prefixLength] ??\n                                    MaskExpression.EMPTY_STRING\n                            )\n                        ) {\n                            this._maskService.actualValue = `${prefix}${el.value}${this._maskService.maskIsShown.slice(\n                                1,\n                                this._maskService.maskIsShown.length\n                            )}${suffix}`;\n                        }\n                    }\n                }\n\n                let caretShift = 0;\n                let backspaceShift = false;\n                if (this._code() === MaskExpression.DELETE && MaskExpression.SEPARATOR) {\n                    this._maskService.deletedSpecialCharacter = true;\n                }\n                if (\n                    this._inputValue().length >= this._maskService.maskExpression.length - 1 &&\n                    this._code() !== MaskExpression.BACKSPACE &&\n                    this._maskService.maskExpression === MaskExpression.DAYS_MONTHS_YEARS &&\n                    position < 10\n                ) {\n                    const inputSymbol = this._inputValue().slice(position - 1, position);\n                    el.value =\n                        this._inputValue().slice(0, position - 1) +\n                        inputSymbol +\n                        this._inputValue().slice(position + 1);\n                }\n                if (\n                    this._maskService.maskExpression === MaskExpression.DAYS_MONTHS_YEARS &&\n                    this.leadZeroDateTime()\n                ) {\n                    if (\n                        (position < 3 && Number(el.value) > 31 && Number(el.value) < 40) ||\n                        (position === 5 && Number(el.value.slice(3, 5)) > 12)\n                    ) {\n                        position = position + 2;\n                    }\n                }\n                if (\n                    this._maskService.maskExpression === MaskExpression.HOURS_MINUTES_SECONDS &&\n                    this.apm()\n                ) {\n                    if (this._justPasted() && el.value.slice(0, 2) === MaskExpression.DOUBLE_ZERO) {\n                        el.value = el.value.slice(1, 2) + el.value.slice(2, el.value.length);\n                    }\n                    el.value =\n                        el.value === MaskExpression.DOUBLE_ZERO\n                            ? MaskExpression.NUMBER_ZERO\n                            : el.value;\n                }\n\n                this._maskService.applyValueChanges(\n                    position,\n                    this._justPasted(),\n                    this._code() === MaskExpression.BACKSPACE ||\n                        this._code() === MaskExpression.DELETE,\n                    (shift: number, _backspaceShift: boolean) => {\n                        this._justPasted.set(false);\n                        caretShift = shift;\n                        backspaceShift = _backspaceShift;\n                    }\n                );\n                // only set the selection if the element is active\n                if (this._getActiveElement() !== el) {\n                    return;\n                }\n\n                if (this._maskService.plusOnePosition) {\n                    position = position + 1;\n                    this._maskService.plusOnePosition = false;\n                }\n                // update position after applyValueChanges to prevent cursor on wrong position when it has an array of maskExpression\n                if (this._maskExpressionArray().length) {\n                    if (this._code() === MaskExpression.BACKSPACE) {\n                        const specialChartMinusOne = this.specialCharacters().includes(\n                            this._maskService.actualValue.slice(position - 1, position)\n                        );\n                        const allowFewMaskChangeMask =\n                            this._maskService.removeMask(this._inputValue())?.length ===\n                            this._maskService.removeMask(this._maskService.maskExpression)?.length;\n\n                        const specialChartPlusOne = this.specialCharacters().includes(\n                            this._maskService.actualValue.slice(position, position + 1)\n                        );\n                        if (allowFewMaskChangeMask && !specialChartPlusOne) {\n                            position = (el.selectionStart as number) + 1;\n                        } else {\n                            position = specialChartMinusOne ? position - 1 : position;\n                        }\n                    } else {\n                        position =\n                            el.selectionStart === 1\n                                ? (el.selectionStart as number) + this._maskService.prefix.length\n                                : (el.selectionStart as number);\n                    }\n                }\n                this._position.set(\n                    this._position() === 1 && this._inputValue().length === 1\n                        ? null\n                        : this._position()\n                );\n                let positionToApply: number = this._position()\n                    ? this._inputValue().length + position + caretShift\n                    : position +\n                      (this._code() === MaskExpression.BACKSPACE && !backspaceShift\n                          ? 0\n                          : caretShift);\n                if (positionToApply > this._getActualInputLength()) {\n                    positionToApply =\n                        el.value === this._maskService.decimalMarker && el.value.length === 1\n                            ? this._getActualInputLength() + 1\n                            : this._getActualInputLength();\n                }\n                if (positionToApply < 0) {\n                    positionToApply = 0;\n                }\n                el.setSelectionRange(positionToApply, positionToApply);\n                this._position.set(null);\n            } else {\n                // eslint-disable-next-line no-console\n                console.warn(\n                    'Ngx-mask writeValue work with string | number, your current value:',\n                    typeof transformedValue\n                );\n            }\n        } else {\n            if (!this._maskValue()) {\n                this.onChange(el.value);\n                return;\n            }\n            this._maskService.applyValueChanges(\n                el.value.length,\n                this._justPasted(),\n                this._code() === MaskExpression.BACKSPACE || this._code() === MaskExpression.DELETE\n            );\n        }\n    }\n\n    // IME starts\n    @HostListener('compositionstart', ['$event'])\n    public onCompositionStart(): void {\n        this._isComposing.set(true);\n    }\n\n    // IME completes\n    @HostListener('compositionend', ['$event'])\n    public onCompositionEnd(e: CustomKeyboardEvent): void {\n        this._isComposing.set(false);\n        this._justPasted.set(true);\n        this.onInput(e);\n    }\n\n    @HostListener('blur', ['$event'])\n    public onBlur(e: CustomKeyboardEvent): void {\n        if (this._maskValue()) {\n            const el: HTMLInputElement = e.target as HTMLInputElement;\n            if (\n                this._maskService.leadZero &&\n                el.value.length > 0 &&\n                typeof this._maskService.decimalMarker === 'string'\n            ) {\n                const maskExpression = this._maskService.maskExpression;\n                const decimalMarker = this._maskService.decimalMarker as string;\n                const suffix = this._maskService.suffix;\n                const precision = Number(\n                    this._maskService.maskExpression.slice(\n                        maskExpression.length - 1,\n                        maskExpression.length\n                    )\n                );\n\n                if (precision > 0) {\n                    el.value = suffix ? el.value.split(suffix).join('') : el.value;\n                    const decimalPart = el.value.split(decimalMarker)[1] as string;\n\n                    el.value = el.value.includes(decimalMarker)\n                        ? el.value +\n                          MaskExpression.NUMBER_ZERO.repeat(precision - decimalPart.length) +\n                          suffix\n                        : el.value +\n                          decimalMarker +\n                          MaskExpression.NUMBER_ZERO.repeat(precision) +\n                          suffix;\n                    this._maskService.actualValue = el.value;\n                }\n            }\n            this._maskService.clearIfNotMatchFn();\n        }\n        this._isFocused.set(false);\n        this.onTouch();\n    }\n\n    @HostListener('click', ['$event'])\n    public onClick(e: MouseEvent | CustomKeyboardEvent): void {\n        if (!this._maskValue()) {\n            return;\n        }\n\n        const el: HTMLInputElement = e.target as HTMLInputElement;\n        const posStart = 0;\n        const posEnd = 0;\n\n        if (\n            el !== null &&\n            el.selectionStart !== null &&\n            el.selectionStart === el.selectionEnd &&\n            el.selectionStart > this._maskService.prefix.length &&\n            (e as any).keyCode !== 38\n        ) {\n            if (this._maskService.showMaskTyped && !this.keepCharacterPositions()) {\n                // We are showing the mask in the input\n                this._maskService.maskIsShown = this._maskService.showMaskInInput();\n                if (\n                    el.setSelectionRange &&\n                    this._maskService.prefix + this._maskService.maskIsShown === el.value\n                ) {\n                    // the input ONLY contains the mask, so position the cursor at the start\n                    el.focus();\n                    el.setSelectionRange(posStart, posEnd);\n                } else {\n                    // the input contains some characters already\n                    if (el.selectionStart > this._maskService.actualValue.length) {\n                        // if the user clicked beyond our value's length, position the cursor at the end of our value\n                        el.setSelectionRange(\n                            this._maskService.actualValue.length,\n                            this._maskService.actualValue.length\n                        );\n                    }\n                }\n            }\n        }\n        const nextValue: string | null =\n            el &&\n            (el.value === this._maskService.prefix\n                ? this._maskService.prefix + this._maskService.maskIsShown\n                : el.value);\n\n        /** Fix of cursor position jumping to end in most browsers no matter where cursor is inserted onFocus */\n        if (el && el.value !== nextValue) {\n            el.value = nextValue;\n        }\n        /** fix of cursor position with prefix when mouse click occur */\n        if (\n            el &&\n            el.type !== 'number' &&\n            ((el.selectionStart as number) || (el.selectionEnd as number)) <=\n                this._maskService.prefix.length\n        ) {\n            const specialCharactersAtTheStart =\n                this._maskService.maskExpression.match(\n                    new RegExp(\n                        `^[${this._maskService.specialCharacters.map((c) => `\\\\${c}`).join('')}]+`\n                    )\n                )?.[0].length || 0;\n\n            el.selectionStart = this._maskService.prefix.length + specialCharactersAtTheStart;\n            return;\n        }\n        /** select only inserted text */\n        if (el && (el.selectionEnd as number) > this._getActualInputLength()) {\n            el.selectionEnd = this._getActualInputLength();\n        }\n    }\n\n    @HostListener('keydown', ['$event'])\n    public onKeyDown(e: CustomKeyboardEvent): void {\n        if (!this._maskValue()) {\n            return;\n        }\n\n        if (this._isComposing()) {\n            // User finalize their choice from IME composition, so trigger onInput() for the composed text.\n            if (e.key === 'Enter') {\n                this.onCompositionEnd(e);\n            }\n            return;\n        }\n\n        this._code.set(e.code ? e.code : e.key);\n        const el: HTMLInputElement = e.target as HTMLInputElement;\n        this._inputValue.set(el.value);\n        this._setMask();\n\n        if (el.type !== 'number') {\n            if (e.key === MaskExpression.ARROW_UP) {\n                e.preventDefault();\n            }\n            if (\n                e.key === MaskExpression.ARROW_LEFT ||\n                e.key === MaskExpression.BACKSPACE ||\n                e.key === MaskExpression.DELETE\n            ) {\n                if (e.key === MaskExpression.BACKSPACE && el.value.length === 0) {\n                    el.selectionStart = el.selectionEnd;\n                }\n                if (e.key === MaskExpression.BACKSPACE && (el.selectionStart as number) !== 0) {\n                    const prefixLength = this.prefix().length;\n                    // If specialChars is false, (shouldn't ever happen) then set to the defaults\n                    const specialCharacters = this.specialCharacters().length\n                        ? this.specialCharacters()\n                        : this._config.specialCharacters;\n\n                    if (prefixLength > 1 && (el.selectionStart as number) <= prefixLength) {\n                        el.setSelectionRange(prefixLength, el.selectionEnd);\n                    } else {\n                        if (\n                            this._inputValue().length !== (el.selectionStart as number) &&\n                            (el.selectionStart as number) !== 1\n                        ) {\n                            while (\n                                specialCharacters.includes(\n                                    (\n                                        this._inputValue()[(el.selectionStart as number) - 1] ??\n                                        MaskExpression.EMPTY_STRING\n                                    ).toString()\n                                ) &&\n                                ((prefixLength >= 1 &&\n                                    (el.selectionStart as number) > prefixLength) ||\n                                    prefixLength === 0)\n                            ) {\n                                el.setSelectionRange(\n                                    (el.selectionStart as number) - 1,\n                                    el.selectionEnd\n                                );\n                            }\n                        }\n                    }\n                }\n                this.checkSelectionOnDeletion(el);\n                if (\n                    this._maskService.prefix.length &&\n                    (el.selectionStart as number) <= this._maskService.prefix.length &&\n                    (el.selectionEnd as number) <= this._maskService.prefix.length\n                ) {\n                    e.preventDefault();\n                }\n                const cursorStart: number | null = el.selectionStart;\n                if (\n                    e.key === MaskExpression.BACKSPACE &&\n                    !el.readOnly &&\n                    cursorStart === 0 &&\n                    el.selectionEnd === el.value.length &&\n                    el.value.length !== 0\n                ) {\n                    this._position.set(\n                        this._maskService.prefix ? this._maskService.prefix.length : 0\n                    );\n                    this._maskService.applyMask(\n                        this._maskService.prefix,\n                        this._maskService.maskExpression,\n                        this._position() as number\n                    );\n                }\n            }\n            if (\n                !!this.suffix() &&\n                this.suffix().length > 1 &&\n                this._inputValue().length - this.suffix().length < (el.selectionStart as number)\n            ) {\n                el.setSelectionRange(\n                    this._inputValue().length - this.suffix().length,\n                    this._inputValue().length\n                );\n            } else if (\n                (e.code === 'KeyA' && e.ctrlKey) ||\n                (e.code === 'KeyA' && e.metaKey) // Cmd + A (Mac)\n            ) {\n                el.setSelectionRange(0, this._getActualInputLength());\n                e.preventDefault();\n            }\n            this._maskService.selStart = el.selectionStart;\n            this._maskService.selEnd = el.selectionEnd;\n        }\n    }\n\n    /** It writes the value in the input */\n    public async writeValue(controlValue: unknown): Promise<void> {\n        let value = controlValue;\n        const inputTransformFn = this._maskService.inputTransformFn;\n        if (typeof value === 'object' && value !== null && 'value' in value) {\n            if ('disable' in value) {\n                this.setDisabledState(Boolean(value.disable));\n            }\n\n            value = value.value;\n        }\n        if (value !== null) {\n            value = inputTransformFn ? inputTransformFn(value) : value;\n        }\n        if (\n            typeof value === 'string' ||\n            typeof value === 'number' ||\n            value === null ||\n            typeof value === 'undefined'\n        ) {\n            if (value === null || typeof value === 'undefined' || value === '') {\n                this._maskService.currentValue = '';\n                this._maskService.previousValue = '';\n            }\n\n            let inputValue: string | number | null | undefined = value;\n            if (\n                typeof inputValue === 'number' ||\n                this._maskValue().startsWith(MaskExpression.SEPARATOR)\n            ) {\n                inputValue = String(inputValue);\n                const localeDecimalMarker = this._maskService.currentLocaleDecimalMarker();\n                if (!Array.isArray(this._maskService.decimalMarker)) {\n                    inputValue =\n                        this._maskService.decimalMarker !== localeDecimalMarker\n                            ? inputValue.replace(\n                                  localeDecimalMarker,\n                                  this._maskService.decimalMarker\n                              )\n                            : inputValue;\n                }\n\n                if (\n                    this._maskService.leadZero &&\n                    inputValue &&\n                    this.mask() &&\n                    this.dropSpecialCharacters() !== false\n                ) {\n                    inputValue = this._maskService._checkPrecision(\n                        this._maskService.maskExpression,\n                        inputValue as string\n                    );\n                }\n\n                if (\n                    this._maskService.decimalMarker === MaskExpression.COMMA ||\n                    (Array.isArray(this._maskService.decimalMarker) &&\n                        this._maskService.thousandSeparator === MaskExpression.DOT)\n                ) {\n                    inputValue = inputValue\n                        .toString()\n                        .replace(MaskExpression.DOT, MaskExpression.COMMA);\n                }\n                if (this.mask()?.startsWith(MaskExpression.SEPARATOR) && this.leadZero()) {\n                    requestAnimationFrame(() => {\n                        this._maskService.applyMask(\n                            inputValue?.toString() ?? '',\n                            this._maskService.maskExpression\n                        );\n                    });\n                }\n                this._maskService.isNumberValue = true;\n            }\n\n            if (typeof inputValue !== 'string' || value === null || typeof value === 'undefined') {\n                inputValue = '';\n            }\n\n            this._inputValue.set(inputValue);\n            this._setMask();\n\n            if (\n                (inputValue && this._maskService.maskExpression) ||\n                (this._maskService.maskExpression &&\n                    (this._maskService.prefix || this._maskService.showMaskTyped))\n            ) {\n                // Let the service we know we are writing value so that triggering onChange function won't happen during applyMask\n                this._maskService.writingValue = true;\n\n                this._maskService.formElementProperty = [\n                    'value',\n                    this._maskService.applyMask(inputValue, this._maskService.maskExpression),\n                ];\n                // Let the service know we've finished writing value\n                this._maskService.writingValue = false;\n            } else {\n                this._maskService.formElementProperty = ['value', inputValue];\n            }\n            this._inputValue.set(inputValue);\n        } else {\n            // eslint-disable-next-line no-console\n            console.warn(\n                'Ngx-mask writeValue work with string | number, your current value:',\n                typeof value\n            );\n        }\n    }\n\n    public registerOnChange(fn: typeof this.onChange): void {\n        this._maskService.onChange = this.onChange = fn;\n    }\n\n    public registerOnTouched(fn: typeof this.onTouch): void {\n        this.onTouch = fn;\n    }\n\n    private _getActiveElement(document: DocumentOrShadowRoot = this.document): Element | null {\n        const shadowRootEl = document?.activeElement?.shadowRoot;\n        if (!shadowRootEl?.activeElement) {\n            return document.activeElement;\n        } else {\n            return this._getActiveElement(shadowRootEl);\n        }\n    }\n\n    public checkSelectionOnDeletion(el: HTMLInputElement): void {\n        const prefixLength = this.prefix().length;\n        const suffixLength = this.suffix().length;\n        const inputValueLength = this._inputValue().length;\n\n        el.selectionStart = Math.min(\n            Math.max(prefixLength, el.selectionStart as number),\n            inputValueLength - suffixLength\n        );\n        el.selectionEnd = Math.min(\n            Math.max(prefixLength, el.selectionEnd as number),\n            inputValueLength - suffixLength\n        );\n    }\n\n    /** It disables the input element */\n    public setDisabledState(isDisabled: boolean): void {\n        this._maskService.formElementProperty = ['disabled', isDisabled];\n    }\n\n    private _applyMask(): any {\n        this._maskService.maskExpression = this._maskService._repeatPatternSymbols(\n            this._maskValue() || ''\n        );\n        this._maskService.formElementProperty = [\n            'value',\n            this._maskService.applyMask(this._inputValue(), this._maskService.maskExpression),\n        ];\n    }\n\n    private _validateTime(value: string): ValidationErrors | null {\n        const rowMaskLen: number = this._maskValue()\n            .split(MaskExpression.EMPTY_STRING)\n            .filter((s: string) => s !== ':').length;\n        if (!value) {\n            return null; // Don't validate empty values to allow for optional form control\n        }\n\n        if (\n            (+(value[value.length - 1] ?? -1) === 0 && value.length < rowMaskLen) ||\n            value.length <= rowMaskLen - 2\n        ) {\n            return this._createValidationError(value);\n        }\n\n        return null;\n    }\n\n    private _getActualInputLength() {\n        return (\n            this._maskService.actualValue.length ||\n            this._maskService.actualValue.length + this._maskService.prefix.length\n        );\n    }\n\n    private _createValidationError(actualValue: string): ValidationErrors {\n        return {\n            mask: {\n                requiredMask: this._maskValue(),\n                actualValue,\n            },\n        };\n    }\n\n    private _setMask() {\n        this._maskExpressionArray().some((mask): boolean | void => {\n            const specialChart: boolean = mask\n                .split(MaskExpression.EMPTY_STRING)\n                .some((char) => this._maskService.specialCharacters.includes(char));\n            if (\n                (specialChart &&\n                    this._inputValue() &&\n                    this._areAllCharactersInEachStringSame(this._maskExpressionArray())) ||\n                mask.includes(MaskExpression.CURLY_BRACKETS_LEFT)\n            ) {\n                const test =\n                    this._maskService.removeMask(this._inputValue())?.length <=\n                    this._maskService.removeMask(mask)?.length;\n                if (test) {\n                    const maskValue = mask.includes(MaskExpression.CURLY_BRACKETS_LEFT)\n                        ? this._maskService._repeatPatternSymbols(mask)\n                        : mask;\n                    this._maskValue.set(maskValue);\n                    this._maskService.maskExpression = maskValue;\n                    return test;\n                } else {\n                    const expression =\n                        this._maskExpressionArray()[this._maskExpressionArray().length - 1] ??\n                        MaskExpression.EMPTY_STRING;\n\n                    const maskValue = expression.includes(MaskExpression.CURLY_BRACKETS_LEFT)\n                        ? this._maskService._repeatPatternSymbols(expression)\n                        : expression;\n                    this._maskValue.set(maskValue);\n                    this._maskService.maskExpression = maskValue;\n                }\n            } else {\n                const cleanMask = this._maskService.removeMask(mask);\n                const check: boolean = this._maskService\n                    .removeMask(this._inputValue())\n                    ?.split(MaskExpression.EMPTY_STRING)\n                    .every((character, index) => {\n                        const indexMask = cleanMask.charAt(index);\n                        return this._maskService._checkSymbolMask(character, indexMask);\n                    });\n\n                if (check || this._justPasted()) {\n                    this._maskValue.set(mask);\n                    this._maskService.maskExpression = mask;\n                    return check;\n                }\n            }\n        });\n    }\n\n    private _areAllCharactersInEachStringSame(array: string[]): boolean {\n        const specialCharacters = this._maskService.specialCharacters;\n\n        function removeSpecialCharacters(str: string): string {\n            const regex = new RegExp(`[${specialCharacters.map((ch) => `\\\\${ch}`).join('')}]`, 'g');\n            return str.replace(regex, '');\n        }\n\n        const processedArr = array.map(removeSpecialCharacters);\n\n        return processedArr.every((str) => {\n            const uniqueCharacters = new Set(str);\n            return uniqueCharacters.size === 1;\n        });\n    }\n}\n", "import type { PipeTransform } from '@angular/core';\nimport { inject, Pipe } from '@angular/core';\n\nimport type { NgxMaskConfig } from './ngx-mask.config';\nimport { NGX_MASK_CONFIG } from './ngx-mask.config';\nimport { NgxMaskService } from './ngx-mask.service';\nimport { MaskExpression } from './ngx-mask-expression.enum';\n\n@Pipe({\n    name: 'mask',\n    pure: true,\n    standalone: true,\n})\nexport class NgxMaskPipe implements PipeTransform {\n    private readonly defaultOptions = inject<NgxMaskConfig>(NGX_MASK_CONFIG);\n\n    private readonly _maskService = inject(NgxMaskService);\n\n    private _maskExpressionArray: string[] = [];\n\n    private mask = '';\n\n    public transform(\n        value: string | number,\n        mask: string,\n        { patterns, ...config }: Partial<NgxMaskConfig> = {} as Partial<NgxMaskConfig>\n    ): string {\n        let processedValue: string | number = value;\n\n        const currentConfig = {\n            maskExpression: mask,\n            ...this.defaultOptions,\n            ...config,\n            patterns: {\n                ...this._maskService.patterns,\n                ...patterns,\n            },\n        };\n\n        Object.entries(currentConfig).forEach(([key, val]) => {\n            (this._maskService as any)[key] = val;\n        });\n\n        if (mask.includes('||')) {\n            const maskParts = mask.split('||');\n            if (maskParts.length > 1) {\n                this._maskExpressionArray = maskParts.sort(\n                    (a: string, b: string) => a.length - b.length\n                );\n                this._setMask(`${processedValue}`);\n                return this._maskService.applyMask(`${processedValue}`, this.mask);\n            } else {\n                this._maskExpressionArray = [];\n                return this._maskService.applyMask(`${processedValue}`, this.mask);\n            }\n        }\n\n        if (mask.includes(MaskExpression.CURLY_BRACKETS_LEFT)) {\n            return this._maskService.applyMask(\n                `${processedValue}`,\n                this._maskService._repeatPatternSymbols(mask)\n            );\n        }\n\n        if (mask.startsWith(MaskExpression.SEPARATOR)) {\n            if (config.decimalMarker) {\n                this._maskService.decimalMarker = config.decimalMarker;\n            }\n            if (config.thousandSeparator) {\n                this._maskService.thousandSeparator = config.thousandSeparator;\n            }\n            if (config.leadZero) {\n                this._maskService.leadZero = config.leadZero;\n            }\n\n            processedValue = String(processedValue);\n            const localeDecimalMarker = this._maskService.currentLocaleDecimalMarker();\n\n            if (!Array.isArray(this._maskService.decimalMarker)) {\n                processedValue =\n                    this._maskService.decimalMarker !== localeDecimalMarker\n                        ? (processedValue as string).replace(\n                              localeDecimalMarker,\n                              this._maskService.decimalMarker\n                          )\n                        : processedValue;\n            }\n\n            if (\n                this._maskService.leadZero &&\n                processedValue &&\n                this._maskService.dropSpecialCharacters !== false\n            ) {\n                processedValue = this._maskService._checkPrecision(mask, processedValue as string);\n            }\n\n            if (this._maskService.decimalMarker === MaskExpression.COMMA) {\n                processedValue = (processedValue as string).replace(\n                    MaskExpression.DOT,\n                    MaskExpression.COMMA\n                );\n            }\n\n            this._maskService.isNumberValue = true;\n        }\n\n        if (processedValue === null || typeof processedValue === 'undefined') {\n            return this._maskService.applyMask('', mask);\n        }\n\n        return this._maskService.applyMask(`${processedValue}`, mask);\n    }\n\n    private _setMask(value: string) {\n        if (this._maskExpressionArray.length > 0) {\n            this._maskExpressionArray.some((mask): boolean | void => {\n                const test =\n                    this._maskService.removeMask(value)?.length <=\n                    this._maskService.removeMask(mask)?.length;\n                if (value && test) {\n                    this.mask = mask;\n                    return test;\n                } else {\n                    this.mask =\n                        this._maskExpressionArray[this._maskExpressionArray.length - 1] ??\n                        MaskExpression.EMPTY_STRING;\n                }\n            });\n        }\n    }\n}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;AAAA,IAAkB,cA0CjB;AA1CD,CAAA,UAAkB,cAAc,EAAA;AAC5B,IAAA,cAAA,CAAA,WAAA,CAAA,GAAA,WAAuB;AACvB,IAAA,cAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,cAAA,CAAA,IAAA,CAAA,GAAA,IAAS;AACT,IAAA,cAAA,CAAA,UAAA,CAAA,GAAA,UAAqB;AACrB,IAAA,cAAA,CAAA,OAAA,CAAA,GAAA,GAAW;AACX,IAAA,cAAA,CAAA,QAAA,CAAA,GAAA,IAAa;AACb,IAAA,cAAA,CAAA,QAAA,CAAA,GAAA,GAAY;AACZ,IAAA,cAAA,CAAA,MAAA,CAAA,GAAA,GAAU;AACV,IAAA,cAAA,CAAA,OAAA,CAAA,GAAA,GAAW;AACX,IAAA,cAAA,CAAA,SAAA,CAAA,GAAA,IAAc;AACd,IAAA,cAAA,CAAA,YAAA,CAAA,GAAA,IAAiB;AACjB,IAAA,cAAA,CAAA,SAAA,CAAA,GAAA,IAAc;AACd,IAAA,cAAA,CAAA,uBAAA,CAAA,GAAA,UAAkC;AAClC,IAAA,cAAA,CAAA,YAAA,CAAA,GAAA,UAAuB;AACvB,IAAA,cAAA,CAAA,eAAA,CAAA,GAAA,OAAuB;AACvB,IAAA,cAAA,CAAA,iBAAA,CAAA,GAAA,OAAyB;AACzB,IAAA,cAAA,CAAA,mBAAA,CAAA,GAAA,YAAgC;AAChC,IAAA,cAAA,CAAA,aAAA,CAAA,GAAA,OAAqB;AACrB,IAAA,cAAA,CAAA,MAAA,CAAA,GAAA,IAAW;AACX,IAAA,cAAA,CAAA,KAAA,CAAA,GAAA,GAAS;AACT,IAAA,cAAA,CAAA,QAAA,CAAA,GAAA,GAAY;AACZ,IAAA,cAAA,CAAA,UAAA,CAAA,GAAA,GAAc;AACd,IAAA,cAAA,CAAA,KAAA,CAAA,GAAA,GAAS;AACT,IAAA,cAAA,CAAA,OAAA,CAAA,GAAA,GAAW;AACX,IAAA,cAAA,CAAA,qBAAA,CAAA,GAAA,GAAyB;AACzB,IAAA,cAAA,CAAA,sBAAA,CAAA,GAAA,GAA0B;AAC1B,IAAA,cAAA,CAAA,OAAA,CAAA,GAAA,GAAW;AACX,IAAA,cAAA,CAAA,IAAA,CAAA,GAAA,IAAS;AACT,IAAA,cAAA,CAAA,MAAA,CAAA,GAAA,GAAU;AACV,IAAA,cAAA,CAAA,cAAA,CAAA,GAAA,EAAiB;AACjB,IAAA,cAAA,CAAA,aAAA,CAAA,GAAA,GAAiB;AACjB,IAAA,cAAA,CAAA,iBAAA,CAAA,GAAA,GAAqB;AACrB,IAAA,cAAA,CAAA,OAAA,CAAA,GAAA,GAAW;AACX,IAAA,cAAA,CAAA,aAAA,CAAA,GAAA,GAAiB;AACjB,IAAA,cAAA,CAAA,aAAA,CAAA,GAAA,GAAiB;AACjB,IAAA,cAAA,CAAA,aAAA,CAAA,GAAA,GAAiB;AACjB,IAAA,cAAA,CAAA,WAAA,CAAA,GAAA,WAAuB;AACvB,IAAA,cAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACjB,IAAA,cAAA,CAAA,YAAA,CAAA,GAAA,WAAwB;AACxB,IAAA,cAAA,CAAA,UAAA,CAAA,GAAA,SAAoB;AACpB,IAAA,cAAA,CAAA,aAAA,CAAA,GAAA,IAAkB;AACtB,CAAC,EA1CiB,cAAc,KAAd,cAAc,GA0C/B,EAAA,CAAA,CAAA;;MCAY,eAAe,GAAG,IAAI,cAAc,CAAgB,iBAAiB;MACrE,UAAU,GAAG,IAAI,cAAc,CAAgB,qBAAqB;MACpE,cAAc,GAAG,IAAI,cAAc,CAAgB,yBAAyB;AAE5E,MAAA,aAAa,GAAkB;AACxC,IAAA,MAAM,EAAE,EAAE;AACV,IAAA,MAAM,EAAE,EAAE;AACV,IAAA,iBAAiB,EAAE,GAAG;AACtB,IAAA,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;AACzB,IAAA,eAAe,EAAE,KAAK;AACtB,IAAA,aAAa,EAAE,KAAK;AACpB,IAAA,aAAa,EAAE,KAAK;AACpB,IAAA,oBAAoB,EAAE,GAAG;AACzB,IAAA,qBAAqB,EAAE,IAAI;AAC3B,IAAA,WAAW,EAAE,KAAK;AAClB,IAAA,mBAAmB,EAAE,EAAE;AACvB,IAAA,cAAc,EAAE,EAAE;AAClB,IAAA,oBAAoB,EAAE,KAAK;AAC3B,IAAA,UAAU,EAAE,IAAI;AAChB,IAAA,iBAAiB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzF,IAAA,gBAAgB,EAAE,KAAK;AACvB,IAAA,GAAG,EAAE,KAAK;AACV,IAAA,QAAQ,EAAE,KAAK;AACf,IAAA,sBAAsB,EAAE,KAAK;AAC7B,IAAA,mBAAmB,EAAE,KAAK;AAC1B,IAAA,gBAAgB,EAAE,CAAC,KAAc,KAAK,KAAwB;AAC9D,IAAA,iBAAiB,EAAE,CAAC,KAAyC,KAAK,KAAK;IACvE,UAAU,EAAE,IAAI,YAAY,EAAQ;AACpC,IAAA,QAAQ,EAAE;AACN,QAAA,GAAG,EAAE;AACD,YAAA,OAAO,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC;AAC7B,SAAA;AACD,QAAA,GAAG,EAAE;AACD,YAAA,OAAO,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC;AAC1B,YAAA,QAAQ,EAAE,IAAI;AACjB,SAAA;AACD,QAAA,CAAC,EAAE;AACC,YAAA,OAAO,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC;AAC1B,YAAA,MAAM,EAAE,GAAG;AACd,SAAA;AACD,QAAA,CAAC,EAAE;AACC,YAAA,OAAO,EAAE,IAAI,MAAM,CAAC,aAAa,CAAC;AACrC,SAAA;AACD,QAAA,CAAC,EAAE;AACC,YAAA,OAAO,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC;AAClC,SAAA;AACD,QAAA,CAAC,EAAE;AACC,YAAA,OAAO,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC;AAC/B,SAAA;AACD,QAAA,CAAC,EAAE;AACC,YAAA,OAAO,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC;AAC/B,SAAA;AACD,QAAA,CAAC,EAAE;AACC,YAAA,OAAO,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC;AAC7B,SAAA;AACD,QAAA,CAAC,EAAE;AACC,YAAA,OAAO,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC;AAC7B,SAAA;AACD,QAAA,CAAC,EAAE;AACC,YAAA,OAAO,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC;AAC7B,SAAA;AACD,QAAA,CAAC,EAAE;AACC,YAAA,OAAO,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC;AAC7B,SAAA;AACD,QAAA,CAAC,EAAE;AACC,YAAA,OAAO,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC;AAC7B,SAAA;AACD,QAAA,CAAC,EAAE;AACC,YAAA,OAAO,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC;AAC7B,SAAA;AACJ,KAAA;;AAGQ,MAAA,SAAS,GAAa;AAC/B,IAAA,cAAc,CAAC,qBAAqB;AACpC,IAAA,cAAc,CAAC,aAAa;AAC5B,IAAA,cAAc,CAAC,eAAe;;AAGrB,MAAA,iBAAiB,GAAa;AACvC,IAAA,cAAc,CAAC,OAAO;AACtB,IAAA,cAAc,CAAC,UAAU;AACzB,IAAA,cAAc,CAAC,OAAO;AACtB,IAAA,cAAc,CAAC,OAAO;AACtB,IAAA,cAAc,CAAC,SAAS;AACxB,IAAA,cAAc,CAAC,iBAAiB;AAChC,IAAA,cAAc,CAAC,WAAW;AAC1B,IAAA,cAAc,CAAC,IAAI;AACnB,IAAA,cAAc,CAAC,MAAM;;;MC5HZ,qBAAqB,CAAA;AACpB,IAAA,OAAO,GAAG,MAAM,CAAgB,eAAe,CAAC;AAEnD,IAAA,qBAAqB,GACxB,IAAI,CAAC,OAAO,CAAC,qBAAqB;AAE/B,IAAA,WAAW,GAAiC,IAAI,CAAC,OAAO,CAAC,WAAW;AAEpE,IAAA,eAAe,GAAqC,IAAI,CAAC,OAAO,CAAC,eAAe;AAEhF,IAAA,iBAAiB,GAAuC,IAAI,CAAC,OAAO,CAAC,iBAAiB;AAEtF,IAAA,QAAQ,GAA8B,IAAI,CAAC,OAAO,CAAC,QAAQ;AAE3D,IAAA,MAAM,GAA4B,IAAI,CAAC,OAAO,CAAC,MAAM;AAErD,IAAA,MAAM,GAA4B,IAAI,CAAC,OAAO,CAAC,MAAM;AAErD,IAAA,iBAAiB,GAAuC,IAAI,CAAC,OAAO,CAAC,iBAAiB;AAEtF,IAAA,aAAa,GAAmC,IAAI,CAAC,OAAO,CAAC,aAAa;AAE1E,IAAA,aAAa;AAEb,IAAA,aAAa,GAAmC,IAAI,CAAC,OAAO,CAAC,aAAa;AAE1E,IAAA,oBAAoB,GACvB,IAAI,CAAC,OAAO,CAAC,oBAAoB;AAE9B,IAAA,UAAU,GAAgC,IAAI,CAAC,OAAO,CAAC,UAAU;AAEjE,IAAA,cAAc,GAAoC,IAAI,CAAC,OAAO,CAAC,cAAc;AAE7E,IAAA,oBAAoB,GACvB,IAAI,CAAC,OAAO,CAAC,oBAAoB;AAE9B,IAAA,gBAAgB,GAAsC,IAAI,CAAC,OAAO,CAAC,gBAAgB;AAEnF,IAAA,QAAQ,GAA8B,IAAI,CAAC,OAAO,CAAC,QAAQ;AAE3D,IAAA,GAAG,GAAyB,IAAI,CAAC,OAAO,CAAC,GAAG;AAE5C,IAAA,gBAAgB,GACnB,IAAI,CAAC,OAAO,CAAC,gBAAgB;AAE1B,IAAA,iBAAiB,GACpB,IAAI,CAAC,OAAO,CAAC,iBAAiB;AAE3B,IAAA,sBAAsB,GACzB,IAAI,CAAC,OAAO,CAAC,sBAAsB;AAEhC,IAAA,aAAa,GAAmC,IAAI,CAAC,OAAO,CAAC,aAAa;AAE1E,IAAA,mBAAmB,GACtB,IAAI,CAAC,OAAO,CAAC,mBAAmB;AAE5B,IAAA,MAAM,GAAG,IAAI,GAAG,EAAU;IAE3B,eAAe,GAAG,KAAK;IAEvB,cAAc,GAAG,EAAE;IAEnB,WAAW,GAAG,EAAE;IAEhB,oBAAoB,GAAG,EAAE;AAEzB,IAAA,mBAAmB,GACtB,IAAI,CAAC,OAAO,CAAC,mBAAmB;IAE7B,uBAAuB,GAAG,KAAK;AAE/B,IAAA,OAAO;AAEP,IAAA,YAAY;AAEZ,IAAA,SAAS,CACZ,UAAwD,EACxD,cAAsB,EACtB,QAAQ,GAAG,CAAC,EACZ,UAAU,GAAG,KAAK,EAClB,UAAU,GAAG,KAAK;;IAElB,EAA8B,GAAA,MAAK,GAAG,EAAA;QAEtC,IAAI,CAAC,cAAc,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;YACnD,OAAO,cAAc,CAAC,YAAY;;QAEtC,IAAI,MAAM,GAAG,CAAC;QACd,IAAI,MAAM,GAAG,EAAE;QACf,IAAI,KAAK,GAAG,KAAK;QACjB,IAAI,cAAc,GAAG,KAAK;QAC1B,IAAI,KAAK,GAAG,CAAC;QACb,IAAI,QAAQ,GAAG,KAAK;QACpB,IAAI,cAAc,GAAG,UAAU;QAC/B,IAAI,iBAAiB,GAAG,QAAQ;AAEhC,QAAA,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,EAAE;AAC7D,YAAA,cAAc,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC;;AAEpF,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5C,YAAA,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC;;QAE9D,IAAI,cAAc,KAAK,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE;YACvC,cAAc,GAAG,EAAE;;AAEvB,QAAA,MAAM,UAAU,GAAa,cAAc,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC;QACzF,IACI,IAAI,CAAC,oBAAoB;AACzB,YAAA,cAAc,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC,KAAK,cAAc,CAAC,KAAK,EACnE;YACE,MAAM,IAAI,cAAc,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC;;AAEtD,QAAA,IAAI,cAAc,KAAK,cAAc,CAAC,EAAE,EAAE;YACtC,MAAM,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC;YACzD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;;YAGtC,cAAc,GAAG,iBAAiB;;QAEtC,MAAM,GAAG,GAAa,EAAE;;AAExB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAI,cAAc,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE;AACjC,gBAAA,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,YAAY,CAAC;;;AAGlE,QAAA,IAAI,cAAc,KAAK,cAAc,CAAC,QAAQ,EAAE;AAC5C,YAAA,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,MAAM,KAAK,EAAE,IAAI,GAAG,CAAC,MAAM,KAAK,EAAE;AAC1D,YAAA,IAAI,GAAG,CAAC,MAAM,GAAG,EAAE,EAAE;;gBAEjB,cAAc,GAAG,oBAAoB;;iBAClC;;gBAEH,cAAc,GAAG,gBAAgB;;;QAGzC,IAAI,cAAc,CAAC,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;AACnD,YAAA,IACI,cAAc,CAAC,KAAK,CAAC,aAAa,CAAC;;iBAElC,cAAc,CAAC,KAAK,CAAC,oCAAoC,CAAC,IAAI,CAAC,UAAU,CAAC,EAC7E;AACE,gBAAA,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;gBACrD,MAAM,SAAS,GAAW,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;AAE3D,gBAAA,cAAc,GAAG,IAAI,CAAC,mBAAmB,CACrC,cAAc,EACd,SAAS,EACT,IAAI,CAAC,aAAa,CACrB;;YAEL,MAAM,aAAa,GACf,OAAO,IAAI,CAAC,aAAa,KAAK,QAAQ,GAAG,IAAI,CAAC,aAAa,GAAG,cAAc,CAAC,GAAG;AACpF,YAAA,IACI,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC;AACzC,gBAAA,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,EACtF;AACE,gBAAA,IAAI,IAAI,GAAW,cAAc,CAAC,SAAS,CACvC,CAAC,EACD,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAC5C;gBACD,IACI,IAAI,CAAC,oBAAoB;AACzB,oBAAA,cAAc,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC,KAAK,cAAc,CAAC,KAAK;oBACjE,CAAC,UAAU,EACb;AACE,oBAAA,IAAI,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;;gBAG7E,cAAc,GAAG,GAAG,IAAI,CAAA,EAAG,cAAc,CAAC,SAAS,CAC/C,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,EACrC,cAAc,CAAC,MAAM,CACxB,EAAE;;YAEP,IAAI,KAAK,GAAG,EAAE;;AAEd,YAAA,IAAI,CAAC,oBAAoB;AACzB,gBAAA,cAAc,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC,KAAK,cAAc,CAAC;mBACrD,KAAK,GAAG,CAAA,EAAG,cAAc,CAAC,KAAK,CAAG,EAAA,cAAc,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC,CAAA,CAAE;AACvG,mBAAG,KAAK,GAAG,cAAc,CAAC;AAC9B,YAAA,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AACxB,gBAAA,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;;iBAC5C;AACH,gBAAA,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAC3B,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CACzD;;;aAEF,IAAI,cAAc,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;AAC5D,YAAA,IACI,cAAc,CAAC,KAAK,CAAC,WAAW,CAAC;AACjC,gBAAA,cAAc,CAAC,KAAK,CAAC,SAAS,CAAC;AAC/B,gBAAA,cAAc,CAAC,KAAK,CAAC,aAAa,CAAC;AACnC,gBAAA,cAAc,CAAC,KAAK,CAAC,sCAAsC,CAAC;AAC5D,gBAAA,cAAc,CAAC,KAAK,CAAC,eAAe,CAAC,EACvC;AACE,gBAAA,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;;YAGzD,MAAM,SAAS,GAAW,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;AAC3D,YAAA,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa;YAEtC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;AACnC,gBAAA,IACI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;AAChD,oBAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAClD;AACE,oBAAA,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;AAC3D,0BAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AACtB,0BAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;;qBACxB;AACH,oBAAA,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CACnC,CAAC,EAAE,KAAK,EAAE,KAAK,IAAI,CAAC,iBAAiB,CAC3B;;;YAItB,IAAI,UAAU,EAAE;AACZ,gBAAA,MAAM,EAAE,kBAAkB,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,gCAAgC,CAC9E,cAAc,EACd,aAA0B,CAC7B;gBACD,MAAM,cAAc,GAAG,cAAc,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC,KAAK;gBACjE,MAAM,mBAAmB,GAAG,cAAc,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC,WAAW;gBAC5E,MAAM,sBAAsB,GAAG,cAAc,CAAC,CAAC,CAAC,KAAK,aAAa;gBAClE,MAAM,uBAAuB,GAAG,cAAc,CAAC,CAAC,CAAC,KAAK,aAAa;AAEnE,gBAAA,IACI,CAAC,sBAAsB,IAAI,CAAC,YAAY;AACxC,qBAAC,cAAc,IAAI,uBAAuB,IAAI,CAAC,YAAY,CAAC;qBAC3D,mBAAmB,IAAI,CAAC,kBAAkB,IAAI,CAAC,YAAY,CAAC,EAC/D;AACE,oBAAA,cAAc,GAAG,cAAc,CAAC,WAAW;;AAG/C,gBAAA,IACI,kBAAkB;oBAClB,YAAY;oBACZ,cAAc;oBACd,iBAAiB,KAAK,CAAC,EACzB;oBACE,IAAI,kBAAkB,GAAG,YAAY,IAAI,kBAAkB,GAAG,YAAY,EAAE;wBACxE,cAAc,GAAG,cAAc,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC;;;gBAIlF,IAAI,CAAC,kBAAkB,IAAI,YAAY,IAAI,cAAc,CAAC,MAAM,GAAG,YAAY,EAAE;AAC7E,oBAAA,cAAc,GAAG;0BACX,cAAc,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,YAAY;AAC1D,0BAAE,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC;;gBAG5C,IAAI,kBAAkB,IAAI,YAAY,IAAI,iBAAiB,KAAK,CAAC,EAAE;AAC/D,oBAAA,IAAI,kBAAkB,GAAG,YAAY,EAAE;wBACnC,cAAc,GAAG,cAAc,CAAC,KAAK,CAAC,kBAAkB,GAAG,CAAC,CAAC;;AAEjE,oBAAA,IAAI,kBAAkB,GAAG,YAAY,EAAE;AACnC,wBAAA,cAAc,GAAG,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC;;;;AAK/D,YAAA,IAAI,SAAS,KAAK,CAAC,EAAE;gBACjB,cAAc,GAAG,IAAI,CAAC;AAClB,sBAAE,cAAc,CAAC,MAAM,GAAG,CAAC;AACzB,wBAAA,cAAc,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC,KAAK;AAC1C,wBAAA,cAAc,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC,WAAW;AAChD,wBAAA,cAAc,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,iBAAiB;AAC5C,wBAAA,cAAc,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC,KAAK;AAC1C,wBAAA,cAAc,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC;AACnC,0BAAE,GAAG,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM;0BACnD,cAAc,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC,WAAW;4BAC9C,cAAc,CAAC,MAAM,GAAG,CAAC;AACzB,4BAAA,cAAc,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,iBAAiB;AAC5C,4BAAA,cAAc,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC,KAAK;AAC1C,4BAAA,cAAc,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC;8BACrC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM;AAC/C,8BAAE;AACR,sBAAE,cAAc,CAAC,MAAM,GAAG,CAAC;AACvB,wBAAA,cAAc,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC,WAAW;AAChD,wBAAA,cAAc,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,iBAAiB;AAC5C,wBAAA,cAAc,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC,KAAK;AAC1C,wBAAA,cAAc,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC;0BACrC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM;0BAC7C,cAAc;;iBACnB;AACH,gBAAA,IACI,cAAc,CAAC,CAAC,CAAC,KAAK,aAAa;oBACnC,cAAc,CAAC,MAAM,GAAG,CAAC;oBACzB,CAAC,UAAU,EACb;oBACE,cAAc;AACV,wBAAA,cAAc,CAAC,WAAW;4BAC1B,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;AACtD,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI;;AAE/B,gBAAA,IACI,cAAc,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC,WAAW;AAChD,oBAAA,cAAc,CAAC,CAAC,CAAC,KAAK,aAAa;AACnC,oBAAA,cAAc,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,iBAAiB;oBAC5C,CAAC,UAAU,EACb;oBACE,cAAc;wBACV,cAAc,CAAC,MAAM,GAAG;8BAClB,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;gCAC1B,aAAa;gCACb,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC;8BACjD,cAAc;AACxB,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI;;gBAE/B,IACI,IAAI,CAAC,oBAAoB;AACzB,oBAAA,CAAC,UAAU;AACX,oBAAA,cAAc,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC,KAAK;AAC1C,qBAAC,cAAc,CAAC,CAAC,CAAC,KAAK,aAAa;wBAChC,cAAc,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC,WAAW,CAAC,EACvD;oBACE,cAAc;wBACV,cAAc,CAAC,CAAC,CAAC,KAAK,aAAa,IAAI,cAAc,CAAC,MAAM,GAAG;8BACzD,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1B,gCAAA,cAAc,CAAC,WAAW;gCAC1B,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM;8BAC7C,cAAc,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC,WAAW;gCAC9C,cAAc,CAAC,MAAM,GAAG,CAAC;AACzB,gCAAA,cAAc,CAAC,CAAC,CAAC,KAAK;kCACtB,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;oCAC1B,aAAa;oCACb,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM;kCAC7C,cAAc;AAC1B,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI;;;;;YAOnC,MAAM,4BAA4B,GAAW,IAAI,CAAC,uBAAuB,CACrE,IAAI,CAAC,iBAAiB,CACzB;YACD,IAAI,YAAY,GAAW,0CAA0C,CAAC,OAAO,CACzE,4BAA4B,EAC5B,EAAE,CACL;;YAED,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;AACnC,gBAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE;AACrC,oBAAA,YAAY,GAAG,YAAY,CAAC,OAAO,CAC/B,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,EACpC,cAAc,CAAC,YAAY,CAC9B;;;iBAEF;AACH,gBAAA,YAAY,GAAG,YAAY,CAAC,OAAO,CAC/B,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,EAChD,EAAE,CACL;;YAGL,MAAM,iBAAiB,GAAG,IAAI,MAAM,CAAC,GAAG,GAAG,YAAY,GAAG,GAAG,CAAC;AAC9D,YAAA,IAAI,cAAc,CAAC,KAAK,CAAC,iBAAiB,CAAC,EAAE;AACzC,gBAAA,cAAc,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;;AAG3E,YAAA,cAAc,GAAG,IAAI,CAAC,mBAAmB,CACrC,cAAc,EACd,SAAS,EACT,IAAI,CAAC,aAAa,CACrB;AACD,YAAA,MAAM,SAAS,GAAW,cAAc,CAAC,OAAO,CAC5C,IAAI,MAAM,CAAC,4BAA4B,EAAE,GAAG,CAAC,EAC7C,EAAE,CACL;AAED,YAAA,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAC/B,SAAS,EACT,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,aAAa,EAClB,SAAS,CACZ;AAED,YAAA,MAAM,UAAU,GACZ,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC;YACvF,MAAM,SAAS,GAAW,MAAM,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM;AAC/D,YAAA,MAAM,yCAAyC,GAC3C,UAAU,IAAI,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc;YAE1E,IACI,CAAC,MAAM,CAAC,iBAAiB,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,iBAAiB;gBACrD,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;AAClD,gBAAA,IAAI,CAAC,MAAM;AACX,gBAAA,UAAU,EACZ;AACE,gBAAA,iBAAiB,GAAG,iBAAiB,GAAG,CAAC;;AACtC,iBAAA,IACH,CAAC,SAAS,GAAG,CAAC,IAAI,MAAM,CAAC,iBAAiB,CAAC,KAAK,IAAI,CAAC,iBAAiB;AACtE,gBAAA,yCAAyC,EAC3C;gBACE,cAAc,GAAG,IAAI;gBACrB,IAAI,MAAM,GAAG,CAAC;AACd,gBAAA,GAAG;oBACC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,GAAG,MAAM,CAAC;AAC3C,oBAAA,MAAM,EAAE;AACZ,iBAAC,QAAQ,MAAM,GAAG,SAAS;;iBACxB,IACH,MAAM,CAAC,iBAAiB,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,iBAAiB;gBACxD,SAAS,KAAK,CAAC,CAAC;gBAChB,SAAS,KAAK,CAAC,CAAC;gBAChB,MAAM,CAAC,iBAAiB,CAAC,KAAK,IAAI,CAAC,iBAAiB,EACtD;AACE,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;gBACnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,GAAG,CAAC,CAAC;;iBACnC,IACH,CAAC,UAAU,KAAK,CAAC;AACb,gBAAA,iBAAiB,GAAG,CAAC;gBACrB,EACI,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,iBAAiB;oBACzD,iBAAiB,GAAG,CAAC,CACxB;AACL,iBAAC,EACG,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,iBAAiB,IAAI,iBAAiB,GAAG,CAAC,CACnF;AACG,oBAAA,SAAS,IAAI,CAAC,CAAC,EACrB;AACE,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;gBACnB,cAAc,GAAG,IAAI;gBACrB,KAAK,GAAG,SAAS;gBAEjB,iBAAiB,IAAI,SAAS;AAC9B,gBAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC;;iBAC/B;AACH,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;;;aAEpB;AACH,YAAA;;AAEI,YAAA,IAAI,CAAC,GAAG,CAAC,EAAE,WAAW,GAAW,UAAU,CAAC,CAAC,CAAE,EAC/C,CAAC,GAAG,UAAU,CAAC,MAAM,EACrB,CAAC,EAAE,EAAE,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,YAAY,EACjE;AACE,gBAAA,IAAI,MAAM,KAAK,cAAc,CAAC,MAAM,EAAE;oBAClC;;gBAGJ,MAAM,mBAAmB,GAAY,cAAc,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ;AAChF,gBAAA,IACI,IAAI,CAAC,gBAAgB,CACjB,WAAW,EACX,cAAc,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,YAAY,CACxD;oBACD,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,cAAc,CAAC,eAAe,EAC/D;oBACE,MAAM,IAAI,WAAW;oBACrB,MAAM,IAAI,CAAC;;qBACR,IACH,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,cAAc,CAAC,WAAW;oBACzD,KAAK;AACL,oBAAA,IAAI,CAAC,gBAAgB,CACjB,WAAW,EACX,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,cAAc,CAAC,YAAY,CAC5D,EACH;oBACE,MAAM,IAAI,WAAW;oBACrB,MAAM,IAAI,CAAC;oBACX,KAAK,GAAG,KAAK;;AACV,qBAAA,IACH,IAAI,CAAC,gBAAgB,CACjB,WAAW,EACX,cAAc,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,YAAY,CACxD;oBACD,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,cAAc,CAAC,WAAW;oBACzD,CAAC,mBAAmB,EACtB;oBACE,MAAM,IAAI,WAAW;oBACrB,KAAK,GAAG,IAAI;;qBACT,IACH,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,cAAc,CAAC,eAAe;AAC7D,oBAAA,IAAI,CAAC,gBAAgB,CACjB,WAAW,EACX,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,cAAc,CAAC,YAAY,CAC5D,EACH;oBACE,MAAM,IAAI,WAAW;oBACrB,MAAM,IAAI,CAAC;;AACR,qBAAA,IACH,IAAI,CAAC,gBAAgB,CACjB,WAAW,EACX,cAAc,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,YAAY,CACxD,EACH;oBACE,IAAI,cAAc,CAAC,MAAM,CAAC,KAAK,cAAc,CAAC,KAAK,EAAE;wBACjD,IAAI,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;AAC9D,4BAAA,iBAAiB,GAAG,CAAC,IAAI,CAAC;kCACpB,iBAAiB,GAAG;kCACpB,iBAAiB;4BACvB,MAAM,IAAI,CAAC;AACX,4BAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;AACvB,4BAAA,CAAC,EAAE;AACH,4BAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;gCACvB,MAAM,IAAI,GAAG;;4BAEjB;;;oBAGR,IAAI,cAAc,CAAC,MAAM,CAAC,KAAK,cAAc,CAAC,IAAI,EAAE;wBAChD,IACI,IAAI,CAAC;AACD,8BAAE,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC;iCACzC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAC3C,iCAAC,cAAc,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC;AAClD,oCAAA,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;iCACxD,cAAc,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,GAAG;AAC7C,oCAAA,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;AAC7B,8BAAE,CAAC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;AAC1C,iCAAC,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,GAAG;oCACtC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,GAAG;oCACxC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,GAAG;oCACxC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,GAAG;AACxC,oCAAA,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;AACvB,oCAAA,MAAM,GAAG,EAAE,CAAC,EACxB;AACE,4BAAA,iBAAiB,GAAG,iBAAiB,GAAG,CAAC;4BACzC,MAAM,IAAI,CAAC;AACX,4BAAA,CAAC,EAAE;4BACH;;;AAGR,oBAAA,IACI,cAAc,CAAC,MAAM,CAAC,KAAK,cAAc,CAAC,MAAM;wBAChD,cAAc,CAAC,MAAM,CAAC,KAAK,cAAc,CAAC,MAAM,EAClD;AACE,wBAAA,IAAI,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;AACzB,4BAAA,iBAAiB,GAAG,CAAC,IAAI,CAAC;kCACpB,iBAAiB,GAAG;kCACpB,iBAAiB;4BACvB,MAAM,IAAI,CAAC;AACX,4BAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;AACvB,4BAAA,CAAC,EAAE;AACH,4BAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;gCACvB,MAAM,IAAI,GAAG;;4BAEjB;;;oBAGR,MAAM,SAAS,GAAG,EAAE;AACpB,oBAAA,MAAM,gBAAgB,GAAG,cAAc,CAAC,MAAM,CAAW;oBACzD,MAAM,uBAAuB,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,CAAW;oBACpE,MAAM,uBAAuB,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,CAAW;oBACpE,MAAM,wBAAwB,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,CAAW;oBACrE,MAAM,wBAAwB,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,CAAW;AACrE,oBAAA,MAAM,iCAAiC,GAAG,cAAc,CAAC,KAAK,CAC1D,MAAM,GAAG,CAAC,EACV,MAAM,GAAG,CAAC,CACb;AACD,oBAAA,MAAM,8BAA8B,GAAG,cAAc,CAAC,KAAK,CACvD,MAAM,GAAG,CAAC,EACV,MAAM,GAAG,CAAC,CACb;AACD,oBAAA,MAAM,4BAA4B,GAAG,cAAc,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC;AAC7E,oBAAA,MAAM,6BAA6B,GAAG,cAAc,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC;oBAC9E,IAAI,cAAc,CAAC,MAAM,CAAC,KAAK,cAAc,CAAC,GAAG,EAAE;AAC/C,wBAAA,MAAM,kBAAkB,GACpB,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,cAAc,CAAC,MAAM;AACxD,wBAAA,MAAM,mBAAmB,GACrB,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,cAAc,CAAC,MAAM;AACpD,4BAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,wBAAwB,CAAC;wBAC7D,IACI,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,gBAAgB;AACjD,6BAAC,CAAC,kBAAkB;AAChB,iCAAC,MAAM,CAAC,4BAA4B,CAAC,GAAG,SAAS;AAC7C,oCAAA,MAAM,CAAC,8BAA8B,CAAC,GAAG,SAAS;oCAClD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC,CAAC;AAClE,6BAAC;AACG,kCAAE,MAAM,CAAC,8BAA8B,CAAC,GAAG,SAAS;qCACjD,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB,CAAC;AAC/C,wCAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC;AAC7D,oCAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB;AAClD,kCAAE,MAAM,CAAC,4BAA4B,CAAC,GAAG,SAAS;AAChD,qCAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,uBAAuB,CAAC;AACrD,wCAAA,CAAC,UAAU,CAAC,CAAC,EACzB;AACE,4BAAA,iBAAiB,GAAG,CAAC,IAAI,CAAC;kCACpB,iBAAiB,GAAG;kCACpB,iBAAiB;4BACvB,MAAM,IAAI,CAAC;AACX,4BAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;AACvB,4BAAA,CAAC,EAAE;AAEH,4BAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;gCACvB,MAAM,IAAI,GAAG;;4BAEjB;;;oBAGR,IAAI,cAAc,CAAC,MAAM,CAAC,KAAK,cAAc,CAAC,KAAK,EAAE;wBACjD,MAAM,WAAW,GAAG,EAAE;;AAEtB,wBAAA,MAAM,WAAW,GACb,MAAM,KAAK,CAAC;AACZ,6BAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;AACpB,gCAAA,MAAM,CAAC,4BAA4B,CAAC,GAAG,WAAW;AAClD,iCAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,uBAAuB,CAAC;oCACrD,CAAC,UAAU,CAAC,CAAC;;AAEzB,wBAAA,MAAM,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC;AACjE,wBAAA,MAAM,cAAc,GAChB,iCAAiC,CAAC,QAAQ,CAAC,YAAY,CAAC;AACxD,4BAAA,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC;6BAC5B,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,wBAAwB,CAAC;AACvD,gCAAA,MAAM,CAAC,8BAA8B,CAAC,GAAG,WAAW;gCACpD,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB,CAAC;gCAClD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;;AAE1D,wBAAA,MAAM,cAAc,GAChB,MAAM,CAAC,iCAAiC,CAAC,IAAI,SAAS;AACtD,4BAAA,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAC5B,iCAA2C,CAC9C;AACD,4BAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,wBAAwB,CAAC;AACzD,6BAAC,MAAM,CAAC,4BAA4B,CAAC,GAAG,WAAW;gCAC/C,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC;;AAEjE,wBAAA,MAAM,iBAAiB,GACnB,CAAC,MAAM,CAAC,4BAA4B,CAAC,GAAG,WAAW,IAAI,MAAM,KAAK,CAAC;AACnE,6BAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,uBAAuB,CAAC;gCACrD,MAAM,KAAK,CAAC,CAAC;;AAErB,wBAAA,MAAM,cAAc,GAChB,MAAM,CAAC,iCAAiC,CAAC,GAAG,SAAS;AACrD,4BAAA,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAC5B,iCAA2C,CAC9C;AACD,4BAAA,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAC5B,6BAAuC,CAC1C;AACD,4BAAA,MAAM,CAAC,6BAA6B,CAAC,GAAG,WAAW;AACnD,4BAAA,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC;;AAEjC,wBAAA,MAAM,cAAc,GAChB,MAAM,CAAC,iCAAiC,CAAC,IAAI,SAAS;AACtD,4BAAA,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAC5B,iCAA2C,CAC9C;AACD,4BAAA,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,wBAAwB,CAAC;AAC1D,4BAAA,MAAM,CAAC,8BAA8B,CAAC,GAAG,WAAW;wBACxD,IACI,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,gBAAgB;4BACjD,WAAW;4BACX,cAAc;4BACd,cAAc;4BACd,cAAc;4BACd,cAAc;6BACb,iBAAiB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAC/C;AACE,4BAAA,iBAAiB,GAAG,CAAC,IAAI,CAAC;kCACpB,iBAAiB,GAAG;kCACpB,iBAAiB;4BACvB,MAAM,IAAI,CAAC;AACX,4BAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;AACvB,4BAAA,CAAC,EAAE;AACH,4BAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;gCACvB,MAAM,IAAI,GAAG;;4BAEjB;;;oBAGR,MAAM,IAAI,WAAW;AACrB,oBAAA,MAAM,EAAE;;AACL,qBAAA,IACH,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC;AAC5C,oBAAA,cAAc,CAAC,MAAM,CAAC,KAAK,WAAW,EACxC;oBACE,MAAM,IAAI,WAAW;AACrB,oBAAA,MAAM,EAAE;;AACL,qBAAA,IACH,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAC1B,cAAc,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,YAAY,CACxD,KAAK,CAAC,CAAC,EACV;AACE,oBAAA,MAAM,IAAI,cAAc,CAAC,MAAM,CAAC;AAChC,oBAAA,MAAM,EAAE;AACR,oBAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;AACvB,oBAAA,CAAC,EAAE;;AACA,qBAAA,IACH,cAAc,CAAC,MAAM,CAAC,KAAK,cAAc,CAAC,WAAW;oBACrD,IAAI,CAAC,aAAa,EACpB;AACE,oBAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;;AACpB,qBAAA,IACH,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,YAAY,CAAC;AACpE,oBAAA,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,YAAY,CAAC,EAAE,QAAQ,EAChF;AACE,oBAAA,IACI,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC;AACpB,wBAAA,cAAc,KAAK,iBAAiB;AACpC,wBAAA,cAAc,KAAK,gBAAgB;AACnC,wBAAA,cAAc,KAAK,oBAAoB;AACvC,wBAAA,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC;AACjC,wBAAA,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,YAAY;8BAC9D,QAAQ,EAChB;AACE,wBAAA,MAAM,IAAI,UAAU,CAAC,MAAM,CAAC;;oBAEhC,IACI,cAAc,CAAC,QAAQ,CACnB,cAAc,CAAC,WAAW,GAAG,cAAc,CAAC,WAAW,CAC1D;AACD,wBAAA,cAAc,CAAC,QAAQ,CACnB,cAAc,CAAC,WAAW,GAAG,cAAc,CAAC,WAAW,CAC1D,EACH;AACE,wBAAA,MAAM,EAAE;;AAGZ,oBAAA,MAAM,EAAE;AACR,oBAAA,CAAC,EAAE;;qBACA,IACH,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,cAAc,CAAC,WAAW;AAC9D,oBAAA,IAAI,CAAC,gBAAgB,CACjB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,cAAc,CAAC,YAAY,CACjE;AACD,oBAAA,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;AACtE,oBAAA,KAAK,EACP;oBACE,MAAM,IAAI,CAAC;oBACX,MAAM,IAAI,WAAW;;qBAClB,IACH,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,cAAc,CAAC,eAAe;AAClE,oBAAA,IAAI,CAAC,gBAAgB,CACjB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,cAAc,CAAC,YAAY,CACjE;AACD,oBAAA,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;AACtE,oBAAA,KAAK,EACP;oBACE,MAAM,IAAI,CAAC;oBACX,MAAM,IAAI,WAAW;;qBAClB,IACH,IAAI,CAAC,aAAa;oBAClB,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC;oBAC/C,WAAW,KAAK,IAAI,CAAC,oBAAoB;AACzC,oBAAA,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,EACxC;oBACE,QAAQ,GAAG,IAAI;;;;AAI3B,QAAA,IACI,MAAM,CAAC,iBAAiB,GAAG,CAAC,CAAC;AAC7B,YAAA,MAAM,CAAC,MAAM,GAAG,CAAC,KAAK,cAAc,CAAC,MAAM;YAC3C,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAC1B,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,cAAc,CAAC,YAAY,CAC3E,KAAK,CAAC,CAAC,EACV;YACE,MAAM,IAAI,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;;AAEvD,QAAA,IAAI,WAAW,GAAW,iBAAiB,GAAG,CAAC;QAE/C,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;AACjC,YAAA,KAAK,EAAE;AACP,YAAA,WAAW,EAAE;;AAGjB,QAAA,IAAI,WAAW,GACX,UAAU,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS;AAC7D,cAAE;cACA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB;AACjC,kBAAE;kBACA,CAAC;QACb,IAAI,QAAQ,EAAE;AACV,YAAA,WAAW,EAAE;;AAGjB,QAAA,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC;AAC/B,QAAA,IAAI,KAAK,GAAG,CAAC,EAAE;AACX,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;;QAEvB,IAAI,WAAW,GAAG,KAAK;QACvB,IAAI,UAAU,EAAE;AACZ,YAAA,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;;AAGnF,QAAA,IAAI,GAAG,GAAG,CAAG,EAAA,IAAI,CAAC,MAAM,CAAA,EAAG,WAAW,GAAG,cAAc,CAAC,YAAY,GAAG,MAAM,CACzE,EAAA,IAAI,CAAC,aAAa,GAAG,EAAE,GAAG,IAAI,CAAC,MACnC,EAAE;AAEF,QAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACrB,GAAG,GAAG,IAAI,CAAC,aAAa,GAAG,GAAG,IAAI,CAAC,MAAM,CAAG,EAAA,MAAM,EAAE,GAAG,CAAG,EAAA,MAAM,EAAE;;AAGtE,QAAA,MAAM,iCAAiC,GACnC,cAAc,CAAC,MAAM,KAAK,CAAC;YAC3B,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAW,CAAC;AAC5D,YAAA,cAAc,KAAK,cAAc,CAAC,CAAC,CAAC;QAExC,IACI,CAAC,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC,CAAW,CAAC;AACnE,YAAA,iCAAiC,EACnC;AACE,YAAA,OAAO,EAAE;;AAGb,QAAA,IAAI,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACnF,IAAI,UAAU,IAAI,MAAM,KAAK,cAAc,CAAC,KAAK,EAAE;AAC/C,gBAAA,OAAO,EAAE;;YAEb,GAAG,GAAG,CAAG,EAAA,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA,EAAG;AACzC,iBAAA,KAAK,CAAC,cAAc,CAAC,KAAK;iBAC1B,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,MAAM,CAAA,CAAE;;AAE1D,QAAA,OAAO,GAAG;;AAGP,IAAA,oBAAoB,CAAC,WAAmB,EAAA;QAC3C,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE;AAC3C,YAAA,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,GAAW,KAAK,GAAG,KAAK,WAAW,CAAC;;AAEhF,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;;AAGtC,IAAA,gBAAgB,CAAC,WAAmB,EAAA;AACvC,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,GAAW,KAAK,GAAG,KAAK,WAAW,CAAC;;IAGrE,gBAAgB,CAAC,WAAmB,EAAE,UAAkB,EAAA;AAC3D,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ;QACvE,QACI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO;AAC/B,YAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;AACxD,YAAA,KAAK;;IAIL,qBAAqB,GAAG,CAC5B,GAAW,EACX,qBAA6B,EAC7B,YAA+B,EAC/B,SAAiB,KACjB;QACA,IAAI,CAAC,GAAa,EAAE;QACpB,IAAI,WAAW,GAAG,EAAE;AAEpB,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;AAC7B,YAAA,MAAM,MAAM,GAAG,IAAI,MAAM,CACrB,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAK,EAAA,EAAA,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CACrF;AACD,YAAA,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;AACrB,YAAA,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,cAAc,CAAC,YAAY;;aAChE;AACH,YAAA,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC;YAC3B,WAAW,GAAG,YAAY;;QAE9B,MAAM,QAAQ,GACV,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,CAAG,EAAA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAE,CAAA,GAAG,cAAc,CAAC,YAAY;QACxE,IAAI,GAAG,GAAW,CAAC,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,YAAY;AACrD,QAAA,MAAM,cAAc,GAAW,IAAI,CAAC,cAAc,CAAC,OAAO,CACtD,KAAK,EACL,cAAc,CAAC,YAAY,CAC9B;AACD,QAAA,IAAI,cAAc,IAAI,CAAC,cAAc,EAAE;YACnC,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC,KAAK,EAAE;gBACjC,GAAG,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM,CAAC,CAAA,CAAE;;iBACjE;gBACH,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM,CAAC;;;QAGjD,MAAM,GAAG,GAAG,cAAc;QAE1B,OAAO,qBAAqB,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AAC3C,YAAA,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,GAAG,qBAAqB,GAAG,IAAI,CAAC;;AAG/D,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;YAClC,OAAO,GAAG,GAAG,QAAQ;;AAClB,aAAA,IAAI,SAAS,KAAK,CAAC,EAAE;AACxB,YAAA,OAAO,GAAG;;AAEd,QAAA,OAAO,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC;AACrD,KAAC;AAEO,IAAA,UAAU,GAAG,CAAC,GAAW,KAAa;QAC1C,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;AAC1C,QAAA,MAAM,KAAK,GAAG,MAAM,CAChB,IAAI,CAAC,oBAAoB,IAAI,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK;cACxD,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM;cAChC,YAAY,CACrB;AAED,QAAA,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,GAAG;AACtD,KAAC;AAEM,IAAA,YAAY,GAAG,CAAC,cAAsB,KAAY;QACrD,MAAM,CAAC,GAAa,cAAc,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC;AAC5D,QAAA,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YACd,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;;AAGlC,QAAA,OAAO,QAAQ;AACnB,KAAC;AAEO,IAAA,oBAAoB,GAAG,CAAC,UAAkB,KAAY;AAC1D,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC/C,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;AAC5D,YAAA,IACI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC3B,gBAAA,CAAC,KAAK,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC;AAC7B,iBAAC,CAAC,GAAG,CAAC,GAAG,CAAC;oBACN,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,EAC9E;gBACE,OAAO,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,cAAc,CAAC,YAAY,CAAC;;;AAGtE,QAAA,OAAO,UAAU;AACrB,KAAC;IAEO,mBAAmB,GAAG,CAC1B,UAAkB,EAClB,SAAiB,EACjB,aAA6C,KACrC;QACR,IAAI,mBAAmB,GAAG,UAAU;QACpC,IAAI,sBAAsB,GAAG,aAAa;AAE1C,QAAA,IAAI,SAAS,GAAG,QAAQ,EAAE;;AAEtB,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,sBAAsB,CAAC,EAAE;AACvC,gBAAA,MAAM,MAAM,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,IAAI,CAAC,iBAAiB,CAAC;AAEjF,gBAAA,sBAAsB,GAAG,MAAM,GAAG,MAAM,GAAG,sBAAsB,CAAC,CAAC,CAAC;;AAExE,YAAA,MAAM,cAAc,GAAG,IAAI,MAAM,CAC7B,IAAI,CAAC,uBAAuB,CAAC,sBAAsB,CAAC,GAAG,CAAA,IAAA,EAAO,SAAS,CAAA,IAAA,CAAM,CAChF;YACD,MAAM,cAAc,GAChB,mBAAmB,CAAC,KAAK,CAAC,cAAc,CAAC;AAC7C,YAAA,MAAM,oBAAoB,GAAW,CAAC,cAAc,IAAI,cAAc,CAAC,CAAC,CAAC,EAAE,MAAM,KAAK,CAAC;AACvF,YAAA,IAAI,oBAAoB,GAAG,CAAC,GAAG,SAAS,EAAE;AACtC,gBAAA,MAAM,IAAI,GAAG,oBAAoB,GAAG,CAAC,GAAG,SAAS;AAEjD,gBAAA,mBAAmB,GAAG,mBAAmB,CAAC,SAAS,CAC/C,CAAC,EACD,mBAAmB,CAAC,MAAM,GAAG,IAAI,CACpC;;YAEL,IACI,SAAS,KAAK,CAAC;AACf,gBAAA,IAAI,CAAC,kBAAkB,CACnB,mBAAmB,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC,EACnD,sBAAsB,EACtB,IAAI,CAAC,iBAAiB,CACzB,EACH;AACE,gBAAA,mBAAmB,GAAG,mBAAmB,CAAC,SAAS,CAC/C,CAAC,EACD,mBAAmB,CAAC,MAAM,GAAG,CAAC,CACjC;;;AAGT,QAAA,OAAO,mBAAmB;AAC9B,KAAC;AAEO,IAAA,eAAe,CAAC,GAAW,EAAA;AAC/B,QAAA,OAAO;AACF,aAAA,KAAK,CAAC,cAAc,CAAC,YAAY;AACjC,aAAA,MAAM,CAAC,CAAC,CAAS,EAAE,GAAW,KAAI;AAC/B,YAAA,MAAM,eAAe,GACjB,OAAO,IAAI,CAAC,aAAa,KAAK;AAC1B,kBAAE,CAAC,KAAK,IAAI,CAAC;AACb;AACE,oBAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CACvB,CAA8C,CACjD;AACX,YAAA,QACI,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC;gBACjB,CAAC,KAAK,IAAI,CAAC,iBAAiB;gBAC5B,eAAe;AACf,iBAAC,CAAC,KAAK,cAAc,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC,oBAAoB,CAAC;AAE9E,SAAC;AACA,aAAA,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;;AAGlC,IAAA,uBAAuB,CAAC,IAAY,EAAA;;;;QAIxC,IAAI,IAAI,EAAE;YACN,MAAM,aAAa,GAAG,cAAc;AACpC,YAAA,OAAO,IAAI,KAAK,GAAG,GAAG,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAA,EAAA,EAAK,IAAI,CAAA,CAAE,GAAG,IAAI;;AAEvF,QAAA,OAAO,IAAI;;AAGP,IAAA,UAAU,CAAC,MAAc,EAAA;AAC7B,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC;;AAG3C,IAAA,kBAAkB,CAAI,KAAQ,EAAE,aAAsB,EAAE,aAAgB,EAAA;AAC9E,QAAA,OAAO,KAAK,CAAC,OAAO,CAAC,aAAa;AAC9B,cAAE,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,aAAa,CAAC,CAAC,QAAQ,CAAC,KAAK;AACjE,cAAE,KAAK,KAAK,aAAa;;AAGzB,IAAA,QAAQ,CAAC,QAAkB,EAAA;AAC/B,QAAA,OAAO,EACH,QAAQ,CAAC,MAAM,KAAK,CAAC;YACrB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAa,EAAE,KAAa,KAAI;gBAC5C,IAAI,QAAQ,CAAC,MAAM,KAAK,KAAK,GAAG,CAAC,EAAE;AAC/B,oBAAA,OAAO,KAAK,KAAK,cAAc,CAAC,YAAY,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG;;AAEvE,gBAAA,OAAO,KAAK,KAAK,cAAc,CAAC,YAAY,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG;aACtF,CAAC,CACL;;AAGG,IAAA,iBAAiB,CAAC,KAAa,EAAA;QACnC,IAAI,KAAK,KAAK,cAAc,CAAC,KAAK,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7D,YAAA,OAAO,KAAK;;AAEhB,QAAA,MAAM,YAAY,GACd,OAAO,IAAI,CAAC,aAAa,KAAK;cACxB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa;cAChC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC;QAC3C,MAAM,YAAY,GACd,IAAI,CAAC,oBAAoB,IAAI,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;AAChF,QAAA,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE;YACrB,MAAM,WAAW,GAAG,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC;YACrF,OAAO,KAAK,CAAC,WAAW;kBAClB,cAAc,CAAC;AACjB,kBAAE,CAAG,EAAA,YAAY,CAAG,EAAA,WAAW,EAAE;;aAClC;YACH,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,EAAE,CAAC;YACnF,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,CAAC,YAAY,GAAG,CAAC,CAAC;AACrD,YAAA,MAAM,aAAa,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC,QAAQ,EAAE;YAEtE,MAAM,OAAO,GACT,OAAO,IAAI,CAAC,aAAa,KAAK,QAAQ,GAAG,IAAI,CAAC,aAAa,GAAG,cAAc,CAAC,GAAG;AAEpF,YAAA,OAAO,aAAa,KAAK,cAAc,CAAC;kBAClC,cAAc,CAAC;kBACf,CAAG,EAAA,YAAY,CAAG,EAAA,aAAa,GAAG,OAAO,CAAA,EAAG,WAAW,CAAA,CAAE;;;IAI/D,gCAAgC,CAAC,WAAmB,EAAE,aAAwB,EAAA;QAClF,IAAI,kBAAkB,GAAkB,IAAI;QAC5C,IAAI,YAAY,GAAkB,IAAI;AAEtC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,YAAA,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC;YAE3B,IAAI,IAAI,KAAK,aAAa,IAAI,kBAAkB,KAAK,IAAI,EAAE;gBACvD,kBAAkB,GAAG,CAAC;;AAG1B,YAAA,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,YAAY,KAAK,IAAI,EAAE;gBAC7D,YAAY,GAAG,CAAC;;YAGpB,IAAI,kBAAkB,KAAK,IAAI,IAAI,YAAY,KAAK,IAAI,EAAE;gBACtD;;;QAIR,OAAO;YACH,kBAAkB;YAClB,YAAY;SACf;;uGAxiCI,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;2GAArB,qBAAqB,EAAA,CAAA;;2FAArB,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBADjC;;;ACIK,MAAO,cAAe,SAAQ,qBAAqB,CAAA;IAC9C,aAAa,GAAG,KAAK;IACrB,WAAW,GAAG,EAAE;IAChB,QAAQ,GAAkB,IAAI;IAC9B,MAAM,GAAkB,IAAI;IAC5B,WAAW,GAAG,KAAK;IACnB,mBAAmB,GAAa,EAAE;IAClC,aAAa,GAAG,EAAE;IAClB,YAAY,GAAG,EAAE;AACxB;;;AAGG;IACI,YAAY,GAAG,KAAK;IAEnB,UAAU,GAAG,KAAK;AAClB,IAAA,MAAM;AACN,IAAA,IAAI;;AAGL,IAAA,QAAQ,GAAG,CAAC,CAAM,KAAI,GAAG;IAEhB,WAAW,GAAG,MAAM,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAEnD,IAAA,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AAEzB,IAAA,OAAO,GAAG,MAAM,CAAgB,eAAe,CAAC;IAElD,SAAS,GAAG,MAAM,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAElE;;;;;;;;;AASG;AACa,IAAA,SAAS,CACrB,UAAkB,EAClB,cAAsB,EACtB,QAAQ,GAAG,CAAC,EACZ,UAAU,GAAG,KAAK,EAClB,UAAU,GAAG,KAAK;;IAElB,EAA8B,GAAA,MAAK,GAAG,EAAA;;QAGtC,IAAI,CAAC,cAAc,EAAE;AACjB,YAAA,OAAO,UAAU,KAAK,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,UAAU;;;AAI1E,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AACpB,cAAE,IAAI,CAAC,eAAe;AACtB,cAAE,cAAc,CAAC,YAAY;;AAGjC,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,cAAc,CAAC,EAAE,IAAI,IAAI,CAAC,aAAa,EAAE;AACjE,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI,cAAc,CAAC,IAAI,CAAC;;AAE9E,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,cAAc,CAAC,QAAQ,IAAI,IAAI,CAAC,aAAa,EAAE;AACvE,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI,cAAc,CAAC,IAAI,CAAC;;;AAI9E,QAAA,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,EAAE;AACnC,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC;AACnC,YAAA,OAAO,CAAG,EAAA,IAAI,CAAC,MAAM,CAAG,EAAA,IAAI,CAAC,WAAW,CAAG,EAAA,IAAI,CAAC,MAAM,EAAE;;QAG5D,MAAM,SAAS,GACX,CAAC,CAAC,UAAU,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK;AACrC,eAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,cAAc,CAAC,YAAY;AAC3D,cAAE,cAAc,CAAC,YAAY;QACrC,IAAI,aAAa,GAAG,EAAE;QACtB,IAAI,WAAW,GAAG,QAAQ;;QAG1B,IACI,CAAC,IAAI,CAAC,WAAW;AACb,aAAC,UAAU,IAAI,UAAU,CAAC,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACvE,YAAA,CAAC,IAAI,CAAC,YAAY,EACpB;YACE,IAAI,YAAY,GACZ,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK;kBAC9B,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,YAAY;kBAC5C,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC;;YAG7D,IAAI,UAAU,EAAE;AACZ,gBAAA,YAAY,GAAG;AACV,qBAAA,KAAK,CAAC,CAAC,EAAE,QAAQ;qBACjB,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;;;AAIjD,YAAA,IAAI,IAAI,CAAC,aAAa,EAAE;;AAEpB,gBAAA,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC;AACxC,gBAAA,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CACvD,cAAc,CAAC,YAAY,CAC9B;;;AAIL,YAAA,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;gBACtE,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACrC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;;iBAC9B;gBACH,IAAI,UAAU,KAAK,cAAc,CAAC,YAAY,IAAI,YAAY,CAAC,MAAM,EAAE;AACnE,oBAAA,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;wBACtE,IAAI,UAAU,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,EAAE;4BACzC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,SAAS,CAAC;;6BAC7C,IAAI,UAAU,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,EAAE;4BAChD,IAAI,YAAY,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;gCAC/C,IAAI,UAAU,EAAE;oCACZ,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;;qCACtC;oCACH,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;;;iCAE9C;AACH,gCAAA,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;;;;;qBAIxE;oBACH,YAAY,GAAG,EAAE;;;;YAKzB,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACzC,gBAAA,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC;;;AAI/C,YAAA,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;gBACzB,IAAI,YAAY,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE;AACzC,oBAAA,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAClC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CACjD;;qBACE,IAAI,YAAY,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,EAAE;oBAClD,aAAa,GAAG,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;;qBAC3D;oBACH,aAAa,GAAG,UAAU;;;iBAE3B;gBACH,aAAa,GAAG,UAAU;;;;AAKlC,QAAA,IAAI,UAAU,KAAK,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;YACvD,aAAa,GAAG,UAAU;;;AAI9B,QAAA,IACI,UAAU;AACV,YAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAC1B,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,cAAc,CAAC,YAAY,CAClE,KAAK,CAAC,CAAC;AACR,YAAA,IAAI,CAAC,aAAa;AAClB,YAAA,CAAC,IAAI,CAAC,MAAM,EACd;AACE,YAAA,aAAa,GAAG,IAAI,CAAC,YAAY;;;AAIrC,QAAA,IAAI,IAAI,CAAC,uBAAuB,IAAI,WAAW,EAAE;YAC7C,IACI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAC3B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,EAAE,WAAW,GAAG,CAAC,CAAC,CACvD,EACH;AACE,gBAAA,WAAW,GAAG,WAAW,GAAG,CAAC;;AAC1B,iBAAA,IACH,cAAc,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC,CAAC,KAAK,cAAc,CAAC,MAAM,EAClF;AACE,gBAAA,WAAW,GAAG,WAAW,GAAG,CAAC;;AAGjC,YAAA,IAAI,CAAC,uBAAuB,GAAG,KAAK;;;QAIxC,IACI,IAAI,CAAC,aAAa;AAClB,YAAA,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC;AACtC,YAAA,CAAC,IAAI,CAAC,gBAAgB,EACxB;AACE,YAAA,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;;;AAIlD,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,aAAa,GAAG,UAAU;;aACvB;YACH,aAAa;AACT,gBAAA,OAAO,CAAC,aAAa,CAAC,IAAI,aAAa,CAAC,MAAM,GAAG,aAAa,GAAG,UAAU;;;QAInF,IACI,IAAI,CAAC,aAAa;AAClB,YAAA,IAAI,CAAC,sBAAsB;AAC3B,YAAA,IAAI,CAAC,WAAW;AAChB,YAAA,CAAC,UAAU;AACX,YAAA,CAAC,IAAI,CAAC,YAAY,EACpB;AACE,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC;kBACb,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW;AAClC,kBAAE,IAAI,CAAC,WAAW;AACtB,YAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;YAC7B,OAAO,IAAI,CAAC;kBACN,IAAI,CAAC;AACP,kBAAE,CAAA,EAAG,IAAI,CAAC,MAAM,CAAG,EAAA,IAAI,CAAC,WAAW,CAAG,EAAA,IAAI,CAAC,MAAM,EAAE;;;AAI3D,QAAA,MAAM,MAAM,GAAW,KAAK,CAAC,SAAS,CAClC,aAAa,EACb,cAAc,EACd,WAAW,EACX,UAAU,EACV,UAAU,EACV,EAAE,CACL;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;;;AAI9C,QAAA,IACI,IAAI,CAAC,iBAAiB,KAAK,cAAc,CAAC,GAAG;AAC7C,YAAA,IAAI,CAAC,aAAa,KAAK,cAAc,CAAC,GAAG,EAC3C;AACE,YAAA,IAAI,CAAC,aAAa,GAAG,cAAc,CAAC,KAAK;;;QAG7C,IACI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC;AACxD,YAAA,IAAI,CAAC,qBAAqB,KAAK,IAAI,EACrC;AACE,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAClD,CAAC,IAAY,KACT,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,CAAC;aACjF;;;AAIL,QAAA,IAAI,MAAM,IAAI,MAAM,KAAK,EAAE,EAAE;AACzB,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY;AACtC,YAAA,IAAI,CAAC,YAAY,GAAG,MAAM;AAE1B,YAAA,IAAI,CAAC,UAAU;AACX,gBAAA,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,YAAY;qBACvC,aAAa,KAAK,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;qBACzD,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,YAAY,IAAI,UAAU,CAAC;;;;AAKhE,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,EAAE;;AAGrD,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,WAAW,CAAC,EAAE;AACjE,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,OAAO,CAAA,EAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE;;AAEnG,YAAA,OAAO,MAAM;;AAGjB,QAAA,MAAM,MAAM,GAAW,MAAM,CAAC,MAAM;AACpC,QAAA,MAAM,SAAS,GAAG,CAAG,EAAA,IAAI,CAAC,MAAM,CAAA,EAAG,IAAI,CAAC,WAAW,CAAG,EAAA,IAAI,CAAC,MAAM,EAAE;;QAGnE,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;YACpD,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;AAC3D,YAAA,OAAO,CAAG,EAAA,MAAM,CAAG,EAAA,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,iBAAiB,CAAC,CAAA,CAAE;;AAC7D,aAAA,IACH,IAAI,CAAC,cAAc,KAAK,cAAc,CAAC,EAAE;AACzC,YAAA,IAAI,CAAC,cAAc,KAAK,cAAc,CAAC,QAAQ,EACjD;AACE,YAAA,OAAO,CAAG,EAAA,MAAM,CAAG,EAAA,SAAS,EAAE;;QAGlC,OAAO,CAAA,EAAG,MAAM,CAAA,EAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,CAAE;;;AAIxC,IAAA,oBAAoB,CAAC,KAAa,EAAA;QACtC,MAAM,KAAK,GAAG,eAAe;QAC7B,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAC7B,IAAI,iBAAiB,GAAG,CAAC;AACzB,QAAA,OAAO,KAAK,IAAI,IAAI,EAAE;YAClB,iBAAiB,IAAI,CAAC;AACtB,YAAA,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;;AAE7B,QAAA,OAAO,iBAAiB;;AAGrB,IAAA,iBAAiB,CACpB,QAAgB,EAChB,UAAmB,EACnB,UAAmB;;IAEnB,EAA8B,GAAA,MAAK,GAAG,EAAA;AAEtC,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,aAAa;QACnD,IAAI,CAAC,WAAW,EAAE;YACd;;QAGJ,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAC9B,WAAW,CAAC,KAAK,EACjB,IAAI,CAAC,cAAc,EACnB,QAAQ,EACR,UAAU,EACV,UAAU,EACV,EAAE,CACL;AACD,QAAA,IAAI,WAAW,KAAK,IAAI,CAAC,iBAAiB,EAAE,EAAE;YAC1C;;QAEJ,IAAI,CAAC,iBAAiB,EAAE;;IAGrB,SAAS,CAAC,UAAkB,EAAE,cAAsB,EAAA;AACvD,QAAA,OAAO;AACF,aAAA,KAAK,CAAC,cAAc,CAAC,YAAY;AACjC,aAAA,GAAG,CAAC,CAAC,IAAY,EAAE,KAAa,KAAI;YACjC,IACI,IAAI,CAAC,QAAQ;gBACb,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,YAAY,CAAC;AACnE,gBAAA,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,YAAY,CAAC,EAAE,MAAM,EAC7E;AACE,gBAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,YAAY;AACrE,sBAAE,MAAM;;AAEhB,YAAA,OAAO,IAAI;AACf,SAAC;AACA,aAAA,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;;;AAInC,IAAA,cAAc,CAAC,GAAW,EAAA;QAC7B,MAAM,OAAO,GAAa;AACrB,aAAA,KAAK,CAAC,cAAc,CAAC,YAAY;AACjC,aAAA,MAAM,CAAC,CAAC,MAAc,EAAE,CAAS,KAAI;AAClC,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,YAAY;YACtE,QACI,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC;AACvC,iBAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,MAAM,KAAK,QAAQ,CAAC;AAE1E,SAAC,CAAC;QACN,IAAI,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,KAAK,GAAG,EAAE;YACnD,OAAO,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;;AAEpD,QAAA,OAAO,GAAG;;AAGP,IAAA,iBAAiB,CAAC,UAAkB,EAAA;QACvC,IAAI,eAAe,GAAG,EAAE;QACxB,MAAM,aAAa,GACf,CAAC,UAAU;YACP;AACK,iBAAA,KAAK,CAAC,cAAc,CAAC,YAAY;AACjC,iBAAA,GAAG,CAAC,CAAC,UAAkB,EAAE,KAAa,KAAI;AACvC,gBAAA,IACI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAC3B,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,cAAc,CAAC,YAAY,CACvD;AACD,oBAAA,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,CAAC,CAAC,EAC1D;oBACE,eAAe,GAAG,UAAU;AAC5B,oBAAA,OAAO,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;;AAEhC,gBAAA,IAAI,eAAe,CAAC,MAAM,EAAE;oBACxB,MAAM,aAAa,GAAW,eAAe;AAC7C,oBAAA,eAAe,GAAG,cAAc,CAAC,YAAY;AAC7C,oBAAA,OAAO,aAAa;;AAExB,gBAAA,OAAO,UAAU;AACrB,aAAC,CAAC;AACV,YAAA,EAAE;QACN,OAAO,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;;AAG1D;;;;AAIG;AACI,IAAA,cAAc,CAAC,KAAsB,EAAA;AACxC,QAAA,IACI,CAAC,CAAC,KAAK,IAAI,KAAK,KAAK,CAAC;aACrB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC;iBACpD,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;aAClD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC;AACrD,gBAAA,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,EAAE;gBAC/B,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,EAChC;AACE,YAAA,OAAO,MAAM,CAAC,KAAK,CAAC;;QAExB,OAAO,MAAM,CAAC,KAAK;aACd,cAAc,CAAC,UAAU,EAAE;AACxB,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,qBAAqB,EAAE,EAAE;SAC5B;aACA,OAAO,CAAC,CAAI,CAAA,EAAA,cAAc,CAAC,KAAK,CAAG,CAAA,CAAA,EAAE,cAAc,CAAC,KAAK,CAAC;;AAG5D,IAAA,eAAe,CAAC,QAAiB,EAAA;QACpC,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE;AAClD,YAAA,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE;AAChE,gBAAA,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC;;iBAClE;gBACH,OAAO,IAAI,CAAC,mBAAmB;;;AAEhC,aAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YAC3B,IAAI,QAAQ,EAAE;gBACV,IAAI,IAAI,CAAC,cAAc,KAAK,cAAc,CAAC,EAAE,EAAE;AAC3C,oBAAA,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;;gBAErC,IAAI,IAAI,CAAC,cAAc,KAAK,cAAc,CAAC,QAAQ,EAAE;AACjD,oBAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;;;AAG9C,YAAA,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;gBACjE,OAAO,IAAI,CAAC,oBAAoB;;AAEpC,YAAA,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,CAAC;;AAExE,QAAA,OAAO,EAAE;;IAGN,iBAAiB,GAAA;AACpB,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,aAAa;QACnD,IAAI,CAAC,WAAW,EAAE;YACd;;QAEJ,IACI,IAAI,CAAC,eAAe;AACpB,YAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM;AAChE,gBAAA,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,cAAc,CAAC,YAAY;AAC3E,qBAAA,MAAM,EACjB;YACE,IAAI,CAAC,mBAAmB,GAAG,CAAC,OAAO,EAAE,cAAc,CAAC,YAAY,CAAC;YACjE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC,cAAc,CAAC;;;AAI/C,IAAA,IAAW,mBAAmB,CAAC,CAAC,IAAI,EAAE,KAAK,CAA6B,EAAA;QACpE,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACtC;;;QAGJ,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MACnB,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,EAAE,IAAI,EAAE,KAAK,CAAC,CAC5E;;AAGE,IAAA,0BAA0B,CAAC,IAAY,EAAA;QAC1C,MAAM,KAAK,GAAa;AACnB,aAAA,KAAK,CAAC,cAAc,CAAC,YAAY;AACjC,aAAA,MAAM,CAAC,CAAC,IAAY,KAAK,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC9D,OAAO,KAAK,CAAC,MAAM;;AAGhB,IAAA,UAAU,CAAC,UAAkB,EAAA;AAChC,QAAA,OAAO,IAAI,CAAC,WAAW,CACnB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,EAClD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CACvE;;AAGG,IAAA,WAAW,CAAC,QAAgB,EAAA;AAChC,QAAA,IAAI,QAAQ,KAAK,cAAc,CAAC,IAAI,EAAE;AAClC,YAAA,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAA,CAAA,EAAI,IAAI,CAAC,oBAAoB,CAAI,CAAA,EAAA,IAAI,CAAC,oBAAoB,CAAA,CAAA,EAAI,IAAI,CAAC,oBAAoB,EAAE;;QAEhI,MAAM,GAAG,GAAa,EAAE;;AAExB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,YAAY;YACxD,IAAI,CAAC,KAAK,EAAE;gBACR;;AAEJ,YAAA,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AACpB,gBAAA,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;;;AAGvB,QAAA,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE;AACjB,YAAA,OAAO,CAAG,EAAA,IAAI,CAAC,oBAAoB,CAAI,CAAA,EAAA,IAAI,CAAC,oBAAoB,CAAI,CAAA,EAAA,IAAI,CAAC,oBAAoB,EAAE;;AAEnG,QAAA,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE;YACnC,OAAO,CAAA,EAAG,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAA,CAAE;;AAEtE,QAAA,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE;YACnC,OAAO,IAAI,CAAC,oBAAoB;;AAEpC,QAAA,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,IAAI,EAAE,EAAE;AACpC,YAAA,OAAO,EAAE;;AAEb,QAAA,OAAO,EAAE;;AAGL,IAAA,gBAAgB,CAAC,QAAgB,EAAA;AACrC,QAAA,MAAM,GAAG,GACL,CAAG,EAAA,IAAI,CAAC,oBAAoB,CAAA,EAAG,IAAI,CAAC,oBAAoB,CAAA,EAAG,IAAI,CAAC,oBAAoB,CAAE,CAAA;YACtF,CAAI,CAAA,EAAA,IAAI,CAAC,oBAAoB,CAAG,EAAA,IAAI,CAAC,oBAAoB,CAAG,EAAA,IAAI,CAAC,oBAAoB,CAAE,CAAA;YACvF,CAAI,CAAA,EAAA,IAAI,CAAC,oBAAoB,CAAG,EAAA,IAAI,CAAC,oBAAoB,CAAG,EAAA,IAAI,CAAC,oBAAoB,CAAE,CAAA;YACvF,CAAI,CAAA,EAAA,IAAI,CAAC,oBAAoB,CAAA,EAAG,IAAI,CAAC,oBAAoB,EAAE;QAC/D,MAAM,IAAI,GACN,CAAA,EAAG,IAAI,CAAC,oBAAoB,CAAG,EAAA,IAAI,CAAC,oBAAoB,CAAE,CAAA;YAC1D,CAAI,CAAA,EAAA,IAAI,CAAC,oBAAoB,CAAG,EAAA,IAAI,CAAC,oBAAoB,CAAG,EAAA,IAAI,CAAC,oBAAoB,CAAE,CAAA;YACvF,CAAI,CAAA,EAAA,IAAI,CAAC,oBAAoB,CAAG,EAAA,IAAI,CAAC,oBAAoB,CAAG,EAAA,IAAI,CAAC,oBAAoB,CAAE,CAAA;AACvF,YAAA,CAAA,CAAA,EAAI,IAAI,CAAC,oBAAoB,CAAA,EAAG,IAAI,CAAC,oBAAoB,CAAG,EAAA,IAAI,CAAC,oBAAoB,CAAA,EAAG,IAAI,CAAC,oBAAoB,CAAE,CAAA;YACnH,CAAI,CAAA,EAAA,IAAI,CAAC,oBAAoB,CAAA,EAAG,IAAI,CAAC,oBAAoB,EAAE;AAE/D,QAAA,IAAI,QAAQ,KAAK,cAAc,CAAC,IAAI,EAAE;AAClC,YAAA,OAAO,GAAG;;QAEd,MAAM,GAAG,GAAa,EAAE;;AAExB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,YAAY;YACxD,IAAI,CAAC,KAAK,EAAE;gBACR;;AAEJ,YAAA,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AACpB,gBAAA,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;;;AAGvB,QAAA,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE;AACjB,YAAA,OAAO,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC;;AAE5C,QAAA,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE;AACnC,YAAA,OAAO,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;;AAEhD,QAAA,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE;AACnC,YAAA,OAAO,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;;AAEhD,QAAA,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,EAAE,EAAE;AACnC,YAAA,OAAO,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;;AAEhD,QAAA,IAAI,GAAG,CAAC,MAAM,KAAK,EAAE,EAAE;AACnB,YAAA,OAAO,EAAE;;AAEb,QAAA,IAAI,GAAG,CAAC,MAAM,KAAK,EAAE,EAAE;AACnB,YAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,EAAE,EAAE;gBACxB,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC;;YAEtC,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC;;AAEtC,QAAA,IAAI,GAAG,CAAC,MAAM,GAAG,EAAE,IAAI,GAAG,CAAC,MAAM,IAAI,EAAE,EAAE;AACrC,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC;;AAElD,QAAA,OAAO,EAAE;;AAGb;;AAEG;AACK,IAAA,iBAAiB,CAAC,QAAA,GAAiC,IAAI,CAAC,QAAQ,EAAA;AACpE,QAAA,MAAM,YAAY,GAAG,QAAQ,EAAE,aAAa,EAAE,UAAU;AACxD,QAAA,IAAI,CAAC,YAAY,EAAE,aAAa,EAAE;YAC9B,OAAO,QAAQ,CAAC,aAAa;;aAC1B;AACH,YAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC;;;AAInD;;;;;;AAMG;AACK,IAAA,iBAAiB,CAAC,UAAkB,EAAA;AACxC,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC;cACzB,IAAI,CAAC;AACP,cAAE,CAAC,CAAU,KAAK,CAAC;AACvB,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK;AACzB,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;QACxB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE;AAC3C,YAAA,IAAI,CAAC,QAAQ,CACT,iBAAiB,CACb,IAAI,CAAC,SAAS,CACV,IAAI,CAAC,aAAa,CACd,IAAI,CAAC,WAAW,CACZ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,EAClD,IAAI,CAAC,qBAAqB,CAC7B,CACJ,CACJ,CACJ,CACJ;;aACE,IACH,IAAI,CAAC,qBAAqB;AAC1B,aAAC,CAAC,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,CAAC,EAC7D;AACE,YAAA,IAAI,CAAC,QAAQ,CACT,iBAAiB,CACb,IAAI,CAAC,SAAS,CACV,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,CACzE,CACJ,CACJ;;aACE;AACH,YAAA,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;;;AAI5D,IAAA,SAAS,CAAC,KAAyC,EAAA;QACvD,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,KAAK,KAAK,cAAc,CAAC,YAAY,EAAE;AAC9D,YAAA,OAAO,KAAK;;QAEhB,IACI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC;aACvD,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAChD;AACE,YAAA,OAAO,KAAK;;QAGhB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;AACvF,YAAA,OAAO,MAAM,CAAC,KAAK,CAAC;;AAGxB,QAAA,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;AACzB,QAAA,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AAC/E,YAAA,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;AAC3C,YAAA,OAAO,MAAM,CAAC,GAAG,CAAC;;AAGtB,QAAA,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,GAAG;;IAGlC,WAAW,CAAC,KAAa,EAAE,0BAAoC,EAAA;QACnE,IACI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC;YACtD,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,EACpC;AACE,YAAA,OAAO,KAAK;;AAGhB,QAAA,OAAO;AACH,cAAE,KAAK,CAAC,OAAO,CACT,IAAI,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,EACjD,cAAc,CAAC,YAAY;cAE/B,KAAK;;AAGP,IAAA,aAAa,CAAC,KAAa,EAAA;AAC/B,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACd,YAAA,OAAO,KAAK;;QAEhB,OAAO,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,YAAY,CAAC,GAAG,KAAK;;AAG1E,IAAA,aAAa,CAAC,KAAa,EAAA;AAC/B,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACd,YAAA,OAAO,KAAK;;QAEhB,OAAO,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,YAAY,CAAC,GAAG,KAAK;;AAG1E,IAAA,uBAAuB,CAAC,MAAc,EAAA;QAC1C,IAAI,iBAAiB,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB;cAC1D,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,KAAI;gBAChC,OAAQ,IAAI,CAAC,qBAAkC,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC/D,aAAC;AACH,cAAE,IAAI,CAAC,iBAAiB;QAC5B,IACI,CAAC,IAAI,CAAC,uBAAuB;YAC7B,IAAI,CAAC,qBAAqB,EAAE;AAC5B,YAAA,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC;YAC3C,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,EAC1D;AACE,YAAA,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,CACxC,CAAC,IAAI,KAAK,IAAI,KAAK,cAAc,CAAC,WAAW,CAChD;;QAGL,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,iBAA6B,CAAC;;AAG1D,IAAA,gBAAgB,CAAC,0BAAoC,EAAA;QACzD,OAAO,IAAI,MAAM,CACb,0BAA0B,CAAC,GAAG,CAAC,CAAC,IAAY,KAAK,KAAK,IAAI,CAAA,CAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EACvE,IAAI,CACP;;AAGG,IAAA,0BAA0B,CAAC,KAAa,EAAA;QAC5C,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa;cAC1C,IAAI,CAAC;AACP,cAAE,CAAC,IAAI,CAAC,aAAa,CAAC;AAE1B,QAAA,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,cAAc,CAAC,GAAG,CAAC;;AAGrE,IAAA,aAAa,CAAC,MAAc,EAAA;QAC/B,IAAI,eAAe,GAAG,MAAM;AAE5B,QAAA,IAAI,eAAe,KAAK,cAAc,CAAC,YAAY,EAAE;AACjD,YAAA,OAAO,eAAe;;QAG1B,IACI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC;AACtD,YAAA,IAAI,CAAC,aAAa,KAAK,cAAc,CAAC,KAAK,EAC7C;AACE,YAAA,eAAe,GAAG,eAAe,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,cAAc,CAAC,GAAG,CAAC;;QAEvF,MAAM,kBAAkB,GAAkB,IAAI,CAAC,2BAA2B,CACtE,IAAI,CAAC,cAAc,CACtB;QAED,MAAM,cAAc,GAChB,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK;AAC9B,cAAE,IAAI,CAAC,uBAAuB,CAAC,eAAe;AAC9C,cAAE,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC;AAExF,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACrB,YAAA,OAAO,cAAc;;QAEzB,IAAI,kBAAkB,EAAE;AACpB,YAAA,IAAI,eAAe,KAAK,IAAI,CAAC,aAAa,EAAE;AACxC,gBAAA,OAAO,IAAI;;AAEf,YAAA,IAAI,cAAc,CAAC,MAAM,GAAG,EAAE,EAAE;AAC5B,gBAAA,OAAO,MAAM,CAAC,cAAc,CAAC;;YAEjC,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,EAAE,cAAc,CAAC;;aAC7D;AACH,YAAA,OAAO,cAAc;;;IAIrB,qBAAqB,GAAA;AACzB,QAAA,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;;AAE7B,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,cAAc,CAAC,SAAS,CAAC,EAAE;AACrE,gBAAA,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,QAAQ,EAAE;gBAC5D,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO;AAC3C,gBAAA,IACK,aAAa,EAAE,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAa;oBAChE,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,EACpC;AACE,oBAAA,OAAO,IAAI;;;;AAIvB,QAAA,OAAO,KAAK;;;AAGR,IAAA,2BAA2B,CAAC,aAAqB,EAAA;AACrD,QAAA,MAAM,OAAO,GAA4B,aAAa,CAAC,KAAK,CACxD,IAAI,MAAM,CAAC,CAAA,oBAAA,CAAsB,CAAC,CACrC;AACD,QAAA,OAAO,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;;IAGvC,eAAe,CAAC,mBAA2B,EAAE,cAAsB,EAAA;QACtE,MAAM,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC;QACjE,IAAI,KAAK,GAAG,cAAc;AAE1B,QAAA,IACI,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;AACpC,aAAC,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,EACnD;AACE,YAAA,IAAI,IAAI,CAAC,aAAa,KAAK,cAAc,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;gBAC9D,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;;YAEnC,OAAO,IAAI,CAAC;AACR,kBAAE,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC;kBAChD,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;;AAElC,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;;AAG9B,IAAA,qBAAqB,CAAC,OAAe,EAAA;AACxC,QAAA,QACI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC;YACtB;AACK,iBAAA,KAAK,CAAC,cAAc,CAAC,YAAY;iBACjC,MAAM,CAAC,CAAC,KAAa,EAAE,OAAe,EAAE,KAAa,KAAY;AAC9D,gBAAA,IAAI,CAAC,MAAM;AACP,oBAAA,OAAO,KAAK,cAAc,CAAC,mBAAmB,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM;AACxE,gBAAA,IAAI,OAAO,KAAK,cAAc,CAAC,oBAAoB,EAAE;AACjD,oBAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,KAAK,GAAG,OAAO,GAAG,KAAK;;AAEnE,gBAAA,IAAI,CAAC,IAAI,GAAG,KAAK;AACjB,gBAAA,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtE,MAAM,WAAW,GAAW,IAAI,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,IAAI,CACxD,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAC3B;AACD,gBAAA,IACI,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC;oBACxC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,EAC3C;AACE,oBAAA,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AACjD,oBAAA,OAAO,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,mBAAmB;0BACpD,KAAK,GAAG;AACV,0BAAE,OAAO,GAAG,KAAK,GAAG,WAAW;;qBAChC;oBACH,OAAO,KAAK,GAAG,WAAW;;aAEjC,EAAE,EAAE,CAAC;AACd,YAAA,OAAO;;IAIR,0BAA0B,GAAA;AAC7B,QAAA,OAAO,CAAC,GAAG,EAAE,cAAc,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;;uGApzBxC,cAAc,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;2GAAd,cAAc,EAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAD1B;;;ACDD;;AAEG;AACH,SAAS,cAAc,GAAA;AACnB,IAAA,MAAM,UAAU,GAAG,MAAM,CAAiB,cAAc,CAAC;AACzD,IAAA,MAAM,WAAW,GAAG,MAAM,CAA0C,UAAU,CAAC;IAE/E,OAAO,WAAW,YAAY;UACxB,EAAE,GAAG,UAAU,EAAE,GAAG,WAAW,EAAE;UACjC,EAAE,GAAG,UAAU,EAAE,GAAG,WAAW,EAAE;AAC3C;AAEM,SAAU,cAAc,CAAC,WAAqD,EAAA;IAChF,OAAO;AACH,QAAA;AACI,YAAA,OAAO,EAAE,UAAU;AACnB,YAAA,QAAQ,EAAE,WAAW;AACxB,SAAA;AACD,QAAA;AACI,YAAA,OAAO,EAAE,cAAc;AACvB,YAAA,QAAQ,EAAE,aAAa;AAC1B,SAAA;AACD,QAAA;AACI,YAAA,OAAO,EAAE,eAAe;AACxB,YAAA,UAAU,EAAE,cAAc;AAC7B,SAAA;QACD,cAAc;KACjB;AACL;AAEM,SAAU,yBAAyB,CACrC,WAAqD,EAAA;AAErD,IAAA,OAAO,wBAAwB,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;AAChE;;MCNa,gBAAgB,CAAA;AAClB,IAAA,IAAI,GAAG,KAAK,CAA4B,EAAE,CAAC;AAC3C,IAAA,iBAAiB,GAAG,KAAK,CAAqC,EAAE,CAAC;AACjE,IAAA,QAAQ,GAAG,KAAK,CAA4B,EAAE,CAAC;AAC/C,IAAA,MAAM,GAAG,KAAK,CAA0B,EAAE,CAAC;AAC3C,IAAA,MAAM,GAAG,KAAK,CAA0B,EAAE,CAAC;AAC3C,IAAA,iBAAiB,GAAG,KAAK,CAAqC,GAAG,CAAC;AAClE,IAAA,aAAa,GAAG,KAAK,CAAiC,GAAG,CAAC;AAC1D,IAAA,qBAAqB,GAAG,KAAK,CAAgD,IAAI,CAAC;AAClF,IAAA,WAAW,GAAG,KAAK,CAAsC,IAAI,CAAC;AAC9D,IAAA,aAAa,GAAG,KAAK,CAAwC,IAAI,CAAC;AAClE,IAAA,oBAAoB,GAAG,KAAK,CAA+C,IAAI,CAAC;AAChF,IAAA,mBAAmB,GAAG,KAAK,CAA8C,IAAI,CAAC;AAC9E,IAAA,eAAe,GAAG,KAAK,CAA0C,IAAI,CAAC;AACtE,IAAA,UAAU,GAAG,KAAK,CAAqC,IAAI,CAAC;AAC5D,IAAA,cAAc,GAAG,KAAK,CAAyC,EAAE,CAAC;AAClE,IAAA,oBAAoB,GAAG,KAAK,CAA+C,IAAI,CAAC;AAChF,IAAA,gBAAgB,GAAG,KAAK,CAA2C,IAAI,CAAC;AACxE,IAAA,QAAQ,GAAG,KAAK,CAAmC,IAAI,CAAC;AACxD,IAAA,mBAAmB,GAAG,KAAK,CAA8C,IAAI,CAAC;AAC9E,IAAA,GAAG,GAAG,KAAK,CAA8B,IAAI,CAAC;AAC9C,IAAA,gBAAgB,GAAG,KAAK,CAA2C,IAAI,CAAC;AACxE,IAAA,iBAAiB,GAAG,KAAK,CAA4C,IAAI,CAAC;AAC1E,IAAA,sBAAsB,GAAG,KAAK,CAAiD,IAAI,CAAC;AACpF,IAAA,aAAa,GAAG,KAAK,CAAwC,IAAI,CAAC;IAElE,UAAU,GAAG,MAAM,EAAQ;AAE1B,IAAA,UAAU,GAAG,MAAM,CAAS,EAAE,CAAC;AAC/B,IAAA,WAAW,GAAG,MAAM,CAAS,EAAE,CAAC;AAChC,IAAA,SAAS,GAAG,MAAM,CAAgB,IAAI,CAAC;AACvC,IAAA,KAAK,GAAG,MAAM,CAAS,EAAE,CAAC;AAC1B,IAAA,oBAAoB,GAAG,MAAM,CAAW,EAAE,CAAC;AAC3C,IAAA,WAAW,GAAG,MAAM,CAAU,KAAK,CAAC;AACpC,IAAA,UAAU,GAAG,MAAM,CAAU,KAAK,CAAC;;AAEnC,IAAA,YAAY,GAAG,MAAM,CAAU,KAAK,CAAC;IAEtC,YAAY,GAAG,MAAM,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AAE3C,IAAA,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AAElC,IAAA,OAAO,GAAG,MAAM,CAAgB,eAAe,CAAC;;AAGnD,IAAA,QAAQ,GAAG,CAAC,CAAM,KAAI,GAAG;;AAGzB,IAAA,OAAO,GAAG,MAAK,GAAG;AAElB,IAAA,WAAW,CAAC,OAAsB,EAAA;QACrC,MAAM,EACF,IAAI,EACJ,iBAAiB,EACjB,QAAQ,EACR,MAAM,EACN,MAAM,EACN,iBAAiB,EACjB,aAAa,EACb,qBAAqB,EACrB,WAAW,EACX,aAAa,EACb,oBAAoB,EACpB,mBAAmB,EACnB,eAAe,EACf,UAAU,EACV,cAAc,EACd,oBAAoB,EACpB,gBAAgB,EAChB,QAAQ,EACR,mBAAmB,EACnB,GAAG,EACH,gBAAgB,EAChB,iBAAiB,EACjB,sBAAsB,EACtB,aAAa,GAChB,GAAG,OAAO;QACX,IAAI,IAAI,EAAE;AACN,YAAA,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AAC/D,gBAAA,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI;;AAExC,YAAA,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC5E,IAAI,CAAC,oBAAoB,CAAC,GAAG,CACzB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAS,EAAE,CAAS,KAAI;AACrE,oBAAA,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM;iBAC7B,CAAC,CACL;gBACD,IAAI,CAAC,QAAQ,EAAE;;iBACZ;AACH,gBAAA,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,EAAE,CAAC;AACjC,gBAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,IAAI,cAAc,CAAC,YAAY,CAAC;gBACrE,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,EAAE;;;QAG5D,IAAI,iBAAiB,EAAE;AACnB,YAAA,IAAI,CAAC,iBAAiB,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE;gBACnF;;iBACG;gBACH,IAAI,CAAC,YAAY,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,YAAY,IAAI,EAAE;;;QAGlF,IAAI,oBAAoB,EAAE;YACtB,IAAI,CAAC,YAAY,CAAC,oBAAoB,GAAG,oBAAoB,CAAC,YAAY;AAC1E,YAAA,IAAI,IAAI,CAAC,YAAY,CAAC,oBAAoB,EAAE;gBACxC,IAAI,CAAC,YAAY,CAAC,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,MAAM,CAC5E,CAAC,CAAS,KAAK,CAAC,KAAK,cAAc,CAAC,KAAK,CAC5C;;;;AAIT,QAAA,IAAI,QAAQ,IAAI,QAAQ,CAAC,YAAY,EAAE;YACnC,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,QAAQ,CAAC,YAAY;;AAEtD,QAAA,IAAI,GAAG,IAAI,GAAG,CAAC,YAAY,EAAE;YACzB,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,GAAG,CAAC,YAAY;;QAG5C,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,aAAa,CAAC,YAAY;;QAEhE,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC,YAAY;;QAElD,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC,YAAY;;QAElD,IAAI,iBAAiB,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,YAAY;YACpE,IAAI,iBAAiB,CAAC,aAAa,IAAI,iBAAiB,CAAC,YAAY,EAAE;AACnE,gBAAA,MAAM,qBAAqB,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa;gBAE7D,IAAI,iBAAiB,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE;oBACpE,IAAI,CAAC,YAAY,CAAC,aAAa;AAC3B,wBAAA,iBAAiB,CAAC,YAAY,KAAK,cAAc,CAAC;8BAC5C,cAAc,CAAC;AACjB,8BAAE,cAAc,CAAC,KAAK;;gBAElC,IAAI,IAAI,CAAC,YAAY,CAAC,qBAAqB,KAAK,IAAI,EAAE;oBAClD,IAAI,CAAC,YAAY,CAAC,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB;;gBAExE,IACI,OAAO,qBAAqB,KAAK,QAAQ;oBACzC,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,KAAK,QAAQ,EACrD;oBACE,IAAI,CAAC,WAAW,CAAC,GAAG,CAChB,IAAI,CAAC,WAAW;AACX,yBAAA,KAAK,CAAC,iBAAiB,CAAC,aAAa;yBACrC,IAAI,CAAC,EAAE;yBACP,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CACvE;oBACD,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE;;AAEtD,gBAAA,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI;;;QAG7C,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,aAAa,CAAC,YAAY;;QAEhE,IAAI,qBAAqB,EAAE;YACvB,IAAI,CAAC,YAAY,CAAC,qBAAqB,GAAG,qBAAqB,CAAC,YAAY;;QAEhF,IAAI,WAAW,EAAE;YACb,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,WAAW,CAAC,YAAY;AACxD,YAAA,IAAI,WAAW,CAAC,aAAa,KAAK,IAAI,IAAI,WAAW,CAAC,YAAY,KAAK,KAAK,EAAE;gBAC1E,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC;;;QAG3D,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,aAAa,CAAC,YAAY;AAC5D,YAAA,IACI,aAAa,CAAC,aAAa,KAAK,KAAK;gBACrC,aAAa,CAAC,YAAY,KAAK,IAAI;AACnC,gBAAA,IAAI,CAAC,UAAU,EAAE,EACnB;gBACE,qBAAqB,CAAC,MAAK;oBACvB,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,aAAa,CAAC,KAAK,EAAE;AACxD,iBAAC,CAAC;;;QAGV,IAAI,oBAAoB,EAAE;YACtB,IAAI,CAAC,YAAY,CAAC,oBAAoB,GAAG,oBAAoB,CAAC,YAAY;;QAE9E,IAAI,mBAAmB,EAAE;YACrB,IAAI,CAAC,YAAY,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,YAAY;;QAE5E,IAAI,eAAe,EAAE;YACjB,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,eAAe,CAAC,YAAY;;QAEpE,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,UAAU,CAAC,YAAY;;QAE1D,IAAI,cAAc,EAAE;YAChB,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,cAAc,CAAC,YAAY;;QAElE,IAAI,gBAAgB,EAAE;YAClB,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,YAAY;;QAEtE,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,QAAQ,CAAC,YAAY;;QAEtD,IAAI,mBAAmB,EAAE;YACrB,IAAI,CAAC,YAAY,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,YAAY;;QAE5E,IAAI,gBAAgB,EAAE;YAClB,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,YAAY;;QAEtE,IAAI,iBAAiB,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,YAAY;;QAExE,IAAI,sBAAsB,EAAE;YACxB,IAAI,CAAC,YAAY,CAAC,sBAAsB,GAAG,sBAAsB,CAAC,YAAY;;QAElF,IAAI,CAAC,UAAU,EAAE;;IAGd,QAAQ,CAAC,EAAE,KAAK,EAAe,EAAA;AAClC,QAAA,MAAM,cAAc,GAAW,OAAO,KAAK,KAAK,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK;AAChF,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,EAAE;QAEnC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,IAAI,CAAC,SAAS,EAAE;AAC7C,YAAA,OAAO,IAAI;;AAEf,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;AAC3B,YAAA,OAAO,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC;;AAEtD,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;AAChC,YAAA,OAAO,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC;;QAEtD,IAAI,SAAS,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;AAChD,YAAA,OAAO,IAAI;;AAEf,QAAA,IAAI,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;AACvC,YAAA,OAAO,IAAI;;AAEf,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE;AACnC,YAAA,OAAO,IAAI;;AAEf,QAAA,IAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;AAC/B,YAAA,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;;AAE7C,QAAA,IAAI,SAAS,KAAK,cAAc,CAAC,UAAU,EAAE;YACzC,MAAM,YAAY,GAAG,sBAAsB;YAE3C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,cAAc,EAAE;AACtD,gBAAA,OAAO,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC;;iBAC/C;AACH,gBAAA,OAAO,IAAI;;;QAGnB,IAAI,cAAc,IAAI,cAAc,CAAC,MAAM,IAAI,CAAC,EAAE;YAC9C,IAAI,YAAY,GAAG,CAAC;AAEpB,YAAA,IACI,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAC;gBACtD,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAC,EACzD;gBACE,MAAM,yBAAyB,GAAG,SAAS,CAAC,KAAK,CAC7C,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,mBAAmB,CAAC,GAAG,CAAC,EACzD,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,oBAAoB,CAAC,CACzD;AAED,gBAAA,OAAO,yBAAyB,KAAK,MAAM,CAAC,cAAc,CAAC,MAAM;AAC7D,sBAAE;AACF,sBAAE,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC;;YAErD,IAAI,SAAS,CAAC,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;AAC9C,gBAAA,OAAO,IAAI;;YAEf,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;gBAC1C,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,QAAQ,EAAE;AAC3C,oBAAA,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;wBACvD,MAAM,GAAG,GAAW;AACf,6BAAA,KAAK,CAAC,cAAc,CAAC,YAAY;6BACjC,MAAM,CAAC,CAAC,CAAS,KAAK,CAAC,KAAK,GAAG;AAC/B,6BAAA,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;AACtC,wBAAA,YAAY,IAAI,GAAG,CAAC,MAAM;;yBACvB,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;AACtC,wBAAA,YAAY,EAAE;;oBAElB,IACI,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wBAC7B,cAAc,CAAC,MAAM,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,EACjD;AACE,wBAAA,OAAO,IAAI;;AAEf,oBAAA,IAAI,YAAY,KAAK,SAAS,CAAC,MAAM,EAAE;AACnC,wBAAA,OAAO,IAAI;;;;YAIvB,IACI,CAAC,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC;gBAC9C,cAAc,CAAC,MAAM,GAAG,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC;iBACxE,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC;AAClD,oBAAA,cAAc,CAAC,MAAM,GAAG,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,EAChF;AACE,gBAAA,OAAO,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC;;YAEtD,IACI,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACpD,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAC1D;gBACE,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC;AAClC,gBAAA,MAAM,MAAM,GAAW,IAAI,CAAC,YAAY,CAAC;sBACnC,SAAS,CAAC,MAAM;AAChB,wBAAA,IAAI,CAAC,YAAY,CAAC,0BAA0B,CAAC,SAAS,CAAC;wBACvD;AACF,sBAAE,IAAI,CAAC,MAAM;AACX,0BAAE,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,GAAG;AAC5C,0BAAE,SAAS,CAAC,MAAM,GAAG,YAAY;AAEvC,gBAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AACpB,oBAAA,IAAI,cAAc,CAAC,MAAM,GAAG,MAAM,EAAE;AAChC,wBAAA,OAAO,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC;;;AAG1D,gBAAA,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;oBAClB,MAAM,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAC9C,oBAAA,IACI,cAAc;wBACd,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAW,CAAC;AACzE,wBAAA,MAAM,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AACxD,wBAAA,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAC/B;wBACE,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AAC9C,wBAAA,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,cAAc,CAAC,MAAM,GAAG;AAClE,8BAAE;AACF,8BAAE,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC;;yBAC9C,IACH,CAAC,CAAC,cAAc;AACZ,wBAAA,CAAC,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,QAAQ,CACzC,cAAc,CAAC,CAAC,CAAW,CAC9B;AACD,wBAAA,CAAC,cAAc;AACf,wBAAA,IAAI,CAAC,YAAY,CAAC,qBAAqB;AAC3C,wBAAA,cAAc,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,EACrC;AACE,wBAAA,OAAO,IAAI;;yBACR;AACH,wBAAA,OAAO,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC;;;;YAI9D,IACI,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC;gBACnD,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,EACzD;AACE,gBAAA,OAAO,IAAI;;;QAGnB,IAAI,KAAK,EAAE;AACP,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;AACtB,YAAA,OAAO,IAAI;;AAEf,QAAA,OAAO,IAAI;;IAIR,OAAO,GAAA;AACV,QAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC;;IAGY,OAAO,GAAA;AAC7C,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC;;AAItB,IAAA,aAAa,CAAC,KAAyC,EAAA;;AAE1D,QAAA,IACI,CAAC,KAAK,KAAK,cAAc,CAAC,YAAY;AAClC,YAAA,KAAK,KAAK,IAAI;YACd,OAAO,KAAK,KAAK,WAAW;AAChC,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAC/B;AACE,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAC5D,cAAc,CAAC,YAAY,CAC9B;;;AAKF,IAAA,OAAO,CAAC,CAAsB,EAAA;;AAEjC,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;YACrB;;AAEJ,QAAA,MAAM,EAAE,GAAqB,CAAC,CAAC,MAA0B;AAEzD,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC;cACrC,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,EAAE,CAAC,KAAK;AAC7C,cAAE,EAAE,CAAC,KAAK;AAEd,QAAA,IAAI,EAAE,CAAC,IAAI,KAAK,QAAQ,EAAE;YACtB,IAAI,OAAO,gBAAgB,KAAK,QAAQ,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE;AAC9E,gBAAA,EAAE,CAAC,KAAK,GAAG,gBAAgB,CAAC,QAAQ,EAAE;gBAEtC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC;gBAC9B,IAAI,CAAC,QAAQ,EAAE;AAEf,gBAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;AACpB,oBAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;oBACvB;;AAGJ,gBAAA,IAAI,QAAQ,GACR,EAAE,CAAC,cAAc,KAAK;sBACf,EAAE,CAAC,cAAyB,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;AAC3D,sBAAG,EAAE,CAAC,cAAyB;gBAEvC,IACI,IAAI,CAAC,aAAa,EAAE;oBACpB,IAAI,CAAC,sBAAsB,EAAE;oBAC7B,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,EACrD;AACE,oBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;AAC5B,oBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;AAC5B,oBAAA,MAAM,WAAW,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,CAAC;AAC1D,oBAAA,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM;oBAClC,MAAM,YAAY,GAAY,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAC5D,WAAW,EACX,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,GAAG,CAAC,GAAG,YAAY,CAAC;wBACzD,cAAc,CAAC,YAAY,CAClC;oBAED,MAAM,qBAAqB,GAAY,IAAI,CAAC,YAAY,CAAC,gBAAgB,CACrE,WAAW,EACX,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,GAAG,CAAC,GAAG,YAAY,CAAC;wBACzD,cAAc,CAAC,YAAY,CAClC;AACD,oBAAA,MAAM,oBAAoB,GACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY,CAAC,MAAM;AAC3D,oBAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,YAAY;AAClE,oBAAA,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,YAAY;oBAE9D,MAAM,iBAAiB,GACnB,IAAI,CAAC,KAAK,EAAE,KAAK,cAAc,CAAC,SAAS;AACzC,wBAAA,IAAI,CAAC,KAAK,EAAE,KAAK,cAAc,CAAC,MAAM;oBAE1C,IAAI,iBAAiB,EAAE;wBACnB,IAAI,CAAC,oBAAoB,EAAE;4BACvB,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,KAAK,YAAY,EAAE;AAC7C,gCAAA,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,GAAG,MAAM,CAAA,EAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;;AACrI,iCAAA,IACH,IAAI,CAAC,YAAY,CAAC,QAAQ;gCAC1B,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,GAAG,YAAY,EACrD;gCACE,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAG,EAAA,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA,CAAE;;iCAC5G;gCACH,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,CAAG,EAAA,MAAM,CAAG,EAAA,IAAI,CAAC,WAAW;qCACvD,KAAK,CAAC,MAAM;qCACZ,IAAI,CAAC,EAAE;qCACP,KAAK,CACF,CAAC,EACD,QAAQ,CACX,CAAG,EAAA,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA,EAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAC/F,MAAM,GAAG,YAAY,EACrB,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,GAAG,YAAY,CACtD,CAAG,EAAA,MAAM,EAAE;;;6BAEb,IACH,CAAC,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,QAAQ,CACzC,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,CAClC,QAAQ,GAAG,YAAY,EACvB,QAAQ,GAAG,CAAC,GAAG,YAAY,CAC9B,CACJ;AACD,4BAAA,oBAAoB,EACtB;AACE,4BAAA,IAAI,QAAQ,KAAK,CAAC,IAAI,MAAM,EAAE;AAC1B,gCAAA,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,GAAG,MAAM,CAAA,EAAG,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAG,EAAA,EAAE,CAAC;qCACnF,KAAK,CAAC,MAAM;qCACZ,IAAI,CAAC,EAAE;qCACP,KAAK,CAAC,MAAM;AACZ,qCAAA,IAAI,CAAC,EAAE,CAAC,CAAG,EAAA,MAAM,EAAE;AAExB,gCAAA,QAAQ,GAAG,QAAQ,GAAG,CAAC;;iCACpB;AACH,gCAAA,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC;gCAC7C,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC;AAC1C,gCAAA,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,GAAG,KAAK,CAAA,EAAG,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAG,EAAA,KAAK,EAAE;;;AAGnG,wBAAA,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,KAAK,cAAc,CAAC,MAAM,GAAG,QAAQ,GAAG,CAAC,GAAG,QAAQ;;oBAE/E,IAAI,CAAC,iBAAiB,EAAE;wBACpB,IAAI,CAAC,YAAY,IAAI,CAAC,qBAAqB,IAAI,oBAAoB,EAAE;4BACjE,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC;;6BACrC,IACH,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,QAAQ,CACxC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,CACzC;4BACD,qBAAqB;4BACrB,CAAC,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,QAAQ,CACzC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,CAC7C,EACH;4BACE,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,CAAG,EAAA,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAA,EAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,GAAG,WAAW,CAAA,EAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAA,CAAE;AAC1J,4BAAA,QAAQ,GAAG,QAAQ,GAAG,CAAC;;6BACpB,IAAI,YAAY,EAAE;AACrB,4BAAA,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,EAAE;AACzC,gCAAA,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,CAAA,EAAG,MAAM,CAAA,EAAG,WAAW,CAAA,EAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CACzF,CAAC,EACD,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,CACvC,CAAG,EAAA,MAAM,EAAE;;iCACT;gCACH,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,CAAG,EAAA,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,GAAG,WAAW,CAAA,EAAG,EAAE,CAAC;AACjF,qCAAA,KAAK,CAAC,QAAQ,GAAG,CAAC;qCAClB,KAAK,CAAC,MAAM;AACZ,qCAAA,IAAI,CAAC,EAAE,CAAC,CAAG,EAAA,MAAM,EAAE;;;AAEzB,6BAAA,IACH,MAAM;AACN,4BAAA,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC;4BACrB,QAAQ,GAAG,YAAY,KAAK,CAAC;4BAC7B,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAC9B,EAAE,CAAC,KAAK,EACR,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,GAAG,CAAC,GAAG,YAAY,CAAC;AACzD,gCAAA,cAAc,CAAC,YAAY,CAClC,EACH;AACE,4BAAA,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,CAAG,EAAA,MAAM,CAAG,EAAA,EAAE,CAAC,KAAK,CAAG,EAAA,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CACtF,CAAC,EACD,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,CACvC,CAAG,EAAA,MAAM,EAAE;;;;gBAKxB,IAAI,UAAU,GAAG,CAAC;gBAClB,IAAI,cAAc,GAAG,KAAK;AAC1B,gBAAA,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,cAAc,CAAC,MAAM,IAAI,cAAc,CAAC,SAAS,EAAE;AACpE,oBAAA,IAAI,CAAC,YAAY,CAAC,uBAAuB,GAAG,IAAI;;AAEpD,gBAAA,IACI,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC;AACxE,oBAAA,IAAI,CAAC,KAAK,EAAE,KAAK,cAAc,CAAC,SAAS;AACzC,oBAAA,IAAI,CAAC,YAAY,CAAC,cAAc,KAAK,cAAc,CAAC,iBAAiB;oBACrE,QAAQ,GAAG,EAAE,EACf;AACE,oBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,CAAC;AACpE,oBAAA,EAAE,CAAC,KAAK;wBACJ,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC;4BACzC,WAAW;4BACX,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;;gBAE9C,IACI,IAAI,CAAC,YAAY,CAAC,cAAc,KAAK,cAAc,CAAC,iBAAiB;AACrE,oBAAA,IAAI,CAAC,gBAAgB,EAAE,EACzB;oBACE,IACI,CAAC,QAAQ,GAAG,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE;yBAC9D,QAAQ,KAAK,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EACvD;AACE,wBAAA,QAAQ,GAAG,QAAQ,GAAG,CAAC;;;gBAG/B,IACI,IAAI,CAAC,YAAY,CAAC,cAAc,KAAK,cAAc,CAAC,qBAAqB;AACzE,oBAAA,IAAI,CAAC,GAAG,EAAE,EACZ;oBACE,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,cAAc,CAAC,WAAW,EAAE;AAC3E,wBAAA,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC;;AAExE,oBAAA,EAAE,CAAC,KAAK;AACJ,wBAAA,EAAE,CAAC,KAAK,KAAK,cAAc,CAAC;8BACtB,cAAc,CAAC;AACjB,8BAAE,EAAE,CAAC,KAAK;;AAGtB,gBAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAC/B,QAAQ,EACR,IAAI,CAAC,WAAW,EAAE,EAClB,IAAI,CAAC,KAAK,EAAE,KAAK,cAAc,CAAC,SAAS;AACrC,oBAAA,IAAI,CAAC,KAAK,EAAE,KAAK,cAAc,CAAC,MAAM,EAC1C,CAAC,KAAa,EAAE,eAAwB,KAAI;AACxC,oBAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC3B,UAAU,GAAG,KAAK;oBAClB,cAAc,GAAG,eAAe;AACpC,iBAAC,CACJ;;AAED,gBAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,KAAK,EAAE,EAAE;oBACjC;;AAGJ,gBAAA,IAAI,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE;AACnC,oBAAA,QAAQ,GAAG,QAAQ,GAAG,CAAC;AACvB,oBAAA,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,KAAK;;;AAG7C,gBAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE;oBACpC,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,cAAc,CAAC,SAAS,EAAE;wBAC3C,MAAM,oBAAoB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,QAAQ,CAC1D,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,CAAC,CAC9D;AACD,wBAAA,MAAM,sBAAsB,GACxB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,MAAM;AACxD,4BAAA,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,EAAE,MAAM;wBAE1E,MAAM,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,QAAQ,CACzD,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,CAC9D;AACD,wBAAA,IAAI,sBAAsB,IAAI,CAAC,mBAAmB,EAAE;AAChD,4BAAA,QAAQ,GAAI,EAAE,CAAC,cAAyB,GAAG,CAAC;;6BACzC;AACH,4BAAA,QAAQ,GAAG,oBAAoB,GAAG,QAAQ,GAAG,CAAC,GAAG,QAAQ;;;yBAE1D;wBACH,QAAQ;4BACJ,EAAE,CAAC,cAAc,KAAK;kCACf,EAAE,CAAC,cAAyB,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;AAC3D,kCAAG,EAAE,CAAC,cAAyB;;;AAG/C,gBAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CACd,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,KAAK;AACpD,sBAAE;AACF,sBAAE,IAAI,CAAC,SAAS,EAAE,CACzB;AACD,gBAAA,IAAI,eAAe,GAAW,IAAI,CAAC,SAAS;sBACtC,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,GAAG,QAAQ,GAAG;AACzC,sBAAE,QAAQ;yBACP,IAAI,CAAC,KAAK,EAAE,KAAK,cAAc,CAAC,SAAS,IAAI,CAAC;AAC3C,8BAAE;8BACA,UAAU,CAAC;AACvB,gBAAA,IAAI,eAAe,GAAG,IAAI,CAAC,qBAAqB,EAAE,EAAE;oBAChD,eAAe;AACX,wBAAA,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,YAAY,CAAC,aAAa,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK;AAChE,8BAAE,IAAI,CAAC,qBAAqB,EAAE,GAAG;AACjC,8BAAE,IAAI,CAAC,qBAAqB,EAAE;;AAE1C,gBAAA,IAAI,eAAe,GAAG,CAAC,EAAE;oBACrB,eAAe,GAAG,CAAC;;AAEvB,gBAAA,EAAE,CAAC,iBAAiB,CAAC,eAAe,EAAE,eAAe,CAAC;AACtD,gBAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;;iBACrB;;gBAEH,OAAO,CAAC,IAAI,CACR,oEAAoE,EACpE,OAAO,gBAAgB,CAC1B;;;aAEF;AACH,YAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;AACpB,gBAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;gBACvB;;AAEJ,YAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAC/B,EAAE,CAAC,KAAK,CAAC,MAAM,EACf,IAAI,CAAC,WAAW,EAAE,EAClB,IAAI,CAAC,KAAK,EAAE,KAAK,cAAc,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,cAAc,CAAC,MAAM,CACtF;;;;IAMF,kBAAkB,GAAA;AACrB,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC;;;AAKxB,IAAA,gBAAgB,CAAC,CAAsB,EAAA;AAC1C,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC;AAC5B,QAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;;AAIZ,IAAA,MAAM,CAAC,CAAsB,EAAA;AAChC,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;AACnB,YAAA,MAAM,EAAE,GAAqB,CAAC,CAAC,MAA0B;AACzD,YAAA,IACI,IAAI,CAAC,YAAY,CAAC,QAAQ;AAC1B,gBAAA,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;gBACnB,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,KAAK,QAAQ,EACrD;AACE,gBAAA,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc;AACvD,gBAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,aAAuB;AAC/D,gBAAA,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM;gBACvC,MAAM,SAAS,GAAG,MAAM,CACpB,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,CAClC,cAAc,CAAC,MAAM,GAAG,CAAC,EACzB,cAAc,CAAC,MAAM,CACxB,CACJ;AAED,gBAAA,IAAI,SAAS,GAAG,CAAC,EAAE;oBACf,EAAE,CAAC,KAAK,GAAG,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK;AAC9D,oBAAA,MAAM,WAAW,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAW;oBAE9D,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa;0BACpC,EAAE,CAAC,KAAK;4BACR,cAAc,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC;4BACjE;0BACA,EAAE,CAAC,KAAK;4BACR,aAAa;AACb,4BAAA,cAAc,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC;AAC5C,4BAAA,MAAM;oBACZ,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,EAAE,CAAC,KAAK;;;AAGhD,YAAA,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE;;AAEzC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC;QAC1B,IAAI,CAAC,OAAO,EAAE;;AAIX,IAAA,OAAO,CAAC,CAAmC,EAAA;AAC9C,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;YACpB;;AAGJ,QAAA,MAAM,EAAE,GAAqB,CAAC,CAAC,MAA0B;QACzD,MAAM,QAAQ,GAAG,CAAC;QAClB,MAAM,MAAM,GAAG,CAAC;QAEhB,IACI,EAAE,KAAK,IAAI;YACX,EAAE,CAAC,cAAc,KAAK,IAAI;AAC1B,YAAA,EAAE,CAAC,cAAc,KAAK,EAAE,CAAC,YAAY;YACrC,EAAE,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM;AAClD,YAAA,CAAS,CAAC,OAAO,KAAK,EAAE,EAC3B;AACE,YAAA,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE;;gBAEnE,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE;gBACnE,IACI,EAAE,CAAC,iBAAiB;AACpB,oBAAA,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,KAAK,EAAE,CAAC,KAAK,EACvE;;oBAEE,EAAE,CAAC,KAAK,EAAE;AACV,oBAAA,EAAE,CAAC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC;;qBACnC;;AAEH,oBAAA,IAAI,EAAE,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,EAAE;;AAE1D,wBAAA,EAAE,CAAC,iBAAiB,CAChB,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,EACpC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,CACvC;;;;;QAKjB,MAAM,SAAS,GACX,EAAE;aACD,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,YAAY,CAAC;kBAC1B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;AAC/C,kBAAE,EAAE,CAAC,KAAK,CAAC;;QAGnB,IAAI,EAAE,IAAI,EAAE,CAAC,KAAK,KAAK,SAAS,EAAE;AAC9B,YAAA,EAAE,CAAC,KAAK,GAAG,SAAS;;;AAGxB,QAAA,IACI,EAAE;YACF,EAAE,CAAC,IAAI,KAAK,QAAQ;AACpB,YAAA,CAAE,EAAE,CAAC,cAAyB,IAAK,EAAE,CAAC,YAAuB;AACzD,gBAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EACrC;YACE,MAAM,2BAA2B,GAC7B,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,CAClC,IAAI,MAAM,CACN,KAAK,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAK,EAAA,EAAA,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAC7E,CACJ,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC;AAEtB,YAAA,EAAE,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,GAAG,2BAA2B;YACjF;;;QAGJ,IAAI,EAAE,IAAK,EAAE,CAAC,YAAuB,GAAG,IAAI,CAAC,qBAAqB,EAAE,EAAE;AAClE,YAAA,EAAE,CAAC,YAAY,GAAG,IAAI,CAAC,qBAAqB,EAAE;;;AAK/C,IAAA,SAAS,CAAC,CAAsB,EAAA;AACnC,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;YACpB;;AAGJ,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;;AAErB,YAAA,IAAI,CAAC,CAAC,GAAG,KAAK,OAAO,EAAE;AACnB,gBAAA,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;;YAE5B;;QAGJ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;AACvC,QAAA,MAAM,EAAE,GAAqB,CAAC,CAAC,MAA0B;QACzD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC;QAC9B,IAAI,CAAC,QAAQ,EAAE;AAEf,QAAA,IAAI,EAAE,CAAC,IAAI,KAAK,QAAQ,EAAE;YACtB,IAAI,CAAC,CAAC,GAAG,KAAK,cAAc,CAAC,QAAQ,EAAE;gBACnC,CAAC,CAAC,cAAc,EAAE;;AAEtB,YAAA,IACI,CAAC,CAAC,GAAG,KAAK,cAAc,CAAC,UAAU;AACnC,gBAAA,CAAC,CAAC,GAAG,KAAK,cAAc,CAAC,SAAS;AAClC,gBAAA,CAAC,CAAC,GAAG,KAAK,cAAc,CAAC,MAAM,EACjC;AACE,gBAAA,IAAI,CAAC,CAAC,GAAG,KAAK,cAAc,CAAC,SAAS,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AAC7D,oBAAA,EAAE,CAAC,cAAc,GAAG,EAAE,CAAC,YAAY;;AAEvC,gBAAA,IAAI,CAAC,CAAC,GAAG,KAAK,cAAc,CAAC,SAAS,IAAK,EAAE,CAAC,cAAyB,KAAK,CAAC,EAAE;oBAC3E,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM;;AAEzC,oBAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC/C,0BAAE,IAAI,CAAC,iBAAiB;AACxB,0BAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;oBAEpC,IAAI,YAAY,GAAG,CAAC,IAAK,EAAE,CAAC,cAAyB,IAAI,YAAY,EAAE;wBACnE,EAAE,CAAC,iBAAiB,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC;;yBAChD;wBACH,IACI,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,KAAM,EAAE,CAAC,cAAyB;AAC1D,4BAAA,EAAE,CAAC,cAAyB,KAAK,CAAC,EACrC;AACE,4BAAA,OACI,iBAAiB,CAAC,QAAQ,CACtB,CACI,IAAI,CAAC,WAAW,EAAE,CAAE,EAAE,CAAC,cAAyB,GAAG,CAAC,CAAC;AACrD,gCAAA,cAAc,CAAC,YAAY,EAC7B,QAAQ,EAAE,CACf;iCACA,CAAC,YAAY,IAAI,CAAC;AACd,oCAAA,EAAE,CAAC,cAAyB,GAAG,YAAY;AAC5C,oCAAA,YAAY,KAAK,CAAC,CAAC,EACzB;AACE,gCAAA,EAAE,CAAC,iBAAiB,CACf,EAAE,CAAC,cAAyB,GAAG,CAAC,EACjC,EAAE,CAAC,YAAY,CAClB;;;;;AAKjB,gBAAA,IAAI,CAAC,wBAAwB,CAAC,EAAE,CAAC;AACjC,gBAAA,IACI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM;oBAC9B,EAAE,CAAC,cAAyB,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM;oBAC/D,EAAE,CAAC,YAAuB,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAChE;oBACE,CAAC,CAAC,cAAc,EAAE;;AAEtB,gBAAA,MAAM,WAAW,GAAkB,EAAE,CAAC,cAAc;AACpD,gBAAA,IACI,CAAC,CAAC,GAAG,KAAK,cAAc,CAAC,SAAS;oBAClC,CAAC,EAAE,CAAC,QAAQ;AACZ,oBAAA,WAAW,KAAK,CAAC;AACjB,oBAAA,EAAE,CAAC,YAAY,KAAK,EAAE,CAAC,KAAK,CAAC,MAAM;AACnC,oBAAA,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EACvB;oBACE,IAAI,CAAC,SAAS,CAAC,GAAG,CACd,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CACjE;oBACD,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,IAAI,CAAC,YAAY,CAAC,MAAM,EACxB,IAAI,CAAC,YAAY,CAAC,cAAc,EAChC,IAAI,CAAC,SAAS,EAAY,CAC7B;;;AAGT,YAAA,IACI,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE;AACf,gBAAA,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,GAAG,CAAC;AACxB,gBAAA,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,GAAI,EAAE,CAAC,cAAyB,EAClF;gBACE,EAAE,CAAC,iBAAiB,CAChB,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,EAChD,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAC5B;;iBACE,IACH,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,OAAO;iBAC9B,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,OAAO,CAAC;cAClC;gBACE,EAAE,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBACrD,CAAC,CAAC,cAAc,EAAE;;YAEtB,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE,CAAC,cAAc;YAC9C,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE,CAAC,YAAY;;;;IAK3C,MAAM,UAAU,CAAC,YAAqB,EAAA;QACzC,IAAI,KAAK,GAAG,YAAY;AACxB,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB;AAC3D,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,OAAO,IAAI,KAAK,EAAE;AACjE,YAAA,IAAI,SAAS,IAAI,KAAK,EAAE;gBACpB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;;AAGjD,YAAA,KAAK,GAAG,KAAK,CAAC,KAAK;;AAEvB,QAAA,IAAI,KAAK,KAAK,IAAI,EAAE;AAChB,YAAA,KAAK,GAAG,gBAAgB,GAAG,gBAAgB,CAAC,KAAK,CAAC,GAAG,KAAK;;QAE9D,IACI,OAAO,KAAK,KAAK,QAAQ;YACzB,OAAO,KAAK,KAAK,QAAQ;AACzB,YAAA,KAAK,KAAK,IAAI;AACd,YAAA,OAAO,KAAK,KAAK,WAAW,EAC9B;AACE,YAAA,IAAI,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,EAAE,EAAE;AAChE,gBAAA,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,EAAE;AACnC,gBAAA,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,EAAE;;YAGxC,IAAI,UAAU,GAAuC,KAAK;YAC1D,IACI,OAAO,UAAU,KAAK,QAAQ;gBAC9B,IAAI,CAAC,UAAU,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,EACxD;AACE,gBAAA,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;gBAC/B,MAAM,mBAAmB,GAAG,IAAI,CAAC,YAAY,CAAC,0BAA0B,EAAE;AAC1E,gBAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE;oBACjD,UAAU;AACN,wBAAA,IAAI,CAAC,YAAY,CAAC,aAAa,KAAK;AAChC,8BAAE,UAAU,CAAC,OAAO,CACd,mBAAmB,EACnB,IAAI,CAAC,YAAY,CAAC,aAAa;8BAEnC,UAAU;;AAGxB,gBAAA,IACI,IAAI,CAAC,YAAY,CAAC,QAAQ;oBAC1B,UAAU;oBACV,IAAI,CAAC,IAAI,EAAE;AACX,oBAAA,IAAI,CAAC,qBAAqB,EAAE,KAAK,KAAK,EACxC;AACE,oBAAA,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAC1C,IAAI,CAAC,YAAY,CAAC,cAAc,EAChC,UAAoB,CACvB;;gBAGL,IACI,IAAI,CAAC,YAAY,CAAC,aAAa,KAAK,cAAc,CAAC,KAAK;qBACvD,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;wBAC3C,IAAI,CAAC,YAAY,CAAC,iBAAiB,KAAK,cAAc,CAAC,GAAG,CAAC,EACjE;AACE,oBAAA,UAAU,GAAG;AACR,yBAAA,QAAQ;yBACR,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,cAAc,CAAC,KAAK,CAAC;;AAE1D,gBAAA,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;oBACtE,qBAAqB,CAAC,MAAK;AACvB,wBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,EAC5B,IAAI,CAAC,YAAY,CAAC,cAAc,CACnC;AACL,qBAAC,CAAC;;AAEN,gBAAA,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI;;AAG1C,YAAA,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;gBAClF,UAAU,GAAG,EAAE;;AAGnB,YAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC;YAChC,IAAI,CAAC,QAAQ,EAAE;YAEf,IACI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc;AAC/C,iBAAC,IAAI,CAAC,YAAY,CAAC,cAAc;AAC7B,qBAAC,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,EACpE;;AAEE,gBAAA,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI;AAErC,gBAAA,IAAI,CAAC,YAAY,CAAC,mBAAmB,GAAG;oBACpC,OAAO;AACP,oBAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;iBAC5E;;AAED,gBAAA,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,KAAK;;iBACnC;gBACH,IAAI,CAAC,YAAY,CAAC,mBAAmB,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC;;AAEjE,YAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC;;aAC7B;;YAEH,OAAO,CAAC,IAAI,CACR,oEAAoE,EACpE,OAAO,KAAK,CACf;;;AAIF,IAAA,gBAAgB,CAAC,EAAwB,EAAA;QAC5C,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,EAAE;;AAG5C,IAAA,iBAAiB,CAAC,EAAuB,EAAA;AAC5C,QAAA,IAAI,CAAC,OAAO,GAAG,EAAE;;AAGb,IAAA,iBAAiB,CAAC,QAAA,GAAiC,IAAI,CAAC,QAAQ,EAAA;AACpE,QAAA,MAAM,YAAY,GAAG,QAAQ,EAAE,aAAa,EAAE,UAAU;AACxD,QAAA,IAAI,CAAC,YAAY,EAAE,aAAa,EAAE;YAC9B,OAAO,QAAQ,CAAC,aAAa;;aAC1B;AACH,YAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC;;;AAI5C,IAAA,wBAAwB,CAAC,EAAoB,EAAA;QAChD,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM;QACzC,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM;QACzC,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM;QAElD,EAAE,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CACxB,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,EAAE,CAAC,cAAwB,CAAC,EACnD,gBAAgB,GAAG,YAAY,CAClC;QACD,EAAE,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CACtB,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,EAAE,CAAC,YAAsB,CAAC,EACjD,gBAAgB,GAAG,YAAY,CAClC;;;AAIE,IAAA,gBAAgB,CAAC,UAAmB,EAAA;QACvC,IAAI,CAAC,YAAY,CAAC,mBAAmB,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC;;IAG5D,UAAU,GAAA;AACd,QAAA,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,qBAAqB,CACtE,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAC1B;AACD,QAAA,IAAI,CAAC,YAAY,CAAC,mBAAmB,GAAG;YACpC,OAAO;AACP,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;SACpF;;AAGG,IAAA,aAAa,CAAC,KAAa,EAAA;AAC/B,QAAA,MAAM,UAAU,GAAW,IAAI,CAAC,UAAU;AACrC,aAAA,KAAK,CAAC,cAAc,CAAC,YAAY;AACjC,aAAA,MAAM,CAAC,CAAC,CAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM;QAC5C,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,IAAI,CAAC;;QAGhB,IACI,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,UAAU;AACpE,YAAA,KAAK,CAAC,MAAM,IAAI,UAAU,GAAG,CAAC,EAChC;AACE,YAAA,OAAO,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC;;AAG7C,QAAA,OAAO,IAAI;;IAGP,qBAAqB,GAAA;AACzB,QAAA,QACI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM;AACpC,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM;;AAItE,IAAA,sBAAsB,CAAC,WAAmB,EAAA;QAC9C,OAAO;AACH,YAAA,IAAI,EAAE;AACF,gBAAA,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE;gBAC/B,WAAW;AACd,aAAA;SACJ;;IAGG,QAAQ,GAAA;QACZ,IAAI,CAAC,oBAAoB,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,KAAoB;YACtD,MAAM,YAAY,GAAY;AACzB,iBAAA,KAAK,CAAC,cAAc,CAAC,YAAY;AACjC,iBAAA,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACvE,YAAA,IACI,CAAC,YAAY;gBACT,IAAI,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBACvE,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAC,EACnD;AACE,gBAAA,MAAM,IAAI,GACN,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,MAAM;oBACxD,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,MAAM;gBAC9C,IAAI,IAAI,EAAE;oBACN,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,mBAAmB;0BAC5D,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,IAAI;0BAC5C,IAAI;AACV,oBAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC;AAC9B,oBAAA,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,SAAS;AAC5C,oBAAA,OAAO,IAAI;;qBACR;AACH,oBAAA,MAAM,UAAU,GACZ,IAAI,CAAC,oBAAoB,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;wBACnE,cAAc,CAAC,YAAY;oBAE/B,MAAM,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,mBAAmB;0BAClE,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,UAAU;0BAClD,UAAU;AAChB,oBAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC;AAC9B,oBAAA,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,SAAS;;;iBAE7C;gBACH,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC;AACpD,gBAAA,MAAM,KAAK,GAAY,IAAI,CAAC;AACvB,qBAAA,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE;AAC9B,sBAAE,KAAK,CAAC,cAAc,CAAC,YAAY;AAClC,qBAAA,KAAK,CAAC,CAAC,SAAS,EAAE,KAAK,KAAI;oBACxB,MAAM,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC;oBACzC,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAC;AACnE,iBAAC,CAAC;AAEN,gBAAA,IAAI,KAAK,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;AAC7B,oBAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC;AACzB,oBAAA,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI;AACvC,oBAAA,OAAO,KAAK;;;AAGxB,SAAC,CAAC;;AAGE,IAAA,iCAAiC,CAAC,KAAe,EAAA;AACrD,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB;QAE7D,SAAS,uBAAuB,CAAC,GAAW,EAAA;AACxC,YAAA,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,CAAA,CAAA,EAAI,iBAAiB,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAA,EAAA,EAAK,EAAE,CAAA,CAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAG,CAAA,CAAA,EAAE,GAAG,CAAC;YACvF,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;;QAGjC,MAAM,YAAY,GAAG,KAAK,CAAC,GAAG,CAAC,uBAAuB,CAAC;AAEvD,QAAA,OAAO,YAAY,CAAC,KAAK,CAAC,CAAC,GAAG,KAAI;AAC9B,YAAA,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC;AACrC,YAAA,OAAO,gBAAgB,CAAC,IAAI,KAAK,CAAC;AACtC,SAAC,CAAC;;uGA3nCG,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,EAfd,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,6BAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,EAAA,iBAAA,EAAA,MAAA,EAAA,UAAA,EAAA,MAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,iBAAA,EAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,UAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,QAAA,EAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,UAAA,EAAA,UAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,MAAA,EAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,MAAA,EAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,QAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,iBAAA,EAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,UAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,aAAA,EAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,UAAA,EAAA,eAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,qBAAA,EAAA,EAAA,iBAAA,EAAA,uBAAA,EAAA,UAAA,EAAA,uBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,WAAA,EAAA,EAAA,iBAAA,EAAA,aAAA,EAAA,UAAA,EAAA,aAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,aAAA,EAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,UAAA,EAAA,eAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,oBAAA,EAAA,EAAA,iBAAA,EAAA,sBAAA,EAAA,UAAA,EAAA,sBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,mBAAA,EAAA,EAAA,iBAAA,EAAA,qBAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,eAAA,EAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,iBAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,EAAA,iBAAA,EAAA,gBAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,oBAAA,EAAA,EAAA,iBAAA,EAAA,sBAAA,EAAA,UAAA,EAAA,sBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,gBAAA,EAAA,EAAA,iBAAA,EAAA,kBAAA,EAAA,UAAA,EAAA,kBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,QAAA,EAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,UAAA,EAAA,UAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,mBAAA,EAAA,EAAA,iBAAA,EAAA,qBAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,GAAA,EAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,UAAA,EAAA,KAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,gBAAA,EAAA,EAAA,iBAAA,EAAA,kBAAA,EAAA,UAAA,EAAA,kBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,iBAAA,EAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,UAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,sBAAA,EAAA,EAAA,iBAAA,EAAA,wBAAA,EAAA,UAAA,EAAA,wBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,aAAA,EAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,UAAA,EAAA,eAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,YAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,WAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,uBAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,kBAAA,EAAA,4BAAA,EAAA,gBAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,gBAAA,EAAA,OAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,mBAAA,EAAA,EAAA,EAAA,SAAA,EAAA;AACP,YAAA;AACI,gBAAA,OAAO,EAAE,iBAAiB;AAC1B,gBAAA,WAAW,EAAE,gBAAgB;AAC7B,gBAAA,KAAK,EAAE,IAAI;AACd,aAAA;AACD,YAAA;AACI,gBAAA,OAAO,EAAE,aAAa;AACtB,gBAAA,WAAW,EAAE,gBAAgB;AAC7B,gBAAA,KAAK,EAAE,IAAI;AACd,aAAA;YACD,cAAc;AACjB,SAAA,EAAA,QAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAGQ,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAlB5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACP,oBAAA,QAAQ,EAAE,6BAA6B;AACvC,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,SAAS,EAAE;AACP,wBAAA;AACI,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,WAAW,EAAkB,gBAAA;AAC7B,4BAAA,KAAK,EAAE,IAAI;AACd,yBAAA;AACD,wBAAA;AACI,4BAAA,OAAO,EAAE,aAAa;AACtB,4BAAA,WAAW,EAAkB,gBAAA;AAC7B,4BAAA,KAAK,EAAE,IAAI;AACd,yBAAA;wBACD,cAAc;AACjB,qBAAA;AACD,oBAAA,QAAQ,EAAE,cAAc;AAC3B,iBAAA;8BAuWU,OAAO,EAAA,CAAA;sBADb,YAAY;uBAAC,OAAO;gBAKqB,OAAO,EAAA,CAAA;sBAAhD,YAAY;uBAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;gBAK1B,aAAa,EAAA,CAAA;sBADnB,YAAY;uBAAC,eAAe,EAAE,CAAC,QAAQ,CAAC;gBAgBlC,OAAO,EAAA,CAAA;sBADb,YAAY;uBAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;gBAsR1B,kBAAkB,EAAA,CAAA;sBADxB,YAAY;uBAAC,kBAAkB,EAAE,CAAC,QAAQ,CAAC;gBAOrC,gBAAgB,EAAA,CAAA;sBADtB,YAAY;uBAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC;gBAQnC,MAAM,EAAA,CAAA;sBADZ,YAAY;uBAAC,MAAM,EAAE,CAAC,QAAQ,CAAC;gBAyCzB,OAAO,EAAA,CAAA;sBADb,YAAY;uBAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;gBAyE1B,SAAS,EAAA,CAAA;sBADf,YAAY;uBAAC,SAAS,EAAE,CAAC,QAAQ,CAAC;;;MCryB1B,WAAW,CAAA;AACH,IAAA,cAAc,GAAG,MAAM,CAAgB,eAAe,CAAC;AAEvD,IAAA,YAAY,GAAG,MAAM,CAAC,cAAc,CAAC;IAE9C,oBAAoB,GAAa,EAAE;IAEnC,IAAI,GAAG,EAAE;IAEV,SAAS,CACZ,KAAsB,EACtB,IAAY,EACZ,EAAE,QAAQ,EAAE,GAAG,MAAM,EAAA,GAA6B,EAA4B,EAAA;QAE9E,IAAI,cAAc,GAAoB,KAAK;AAE3C,QAAA,MAAM,aAAa,GAAG;AAClB,YAAA,cAAc,EAAE,IAAI;YACpB,GAAG,IAAI,CAAC,cAAc;AACtB,YAAA,GAAG,MAAM;AACT,YAAA,QAAQ,EAAE;AACN,gBAAA,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ;AAC7B,gBAAA,GAAG,QAAQ;AACd,aAAA;SACJ;AAED,QAAA,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,KAAI;AAChD,YAAA,IAAI,CAAC,YAAoB,CAAC,GAAG,CAAC,GAAG,GAAG;AACzC,SAAC,CAAC;AAEF,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACrB,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AAClC,YAAA,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtB,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC,IAAI,CACtC,CAAC,CAAS,EAAE,CAAS,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAChD;AACD,gBAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAA,CAAE,CAAC;AAClC,gBAAA,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA,EAAG,cAAc,CAAA,CAAE,EAAE,IAAI,CAAC,IAAI,CAAC;;iBAC/D;AACH,gBAAA,IAAI,CAAC,oBAAoB,GAAG,EAAE;AAC9B,gBAAA,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA,EAAG,cAAc,CAAA,CAAE,EAAE,IAAI,CAAC,IAAI,CAAC;;;QAI1E,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAC,EAAE;AACnD,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAC9B,GAAG,cAAc,CAAA,CAAE,EACnB,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAChD;;QAGL,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;AAC3C,YAAA,IAAI,MAAM,CAAC,aAAa,EAAE;gBACtB,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa;;AAE1D,YAAA,IAAI,MAAM,CAAC,iBAAiB,EAAE;gBAC1B,IAAI,CAAC,YAAY,CAAC,iBAAiB,GAAG,MAAM,CAAC,iBAAiB;;AAElE,YAAA,IAAI,MAAM,CAAC,QAAQ,EAAE;gBACjB,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ;;AAGhD,YAAA,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;YACvC,MAAM,mBAAmB,GAAG,IAAI,CAAC,YAAY,CAAC,0BAA0B,EAAE;AAE1E,YAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE;gBACjD,cAAc;AACV,oBAAA,IAAI,CAAC,YAAY,CAAC,aAAa,KAAK;AAChC,0BAAG,cAAyB,CAAC,OAAO,CAC9B,mBAAmB,EACnB,IAAI,CAAC,YAAY,CAAC,aAAa;0BAEnC,cAAc;;AAG5B,YAAA,IACI,IAAI,CAAC,YAAY,CAAC,QAAQ;gBAC1B,cAAc;AACd,gBAAA,IAAI,CAAC,YAAY,CAAC,qBAAqB,KAAK,KAAK,EACnD;gBACE,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,EAAE,cAAwB,CAAC;;YAGtF,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,KAAK,cAAc,CAAC,KAAK,EAAE;AAC1D,gBAAA,cAAc,GAAI,cAAyB,CAAC,OAAO,CAC/C,cAAc,CAAC,GAAG,EAClB,cAAc,CAAC,KAAK,CACvB;;AAGL,YAAA,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI;;QAG1C,IAAI,cAAc,KAAK,IAAI,IAAI,OAAO,cAAc,KAAK,WAAW,EAAE;YAClE,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC;;AAGhD,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAG,EAAA,cAAc,CAAE,CAAA,EAAE,IAAI,CAAC;;AAGzD,IAAA,QAAQ,CAAC,KAAa,EAAA;QAC1B,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;YACtC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,IAAI,KAAoB;gBACpD,MAAM,IAAI,GACN,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,MAAM;oBAC3C,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,MAAM;AAC9C,gBAAA,IAAI,KAAK,IAAI,IAAI,EAAE;AACf,oBAAA,IAAI,CAAC,IAAI,GAAG,IAAI;AAChB,oBAAA,OAAO,IAAI;;qBACR;AACH,oBAAA,IAAI,CAAC,IAAI;wBACL,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC;4BAC/D,cAAc,CAAC,YAAY;;AAEvC,aAAC,CAAC;;;uGAlHD,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA;qGAAX,WAAW,EAAA,YAAA,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA;;2FAAX,WAAW,EAAA,UAAA,EAAA,CAAA;kBALvB,IAAI;AAAC,YAAA,IAAA,EAAA,CAAA;AACF,oBAAA,IAAI,EAAE,MAAM;AACZ,oBAAA,IAAI,EAAE,IAAI;AACV,oBAAA,UAAU,EAAE,IAAI;AACnB,iBAAA;;;ACZD;;AAEG;;;;"}