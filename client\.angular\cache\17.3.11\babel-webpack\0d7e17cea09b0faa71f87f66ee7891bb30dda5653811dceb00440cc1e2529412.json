{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../prospects.service\";\nfunction ProspectsDetailsComponent_p_tabPanel_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", tab_r1.routerLink);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", tab_r1.label, \" \");\n  }\n}\nfunction ProspectsDetailsComponent_p_tabPanel_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 38);\n    i0.ɵɵtemplate(1, ProspectsDetailsComponent_p_tabPanel_9_ng_template_1_Template, 2, 2, \"ng-template\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"headerStyleClass\", \"m-0 p-0\");\n  }\n}\nexport class ProspectsDetailsComponent {\n  constructor(route, router, messageservice, formBuilder, confirmationservice, prospectsservice) {\n    this.route = route;\n    this.router = router;\n    this.messageservice = messageservice;\n    this.formBuilder = formBuilder;\n    this.confirmationservice = confirmationservice;\n    this.prospectsservice = prospectsservice;\n    this.unsubscribe$ = new Subject();\n    this.prospectDetails = null;\n    this.sidebarDetails = null;\n    this.NoteDetails = null;\n    this.customerData = null;\n    this.items = [];\n    this.activeItem = null;\n    this.breadcrumbitems = [];\n    this.id = '';\n    this.partner_role = '';\n    this.bp_status = '';\n    this.bp_doc_id = '';\n    this.Actions = [];\n    this.activeIndex = 0;\n    this.isSidebarHidden = false;\n    this.submitted = false;\n    this.saving = false;\n    this.GlobalNoteForm = this.formBuilder.group({\n      note: ['']\n    });\n  }\n  ngOnInit() {\n    this.id = this.route.snapshot.paramMap.get('id') || '';\n    this.prospectsservice.getGlobalNote(this.id).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        if (Array.isArray(response?.data) && response.data.length > 0) {\n          this.NoteDetails = response.data[0];\n          this.GlobalNoteForm.patchValue({\n            note: this.stripHtml(response.data[0].note)\n          });\n        } else {\n          this.NoteDetails = {};\n          this.GlobalNoteForm.patchValue({\n            note: ''\n          });\n        }\n      },\n      error: error => {\n        console.error('Error fetching global note:', error);\n      }\n    });\n    this.home = {\n      icon: 'pi pi-home text-lg',\n      routerLink: ['/']\n    };\n    this.makeMenuItems(this.id);\n    if (this.items.length > 0) {\n      this.activeItem = this.items[0];\n    }\n    this.setActiveTabFromURL();\n    this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n      const prospectId = params.get('id');\n      if (prospectId) {\n        this.loadProspectData(prospectId);\n      }\n    });\n    // Listen for route changes to keep active tab in sync\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n      this.setActiveTabFromURL();\n    });\n    this.prospectsservice.prospect.pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      const partner_role = response?.customer?.partner_functions?.find(p => p.partner_function === 'YI');\n      this.partner_role = partner_role?.business_partner?.bp_full_name || null;\n      this.prospectDetails = response || null;\n      this.sidebarDetails = this.formatSidebarDetails(response?.addresses || []);\n    });\n  }\n  makeMenuItems(id) {\n    this.items = [{\n      label: 'Overview',\n      routerLink: `/store/prospects/${id}/overview`\n    }, {\n      label: 'Contacts',\n      routerLink: `/store/prospects/${id}/contacts`\n    }, {\n      label: 'Sales Team',\n      routerLink: `/store/prospects/${id}/sales-team`\n    },\n    // {\n    //   label: 'AI Insights',\n    //   routerLink: `/store/prospects/${id}/ai-insights`,\n    // },\n    // {\n    //   label: 'Organization Data',\n    //   routerLink: `/store/prospects/${id}/organization-data`,\n    // },\n    {\n      label: 'Attachments',\n      routerLink: `/store/prospects/${id}/attachments`\n    }, {\n      label: 'Notes',\n      routerLink: `/store/prospects/${id}/notes`\n    }, {\n      label: 'Activities',\n      routerLink: `/store/prospects/${id}/activities`\n    }];\n  }\n  setActiveTabFromURL() {\n    const fullPath = this.router.url;\n    const currentTab = fullPath.split('/').pop() || 'overview';\n    if (this.items.length === 0) return;\n    const foundIndex = this.items.findIndex(tab => tab.routerLink.endsWith(currentTab));\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\n    this.updateBreadcrumb(this.activeItem?.label || 'Overview');\n  }\n  updateBreadcrumb(activeTab) {\n    this.breadcrumbitems = [{\n      label: 'Prospects',\n      routerLink: ['/store/prospects']\n    }, {\n      label: activeTab,\n      routerLink: []\n    }];\n  }\n  onTabChange(event) {\n    if (this.items.length === 0) return;\n    this.activeIndex = event.index;\n    const selectedTab = this.items[this.activeIndex];\n    if (selectedTab?.routerLink) {\n      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\n    }\n  }\n  loadProspectData(prospectId) {\n    this.prospectsservice.getProspectByID(prospectId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        if (response) {\n          this.bp_doc_id = response?.data?.[0]?.documentId;\n          this.bp_status = response?.data?.[0]?.is_marked_for_archiving;\n          this.Actions = [{\n            name: 'Convert to Customer',\n            code: 'CI'\n          }, {\n            name: this.bp_status ? 'Set As Active' : 'Set As Obsolete',\n            code: this.bp_status ? 'SAA' : 'SAO'\n          }];\n        }\n      },\n      error: error => {\n        console.error('Error fetching data:', error);\n      }\n    });\n  }\n  formatSidebarDetails(addresses) {\n    return addresses.filter(address => address?.address_usages?.some(usage => usage.address_usage === 'XXDEFAULT')).map(address => ({\n      ...address,\n      address: [address?.house_number, address?.street_name, address?.city_name, address?.region, address?.country, address?.postal_code].filter(Boolean).join(', '),\n      email_address: address?.emails?.[0]?.email_address || '-',\n      phone_number: (() => {\n        const phone = address?.phone_numbers?.[0];\n        if (!phone || !phone.phone_number) {\n          return '-';\n        }\n        const countryCode = phone.destination_location_country;\n        const rawNumber = phone.phone_number;\n        return this.prospectsservice.getDialCode(countryCode, rawNumber);\n      })(),\n      website_url: address?.home_page_urls?.[0]?.website_url || '-'\n    }));\n  }\n  onActionChange(event) {\n    const actionCode = event.value?.code;\n    const actionsMap = {\n      CI: () => this.ConvertToCustomer(event),\n      SAA: () => this.UpdateStatus(this.bp_doc_id, 'false'),\n      SAO: () => this.UpdateStatus(this.bp_doc_id, 'true')\n    };\n    const action = actionsMap[actionCode];\n    if (action) {\n      this.confirmationservice.confirm({\n        message: 'Are you sure you want to proceed with this action?',\n        header: 'Confirm',\n        icon: 'pi pi-exclamation-triangle',\n        accept: action\n      });\n    }\n  }\n  ConvertToCustomer(event) {\n    this.customerData = this.createBusinessPartnerObject(this.prospectDetails);\n    this.prospectsservice.bpCreation(this.customerData).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Convert to Customer Successfully!'\n        });\n        this.prospectsservice.getProspectByID(this.id).pipe(takeUntil(this.unsubscribe$)).subscribe();\n      },\n      error: error => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: error?.error?.message || 'Error while processing your request.'\n        });\n        console.error('Error fetching data:', error);\n      }\n    });\n  }\n  createBusinessPartnerObject(data) {\n    const formatAddress = address => ({\n      Country: address.country_code || 'US',\n      Region: address.region || '',\n      HouseNumber: address.house_number || '',\n      AdditionalStreetPrefixName: address.additional_street_prefix_name || '',\n      AdditionalStreetSuffixName: address.additional_street_suffix_name || '',\n      StreetName: address.street_name || '',\n      PostalCode: address.postal_code || '',\n      CityName: address.city_name || '',\n      PrfrdCommMediumType: address.prfrd_comm_medium_type || '',\n      Language: 'EN',\n      to_AddressUsage: address.address_usages?.map(usage => ({\n        AddressUsage: usage.address_usage || 'XXDEFAULT'\n      })) || [],\n      to_EmailAddress: address.emails?.map(email => ({\n        EmailAddress: email.email_address || ''\n      })) || [],\n      to_PhoneNumber: address.phone_numbers?.filter(ph => ph.phone_number_type === '1').map(ph => ({\n        PhoneNumber: ph.phone_number || ''\n      })) || [],\n      to_URLAddress: address.home_page_urls?.map(url => ({\n        WebsiteURL: url.website_url || ''\n      })) || [],\n      to_FaxNumber: address.fax_numbers?.map(fax => ({\n        FaxNumber: fax.fax_number || ''\n      })) || [],\n      to_MobilePhoneNumber: address.phone_numbers?.filter(ph => ph.phone_number_type === '3').map(ph => ({\n        PhoneNumber: ph.phone_number || ''\n      })) || []\n    });\n    const mainPartner = {\n      BusinessPartner: data.bp_id || '',\n      Name1: data.bp_full_name || '',\n      Name2: '',\n      Name3: '',\n      CategoryCode: '2',\n      IsMarkedForArchiving: data?.is_marked_for_archiving || false,\n      to_BusinessPartnerAddress: data.addresses?.map(formatAddress) || [],\n      to_BusinessPartnerRole: [{\n        BusinessPartnerRole: 'FLCU01'\n      }, {\n        BusinessPartnerRole: 'FLCU00'\n      }],\n      to_Customer: {\n        Customer: data.customer?.customer_id || '',\n        DeletedIndicator: false,\n        DeliveryIsBlockedForCustomer: 'ZN',\n        // ZN means new customer code\n        to_CustomerSalesArea: (data.customer?.partner_functions || []).reduce((acc, pf) => {\n          const key = `${pf.sales_organization}-${pf.distribution_channel}-${pf.division}`;\n          if (!acc[key]) {\n            acc[key] = {\n              SalesOrganization: pf.sales_organization || '',\n              DistributionChannel: pf.distribution_channel || '',\n              Division: pf.division || '',\n              Currency: 'USD',\n              to_PartnerFunction: []\n            };\n            // Add partner functions only once per SalesArea\n            acc[key].to_PartnerFunction.push(...['AG', 'RE', 'RG', 'WE'].map(func => ({\n              PartnerFunction: func,\n              BPCustomerNumber: data.customer.customer_id || ''\n            })));\n            // Sales Team Data for Prospect to customer\n            acc[key].to_PartnerFunction.filter(pf => pf.partner_function && pf.bp_customer_number).push(...data.customer.partner_functions.map(partner => ({\n              PartnerFunction: partner.partner_function || '',\n              BPCustomerNumber: partner?.bp_customer_number || ''\n            })));\n            // data.contact_companies?.forEach((company: any) => {\n            //   acc[key].to_PartnerFunction.push({\n            //     PartnerFunction: 'CP',\n            //     BPCustomerNumber: company?.bp_person_id || '',\n            //   });\n            // });\n          }\n          return acc;\n        }, {})\n      }\n    };\n    mainPartner.to_Customer.to_CustomerSalesArea = Object.values(mainPartner.to_Customer.to_CustomerSalesArea);\n    const contacts = (data.contact_companies || []).map(company => ({\n      BusinessPartner: company.bp_person_id || '',\n      Name1: company.business_partner_person?.first_name || '',\n      Name2: company.business_partner_person?.middle_name || '',\n      Name3: company.business_partner_person?.last_name || '',\n      CategoryCode: '1',\n      IsMarkedForArchiving: company?.business_partner_person?.is_marked_for_archiving || false,\n      to_BusinessPartnerAddress: company.business_partner_person?.addresses?.map(formatAddress) || [],\n      to_BusinessPartnerRole: [{\n        BusinessPartnerRole: 'BUP001'\n      }]\n    }));\n    return {\n      BP: [mainPartner, ...contacts],\n      BP_Relationship: (data?.contact_companies || []).map(company => ({\n        BusinessPartnerID: company?.bp_company_id || '',\n        RelationshipBusinessPartnerID: company?.bp_person_id || '',\n        ValidityStartDate: company?.validity_start_date || new Date(),\n        ValidityEndDate: company?.validity_end_date || new Date('9999-12-29T23:59:59.000Z'),\n        RoleCode: 'BUR001',\n        ContactPerson: {\n          ContactPersonVipType: company?.person_func_and_dept?.contact_person_vip_type ? '1' : '',\n          ContactPersonDepartment: company?.person_func_and_dept?.contact_person_department || '',\n          ContactPersonFunction: company?.person_func_and_dept?.contact_person_function || ''\n        }\n      }))\n    };\n  }\n  onNoteSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      if (_this.GlobalNoteForm.invalid) {\n        return;\n      }\n      _this.saving = true;\n      const value = {\n        ..._this.GlobalNoteForm.value\n      };\n      const data = {\n        note: value?.note,\n        bp_id: _this?.id,\n        ...(!_this.NoteDetails.documentId ? {\n          is_global_note: true\n        } : {})\n      };\n      const apiCall = _this.NoteDetails && _this.NoteDetails.documentId ? _this.prospectsservice.updateNote(_this.NoteDetails.documentId, data) // Update if exists\n      : _this.prospectsservice.createNote(data); // Create if not exists\n      apiCall.pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        next: () => {\n          _this.messageservice.add({\n            severity: 'success',\n            detail: 'Prospect Note Updated SuccessFully!'\n          });\n          _this.prospectsservice.getProspectByID(_this.id).pipe(takeUntil(_this.unsubscribe$)).subscribe();\n        },\n        error: () => {\n          _this.saving = false;\n          _this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  UpdateStatus(docid, status) {\n    const data = {\n      is_marked_for_archiving: status\n    };\n    this.prospectsservice.updateBpStatus(docid, data).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Action Updated Successfully!'\n        });\n        setTimeout(() => {\n          window.location.reload();\n        }, 1000);\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  stripHtml(html) {\n    const temp = document.createElement('div');\n    temp.innerHTML = html;\n    return temp.textContent || temp.innerText || '';\n  }\n  get formattedPhoneNumber() {\n    const phone = this.prospectDetails?.contact_companies?.[0]?.business_partner_person?.addresses?.[0]?.phone_numbers?.[0];\n    if (!phone || !phone.phone_number) {\n      return '-';\n    }\n    const countryCode = phone.destination_location_country;\n    const rawNumber = phone.phone_number;\n    return this.prospectsservice.getDialCode(countryCode, rawNumber);\n  }\n  goToBack() {\n    this.router.navigate(['/store/prospects']);\n  }\n  toggleSidebar() {\n    this.isSidebarHidden = !this.isSidebarHidden;\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function ProspectsDetailsComponent_Factory(t) {\n      return new (t || ProspectsDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i2.ConfirmationService), i0.ɵɵdirectiveInject(i4.ProspectsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProspectsDetailsComponent,\n      selectors: [[\"app-prospects-details\"]],\n      decls: 84,\n      vars: 29,\n      consts: [[\"position\", \"top-center\", 3, \"life\"], [1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-0\", \"border-round-lg\"], [1, \"filter-sec\", \"my-4\", \"flex\", \"align-items-center\", \"justify-content-between\", \"h-3rem\", \"gap-3\"], [1, \"breadcrumb-sec\", \"flex\", \"align-items-center\", \"gap-3\"], [3, \"model\", \"home\", \"styleClass\"], [\"optionLabel\", \"name\", \"placeholder\", \"Action\", 3, \"onChange\", \"options\", \"styleClass\"], [1, \"details-tabs-sec\"], [1, \"details-tabs-list\"], [3, \"activeIndexChange\", \"onChange\", \"scrollable\", \"activeIndex\"], [3, \"headerStyleClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"details-tabs-result\", \"p-3\", \"bg-whight-light\"], [1, \"grid\", \"mt-0\", \"relative\", \"flex-nowrap\"], [1, \"col-12\", \"lg:w-28rem\", \"md:w-28rem\", \"sm:w-full\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\"], [1, \"left-side-bar-top\", \"p-4\", \"bg-primary\", \"overflow-hidden\"], [1, \"flex\", \"align-items-start\", \"gap-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\", \"surface-0\", \"border-circle\"], [1, \"m-0\", \"p-0\", \"text-primary\", \"font-bold\"], [1, \"flex\", \"flex-column\", \"gap-4\", \"flex-1\"], [1, \"mt-3\", \"mb-1\", \"font-semibold\"], [1, \"flex\", \"flex-column\", \"gap-3\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"flex\", \"w-9rem\", \"font-semibold\"], [1, \"left-side-bar-bottom\", \"px-3\", \"py-4\"], [1, \"flex\", \"flex-column\", \"gap-5\", \"p-0\", \"m-0\", \"list-none\"], [1, \"flex\", \"gap-2\", \"font-medium\", \"text-color-secondary\", \"align-items-start\"], [1, \"flex\", \"w-10rem\", \"align-items-center\", \"gap-2\", \"text-800\", \"font-semibold\"], [1, \"material-symbols-rounded\"], [1, \"flex-1\"], [3, \"formGroup\"], [1, \"w-full\", \"bg-white\", \"border-round\", \"shadow-1\", \"overflow-hidden\", \"mt-5\", \"p-3\"], [1, \"mb-3\", \"font-semibold\", \"text-color\"], [1, \"flex\", \"flex-column\", \"gap-3\"], [\"formControlName\", \"note\", \"rows\", \"4\", \"placeholder\", \"Enter your note here...\", 1, \"w-full\", \"h-8rem\", \"p-2\", \"border-1\", \"border-round\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Save Note\", 1, \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\", 3, \"click\"], [1, \"col-12\", \"lg:flex-1\", \"md:flex-1\", \"relative\", \"all-page-details\"], [\"icon\", \"pi pi-angle-left\", 1, \"arrow-btn\", \"inline-flex\", \"absolute\", \"transition-all\", \"transition-duration-300\", \"transition-ease-in-out\", 3, \"click\", \"rounded\", \"outlined\", \"styleClass\"], [1, \"confirm-popup\"], [3, \"headerStyleClass\"], [\"pTemplate\", \"header\"], [\"routerLinkActive\", \"active-tab\", 1, \"tab-link\", \"flex\", \"align-items-center\", \"justify-content-center\", \"white-space-nowrap\", 3, \"routerLink\"]],\n      template: function ProspectsDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"p-toast\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"p-breadcrumb\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p-dropdown\", 5);\n          i0.ɵɵlistener(\"onChange\", function ProspectsDetailsComponent_Template_p_dropdown_onChange_5_listener($event) {\n            return ctx.onActionChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"p-tabView\", 8);\n          i0.ɵɵtwoWayListener(\"activeIndexChange\", function ProspectsDetailsComponent_Template_p_tabView_activeIndexChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.activeIndex, $event) || (ctx.activeIndex = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"onChange\", function ProspectsDetailsComponent_Template_p_tabView_onChange_8_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵtemplate(9, ProspectsDetailsComponent_p_tabPanel_9_Template, 2, 1, \"p-tabPanel\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"div\", 14)(15, \"div\", 15)(16, \"div\", 16)(17, \"h5\", 17);\n          i0.ɵɵtext(18);\n          i0.ɵɵpipe(19, \"initials\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 18)(21, \"h5\", 19);\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"ul\", 20)(24, \"li\", 21)(25, \"span\", 22);\n          i0.ɵɵtext(26, \"CRM ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"li\", 21)(29, \"span\", 22);\n          i0.ɵɵtext(30, \"Account Owner \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"li\", 21)(33, \"span\", 22);\n          i0.ɵɵtext(34, \"Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(36, \"div\", 23)(37, \"ul\", 24)(38, \"li\", 25)(39, \"span\", 26)(40, \"i\", 27);\n          i0.ɵɵtext(41, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(42, \" Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"span\", 28);\n          i0.ɵɵtext(44);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"li\", 25)(46, \"span\", 26)(47, \"i\", 27);\n          i0.ɵɵtext(48, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(49, \" Phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"span\", 28);\n          i0.ɵɵtext(51);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"li\", 25)(53, \"span\", 26)(54, \"i\", 27);\n          i0.ɵɵtext(55, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(56, \" Main Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"span\", 28);\n          i0.ɵɵtext(58);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(59, \"li\", 25)(60, \"span\", 26)(61, \"i\", 27);\n          i0.ɵɵtext(62, \"mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(63, \" Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"span\", 28);\n          i0.ɵɵtext(65);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"li\", 25)(67, \"span\", 26)(68, \"i\", 27);\n          i0.ɵɵtext(69, \"language\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(70, \" Website\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"span\", 28);\n          i0.ɵɵtext(72);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(73, \"form\", 29)(74, \"div\", 30)(75, \"h4\", 31);\n          i0.ɵɵtext(76, \"Global Note\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"div\", 32);\n          i0.ɵɵelement(78, \"textarea\", 33);\n          i0.ɵɵelementStart(79, \"button\", 34);\n          i0.ɵɵlistener(\"click\", function ProspectsDetailsComponent_Template_button_click_79_listener() {\n            return ctx.onNoteSubmit();\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(80, \"div\", 35)(81, \"p-button\", 36);\n          i0.ɵɵlistener(\"click\", function ProspectsDetailsComponent_Template_p_button_click_81_listener() {\n            return ctx.toggleSidebar();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(82, \"router-outlet\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(83, \"p-confirmDialog\", 37);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"model\", ctx.breadcrumbitems)(\"home\", ctx.home)(\"styleClass\", \"py-2 px-0 border-none\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"options\", ctx.Actions)(\"styleClass\", \"w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"scrollable\", true);\n          i0.ɵɵtwoWayProperty(\"activeIndex\", ctx.activeIndex);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"sidebar-hide\", ctx.isSidebarHidden);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 27, (ctx.prospectDetails == null ? null : ctx.prospectDetails.bp_full_name) || \"-\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate((ctx.prospectDetails == null ? null : ctx.prospectDetails.bp_full_name) || \"-\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" : \", (ctx.prospectDetails == null ? null : ctx.prospectDetails.bp_id) || \"-\", \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" : \", ctx.partner_role || \"-\", \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" : \", ((ctx.prospectDetails == null ? null : ctx.prospectDetails.contact_companies == null ? null : ctx.prospectDetails.contact_companies[0] == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.first_name) || \"-\") + \" \" + ((ctx.prospectDetails == null ? null : ctx.prospectDetails.contact_companies == null ? null : ctx.prospectDetails.contact_companies[0] == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person == null ? null : ctx.prospectDetails.contact_companies[0].business_partner_person.last_name) || \"-\"), \" \");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].phone_number) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.formattedPhoneNumber);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].email_address) || \"-\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate((ctx.sidebarDetails == null ? null : ctx.sidebarDetails[0] == null ? null : ctx.sidebarDetails[0].website_url) || \"-\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.GlobalNoteForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵclassProp(\"arrow-round\", ctx.isSidebarHidden);\n          i0.ɵɵproperty(\"rounded\", true)(\"outlined\", true)(\"styleClass\", \"p-0 w-2rem h-2rem border-2 surface-0\");\n        }\n      },\n      styles: [\".prospect-popup .p-dialog {\\n  margin-right: 50px;\\n}\\n  .prospect-popup .p-dialog .p-dialog-header {\\n  background: var(--surface-0);\\n  border-bottom: 1px solid var(--surface-100);\\n}\\n  .prospect-popup .p-dialog .p-dialog-header h4 {\\n  margin: 0;\\n}\\n  .prospect-popup .p-dialog .p-dialog-content {\\n  background: var(--surface-0);\\n  padding: 1.714rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvcHJvc3BlY3RzL3Byb3NwZWN0cy1kZXRhaWxzL3Byb3NwZWN0cy1kZXRhaWxzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUVRO0VBQ0ksa0JBQUE7QUFEWjtBQUdZO0VBQ0ksNEJBQUE7RUFDQSwyQ0FBQTtBQURoQjtBQUdnQjtFQUNJLFNBQUE7QUFEcEI7QUFLWTtFQUNJLDRCQUFBO0VBQ0EsaUJBQUE7QUFIaEIiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAge1xyXG4gICAgLnByb3NwZWN0LXBvcHVwIHtcclxuICAgICAgICAucC1kaWFsb2cge1xyXG4gICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDUwcHg7XHJcblxyXG4gICAgICAgICAgICAucC1kaWFsb2ctaGVhZGVyIHtcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IHZhcigtLXN1cmZhY2UtMCk7XHJcbiAgICAgICAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgdmFyKC0tc3VyZmFjZS0xMDApO1xyXG5cclxuICAgICAgICAgICAgICAgIGg0IHtcclxuICAgICAgICAgICAgICAgICAgICBtYXJnaW46IDA7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC5wLWRpYWxvZy1jb250ZW50IHtcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IHZhcigtLXN1cmZhY2UtMCk7XHJcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAxLjcxNHJlbTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIFxyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "tab_r1", "routerLink", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵtemplate", "ProspectsDetailsComponent_p_tabPanel_9_ng_template_1_Template", "ProspectsDetailsComponent", "constructor", "route", "router", "messageservice", "formBuilder", "confirmationservice", "prospectsservice", "unsubscribe$", "prospectDetails", "sidebarDetails", "NoteDetails", "customerData", "items", "activeItem", "breadcrumbitems", "id", "partner_role", "bp_status", "bp_doc_id", "Actions", "activeIndex", "isSidebarHidden", "submitted", "saving", "GlobalNoteForm", "group", "note", "ngOnInit", "snapshot", "paramMap", "get", "getGlobalNote", "pipe", "subscribe", "next", "response", "Array", "isArray", "data", "length", "patchValue", "stripHtml", "error", "console", "home", "icon", "makeMenuItems", "setActiveTabFromURL", "params", "prospectId", "loadProspectData", "events", "prospect", "customer", "partner_functions", "find", "p", "partner_function", "business_partner", "bp_full_name", "formatSidebarDetails", "addresses", "fullPath", "url", "currentTab", "split", "pop", "foundIndex", "findIndex", "tab", "endsWith", "updateBreadcrumb", "activeTab", "onTabChange", "event", "index", "selectedTab", "navigateByUrl", "getProspectByID", "documentId", "is_marked_for_archiving", "name", "code", "filter", "address", "address_usages", "some", "usage", "address_usage", "map", "house_number", "street_name", "city_name", "region", "country", "postal_code", "Boolean", "join", "email_address", "emails", "phone_number", "phone", "phone_numbers", "countryCode", "destination_location_country", "rawNumber", "getDialCode", "website_url", "home_page_urls", "onActionChange", "actionCode", "value", "actionsMap", "CI", "ConvertToCustomer", "SAA", "UpdateStatus", "SAO", "action", "confirm", "message", "header", "accept", "createBusinessPartnerObject", "bpCreation", "add", "severity", "detail", "formatAddress", "Country", "country_code", "Region", "HouseNumber", "AdditionalStreetPrefixName", "additional_street_prefix_name", "AdditionalStreetSuffixName", "additional_street_suffix_name", "StreetName", "PostalCode", "CityName", "PrfrdCommMediumType", "prfrd_comm_medium_type", "Language", "to_AddressUsage", "AddressUsage", "to_<PERSON><PERSON><PERSON><PERSON><PERSON>", "email", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "to_PhoneNumber", "ph", "phone_number_type", "PhoneNumber", "to_URLAddress", "WebsiteURL", "to_FaxNumber", "fax_numbers", "fax", "FaxNumber", "fax_number", "to_MobilePhoneNumber", "main<PERSON><PERSON>ner", "BusinessPartner", "bp_id", "Name1", "Name2", "Name3", "CategoryCode", "IsMarkedForArchiving", "to_BusinessPartnerAddress", "to_BusinessPartnerRole", "BusinessPartnerRole", "to_Customer", "Customer", "customer_id", "DeletedIndicator", "DeliveryIsBlockedForCustomer", "to_CustomerSalesArea", "reduce", "acc", "pf", "key", "sales_organization", "distribution_channel", "division", "SalesOrganization", "DistributionChannel", "Division", "<PERSON><PERSON><PERSON><PERSON>", "to_PartnerFunction", "push", "func", "PartnerFunction", "BPCustomerNumber", "bp_customer_number", "partner", "Object", "values", "contacts", "contact_companies", "company", "bp_person_id", "business_partner_person", "first_name", "middle_name", "last_name", "BP", "BP_Relationship", "BusinessPartnerID", "bp_company_id", "RelationshipBusinessPartnerID", "ValidityStartDate", "validity_start_date", "Date", "ValidityEndDate", "validity_end_date", "RoleCode", "<PERSON><PERSON><PERSON>", "ContactPersonVipType", "person_func_and_dept", "contact_person_vip_type", "ContactPersonDepartment", "contact_person_department", "ContactPersonFunction", "contact_person_function", "onNoteSubmit", "_this", "_asyncToGenerator", "invalid", "is_global_note", "apiCall", "updateNote", "createNote", "docid", "status", "updateBpStatus", "setTimeout", "window", "location", "reload", "html", "temp", "document", "createElement", "innerHTML", "textContent", "innerText", "formattedPhoneNumber", "goToBack", "navigate", "toggleSidebar", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "MessageService", "i3", "FormBuilder", "ConfirmationService", "i4", "ProspectsService", "selectors", "decls", "vars", "consts", "template", "ProspectsDetailsComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "ProspectsDetailsComponent_Template_p_dropdown_onChange_5_listener", "$event", "ɵɵtwoWayListener", "ProspectsDetailsComponent_Template_p_tabView_activeIndexChange_8_listener", "ɵɵtwoWayBindingSet", "ProspectsDetailsComponent_Template_p_tabView_onChange_8_listener", "ProspectsDetailsComponent_p_tabPanel_9_Template", "ProspectsDetailsComponent_Template_button_click_79_listener", "ProspectsDetailsComponent_Template_p_button_click_81_listener", "ɵɵtwoWayProperty", "ɵɵclassProp", "ɵɵpipeBind1", "ɵɵtextInterpolate"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\prospects-details\\prospects-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\prospects\\prospects-details\\prospects-details.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ProspectsService } from '../prospects.service';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { FormGroup, FormBuilder } from '@angular/forms';\r\n\r\ninterface Actions {\r\n  name: string;\r\n  code: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-prospects-details',\r\n  templateUrl: './prospects-details.component.html',\r\n  styleUrl: './prospects-details.component.scss',\r\n})\r\nexport class ProspectsDetailsComponent implements OnInit, OnDestroy {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public prospectDetails: any = null;\r\n  public sidebarDetails: any = null;\r\n  public NoteDetails: any = null;\r\n  public customerData: any = null;\r\n  public items: MenuItem[] = [];\r\n  public activeItem: MenuItem | null = null;\r\n  public home: MenuItem | any;\r\n  public breadcrumbitems: MenuItem[] = [];\r\n  public id: string = '';\r\n  public partner_role: string = '';\r\n  public bp_status: string = '';\r\n  public bp_doc_id: string = '';\r\n  public Actions: Actions[] = [];\r\n  public activeIndex: number = 0;\r\n  public isSidebarHidden = false;\r\n  public submitted = false;\r\n  public saving = false;\r\n\r\n  public GlobalNoteForm: FormGroup = this.formBuilder.group({\r\n    note: [''],\r\n  });\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private messageservice: MessageService,\r\n    private formBuilder: FormBuilder,\r\n    private confirmationservice: ConfirmationService,\r\n    private prospectsservice: ProspectsService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.id = this.route.snapshot.paramMap.get('id') || '';\r\n    this.prospectsservice\r\n      .getGlobalNote(this.id)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response) => {\r\n          if (Array.isArray(response?.data) && response.data.length > 0) {\r\n            this.NoteDetails = response.data[0];\r\n            this.GlobalNoteForm.patchValue({\r\n              note: this.stripHtml(response.data[0].note),\r\n            });\r\n          } else {\r\n            this.NoteDetails = {};\r\n            this.GlobalNoteForm.patchValue({\r\n              note: '',\r\n            });\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching global note:', error);\r\n        },\r\n      });\r\n\r\n    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };\r\n\r\n    this.makeMenuItems(this.id);\r\n\r\n    if (this.items.length > 0) {\r\n      this.activeItem = this.items[0];\r\n    }\r\n\r\n    this.setActiveTabFromURL();\r\n\r\n    this.route.paramMap\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((params) => {\r\n        const prospectId = params.get('id');\r\n        if (prospectId) {\r\n          this.loadProspectData(prospectId);\r\n        }\r\n      });\r\n\r\n    // Listen for route changes to keep active tab in sync\r\n    this.router.events.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\r\n      this.setActiveTabFromURL();\r\n    });\r\n\r\n    this.prospectsservice.prospect\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        const partner_role = response?.customer?.partner_functions?.find(\r\n          (p: any) => p.partner_function === 'YI'\r\n        );\r\n        this.partner_role =\r\n          partner_role?.business_partner?.bp_full_name || null;\r\n        this.prospectDetails = response || null;\r\n        this.sidebarDetails = this.formatSidebarDetails(\r\n          response?.addresses || []\r\n        );\r\n      });\r\n  }\r\n\r\n  makeMenuItems(id: string) {\r\n    this.items = [\r\n      { label: 'Overview', routerLink: `/store/prospects/${id}/overview` },\r\n      { label: 'Contacts', routerLink: `/store/prospects/${id}/contacts` },\r\n      { label: 'Sales Team', routerLink: `/store/prospects/${id}/sales-team` },\r\n      // {\r\n      //   label: 'AI Insights',\r\n      //   routerLink: `/store/prospects/${id}/ai-insights`,\r\n      // },\r\n      // {\r\n      //   label: 'Organization Data',\r\n      //   routerLink: `/store/prospects/${id}/organization-data`,\r\n      // },\r\n      {\r\n        label: 'Attachments',\r\n        routerLink: `/store/prospects/${id}/attachments`,\r\n      },\r\n      { label: 'Notes', routerLink: `/store/prospects/${id}/notes` },\r\n      { label: 'Activities', routerLink: `/store/prospects/${id}/activities` },\r\n    ];\r\n  }\r\n\r\n  setActiveTabFromURL() {\r\n    const fullPath = this.router.url;\r\n    const currentTab = fullPath.split('/').pop() || 'overview';\r\n\r\n    if (this.items.length === 0) return;\r\n\r\n    const foundIndex = this.items.findIndex((tab) =>\r\n      tab.routerLink.endsWith(currentTab)\r\n    );\r\n    this.activeIndex = foundIndex !== -1 ? foundIndex : 0;\r\n    this.activeItem = this.items[this.activeIndex] || this.items[0] || null;\r\n\r\n    this.updateBreadcrumb(this.activeItem?.label || 'Overview');\r\n  }\r\n\r\n  updateBreadcrumb(activeTab: string) {\r\n    this.breadcrumbitems = [\r\n      { label: 'Prospects', routerLink: ['/store/prospects'] },\r\n      { label: activeTab, routerLink: [] },\r\n    ];\r\n  }\r\n\r\n  onTabChange(event: { index: number }) {\r\n    if (this.items.length === 0) return;\r\n\r\n    this.activeIndex = event.index;\r\n    const selectedTab = this.items[this.activeIndex];\r\n\r\n    if (selectedTab?.routerLink) {\r\n      this.router.navigateByUrl(selectedTab.routerLink); // Ensure navigation occurs\r\n    }\r\n  }\r\n\r\n  private loadProspectData(prospectId: string): void {\r\n    this.prospectsservice\r\n      .getProspectByID(prospectId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          if (response) {\r\n            this.bp_doc_id = response?.data?.[0]?.documentId;\r\n            this.bp_status = response?.data?.[0]?.is_marked_for_archiving;\r\n            this.Actions = [\r\n              { name: 'Convert to Customer', code: 'CI' },\r\n              {\r\n                name: this.bp_status ? 'Set As Active' : 'Set As Obsolete',\r\n                code: this.bp_status ? 'SAA' : 'SAO',\r\n              },\r\n            ];\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching data:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  private formatSidebarDetails(addresses: any[]): any[] {\r\n    return addresses\r\n      .filter((address: any) =>\r\n        address?.address_usages?.some(\r\n          (usage: any) => usage.address_usage === 'XXDEFAULT'\r\n        )\r\n      )\r\n      .map((address: any) => ({\r\n        ...address,\r\n        address: [\r\n          address?.house_number,\r\n          address?.street_name,\r\n          address?.city_name,\r\n          address?.region,\r\n          address?.country,\r\n          address?.postal_code,\r\n        ]\r\n          .filter(Boolean)\r\n          .join(', '),\r\n        email_address: address?.emails?.[0]?.email_address || '-',\r\n        phone_number: (() => {\r\n          const phone = address?.phone_numbers?.[0];\r\n          if (!phone || !phone.phone_number) {\r\n            return '-';\r\n          }\r\n          const countryCode = phone.destination_location_country;\r\n          const rawNumber = phone.phone_number;\r\n          return this.prospectsservice.getDialCode(countryCode, rawNumber);\r\n        })(),\r\n        website_url: address?.home_page_urls?.[0]?.website_url || '-',\r\n      }));\r\n  }\r\n\r\n  onActionChange(event: any) {\r\n    const actionCode = event.value?.code;\r\n\r\n    const actionsMap: { [key: string]: () => void } = {\r\n      CI: () => this.ConvertToCustomer(event),\r\n      SAA: () => this.UpdateStatus(this.bp_doc_id, 'false'),\r\n      SAO: () => this.UpdateStatus(this.bp_doc_id, 'true'),\r\n    };\r\n\r\n    const action = actionsMap[actionCode];\r\n\r\n    if (action) {\r\n      this.confirmationservice.confirm({\r\n        message: 'Are you sure you want to proceed with this action?',\r\n        header: 'Confirm',\r\n        icon: 'pi pi-exclamation-triangle',\r\n        accept: action,\r\n      });\r\n    }\r\n  }\r\n\r\n  ConvertToCustomer(event: any) {\r\n    this.customerData = this.createBusinessPartnerObject(this.prospectDetails);\r\n    this.prospectsservice\r\n      .bpCreation(this.customerData)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Convert to Customer Successfully!',\r\n          });\r\n          this.prospectsservice\r\n            .getProspectByID(this.id)\r\n            .pipe(takeUntil(this.unsubscribe$))\r\n            .subscribe();\r\n        },\r\n        error: (error: any) => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail:\r\n              error?.error?.message || 'Error while processing your request.',\r\n          });\r\n          console.error('Error fetching data:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  createBusinessPartnerObject(data: any): any {\r\n    const formatAddress = (address: any) => ({\r\n      Country: address.country_code || 'US',\r\n      Region: address.region || '',\r\n      HouseNumber: address.house_number || '',\r\n      AdditionalStreetPrefixName: address.additional_street_prefix_name || '',\r\n      AdditionalStreetSuffixName: address.additional_street_suffix_name || '',\r\n      StreetName: address.street_name || '',\r\n      PostalCode: address.postal_code || '',\r\n      CityName: address.city_name || '',\r\n      PrfrdCommMediumType: address.prfrd_comm_medium_type || '',\r\n      Language: 'EN',\r\n      to_AddressUsage:\r\n        address.address_usages?.map((usage: any) => ({\r\n          AddressUsage: usage.address_usage || 'XXDEFAULT',\r\n        })) || [],\r\n      to_EmailAddress:\r\n        address.emails?.map((email: any) => ({\r\n          EmailAddress: email.email_address || '',\r\n        })) || [],\r\n      to_PhoneNumber:\r\n        address.phone_numbers\r\n          ?.filter((ph: any) => ph.phone_number_type === '1')\r\n          .map((ph: any) => ({ PhoneNumber: ph.phone_number || '' })) || [],\r\n      to_URLAddress:\r\n        address.home_page_urls?.map((url: any) => ({\r\n          WebsiteURL: url.website_url || '',\r\n        })) || [],\r\n      to_FaxNumber:\r\n        address.fax_numbers?.map((fax: any) => ({\r\n          FaxNumber: fax.fax_number || '',\r\n        })) || [],\r\n      to_MobilePhoneNumber:\r\n        address.phone_numbers\r\n          ?.filter((ph: any) => ph.phone_number_type === '3')\r\n          .map((ph: any) => ({ PhoneNumber: ph.phone_number || '' })) || [],\r\n    });\r\n\r\n    const mainPartner = {\r\n      BusinessPartner: data.bp_id || '',\r\n      Name1: data.bp_full_name || '',\r\n      Name2: '',\r\n      Name3: '',\r\n      CategoryCode: '2',\r\n      IsMarkedForArchiving: data?.is_marked_for_archiving || false,\r\n      to_BusinessPartnerAddress: data.addresses?.map(formatAddress) || [],\r\n      to_BusinessPartnerRole: [\r\n        { BusinessPartnerRole: 'FLCU01' },\r\n        { BusinessPartnerRole: 'FLCU00' },\r\n      ],\r\n      to_Customer: {\r\n        Customer: data.customer?.customer_id || '',\r\n        DeletedIndicator: false,\r\n        DeliveryIsBlockedForCustomer: 'ZN', // ZN means new customer code\r\n        to_CustomerSalesArea: (data.customer?.partner_functions || []).reduce(\r\n          (acc: any, pf: any) => {\r\n            const key = `${pf.sales_organization}-${pf.distribution_channel}-${pf.division}`;\r\n            if (!acc[key]) {\r\n              acc[key] = {\r\n                SalesOrganization: pf.sales_organization || '',\r\n                DistributionChannel: pf.distribution_channel || '',\r\n                Division: pf.division || '',\r\n                Currency: 'USD',\r\n                to_PartnerFunction: [],\r\n              };\r\n\r\n              // Add partner functions only once per SalesArea\r\n              acc[key].to_PartnerFunction.push(\r\n                ...['AG', 'RE', 'RG', 'WE'].map((func) => ({\r\n                  PartnerFunction: func,\r\n                  BPCustomerNumber: data.customer.customer_id || '',\r\n                }))\r\n              );\r\n\r\n              // Sales Team Data for Prospect to customer\r\n              acc[key].to_PartnerFunction\r\n                .filter(\r\n                  (pf: any) => pf.partner_function && pf.bp_customer_number\r\n                )\r\n                .push(\r\n                  ...data.customer.partner_functions.map((partner: any) => ({\r\n                    PartnerFunction: partner.partner_function || '',\r\n                    BPCustomerNumber: partner?.bp_customer_number || '',\r\n                  }))\r\n                );\r\n\r\n              // data.contact_companies?.forEach((company: any) => {\r\n              //   acc[key].to_PartnerFunction.push({\r\n              //     PartnerFunction: 'CP',\r\n              //     BPCustomerNumber: company?.bp_person_id || '',\r\n              //   });\r\n              // });\r\n            }\r\n            return acc;\r\n          },\r\n          {}\r\n        ),\r\n      },\r\n    };\r\n\r\n    mainPartner.to_Customer.to_CustomerSalesArea = Object.values(\r\n      mainPartner.to_Customer.to_CustomerSalesArea\r\n    );\r\n\r\n    const contacts = (data.contact_companies || []).map((company: any) => ({\r\n      BusinessPartner: company.bp_person_id || '',\r\n      Name1: company.business_partner_person?.first_name || '',\r\n      Name2: company.business_partner_person?.middle_name || '',\r\n      Name3: company.business_partner_person?.last_name || '',\r\n      CategoryCode: '1',\r\n      IsMarkedForArchiving:\r\n        company?.business_partner_person?.is_marked_for_archiving || false,\r\n      to_BusinessPartnerAddress:\r\n        company.business_partner_person?.addresses?.map(formatAddress) || [],\r\n      to_BusinessPartnerRole: [{ BusinessPartnerRole: 'BUP001' }],\r\n    }));\r\n\r\n    return {\r\n      BP: [mainPartner, ...contacts],\r\n      BP_Relationship: (data?.contact_companies || []).map((company: any) => ({\r\n        BusinessPartnerID: company?.bp_company_id || '',\r\n        RelationshipBusinessPartnerID: company?.bp_person_id || '',\r\n        ValidityStartDate: company?.validity_start_date || new Date(),\r\n        ValidityEndDate:\r\n          company?.validity_end_date || new Date('9999-12-29T23:59:59.000Z'),\r\n        RoleCode: 'BUR001',\r\n        ContactPerson: {\r\n          ContactPersonVipType: company?.person_func_and_dept\r\n            ?.contact_person_vip_type\r\n            ? '1'\r\n            : '',\r\n          ContactPersonDepartment:\r\n            company?.person_func_and_dept?.contact_person_department || '',\r\n          ContactPersonFunction:\r\n            company?.person_func_and_dept?.contact_person_function || '',\r\n        },\r\n      })),\r\n    };\r\n  }\r\n\r\n  async onNoteSubmit() {\r\n    this.submitted = true;\r\n\r\n    if (this.GlobalNoteForm.invalid) {\r\n      return;\r\n    }\r\n    this.saving = true;\r\n    const value = { ...this.GlobalNoteForm.value };\r\n\r\n    const data = {\r\n      note: value?.note,\r\n      bp_id: this?.id,\r\n      ...(!this.NoteDetails.documentId ? { is_global_note: true } : {}),\r\n    };\r\n\r\n    const apiCall =\r\n      this.NoteDetails && this.NoteDetails.documentId\r\n        ? this.prospectsservice.updateNote(this.NoteDetails.documentId, data) // Update if exists\r\n        : this.prospectsservice.createNote(data); // Create if not exists\r\n    apiCall.pipe(takeUntil(this.unsubscribe$)).subscribe({\r\n      next: () => {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: 'Prospect Note Updated SuccessFully!',\r\n        });\r\n\r\n        this.prospectsservice\r\n          .getProspectByID(this.id)\r\n          .pipe(takeUntil(this.unsubscribe$))\r\n          .subscribe();\r\n      },\r\n      error: () => {\r\n        this.saving = false;\r\n        this.messageservice.add({\r\n          severity: 'error',\r\n          detail: 'Error while processing your request.',\r\n        });\r\n      },\r\n    });\r\n  }\r\n\r\n  UpdateStatus(docid: any, status: any) {\r\n    const data = {\r\n      is_marked_for_archiving: status,\r\n    };\r\n    this.prospectsservice\r\n      .updateBpStatus(docid, data)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Action Updated Successfully!',\r\n          });\r\n\r\n          setTimeout(() => {\r\n            window.location.reload();\r\n          }, 1000);\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  stripHtml(html: string): string {\r\n    const temp = document.createElement('div');\r\n    temp.innerHTML = html;\r\n    return temp.textContent || temp.innerText || '';\r\n  }\r\n\r\n  get formattedPhoneNumber(): string {\r\n    const phone =\r\n      this.prospectDetails?.contact_companies?.[0]?.business_partner_person\r\n        ?.addresses?.[0]?.phone_numbers?.[0];\r\n\r\n    if (!phone || !phone.phone_number) {\r\n      return '-';\r\n    }\r\n\r\n    const countryCode = phone.destination_location_country;\r\n    const rawNumber = phone.phone_number;\r\n\r\n    return this.prospectsservice.getDialCode(countryCode, rawNumber);\r\n  }\r\n\r\n  goToBack() {\r\n    this.router.navigate(['/store/prospects']);\r\n  }\r\n\r\n  toggleSidebar() {\r\n    this.isSidebarHidden = !this.isSidebarHidden;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<p-toast position=\"top-center\" [life]=\"3000\"></p-toast>\r\n<div class=\"col-12 all-overview-body m-0 p-0 border-round-lg\">\r\n    <div class=\"filter-sec my-4 flex align-items-center justify-content-between h-3rem gap-3\">\r\n        <div class=\"breadcrumb-sec flex align-items-center gap-3\">\r\n            <!-- <p-button icon=\"pi pi-arrow-left\" class=\"p-button-primary p-back-button\" label=\"Back\"\r\n                (onClick)=\"goToBack()\"></p-button> -->\r\n            <p-breadcrumb [model]=\"breadcrumbitems\" [home]=\"home\" [styleClass]=\"'py-2 px-0 border-none'\" />\r\n        </div>\r\n        <p-dropdown [options]=\"Actions\" optionLabel=\"name\" (onChange)=\"onActionChange($event)\" placeholder=\"Action\"\r\n            [styleClass]=\"'w-13rem h-3rem px-2 py-1 bg-light-blue border-round-3xl border-none font-semibold'\" />\r\n    </div>\r\n    <div class=\"details-tabs-sec\">\r\n        <div class=\"details-tabs-list\">\r\n            <p-tabView [scrollable]=\"true\" [(activeIndex)]=\"activeIndex\" (onChange)=\"onTabChange($event)\">\r\n                <p-tabPanel *ngFor=\"let tab of items; let i = index\" [headerStyleClass]=\"'m-0 p-0'\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <a [routerLink]=\"tab.routerLink\"\r\n                            class=\"tab-link flex align-items-center justify-content-center white-space-nowrap\"\r\n                            routerLinkActive=\"active-tab\">\r\n                            {{ tab.label }}\r\n                        </a>\r\n                    </ng-template>\r\n                </p-tabPanel>\r\n            </p-tabView>\r\n\r\n        </div>\r\n        \r\n        <div class=\"details-tabs-result p-3 bg-whight-light\">\r\n            <div class=\"grid mt-0 relative flex-nowrap\">\r\n                <div class=\"col-12 lg:w-28rem md:w-28rem sm:w-full\" [class.sidebar-hide]=\"isSidebarHidden\">\r\n                    <div class=\"w-full bg-white border-round shadow-1 overflow-hidden\">\r\n                        <div class=\"left-side-bar-top p-4 bg-primary overflow-hidden\">\r\n                            <div class=\"flex align-items-start gap-4\">\r\n                                <div\r\n                                    class=\"flex align-items-center justify-content-center w-3rem h-3rem surface-0 border-circle\">\r\n                                    <h5 class=\"m-0 p-0 text-primary font-bold\">\r\n                                        {{ (prospectDetails?.bp_full_name || \"-\")  | initials }}\r\n                                    </h5>\r\n                                </div>\r\n                                <div class=\"flex flex-column gap-4 flex-1\">\r\n                                    <h5 class=\"mt-3 mb-1 font-semibold\">{{prospectDetails?.bp_full_name || \"-\"}}</h5>\r\n                                    <ul class=\"flex flex-column gap-3 p-0 m-0 list-none\">\r\n                                        <li class=\"flex align-items-center gap-2\"><span\r\n                                                class=\"flex w-9rem font-semibold\">CRM ID</span> :\r\n                                            {{prospectDetails?.bp_id || \"-\"}}\r\n                                        </li>\r\n                                        <!-- <li class=\"flex align-items-center gap-2\"><span class=\"flex w-9rem\">S4/HANA\r\n                                                ID</span> :\r\n                                            152ASD5585</li> -->\r\n                                        <li class=\"flex align-items-center gap-2\"><span\r\n                                                class=\"flex w-9rem font-semibold\">Account\r\n                                                Owner </span> :\r\n                                            {{partner_role || \"-\"}}</li>\r\n                                        <li class=\"flex align-items-center gap-2\"><span\r\n                                                class=\"flex w-9rem font-semibold\">Main\r\n                                                Contact</span> :\r\n                                            {{\r\n                                            (prospectDetails?.contact_companies?.[0]?.business_partner_person?.first_name\r\n                                            || \"-\") + \" \" +\r\n                                            (prospectDetails?.contact_companies?.[0]?.business_partner_person?.last_name\r\n                                            || \"-\") }}\r\n                                        </li>\r\n                                    </ul>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"left-side-bar-bottom px-3 py-4\">\r\n                            <ul class=\"flex flex-column gap-5 p-0 m-0 list-none\">\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">location_on</i> Address</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i> Phone</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.phone_number || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">phone_in_talk</i> Main Contact</span>\r\n                                    <span class=\"flex-1\">{{ formattedPhoneNumber }}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">mail</i>\r\n                                        Email</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.email_address || \"-\"}}</span>\r\n                                </li>\r\n                                <li class=\"flex gap-2 font-medium text-color-secondary align-items-start\">\r\n                                    <span class=\"flex w-10rem align-items-center gap-2 text-800 font-semibold\"><i\r\n                                            class=\"material-symbols-rounded\">language</i> Website</span>\r\n                                    <span class=\"flex-1\">{{sidebarDetails?.[0]?.website_url || \"-\"}}</span>\r\n                                </li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n                    <form [formGroup]=\"GlobalNoteForm\">\r\n                        <div class=\"w-full bg-white border-round shadow-1 overflow-hidden mt-5 p-3\">\r\n                            <h4 class=\"mb-3 font-semibold text-color\">Global Note</h4>\r\n                            <div class=\"flex flex-column gap-3\">\r\n                                <textarea formControlName=\"note\" rows=\"4\"\r\n                                    class=\"w-full h-8rem p-2 border-1 border-round\"\r\n                                    placeholder=\"Enter your note here...\"></textarea>\r\n                                <button pButton type=\"button\" (click)=\"onNoteSubmit()\" label=\"Save Note\"\r\n                                    class=\"p-button-rounded justify-content-center w-9rem h-3rem\"></button>\r\n                            </div>\r\n                        </div>\r\n                    </form>\r\n\r\n                </div>\r\n                <div class=\"col-12 lg:flex-1 md:flex-1 relative all-page-details\">\r\n                    <p-button icon=\"pi pi-angle-left\" [rounded]=\"true\" [outlined]=\"true\"\r\n                        [styleClass]=\"'p-0 w-2rem h-2rem border-2 surface-0'\"\r\n                        class=\"arrow-btn inline-flex absolute transition-all transition-duration-300 transition-ease-in-out\"\r\n                        (click)=\"toggleSidebar()\" [class.arrow-round]=\"isSidebarHidden\" />\r\n                    <router-outlet></router-outlet>\r\n                </div>\r\n                <!-- <div class=\"col-12 lg:flex-1 md:flex-1\">\r\n                    <router-outlet></router-outlet>\r\n                </div> -->\r\n            </div>\r\n        </div>\r\n\r\n    </div>\r\n</div>\r\n<p-confirmDialog class=\"confirm-popup\"></p-confirmDialog>"], "mappings": ";AAIA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;ICYjBC,EAAA,CAAAC,cAAA,YAEkC;IAC9BD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAJDH,EAAA,CAAAI,UAAA,eAAAC,MAAA,CAAAC,UAAA,CAA6B;IAG5BN,EAAA,CAAAO,SAAA,EACJ;IADIP,EAAA,CAAAQ,kBAAA,MAAAH,MAAA,CAAAI,KAAA,MACJ;;;;;IANRT,EAAA,CAAAC,cAAA,qBAAoF;IAChFD,EAAA,CAAAU,UAAA,IAAAC,6DAAA,0BAAgC;IAOpCX,EAAA,CAAAG,YAAA,EAAa;;;IARwCH,EAAA,CAAAI,UAAA,+BAA8B;;;ADInG,OAAM,MAAOQ,yBAAyB;EAwBpCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,mBAAwC,EACxCC,gBAAkC;IALlC,KAAAL,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IA7BlB,KAAAC,YAAY,GAAG,IAAItB,OAAO,EAAQ;IACnC,KAAAuB,eAAe,GAAQ,IAAI;IAC3B,KAAAC,cAAc,GAAQ,IAAI;IAC1B,KAAAC,WAAW,GAAQ,IAAI;IACvB,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,KAAK,GAAe,EAAE;IACtB,KAAAC,UAAU,GAAoB,IAAI;IAElC,KAAAC,eAAe,GAAe,EAAE;IAChC,KAAAC,EAAE,GAAW,EAAE;IACf,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,OAAO,GAAc,EAAE;IACvB,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,MAAM,GAAG,KAAK;IAEd,KAAAC,cAAc,GAAc,IAAI,CAACpB,WAAW,CAACqB,KAAK,CAAC;MACxDC,IAAI,EAAE,CAAC,EAAE;KACV,CAAC;EASC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACZ,EAAE,GAAG,IAAI,CAACd,KAAK,CAAC2B,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IACtD,IAAI,CAACxB,gBAAgB,CAClByB,aAAa,CAAC,IAAI,CAAChB,EAAE,CAAC,CACtBiB,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAClC0B,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIC,KAAK,CAACC,OAAO,CAACF,QAAQ,EAAEG,IAAI,CAAC,IAAIH,QAAQ,CAACG,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;UAC7D,IAAI,CAAC7B,WAAW,GAAGyB,QAAQ,CAACG,IAAI,CAAC,CAAC,CAAC;UACnC,IAAI,CAACd,cAAc,CAACgB,UAAU,CAAC;YAC7Bd,IAAI,EAAE,IAAI,CAACe,SAAS,CAACN,QAAQ,CAACG,IAAI,CAAC,CAAC,CAAC,CAACZ,IAAI;WAC3C,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAAChB,WAAW,GAAG,EAAE;UACrB,IAAI,CAACc,cAAc,CAACgB,UAAU,CAAC;YAC7Bd,IAAI,EAAE;WACP,CAAC;QACJ;MACF,CAAC;MACDgB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;KACD,CAAC;IAEJ,IAAI,CAACE,IAAI,GAAG;MAAEC,IAAI,EAAE,oBAAoB;MAAEpD,UAAU,EAAE,CAAC,GAAG;IAAC,CAAE;IAE7D,IAAI,CAACqD,aAAa,CAAC,IAAI,CAAC/B,EAAE,CAAC;IAE3B,IAAI,IAAI,CAACH,KAAK,CAAC2B,MAAM,GAAG,CAAC,EAAE;MACzB,IAAI,CAAC1B,UAAU,GAAG,IAAI,CAACD,KAAK,CAAC,CAAC,CAAC;IACjC;IAEA,IAAI,CAACmC,mBAAmB,EAAE;IAE1B,IAAI,CAAC9C,KAAK,CAAC4B,QAAQ,CAChBG,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAClC0B,SAAS,CAAEe,MAAM,IAAI;MACpB,MAAMC,UAAU,GAAGD,MAAM,CAAClB,GAAG,CAAC,IAAI,CAAC;MACnC,IAAImB,UAAU,EAAE;QACd,IAAI,CAACC,gBAAgB,CAACD,UAAU,CAAC;MACnC;IACF,CAAC,CAAC;IAEJ;IACA,IAAI,CAAC/C,MAAM,CAACiD,MAAM,CAACnB,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAAC0B,SAAS,CAAC,MAAK;MACnE,IAAI,CAACc,mBAAmB,EAAE;IAC5B,CAAC,CAAC;IAEF,IAAI,CAACzC,gBAAgB,CAAC8C,QAAQ,CAC3BpB,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAClC0B,SAAS,CAAEE,QAAa,IAAI;MAC3B,MAAMnB,YAAY,GAAGmB,QAAQ,EAAEkB,QAAQ,EAAEC,iBAAiB,EAAEC,IAAI,CAC7DC,CAAM,IAAKA,CAAC,CAACC,gBAAgB,KAAK,IAAI,CACxC;MACD,IAAI,CAACzC,YAAY,GACfA,YAAY,EAAE0C,gBAAgB,EAAEC,YAAY,IAAI,IAAI;MACtD,IAAI,CAACnD,eAAe,GAAG2B,QAAQ,IAAI,IAAI;MACvC,IAAI,CAAC1B,cAAc,GAAG,IAAI,CAACmD,oBAAoB,CAC7CzB,QAAQ,EAAE0B,SAAS,IAAI,EAAE,CAC1B;IACH,CAAC,CAAC;EACN;EAEAf,aAAaA,CAAC/B,EAAU;IACtB,IAAI,CAACH,KAAK,GAAG,CACX;MAAEhB,KAAK,EAAE,UAAU;MAAEH,UAAU,EAAE,oBAAoBsB,EAAE;IAAW,CAAE,EACpE;MAAEnB,KAAK,EAAE,UAAU;MAAEH,UAAU,EAAE,oBAAoBsB,EAAE;IAAW,CAAE,EACpE;MAAEnB,KAAK,EAAE,YAAY;MAAEH,UAAU,EAAE,oBAAoBsB,EAAE;IAAa,CAAE;IACxE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACEnB,KAAK,EAAE,aAAa;MACpBH,UAAU,EAAE,oBAAoBsB,EAAE;KACnC,EACD;MAAEnB,KAAK,EAAE,OAAO;MAAEH,UAAU,EAAE,oBAAoBsB,EAAE;IAAQ,CAAE,EAC9D;MAAEnB,KAAK,EAAE,YAAY;MAAEH,UAAU,EAAE,oBAAoBsB,EAAE;IAAa,CAAE,CACzE;EACH;EAEAgC,mBAAmBA,CAAA;IACjB,MAAMe,QAAQ,GAAG,IAAI,CAAC5D,MAAM,CAAC6D,GAAG;IAChC,MAAMC,UAAU,GAAGF,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,IAAI,UAAU;IAE1D,IAAI,IAAI,CAACtD,KAAK,CAAC2B,MAAM,KAAK,CAAC,EAAE;IAE7B,MAAM4B,UAAU,GAAG,IAAI,CAACvD,KAAK,CAACwD,SAAS,CAAEC,GAAG,IAC1CA,GAAG,CAAC5E,UAAU,CAAC6E,QAAQ,CAACN,UAAU,CAAC,CACpC;IACD,IAAI,CAAC5C,WAAW,GAAG+C,UAAU,KAAK,CAAC,CAAC,GAAGA,UAAU,GAAG,CAAC;IACrD,IAAI,CAACtD,UAAU,GAAG,IAAI,CAACD,KAAK,CAAC,IAAI,CAACQ,WAAW,CAAC,IAAI,IAAI,CAACR,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;IAEvE,IAAI,CAAC2D,gBAAgB,CAAC,IAAI,CAAC1D,UAAU,EAAEjB,KAAK,IAAI,UAAU,CAAC;EAC7D;EAEA2E,gBAAgBA,CAACC,SAAiB;IAChC,IAAI,CAAC1D,eAAe,GAAG,CACrB;MAAElB,KAAK,EAAE,WAAW;MAAEH,UAAU,EAAE,CAAC,kBAAkB;IAAC,CAAE,EACxD;MAAEG,KAAK,EAAE4E,SAAS;MAAE/E,UAAU,EAAE;IAAE,CAAE,CACrC;EACH;EAEAgF,WAAWA,CAACC,KAAwB;IAClC,IAAI,IAAI,CAAC9D,KAAK,CAAC2B,MAAM,KAAK,CAAC,EAAE;IAE7B,IAAI,CAACnB,WAAW,GAAGsD,KAAK,CAACC,KAAK;IAC9B,MAAMC,WAAW,GAAG,IAAI,CAAChE,KAAK,CAAC,IAAI,CAACQ,WAAW,CAAC;IAEhD,IAAIwD,WAAW,EAAEnF,UAAU,EAAE;MAC3B,IAAI,CAACS,MAAM,CAAC2E,aAAa,CAACD,WAAW,CAACnF,UAAU,CAAC,CAAC,CAAC;IACrD;EACF;EAEQyD,gBAAgBA,CAACD,UAAkB;IACzC,IAAI,CAAC3C,gBAAgB,CAClBwE,eAAe,CAAC7B,UAAU,CAAC,CAC3BjB,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAClC0B,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAACjB,SAAS,GAAGiB,QAAQ,EAAEG,IAAI,GAAG,CAAC,CAAC,EAAEyC,UAAU;UAChD,IAAI,CAAC9D,SAAS,GAAGkB,QAAQ,EAAEG,IAAI,GAAG,CAAC,CAAC,EAAE0C,uBAAuB;UAC7D,IAAI,CAAC7D,OAAO,GAAG,CACb;YAAE8D,IAAI,EAAE,qBAAqB;YAAEC,IAAI,EAAE;UAAI,CAAE,EAC3C;YACED,IAAI,EAAE,IAAI,CAAChE,SAAS,GAAG,eAAe,GAAG,iBAAiB;YAC1DiE,IAAI,EAAE,IAAI,CAACjE,SAAS,GAAG,KAAK,GAAG;WAChC,CACF;QACH;MACF,CAAC;MACDyB,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;KACD,CAAC;EACN;EAEQkB,oBAAoBA,CAACC,SAAgB;IAC3C,OAAOA,SAAS,CACbsB,MAAM,CAAEC,OAAY,IACnBA,OAAO,EAAEC,cAAc,EAAEC,IAAI,CAC1BC,KAAU,IAAKA,KAAK,CAACC,aAAa,KAAK,WAAW,CACpD,CACF,CACAC,GAAG,CAAEL,OAAY,KAAM;MACtB,GAAGA,OAAO;MACVA,OAAO,EAAE,CACPA,OAAO,EAAEM,YAAY,EACrBN,OAAO,EAAEO,WAAW,EACpBP,OAAO,EAAEQ,SAAS,EAClBR,OAAO,EAAES,MAAM,EACfT,OAAO,EAAEU,OAAO,EAChBV,OAAO,EAAEW,WAAW,CACrB,CACEZ,MAAM,CAACa,OAAO,CAAC,CACfC,IAAI,CAAC,IAAI,CAAC;MACbC,aAAa,EAAEd,OAAO,EAAEe,MAAM,GAAG,CAAC,CAAC,EAAED,aAAa,IAAI,GAAG;MACzDE,YAAY,EAAE,CAAC,MAAK;QAClB,MAAMC,KAAK,GAAGjB,OAAO,EAAEkB,aAAa,GAAG,CAAC,CAAC;QACzC,IAAI,CAACD,KAAK,IAAI,CAACA,KAAK,CAACD,YAAY,EAAE;UACjC,OAAO,GAAG;QACZ;QACA,MAAMG,WAAW,GAAGF,KAAK,CAACG,4BAA4B;QACtD,MAAMC,SAAS,GAAGJ,KAAK,CAACD,YAAY;QACpC,OAAO,IAAI,CAAC9F,gBAAgB,CAACoG,WAAW,CAACH,WAAW,EAAEE,SAAS,CAAC;MAClE,CAAC,EAAC,CAAE;MACJE,WAAW,EAAEvB,OAAO,EAAEwB,cAAc,GAAG,CAAC,CAAC,EAAED,WAAW,IAAI;KAC3D,CAAC,CAAC;EACP;EAEAE,cAAcA,CAACnC,KAAU;IACvB,MAAMoC,UAAU,GAAGpC,KAAK,CAACqC,KAAK,EAAE7B,IAAI;IAEpC,MAAM8B,UAAU,GAAkC;MAChDC,EAAE,EAAEA,CAAA,KAAM,IAAI,CAACC,iBAAiB,CAACxC,KAAK,CAAC;MACvCyC,GAAG,EAAEA,CAAA,KAAM,IAAI,CAACC,YAAY,CAAC,IAAI,CAAClG,SAAS,EAAE,OAAO,CAAC;MACrDmG,GAAG,EAAEA,CAAA,KAAM,IAAI,CAACD,YAAY,CAAC,IAAI,CAAClG,SAAS,EAAE,MAAM;KACpD;IAED,MAAMoG,MAAM,GAAGN,UAAU,CAACF,UAAU,CAAC;IAErC,IAAIQ,MAAM,EAAE;MACV,IAAI,CAACjH,mBAAmB,CAACkH,OAAO,CAAC;QAC/BC,OAAO,EAAE,oDAAoD;QAC7DC,MAAM,EAAE,SAAS;QACjB5E,IAAI,EAAE,4BAA4B;QAClC6E,MAAM,EAAEJ;OACT,CAAC;IACJ;EACF;EAEAJ,iBAAiBA,CAACxC,KAAU;IAC1B,IAAI,CAAC/D,YAAY,GAAG,IAAI,CAACgH,2BAA2B,CAAC,IAAI,CAACnH,eAAe,CAAC;IAC1E,IAAI,CAACF,gBAAgB,CAClBsH,UAAU,CAAC,IAAI,CAACjH,YAAY,CAAC,CAC7BqB,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAClC0B,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAAChC,cAAc,CAAC0H,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACzH,gBAAgB,CAClBwE,eAAe,CAAC,IAAI,CAAC/D,EAAE,CAAC,CACxBiB,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAClC0B,SAAS,EAAE;MAChB,CAAC;MACDS,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAACvC,cAAc,CAAC0H,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EACJrF,KAAK,EAAEA,KAAK,EAAE8E,OAAO,IAAI;SAC5B,CAAC;QACF7E,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;KACD,CAAC;EACN;EAEAiF,2BAA2BA,CAACrF,IAAS;IACnC,MAAM0F,aAAa,GAAI5C,OAAY,KAAM;MACvC6C,OAAO,EAAE7C,OAAO,CAAC8C,YAAY,IAAI,IAAI;MACrCC,MAAM,EAAE/C,OAAO,CAACS,MAAM,IAAI,EAAE;MAC5BuC,WAAW,EAAEhD,OAAO,CAACM,YAAY,IAAI,EAAE;MACvC2C,0BAA0B,EAAEjD,OAAO,CAACkD,6BAA6B,IAAI,EAAE;MACvEC,0BAA0B,EAAEnD,OAAO,CAACoD,6BAA6B,IAAI,EAAE;MACvEC,UAAU,EAAErD,OAAO,CAACO,WAAW,IAAI,EAAE;MACrC+C,UAAU,EAAEtD,OAAO,CAACW,WAAW,IAAI,EAAE;MACrC4C,QAAQ,EAAEvD,OAAO,CAACQ,SAAS,IAAI,EAAE;MACjCgD,mBAAmB,EAAExD,OAAO,CAACyD,sBAAsB,IAAI,EAAE;MACzDC,QAAQ,EAAE,IAAI;MACdC,eAAe,EACb3D,OAAO,CAACC,cAAc,EAAEI,GAAG,CAAEF,KAAU,KAAM;QAC3CyD,YAAY,EAAEzD,KAAK,CAACC,aAAa,IAAI;OACtC,CAAC,CAAC,IAAI,EAAE;MACXyD,eAAe,EACb7D,OAAO,CAACe,MAAM,EAAEV,GAAG,CAAEyD,KAAU,KAAM;QACnCC,YAAY,EAAED,KAAK,CAAChD,aAAa,IAAI;OACtC,CAAC,CAAC,IAAI,EAAE;MACXkD,cAAc,EACZhE,OAAO,CAACkB,aAAa,EACjBnB,MAAM,CAAEkE,EAAO,IAAKA,EAAE,CAACC,iBAAiB,KAAK,GAAG,CAAC,CAClD7D,GAAG,CAAE4D,EAAO,KAAM;QAAEE,WAAW,EAAEF,EAAE,CAACjD,YAAY,IAAI;MAAE,CAAE,CAAC,CAAC,IAAI,EAAE;MACrEoD,aAAa,EACXpE,OAAO,CAACwB,cAAc,EAAEnB,GAAG,CAAE1B,GAAQ,KAAM;QACzC0F,UAAU,EAAE1F,GAAG,CAAC4C,WAAW,IAAI;OAChC,CAAC,CAAC,IAAI,EAAE;MACX+C,YAAY,EACVtE,OAAO,CAACuE,WAAW,EAAElE,GAAG,CAAEmE,GAAQ,KAAM;QACtCC,SAAS,EAAED,GAAG,CAACE,UAAU,IAAI;OAC9B,CAAC,CAAC,IAAI,EAAE;MACXC,oBAAoB,EAClB3E,OAAO,CAACkB,aAAa,EACjBnB,MAAM,CAAEkE,EAAO,IAAKA,EAAE,CAACC,iBAAiB,KAAK,GAAG,CAAC,CAClD7D,GAAG,CAAE4D,EAAO,KAAM;QAAEE,WAAW,EAAEF,EAAE,CAACjD,YAAY,IAAI;MAAE,CAAE,CAAC,CAAC,IAAI;KACpE,CAAC;IAEF,MAAM4D,WAAW,GAAG;MAClBC,eAAe,EAAE3H,IAAI,CAAC4H,KAAK,IAAI,EAAE;MACjCC,KAAK,EAAE7H,IAAI,CAACqB,YAAY,IAAI,EAAE;MAC9ByG,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,YAAY,EAAE,GAAG;MACjBC,oBAAoB,EAAEjI,IAAI,EAAE0C,uBAAuB,IAAI,KAAK;MAC5DwF,yBAAyB,EAAElI,IAAI,CAACuB,SAAS,EAAE4B,GAAG,CAACuC,aAAa,CAAC,IAAI,EAAE;MACnEyC,sBAAsB,EAAE,CACtB;QAAEC,mBAAmB,EAAE;MAAQ,CAAE,EACjC;QAAEA,mBAAmB,EAAE;MAAQ,CAAE,CAClC;MACDC,WAAW,EAAE;QACXC,QAAQ,EAAEtI,IAAI,CAACe,QAAQ,EAAEwH,WAAW,IAAI,EAAE;QAC1CC,gBAAgB,EAAE,KAAK;QACvBC,4BAA4B,EAAE,IAAI;QAAE;QACpCC,oBAAoB,EAAE,CAAC1I,IAAI,CAACe,QAAQ,EAAEC,iBAAiB,IAAI,EAAE,EAAE2H,MAAM,CACnE,CAACC,GAAQ,EAAEC,EAAO,KAAI;UACpB,MAAMC,GAAG,GAAG,GAAGD,EAAE,CAACE,kBAAkB,IAAIF,EAAE,CAACG,oBAAoB,IAAIH,EAAE,CAACI,QAAQ,EAAE;UAChF,IAAI,CAACL,GAAG,CAACE,GAAG,CAAC,EAAE;YACbF,GAAG,CAACE,GAAG,CAAC,GAAG;cACTI,iBAAiB,EAAEL,EAAE,CAACE,kBAAkB,IAAI,EAAE;cAC9CI,mBAAmB,EAAEN,EAAE,CAACG,oBAAoB,IAAI,EAAE;cAClDI,QAAQ,EAAEP,EAAE,CAACI,QAAQ,IAAI,EAAE;cAC3BI,QAAQ,EAAE,KAAK;cACfC,kBAAkB,EAAE;aACrB;YAED;YACAV,GAAG,CAACE,GAAG,CAAC,CAACQ,kBAAkB,CAACC,IAAI,CAC9B,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAACpG,GAAG,CAAEqG,IAAI,KAAM;cACzCC,eAAe,EAAED,IAAI;cACrBE,gBAAgB,EAAE1J,IAAI,CAACe,QAAQ,CAACwH,WAAW,IAAI;aAChD,CAAC,CAAC,CACJ;YAED;YACAK,GAAG,CAACE,GAAG,CAAC,CAACQ,kBAAkB,CACxBzG,MAAM,CACJgG,EAAO,IAAKA,EAAE,CAAC1H,gBAAgB,IAAI0H,EAAE,CAACc,kBAAkB,CAC1D,CACAJ,IAAI,CACH,GAAGvJ,IAAI,CAACe,QAAQ,CAACC,iBAAiB,CAACmC,GAAG,CAAEyG,OAAY,KAAM;cACxDH,eAAe,EAAEG,OAAO,CAACzI,gBAAgB,IAAI,EAAE;cAC/CuI,gBAAgB,EAAEE,OAAO,EAAED,kBAAkB,IAAI;aAClD,CAAC,CAAC,CACJ;YAEH;YACA;YACA;YACA;YACA;YACA;UACF;UACA,OAAOf,GAAG;QACZ,CAAC,EACD,EAAE;;KAGP;IAEDlB,WAAW,CAACW,WAAW,CAACK,oBAAoB,GAAGmB,MAAM,CAACC,MAAM,CAC1DpC,WAAW,CAACW,WAAW,CAACK,oBAAoB,CAC7C;IAED,MAAMqB,QAAQ,GAAG,CAAC/J,IAAI,CAACgK,iBAAiB,IAAI,EAAE,EAAE7G,GAAG,CAAE8G,OAAY,KAAM;MACrEtC,eAAe,EAAEsC,OAAO,CAACC,YAAY,IAAI,EAAE;MAC3CrC,KAAK,EAAEoC,OAAO,CAACE,uBAAuB,EAAEC,UAAU,IAAI,EAAE;MACxDtC,KAAK,EAAEmC,OAAO,CAACE,uBAAuB,EAAEE,WAAW,IAAI,EAAE;MACzDtC,KAAK,EAAEkC,OAAO,CAACE,uBAAuB,EAAEG,SAAS,IAAI,EAAE;MACvDtC,YAAY,EAAE,GAAG;MACjBC,oBAAoB,EAClBgC,OAAO,EAAEE,uBAAuB,EAAEzH,uBAAuB,IAAI,KAAK;MACpEwF,yBAAyB,EACvB+B,OAAO,CAACE,uBAAuB,EAAE5I,SAAS,EAAE4B,GAAG,CAACuC,aAAa,CAAC,IAAI,EAAE;MACtEyC,sBAAsB,EAAE,CAAC;QAAEC,mBAAmB,EAAE;MAAQ,CAAE;KAC3D,CAAC,CAAC;IAEH,OAAO;MACLmC,EAAE,EAAE,CAAC7C,WAAW,EAAE,GAAGqC,QAAQ,CAAC;MAC9BS,eAAe,EAAE,CAACxK,IAAI,EAAEgK,iBAAiB,IAAI,EAAE,EAAE7G,GAAG,CAAE8G,OAAY,KAAM;QACtEQ,iBAAiB,EAAER,OAAO,EAAES,aAAa,IAAI,EAAE;QAC/CC,6BAA6B,EAAEV,OAAO,EAAEC,YAAY,IAAI,EAAE;QAC1DU,iBAAiB,EAAEX,OAAO,EAAEY,mBAAmB,IAAI,IAAIC,IAAI,EAAE;QAC7DC,eAAe,EACbd,OAAO,EAAEe,iBAAiB,IAAI,IAAIF,IAAI,CAAC,0BAA0B,CAAC;QACpEG,QAAQ,EAAE,QAAQ;QAClBC,aAAa,EAAE;UACbC,oBAAoB,EAAElB,OAAO,EAAEmB,oBAAoB,EAC/CC,uBAAuB,GACvB,GAAG,GACH,EAAE;UACNC,uBAAuB,EACrBrB,OAAO,EAAEmB,oBAAoB,EAAEG,yBAAyB,IAAI,EAAE;UAChEC,qBAAqB,EACnBvB,OAAO,EAAEmB,oBAAoB,EAAEK,uBAAuB,IAAI;;OAE/D,CAAC;KACH;EACH;EAEMC,YAAYA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChBD,KAAI,CAAC3M,SAAS,GAAG,IAAI;MAErB,IAAI2M,KAAI,CAACzM,cAAc,CAAC2M,OAAO,EAAE;QAC/B;MACF;MACAF,KAAI,CAAC1M,MAAM,GAAG,IAAI;MAClB,MAAMwF,KAAK,GAAG;QAAE,GAAGkH,KAAI,CAACzM,cAAc,CAACuF;MAAK,CAAE;MAE9C,MAAMzE,IAAI,GAAG;QACXZ,IAAI,EAAEqF,KAAK,EAAErF,IAAI;QACjBwI,KAAK,EAAE+D,KAAI,EAAElN,EAAE;QACf,IAAI,CAACkN,KAAI,CAACvN,WAAW,CAACqE,UAAU,GAAG;UAAEqJ,cAAc,EAAE;QAAI,CAAE,GAAG,EAAE;OACjE;MAED,MAAMC,OAAO,GACXJ,KAAI,CAACvN,WAAW,IAAIuN,KAAI,CAACvN,WAAW,CAACqE,UAAU,GAC3CkJ,KAAI,CAAC3N,gBAAgB,CAACgO,UAAU,CAACL,KAAI,CAACvN,WAAW,CAACqE,UAAU,EAAEzC,IAAI,CAAC,CAAC;MAAA,EACpE2L,KAAI,CAAC3N,gBAAgB,CAACiO,UAAU,CAACjM,IAAI,CAAC,CAAC,CAAC;MAC9C+L,OAAO,CAACrM,IAAI,CAAC9C,SAAS,CAAC+O,KAAI,CAAC1N,YAAY,CAAC,CAAC,CAAC0B,SAAS,CAAC;QACnDC,IAAI,EAAEA,CAAA,KAAK;UACT+L,KAAI,CAAC9N,cAAc,CAAC0H,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UAEFkG,KAAI,CAAC3N,gBAAgB,CAClBwE,eAAe,CAACmJ,KAAI,CAAClN,EAAE,CAAC,CACxBiB,IAAI,CAAC9C,SAAS,CAAC+O,KAAI,CAAC1N,YAAY,CAAC,CAAC,CAClC0B,SAAS,EAAE;QAChB,CAAC;QACDS,KAAK,EAAEA,CAAA,KAAK;UACVuL,KAAI,CAAC1M,MAAM,GAAG,KAAK;UACnB0M,KAAI,CAAC9N,cAAc,CAAC0H,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACL;EAEAX,YAAYA,CAACoH,KAAU,EAAEC,MAAW;IAClC,MAAMnM,IAAI,GAAG;MACX0C,uBAAuB,EAAEyJ;KAC1B;IACD,IAAI,CAACnO,gBAAgB,CAClBoO,cAAc,CAACF,KAAK,EAAElM,IAAI,CAAC,CAC3BN,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAACqB,YAAY,CAAC,CAAC,CAClC0B,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC/B,cAAc,CAAC0H,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QAEF4G,UAAU,CAAC,MAAK;UACdC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDpM,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACvC,cAAc,CAAC0H,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAtF,SAASA,CAACsM,IAAY;IACpB,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC1CF,IAAI,CAACG,SAAS,GAAGJ,IAAI;IACrB,OAAOC,IAAI,CAACI,WAAW,IAAIJ,IAAI,CAACK,SAAS,IAAI,EAAE;EACjD;EAEA,IAAIC,oBAAoBA,CAAA;IACtB,MAAMjJ,KAAK,GACT,IAAI,CAAC7F,eAAe,EAAE8L,iBAAiB,GAAG,CAAC,CAAC,EAAEG,uBAAuB,EACjE5I,SAAS,GAAG,CAAC,CAAC,EAAEyC,aAAa,GAAG,CAAC,CAAC;IAExC,IAAI,CAACD,KAAK,IAAI,CAACA,KAAK,CAACD,YAAY,EAAE;MACjC,OAAO,GAAG;IACZ;IAEA,MAAMG,WAAW,GAAGF,KAAK,CAACG,4BAA4B;IACtD,MAAMC,SAAS,GAAGJ,KAAK,CAACD,YAAY;IAEpC,OAAO,IAAI,CAAC9F,gBAAgB,CAACoG,WAAW,CAACH,WAAW,EAAEE,SAAS,CAAC;EAClE;EAEA8I,QAAQA,CAAA;IACN,IAAI,CAACrP,MAAM,CAACsP,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACpO,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAqO,WAAWA,CAAA;IACT,IAAI,CAACnP,YAAY,CAAC2B,IAAI,EAAE;IACxB,IAAI,CAAC3B,YAAY,CAACoP,QAAQ,EAAE;EAC9B;;;uBAhfW5P,yBAAyB,EAAAZ,EAAA,CAAAyQ,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA3Q,EAAA,CAAAyQ,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA5Q,EAAA,CAAAyQ,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAA9Q,EAAA,CAAAyQ,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAhR,EAAA,CAAAyQ,iBAAA,CAAAI,EAAA,CAAAI,mBAAA,GAAAjR,EAAA,CAAAyQ,iBAAA,CAAAS,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAzBvQ,yBAAyB;MAAAwQ,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClBtC1R,EAAA,CAAA4R,SAAA,iBAAuD;UAG/C5R,EAFR,CAAAC,cAAA,aAA8D,aACgC,aAC5B;UAGtDD,EAAA,CAAA4R,SAAA,sBAA+F;UACnG5R,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,oBACyG;UADtDD,EAAA,CAAA6R,UAAA,sBAAAC,kEAAAC,MAAA;YAAA,OAAYJ,GAAA,CAAAjK,cAAA,CAAAqK,MAAA,CAAsB;UAAA,EAAC;UAE1F/R,EAFI,CAAAG,YAAA,EACyG,EACvG;UAGEH,EAFR,CAAAC,cAAA,aAA8B,aACK,mBACmE;UAA/DD,EAAA,CAAAgS,gBAAA,+BAAAC,0EAAAF,MAAA;YAAA/R,EAAA,CAAAkS,kBAAA,CAAAP,GAAA,CAAA1P,WAAA,EAAA8P,MAAA,MAAAJ,GAAA,CAAA1P,WAAA,GAAA8P,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAAC/R,EAAA,CAAA6R,UAAA,sBAAAM,iEAAAJ,MAAA;YAAA,OAAYJ,GAAA,CAAArM,WAAA,CAAAyM,MAAA,CAAmB;UAAA,EAAC;UACzF/R,EAAA,CAAAU,UAAA,IAAA0R,+CAAA,wBAAoF;UAW5FpS,EAFI,CAAAG,YAAA,EAAY,EAEV;UAUsBH,EAR5B,CAAAC,cAAA,eAAqD,eACL,eACmD,eACpB,eACD,eAChB,eAE2D,cAClD;UACvCD,EAAA,CAAAE,MAAA,IACJ;;UACJF,EADI,CAAAG,YAAA,EAAK,EACH;UAEFH,EADJ,CAAAC,cAAA,eAA2C,cACH;UAAAD,EAAA,CAAAE,MAAA,IAAwC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEnCH,EAD9C,CAAAC,cAAA,cAAqD,cACP,gBACA;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IAExD;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIqCH,EAA1C,CAAAC,cAAA,cAA0C,gBACA;UAAAD,EAAA,CAAAE,MAAA,sBAC5B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IACK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACUH,EAA1C,CAAAC,cAAA,cAA0C,gBACA;UAAAD,EAAA,CAAAE,MAAA,oBAC3B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,IAMvB;UAIhBF,EAJgB,CAAAG,YAAA,EAAK,EACJ,EACH,EACJ,EACJ;UAIiFH,EAHvF,CAAAC,cAAA,eAA4C,cACa,cACyB,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvEH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAAuC;UAChEF,EADgE,CAAAG,YAAA,EAAO,EAClE;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvEH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA4C;UACrEF,EADqE,CAAAG,YAAA,EAAO,EACvE;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9EH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA0B;UACnDF,EADmD,CAAAG,YAAA,EAAO,EACrD;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC7CH,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChBH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA6C;UACtEF,EADsE,CAAAG,YAAA,EAAO,EACxE;UAE0EH,EAD/E,CAAAC,cAAA,cAA0E,gBACK,aAClC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpEH,EAAA,CAAAC,cAAA,gBAAqB;UAAAD,EAAA,CAAAE,MAAA,IAA2C;UAIhFF,EAJgF,CAAAG,YAAA,EAAO,EACtE,EACJ,EACH,EACJ;UAGEH,EAFR,CAAAC,cAAA,gBAAmC,eAC6C,cAC9B;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1DH,EAAA,CAAAC,cAAA,eAAoC;UAChCD,EAAA,CAAA4R,SAAA,oBAEqD;UACrD5R,EAAA,CAAAC,cAAA,kBACkE;UADpCD,EAAA,CAAA6R,UAAA,mBAAAQ,4DAAA;YAAA,OAASV,GAAA,CAAA9C,YAAA,EAAc;UAAA,EAAC;UAMtE7O,EALkF,CAAAG,YAAA,EAAS,EACzE,EACJ,EACH,EAEL;UAEFH,EADJ,CAAAC,cAAA,eAAkE,oBAIQ;UAAlED,EAAA,CAAA6R,UAAA,mBAAAS,8DAAA;YAAA,OAASX,GAAA,CAAArB,aAAA,EAAe;UAAA,EAAC;UAH7BtQ,EAAA,CAAAG,YAAA,EAGsE;UACtEH,EAAA,CAAA4R,SAAA,qBAA+B;UASnD5R,EARgB,CAAAG,YAAA,EAAM,EAIJ,EACJ,EAEJ,EACJ;UACNH,EAAA,CAAA4R,SAAA,2BAAyD;;;UA9H1B5R,EAAA,CAAAI,UAAA,cAAa;UAMlBJ,EAAA,CAAAO,SAAA,GAAyB;UAAeP,EAAxC,CAAAI,UAAA,UAAAuR,GAAA,CAAAhQ,eAAA,CAAyB,SAAAgQ,GAAA,CAAAlO,IAAA,CAAc,uCAAuC;UAEpFzD,EAAA,CAAAO,SAAA,EAAmB;UAC3BP,EADQ,CAAAI,UAAA,YAAAuR,GAAA,CAAA3P,OAAA,CAAmB,mGACuE;UAIvFhC,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,oBAAmB;UAACJ,EAAA,CAAAuS,gBAAA,gBAAAZ,GAAA,CAAA1P,WAAA,CAA6B;UAC5BjC,EAAA,CAAAO,SAAA,EAAU;UAAVP,EAAA,CAAAI,UAAA,YAAAuR,GAAA,CAAAlQ,KAAA,CAAU;UAeczB,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAwS,WAAA,iBAAAb,GAAA,CAAAzP,eAAA,CAAsC;UAOlElC,EAAA,CAAAO,SAAA,GACJ;UADIP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAyS,WAAA,UAAAd,GAAA,CAAAtQ,eAAA,kBAAAsQ,GAAA,CAAAtQ,eAAA,CAAAmD,YAAA,eACJ;UAGoCxE,EAAA,CAAAO,SAAA,GAAwC;UAAxCP,EAAA,CAAA0S,iBAAA,EAAAf,GAAA,CAAAtQ,eAAA,kBAAAsQ,GAAA,CAAAtQ,eAAA,CAAAmD,YAAA,SAAwC;UAGhBxE,EAAA,CAAAO,SAAA,GAExD;UAFwDP,EAAA,CAAAQ,kBAAA,SAAAmR,GAAA,CAAAtQ,eAAA,kBAAAsQ,GAAA,CAAAtQ,eAAA,CAAA0J,KAAA,cAExD;UAMsB/K,EAAA,CAAAO,SAAA,GACK;UADLP,EAAA,CAAAQ,kBAAA,QAAAmR,GAAA,CAAA9P,YAAA,YACK;UAGJ7B,EAAA,CAAAO,SAAA,GAMvB;UANuBP,EAAA,CAAAQ,kBAAA,UAAAmR,GAAA,CAAAtQ,eAAA,kBAAAsQ,GAAA,CAAAtQ,eAAA,CAAA8L,iBAAA,kBAAAwE,GAAA,CAAAtQ,eAAA,CAAA8L,iBAAA,qBAAAwE,GAAA,CAAAtQ,eAAA,CAAA8L,iBAAA,IAAAG,uBAAA,kBAAAqE,GAAA,CAAAtQ,eAAA,CAAA8L,iBAAA,IAAAG,uBAAA,CAAAC,UAAA,oBAAAoE,GAAA,CAAAtQ,eAAA,kBAAAsQ,GAAA,CAAAtQ,eAAA,CAAA8L,iBAAA,kBAAAwE,GAAA,CAAAtQ,eAAA,CAAA8L,iBAAA,qBAAAwE,GAAA,CAAAtQ,eAAA,CAAA8L,iBAAA,IAAAG,uBAAA,kBAAAqE,GAAA,CAAAtQ,eAAA,CAAA8L,iBAAA,IAAAG,uBAAA,CAAAG,SAAA,eAMvB;UAUiBzN,EAAA,CAAAO,SAAA,GAAuC;UAAvCP,EAAA,CAAA0S,iBAAA,EAAAf,GAAA,CAAArQ,cAAA,kBAAAqQ,GAAA,CAAArQ,cAAA,qBAAAqQ,GAAA,CAAArQ,cAAA,IAAA2E,OAAA,SAAuC;UAKvCjG,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAA0S,iBAAA,EAAAf,GAAA,CAAArQ,cAAA,kBAAAqQ,GAAA,CAAArQ,cAAA,qBAAAqQ,GAAA,CAAArQ,cAAA,IAAA2F,YAAA,SAA4C;UAK5CjH,EAAA,CAAAO,SAAA,GAA0B;UAA1BP,EAAA,CAAA0S,iBAAA,CAAAf,GAAA,CAAAxB,oBAAA,CAA0B;UAM1BnQ,EAAA,CAAAO,SAAA,GAA6C;UAA7CP,EAAA,CAAA0S,iBAAA,EAAAf,GAAA,CAAArQ,cAAA,kBAAAqQ,GAAA,CAAArQ,cAAA,qBAAAqQ,GAAA,CAAArQ,cAAA,IAAAyF,aAAA,SAA6C;UAK7C/G,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAA0S,iBAAA,EAAAf,GAAA,CAAArQ,cAAA,kBAAAqQ,GAAA,CAAArQ,cAAA,qBAAAqQ,GAAA,CAAArQ,cAAA,IAAAkG,WAAA,SAA2C;UAK1ExH,EAAA,CAAAO,SAAA,EAA4B;UAA5BP,EAAA,CAAAI,UAAA,cAAAuR,GAAA,CAAAtP,cAAA,CAA4B;UAkBJrC,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAwS,WAAA,gBAAAb,GAAA,CAAAzP,eAAA,CAAqC;UAF/DlC,EAD8B,CAAAI,UAAA,iBAAgB,kBAAkB,sDACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}