<div class="p-3 w-full bg-white border-round shadow-1">
  <div class="card-heading filter-sec mb-5 flex align-items-center justify-content-start gap-2">
    <h4 class="m-0 pl-3 left-border relative flex">Contacts</h4>
    <div class="flex gap-3 ml-auto align-items-center">
      <p-button label="Reactivate" icon="pi pi-check" iconPos="right" class="font-semibold" [rounded]="true"
        [styleClass]="'px-3'" (click)="reactivateSelectedContacts()"
        [disabled]="!selectedContacts || selectedContacts.length === 0" />
      <p-button label="New Contact" (click)="showNewDialog('right')" icon="pi pi-plus-circle" iconPos="right"
        class="font-semibold" [rounded]="true" [styleClass]="'px-3'" />

      <p-button label="Existing Contact" (click)="showExistingDialog('right')" icon="pi pi-users" iconPos="right"
        class="font-semibold" [rounded]="true" [styleClass]="'px-3'" />

      <p-multiSelect [options]="cols" [(ngModel)]="selectedColumns" optionLabel="header"
        class="table-multiselect-dropdown"
        [styleClass]="'relative w-3rem h-3rem bg-light-blue border-round-3xl border-none font-semibold align-items-center justify-content-center'">
      </p-multiSelect>
    </div>
  </div>

  <div class="table-sec">

    <p-table [value]="contactDetails" [(selection)]="selectedContacts" dataKey="id" [rows]="14" [paginator]="true"
      [lazy]="true" responsiveLayout="scroll" [scrollable]="true" class="scrollable-table" [reorderableColumns]="true"
      (onColReorder)="onColumnReorder($event)">

      <ng-template pTemplate="header">
        <tr>
          <th pFrozenColumn class="border-round-left-lg pl-3 w-2rem text-center table-checkbox">
            <p-tableHeaderCheckbox />
          </th>
          <th pFrozenColumn (click)="customSort('full_name')">
            <div class="flex align-items-center cursor-pointer">
              Name
              <i *ngIf="sortField === 'full_name'" class="ml-2 pi"
                [ngClass]="sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'">
              </i>
              <i *ngIf="sortField !== 'full_name'" class="ml-2 pi pi-sort"></i>
            </div>
          </th>
          <ng-container *ngFor="let col of selectedColumns">
            <th [pSortableColumn]="col.field" pReorderableColumn (click)="customSort(col.field)">
              <div class="flex align-items-center cursor-pointer">
                {{ col.header }}
                <i *ngIf="sortField === col.field" class="ml-2 pi"
                  [ngClass]="sortOrder === 1 ? 'pi-sort-amount-up-alt' : 'pi-sort-amount-down'">
                </i>
                <i *ngIf="sortField !== col.field" class="ml-2 pi pi-sort"></i>
              </div>
            </th>
          </ng-container>
          <th>
            <div class="flex align-items-center">
              Actions
            </div>
          </th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-contact let-columns="columns">
        <tr class="cursor-pointer">
          <td pFrozenColumn class="border-round-left-lg pl-3 w-2rem text-center">
            <p-tableCheckbox [value]="contact" />
          </td>
          <td pFrozenColumn class="font-medium">
            <div class="readonly-field font-medium p-2 text-orange-600 cursor-pointer">
              <a [href]="'/#/store/contacts/' + contact?.documentId + '/overview'"
                style="text-decoration: none; color: inherit;" target="_blank">
                {{ contact?.full_name || '-' }}
              </a>
            </div>
          </td>

          <ng-container *ngFor="let col of selectedColumns">
            <td>
              <ng-container [ngSwitch]="col.field">
                <ng-container *ngSwitchCase="'department_name'">
                  {{ contact?.contact_person_department_name?.name || "-" }}
                </ng-container>

                <ng-container *ngSwitchCase="'email_address'">
                  {{ contact?.email_address || "-" }}
                </ng-container>

                <ng-container *ngSwitchCase="'mobile'">
                  {{ contact?.mobile || "-" }}
                </ng-container>

                <ng-container *ngSwitchCase="'vip_contacts'">
                  <p-checkbox [binary]="true" [ngModel]="contact.contact_person_vip_type"
                    [disabled]="true"></p-checkbox>
                </ng-container>

                <ng-container *ngSwitchCase="'deactivate'">
                  <p-checkbox [binary]="true" [ngModel]="contact.validity_end_date" [disabled]="true"></p-checkbox>
                </ng-container>

                <ng-container *ngSwitchCase="'communication_preference'">
                  {{ contact?.communication_preference }}
                </ng-container>
              </ng-container>
            </td>
          </ng-container>

          <td>
            <button pButton type="button" class="mr-2" icon="pi pi-pencil" pTooltip="Edit"
              (click)="editContact(contact)"></button>
          </td>
        </tr>
      </ng-template>

      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="10" class="border-round-left-lg pl-3">
            No contacts found.
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="loadingbody">
        <tr>
          <td colspan="10" class="border-round-left-lg pl-3">
            Loading contacts data. Please wait...
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>
<p-dialog [modal]="true" [(visible)]="addDialogVisible" [style]="{ width: '38rem' }" [position]="'right'"
  [draggable]="false" class="prospect-popup">
  <ng-template pTemplate="header">
    <h4>Contact Information</h4>
  </ng-template>

  <form [formGroup]="ContactForm" class="relative flex flex-column gap-1">
    <div class="field flex align-items-center text-base">
      <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="First Name ">
        <span class="material-symbols-rounded">person</span>First Name
        <span class="text-red-500">*</span>
      </label>
      <div class="form-input flex-1 relative">
        <input pInputText type="text" id="first_name" formControlName="first_name" class="h-3rem w-full"
          autocomplete="off" [ngClass]="{ 'is-invalid': submitted && f['first_name'].errors }" />
        <div *ngIf="submitted && f['first_name'].errors" class="invalid-feedback absolute top-0 bottom-0 h-1rem m-auto">
          <div *ngIf="f['first_name'].errors['required']">
            First Name is required.
          </div>
        </div>
      </div>
    </div>
    <div class="field flex align-items-center text-base">
      <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Middle Name">
        <span class="material-symbols-rounded">person</span>Middle Name
      </label>
      <div class="form-input flex-1 relative">
        <input pInputText type="text" id="middle_name" formControlName="middle_name" class="h-3rem w-full"
          autocomplete="off" />
      </div>
    </div>
    <div class="field flex align-items-center text-base">
      <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Last Name">
        <span class="material-symbols-rounded">person</span>Last Name
        <span class="text-red-500">*</span>
      </label>
      <div class="form-input flex-1 relative">
        <input pInputText type="text" id="last_name" formControlName="last_name" class="h-3rem w-full"
          autocomplete="off" [ngClass]="{ 'is-invalid': submitted && f['last_name'].errors }" />
        <div *ngIf="submitted && f['last_name'].errors" class="invalid-feedback absolute top-0 bottom-0 h-1rem m-auto">
          <div *ngIf="f['last_name'].errors['required']">
            Last Name is required.
          </div>
        </div>
      </div>
    </div>
    <div class="field flex align-items-center text-base">
      <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Job Title">
        <span class="material-symbols-rounded">work</span>Job Title
      </label>
      <div class="form-input flex-1 relative">
        <input pInputText type="text" id="job_title" formControlName="job_title" class="h-3rem w-full"
          autocomplete="off" />
      </div>
    </div>
    <div class="field flex align-items-center text-base">
      <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Function">
        <span class="material-symbols-rounded">functions</span>Function
      </label>
      <div class="form-input flex-1 relative">
        <p-dropdown [options]="cpFunctions" formControlName="contact_person_function_name" optionLabel="name"
          dataKey="value" placeholder="Select Function" [styleClass]="'h-3rem w-full'"></p-dropdown>
      </div>
    </div>
    <div class="field flex align-items-center text-base">
      <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Department">
        <span class="material-symbols-rounded">inbox_text_person</span>Department
      </label>
      <div class="form-input flex-1 relative">
        <p-dropdown [options]="cpDepartments" formControlName="contact_person_department_name" optionLabel="name"
          dataKey="value" placeholder="Select Department" [styleClass]="'h-3rem w-full'">
        </p-dropdown>
      </div>
    </div>
    <div class="field flex align-items-center text-base">
      <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Country">
        <span class="material-symbols-rounded">map</span>Country <span class="text-red-500">*</span>
      </label>
      <div class="form-input flex-1 relative">
        <p-dropdown [options]="countries" optionLabel="name" optionValue="isoCode" [(ngModel)]="selectedCountry"
          [filter]="true" formControlName="destination_location_country" [styleClass]="'h-3rem w-full'"
          placeholder="Select Country"
          [ngClass]="{ 'is-invalid': submitted && f['destination_location_country'].errors }">
        </p-dropdown>
        <div *ngIf="submitted && f['destination_location_country'].errors"
          class="invalid-feedback absolute top-0 bottom-0 h-1rem m-auto">
          <div *ngIf="
                submitted &&
                f['destination_location_country'].errors &&
                f['destination_location_country'].errors['required']
              ">
            Country is required.
          </div>
        </div>
      </div>
    </div>
    <div class="field flex align-items-center text-base">
      <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Email">
        <span class="material-symbols-rounded">mail</span>Email<span class="text-red-500">*</span>
      </label>
      <div class="form-input flex-1 relative">
        <input pInputText type="email" id="email_address" formControlName="email_address" class="h-3rem w-full"
          autocomplete="off" [ngClass]="{ 'is-invalid': submitted && f['email_address'].errors }" />
        <div *ngIf="submitted && f['email_address'].errors"
          class="invalid-feedback absolute top-0 bottom-0 h-1rem m-auto">
          <div *ngIf="
              submitted &&
              f['email_address'].errors &&
              f['email_address'].errors['required']
            ">
            Email is required.
          </div>
          <div *ngIf="f['email_address'].errors['email']">
            Email is invalid.
          </div>
        </div>
      </div>
    </div>
    <div class="field flex align-items-center text-base">
      <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Phone">
        <span class="material-symbols-rounded">phone_in_talk</span>Phone
      </label>
      <div class="form-input flex-1 relative">
        <div class="flex align-items-center gap-2">
          <app-country-wise-mobile [formGroup]="ContactForm" controlName="destination_location_country"
            phoneFieldName="phone_number" [selectedCountry]="selectedCountryForMobile"
            (validationResult)="phoneValidationMessage = $event"></app-country-wise-mobile>
          <input pInputText type="text" id="phone_number" formControlName="phone_number" class="h-3rem w-full"
            autocomplete="off" (input)="triggerMobileValidation()" />
        </div>
        <div class="p-error" *ngIf="ContactForm.get('phone_number')?.touched">
          <div *ngIf="phoneValidationMessage">{{ phoneValidationMessage }}</div>
        </div>
      </div>
    </div>
    <div class="field flex align-items-center text-base">
      <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Mobile">
        <span class="material-symbols-rounded">smartphone</span>Mobile #
        <span class="text-red-500">*</span>
      </label>
      <div class="form-input flex-1 relative">
        <div class="flex align-items-center gap-2">
          <app-country-wise-mobile [formGroup]="ContactForm" controlName="destination_location_country"
            phoneFieldName="mobile" [selectedCountry]="selectedCountryForMobile"
            (validationResult)="mobileValidationMessage = $event"></app-country-wise-mobile>
          <input pInputText type="text" id="mobile" formControlName="mobile" class="h-3rem w-full" autocomplete="off"
            [ngClass]="{ 'is-invalid': submitted && f['mobile'].errors }" (input)="triggerMobileValidation()" />
        </div>
        <div class="p-error">
          <div *ngIf="(ContactForm.get('mobile')?.touched || submitted) && mobileValidationMessage">
            {{ mobileValidationMessage }}
          </div>
        </div>
        <div class="
          invalid-feedback absolute top-0 bottom-0 h-1rem m-auto">
          <div *ngIf="(ContactForm.get('mobile')?.touched || submitted) && f['mobile'].errors?.required">
            Mobile is required.
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="editid && ContactForm.value.first_name">
      <div class="field flex align-items-center text-base">
        <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="DeActivate">
          <span class="material-symbols-rounded">remove_circle_outline</span>DeActivate
        </label>
        <div class="form-input flex-1 relative">
          <p-checkbox id="validity_end_date" formControlName="validity_end_date" [binary]="true"
            class="h-3rem w-full"></p-checkbox>
        </div>
      </div>
      <div class="field flex align-items-center text-base">
        <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="VIP Contact">
          <span class="material-symbols-rounded">star</span>VIP Contact
        </label>
        <div class="form-input flex-1 relative">
          <p-checkbox id="contact_person_vip_type" formControlName="contact_person_vip_type" [binary]="true"
            class="h-3rem w-full"></p-checkbox>
        </div>
      </div>
    </div>

    <div class="flex justify-content-end gap-2 mt-3">
      <button pButton type="button"
        class="p-button-rounded bg-light-blue border-none text-primary-700 font-medium justify-content-center w-9rem h-3rem"
        (click)="addDialogVisible = false">
        Cancel
      </button>
      <button pButton type="submit" class="p-button-rounded justify-content-center w-9rem h-3rem" (click)="onSubmit()">
        Save
      </button>
    </div>
  </form>
</p-dialog>
<p-dialog [modal]="true" [(visible)]="existingDialogVisible" [style]="{ width: '50rem' }" [position]="'right'"
  [draggable]="false" class="prospect-popup">
  <ng-template pTemplate="header">
    <h4>Contact Information</h4>
  </ng-template>

  <form [formGroup]="ContactForm" class="relative flex flex-column gap-1">
    <div class="field flex align-items-center text-base">
      <label class="relative flex align-items-center text font-semibold w-12rem gap-1" for="Contacts">
        <span class="material-symbols-rounded">person</span>Contacts
      </label>
      <div class="form-input flex-1 relative">
        <ng-select pInputText [items]="contacts$ | async" bindLabel="bp_full_name" [hideSelected]="true"
          [loading]="contactLoading" [minTermLength]="0" formControlName="contactexisting" [typeahead]="contactInput$"
          [maxSelectedItems]="10" appendTo="body" [class]="'multiselect-dropdown p-inputtext p-component p-element'"
          placeholder="Search for a contact">
          <ng-template ng-option-tmp let-item="item">
            <span>{{ item.bp_id }}</span>
            <span *ngIf="item.bp_full_name"> : {{ item.bp_full_name }}</span>
            <span *ngIf="item.email"> : {{ item.email }}</span>
            <span *ngIf="item.mobile"> : {{ item.mobile }}</span>
          </ng-template>
        </ng-select>
      </div>
    </div>
    <div class="flex justify-content-end gap-2 mt-3">
      <button pButton type="button"
        class="p-button-rounded bg-light-blue border-none text-primary-700 font-medium justify-content-center w-9rem h-3rem"
        (click)="existingDialogVisible = false">
        Cancel
      </button>
      <button pButton type="submit" class="p-button-rounded justify-content-center w-9rem h-3rem" (click)="onSubmit()">
        Save
      </button>
    </div>
  </form>
</p-dialog>