{"ast": null, "code": "import withMetadataArgument from './withMetadataArgument.js';\nimport { getExampleNumber as _getExampleNumber } from '../../core/index.js';\nexport function getExampleNumber() {\n  return withMetadataArgument(_getExampleNumber, arguments);\n}", "map": {"version": 3, "names": ["withMetadataArgument", "getExampleNumber", "_getExampleNumber", "arguments"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/max/exports/getExampleNumber.js"], "sourcesContent": ["import withMetadataArgument from './withMetadataArgument.js'\r\nimport { getExampleNumber as _getExampleNumber } from '../../core/index.js'\r\n\r\nexport function getExampleNumber() {\r\n\treturn withMetadataArgument(_getExampleNumber, arguments)\r\n}"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,2BAA2B;AAC5D,SAASC,gBAAgB,IAAIC,iBAAiB,QAAQ,qBAAqB;AAE3E,OAAO,SAASD,gBAAgBA,CAAA,EAAG;EAClC,OAAOD,oBAAoB,CAACE,iBAAiB,EAAEC,SAAS,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}