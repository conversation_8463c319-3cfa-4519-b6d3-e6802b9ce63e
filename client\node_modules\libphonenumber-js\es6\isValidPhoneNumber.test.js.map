{"version": 3, "file": "isValidPhoneNumber.test.js", "names": ["_isValidPhoneNumber", "metadata", "type", "isValidPhoneNumber", "parameters", "push", "apply", "describe", "it", "should", "equal", "defaultCountry"], "sources": ["../source/isValidPhoneNumber.test.js"], "sourcesContent": ["import _isValidPhoneNumber from './isValidPhoneNumber.js'\r\nimport metadata from '../metadata.min.json' assert { type: 'json' }\r\n\r\nfunction isValidPhoneNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _isValidPhoneNumber.apply(this, parameters)\r\n}\r\n\r\ndescribe('isValidPhoneNumber', () => {\r\n\tit('should detect whether a phone number is valid', () => {\r\n\t\tisValidPhoneNumber('8 (800) 555 35 35', 'RU').should.equal(true)\r\n\t\tisValidPhoneNumber('8 (800) 555 35 35 0', 'RU').should.equal(false)\r\n\t\tisValidPhoneNumber('Call: 8 (800) 555 35 35', 'RU').should.equal(false)\r\n\t\tisValidPhoneNumber('8 (800) 555 35 35', { defaultCountry: 'RU' }).should.equal(true)\r\n\t\tisValidPhoneNumber('+7 (800) 555 35 35').should.equal(true)\r\n\t\tisValidPhoneNumber('**** (800) 555 35 35').should.equal(false)\r\n\t\tisValidPhoneNumber(' +7 (800) 555 35 35').should.equal(false)\r\n\t\tisValidPhoneNumber(' ').should.equal(false)\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,mBAAP,MAAgC,yBAAhC;AACA,OAAOC,QAAP,MAAqB,sBAArB,UAAqDC,IAAI,EAAE,MAA3D;;AAEA,SAASC,kBAAT,GAA2C;EAAA,kCAAZC,UAAY;IAAZA,UAAY;EAAA;;EAC1CA,UAAU,CAACC,IAAX,CAAgBJ,QAAhB;EACA,OAAOD,mBAAmB,CAACM,KAApB,CAA0B,IAA1B,EAAgCF,UAAhC,CAAP;AACA;;AAEDG,QAAQ,CAAC,oBAAD,EAAuB,YAAM;EACpCC,EAAE,CAAC,+CAAD,EAAkD,YAAM;IACzDL,kBAAkB,CAAC,mBAAD,EAAsB,IAAtB,CAAlB,CAA8CM,MAA9C,CAAqDC,KAArD,CAA2D,IAA3D;IACAP,kBAAkB,CAAC,qBAAD,EAAwB,IAAxB,CAAlB,CAAgDM,MAAhD,CAAuDC,KAAvD,CAA6D,KAA7D;IACAP,kBAAkB,CAAC,yBAAD,EAA4B,IAA5B,CAAlB,CAAoDM,MAApD,CAA2DC,KAA3D,CAAiE,KAAjE;IACAP,kBAAkB,CAAC,mBAAD,EAAsB;MAAEQ,cAAc,EAAE;IAAlB,CAAtB,CAAlB,CAAkEF,MAAlE,CAAyEC,KAAzE,CAA+E,IAA/E;IACAP,kBAAkB,CAAC,oBAAD,CAAlB,CAAyCM,MAAzC,CAAgDC,KAAhD,CAAsD,IAAtD;IACAP,kBAAkB,CAAC,sBAAD,CAAlB,CAA2CM,MAA3C,CAAkDC,KAAlD,CAAwD,KAAxD;IACAP,kBAAkB,CAAC,qBAAD,CAAlB,CAA0CM,MAA1C,CAAiDC,KAAjD,CAAuD,KAAvD;IACAP,kBAAkB,CAAC,GAAD,CAAlB,CAAwBM,MAAxB,CAA+BC,KAA/B,CAAqC,KAArC;EACA,CATC,CAAF;AAUA,CAXO,CAAR"}