{"name": "ngx-mask", "version": "19.0.7", "description": "awesome ngx mask", "keywords": ["ng2-mask", "ngx-mask", "ng2", "angular-mask", "mask", "angular", "angular2", "angular2-mask", "ng2mask", "js<PERSON>dy"], "license": "MIT", "author": "ngx-mask", "contributors": ["ngx-mask"], "homepage": "https://github.com/JsDaddy/ngx-mask", "repository": {"type": "git", "url": "**************:JsDaddy/ngx-mask.git"}, "bugs": {"url": "https://github.com/JsDaddy/ngx-mask/issues"}, "peerDependencies": {"@angular/common": ">=14.0.0", "@angular/core": ">=14.0.0", "@angular/forms": ">=14.0.0"}, "dependencies": {"tslib": "^2.3.0"}, "module": "fesm2022/ngx-mask.mjs", "typings": "index.d.ts", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "default": "./fesm2022/ngx-mask.mjs"}}, "sideEffects": false}