import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { NgSelectModule } from '@ng-select/ng-select';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { ActivitiesFormComponent } from './activities-form/activities-form.component';
import { EditorModule } from 'primeng/editor';
import { ActivitiesTaskFollowupFormComponent } from './activities-task-followup-form/activities-task-followup-form.component';
import { ActivitiesCallFollowupFormComponent } from './activities-call-followup-form/activities-call-followup-form.component';
import { OpportunitiesFollowupFormComponent } from './opportunities-followup-form/opportunities-followup-form.component';
import { CountryWiseMobileComponent } from './country-wise-mobile/country-wise-mobile.component';

@NgModule({
  declarations: [
    ActivitiesFormComponent,
    ActivitiesTaskFollowupFormComponent,
    ActivitiesCallFollowupFormComponent,
    OpportunitiesFollowupFormComponent,
    CountryWiseMobileComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    DialogModule,
    TableModule,
    NgSelectModule,
    ButtonModule,
    EditorModule,
    DropdownModule,
    CalendarModule,
  ],
  exports: [
    ActivitiesFormComponent,
    ActivitiesTaskFollowupFormComponent,
    ActivitiesCallFollowupFormComponent,
    OpportunitiesFollowupFormComponent,
    CountryWiseMobileComponent
  ],
})
export class CommonFormModule {}
