{"version": 3, "file": "Leniency.test.js", "names": [], "sources": ["../../source/findNumbers/Leniency.test.js"], "sourcesContent": ["/*\r\nimport { containsMoreThanOneSlashInNationalNumber } from './Leniency.js'\r\n\r\ndescribe('Leniency', () => {\r\n\tit('testContainsMoreThanOneSlashInNationalNumber', () => {\r\n\t\t// A date should return true.\r\n\t\tnumber.setCountryCode(1)\r\n\t\tnumber.setCountryCodeSource(CountryCodeSource.FROM_DEFAULT_COUNTRY)\r\n\t\tcontainsMoreThanOneSlashInNationalNumber(number, '1/05/2013').should.equal(true)\r\n\r\n\t\t// Here, the country code source thinks it started with a country calling code, but this is not\r\n\t\t// the same as the part before the slash, so it's still true.\r\n\t\tnumber.setCountryCode(274)\r\n\t\tnumber.setCountryCodeSource(CountryCodeSource.FROM_NUMBER_WITHOUT_PLUS_SIGN)\r\n\t\tcontainsMoreThanOneSlashInNationalNumber(number, '27/4/2013').should.equal(true)\r\n\r\n\t\t// Now it should be false, because the first slash is after the country calling code.\r\n\t\tnumber.setCountryCode(49)\r\n\t\tnumber.setCountryCodeSource(CountryCodeSource.FROM_NUMBER_WITH_PLUS_SIGN)\r\n\t\tcontainsMoreThanOneSlashInNationalNumber(number, '49/69/2013').should.equal(false)\r\n\r\n\t\tnumber.setCountryCode(49)\r\n\t\tnumber.setCountryCodeSource(CountryCodeSource.FROM_NUMBER_WITHOUT_PLUS_SIGN)\r\n\t\tcontainsMoreThanOneSlashInNationalNumber(number, '+49/69/2013').should.equal(false)\r\n\t\tcontainsMoreThanOneSlashInNationalNumber(number, '+ 49/69/2013').should.equal(false)\r\n\t\tcontainsMoreThanOneSlashInNationalNumber(number, '+ 49/69/20/13').should.equal(true)\r\n\r\n\t\t// Here, the first group is not assumed to be the country calling code, even though it is the\r\n\t\t// same as it, so this should return true.\r\n\t\tnumber.setCountryCode(49)\r\n\t\tnumber.setCountryCodeSource(CountryCodeSource.FROM_DEFAULT_COUNTRY)\r\n\t\tcontainsMoreThanOneSlashInNationalNumber(number, '49/69/2013').should.equal(true)\r\n\t})\r\n})\r\n*/"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}