{"version": 3, "file": "isPossibleNumber.test.js", "names": ["metadata", "type", "_isPossibleNumber", "isPossibleNumber", "parameters", "push", "apply", "describe", "it", "should", "equal", "phone", "country", "countryCallingCode", "nationalNumber", "v2", "expect", "to"], "sources": ["../../source/legacy/isPossibleNumber.test.js"], "sourcesContent": ["import metadata from '../../metadata.min.json' assert { type: 'json' }\r\nimport _isPossibleNumber from './isPossibleNumber.js'\r\n\r\nfunction isPossibleNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _isPossibleNumber.apply(this, parameters)\r\n}\r\n\r\ndescribe('isPossibleNumber', () => {\r\n\tit('should work', function()\r\n\t{\r\n\t\tisPossibleNumber('+79992223344').should.equal(true)\r\n\r\n\t\tisPossibleNumber({ phone: '1112223344', country: 'RU' }).should.equal(true)\r\n\t\tisPossibleNumber({ phone: '111222334', country: 'RU' }).should.equal(false)\r\n\t\tisPossibleNumber({ phone: '11122233445', country: 'RU' }).should.equal(false)\r\n\r\n\t\tisPossibleNumber({ phone: '1112223344', countryCallingCode: 7 }).should.equal(true)\r\n\t})\r\n\r\n\tit('should work v2', () => {\r\n\t\tisPossibleNumber({ nationalNumber: '111222334', countryCallingCode: 7 }, { v2: true }).should.equal(false)\r\n\t\tisPossibleNumber({ nationalNumber: '1112223344', countryCallingCode: 7 }, { v2: true }).should.equal(true)\r\n\t\tisPossibleNumber({ nationalNumber: '11122233445', countryCallingCode: 7 }, { v2: true }).should.equal(false)\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\t// Invalid `PhoneNumber` argument.\r\n\t\texpect(() => isPossibleNumber({}, { v2: true })).to.throw('Invalid phone number object passed')\r\n\r\n\t\t// Empty input is passed.\r\n\t\t// This is just to support `isValidNumber({})`\r\n\t\t// for cases when `parseNumber()` returns `{}`.\r\n\t\tisPossibleNumber({}).should.equal(false)\r\n\t\texpect(() => isPossibleNumber({ phone: '1112223344' })).to.throw('Invalid phone number object passed')\r\n\r\n\t\t// Incorrect country.\r\n\t\texpect(() => isPossibleNumber({ phone: '1112223344', country: 'XX' })).to.throw('Unknown country')\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,yBAArB,UAAwDC,IAAI,EAAE,MAA9D;AACA,OAAOC,iBAAP,MAA8B,uBAA9B;;AAEA,SAASC,gBAAT,GAAyC;EAAA,kCAAZC,UAAY;IAAZA,UAAY;EAAA;;EACxCA,UAAU,CAACC,IAAX,CAAgBL,QAAhB;EACA,OAAOE,iBAAiB,CAACI,KAAlB,CAAwB,IAAxB,EAA8BF,UAA9B,CAAP;AACA;;AAEDG,QAAQ,CAAC,kBAAD,EAAqB,YAAM;EAClCC,EAAE,CAAC,aAAD,EAAgB,YAClB;IACCL,gBAAgB,CAAC,cAAD,CAAhB,CAAiCM,MAAjC,CAAwCC,KAAxC,CAA8C,IAA9C;IAEAP,gBAAgB,CAAC;MAAEQ,KAAK,EAAE,YAAT;MAAuBC,OAAO,EAAE;IAAhC,CAAD,CAAhB,CAAyDH,MAAzD,CAAgEC,KAAhE,CAAsE,IAAtE;IACAP,gBAAgB,CAAC;MAAEQ,KAAK,EAAE,WAAT;MAAsBC,OAAO,EAAE;IAA/B,CAAD,CAAhB,CAAwDH,MAAxD,CAA+DC,KAA/D,CAAqE,KAArE;IACAP,gBAAgB,CAAC;MAAEQ,KAAK,EAAE,aAAT;MAAwBC,OAAO,EAAE;IAAjC,CAAD,CAAhB,CAA0DH,MAA1D,CAAiEC,KAAjE,CAAuE,KAAvE;IAEAP,gBAAgB,CAAC;MAAEQ,KAAK,EAAE,YAAT;MAAuBE,kBAAkB,EAAE;IAA3C,CAAD,CAAhB,CAAiEJ,MAAjE,CAAwEC,KAAxE,CAA8E,IAA9E;EACA,CATC,CAAF;EAWAF,EAAE,CAAC,gBAAD,EAAmB,YAAM;IAC1BL,gBAAgB,CAAC;MAAEW,cAAc,EAAE,WAAlB;MAA+BD,kBAAkB,EAAE;IAAnD,CAAD,EAAyD;MAAEE,EAAE,EAAE;IAAN,CAAzD,CAAhB,CAAuFN,MAAvF,CAA8FC,KAA9F,CAAoG,KAApG;IACAP,gBAAgB,CAAC;MAAEW,cAAc,EAAE,YAAlB;MAAgCD,kBAAkB,EAAE;IAApD,CAAD,EAA0D;MAAEE,EAAE,EAAE;IAAN,CAA1D,CAAhB,CAAwFN,MAAxF,CAA+FC,KAA/F,CAAqG,IAArG;IACAP,gBAAgB,CAAC;MAAEW,cAAc,EAAE,aAAlB;MAAiCD,kBAAkB,EAAE;IAArD,CAAD,EAA2D;MAAEE,EAAE,EAAE;IAAN,CAA3D,CAAhB,CAAyFN,MAAzF,CAAgGC,KAAhG,CAAsG,KAAtG;EACA,CAJC,CAAF;EAMAF,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC;IACAQ,MAAM,CAAC;MAAA,OAAMb,gBAAgB,CAAC,EAAD,EAAK;QAAEY,EAAE,EAAE;MAAN,CAAL,CAAtB;IAAA,CAAD,CAAN,CAAiDE,EAAjD,UAA0D,oCAA1D,EAFqC,CAIrC;IACA;IACA;;IACAd,gBAAgB,CAAC,EAAD,CAAhB,CAAqBM,MAArB,CAA4BC,KAA5B,CAAkC,KAAlC;IACAM,MAAM,CAAC;MAAA,OAAMb,gBAAgB,CAAC;QAAEQ,KAAK,EAAE;MAAT,CAAD,CAAtB;IAAA,CAAD,CAAN,CAAwDM,EAAxD,UAAiE,oCAAjE,EARqC,CAUrC;;IACAD,MAAM,CAAC;MAAA,OAAMb,gBAAgB,CAAC;QAAEQ,KAAK,EAAE,YAAT;QAAuBC,OAAO,EAAE;MAAhC,CAAD,CAAtB;IAAA,CAAD,CAAN,CAAuEK,EAAvE,UAAgF,iBAAhF;EACA,CAZC,CAAF;AAaA,CA/BO,CAAR"}