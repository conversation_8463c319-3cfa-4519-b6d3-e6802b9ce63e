{"ast": null, "code": "import withMetadataArgument from './withMetadataArgument.js';\nimport { getCountryCallingCode as _getCountryCallingCode } from '../../core/index.js';\nexport function getCountryCallingCode() {\n  return withMetadataArgument(_getCountryCallingCode, arguments);\n}", "map": {"version": 3, "names": ["withMetadataArgument", "getCountryCallingCode", "_getCountryCallingCode", "arguments"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/max/exports/getCountryCallingCode.js"], "sourcesContent": ["import withMetadataArgument from './withMetadataArgument.js'\r\nimport { getCountryCallingCode as _getCountryCallingCode } from '../../core/index.js'\r\n\r\nexport function getCountryCallingCode() {\r\n\treturn withMetadataArgument(_getCountryCallingCode, arguments)\r\n}"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,2BAA2B;AAC5D,SAASC,qBAAqB,IAAIC,sBAAsB,QAAQ,qBAAqB;AAErF,OAAO,SAASD,qBAAqBA,CAAA,EAAG;EACvC,OAAOD,oBAAoB,CAACE,sBAAsB,EAAEC,SAAS,CAAC;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}