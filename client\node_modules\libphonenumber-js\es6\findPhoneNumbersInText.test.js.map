{"version": 3, "file": "findPhoneNumbersInText.test.js", "names": ["findPhoneNumbersInText", "metadata", "type", "metadataMax", "findPhoneNumbersInTextWithResults", "input", "options", "results", "map", "result", "startsAt", "endsAt", "number", "data", "phone", "nationalNumber", "country", "ext", "describe", "it", "should", "equal", "defaultCountry", "undefined", "NUMBERS", "i", "length", "defaultCallingCode", "deep", "leniency", "phoneNumbers", "v2", "countryCallingCode", "thrower", "numbers", "possibleNumbers", "extended"], "sources": ["../source/findPhoneNumbersInText.test.js"], "sourcesContent": ["import findPhoneNumbersInText from './findPhoneNumbersInText.js'\r\nimport metadata from '../metadata.min.json' assert { type: 'json' }\r\nimport metadataMax from '../metadata.max.json' assert { type: 'json' }\r\n\r\nfunction findPhoneNumbersInTextWithResults(input, options, metadata) {\r\n\tconst results = findPhoneNumbersInText(input, options, metadata)\r\n\treturn results.map((result) => {\r\n\t\tconst { startsAt, endsAt, number } = result\r\n\t\tconst data = {\r\n\t\t\tphone: number.nationalNumber,\r\n\t\t\tstartsAt,\r\n\t\t\tendsAt\r\n\t\t}\r\n\t\tif (number.country) {\r\n\t\t\tdata.country = number.country\r\n\t\t}\r\n\t\tif (number.ext) {\r\n\t\t\tdata.ext = number.ext\r\n\t\t}\r\n\t\treturn data\r\n\t})\r\n}\r\n\r\ndescribe('findPhoneNumbersInText', () => {\r\n\tit('should find phone numbers in text (with default country)', () => {\r\n\t\tfindPhoneNumbersInText('+7 (800) 555-35-35', 'US', metadata)[0].number.number.should.equal('+78005553535')\r\n\t})\r\n\r\n\tit('should find phone numbers in text (with default country in options)', () => {\r\n\t\tfindPhoneNumbersInText('+7 (800) 555-35-35', { defaultCountry: 'US' }, metadata)[0].number.number.should.equal('+78005553535')\r\n\t})\r\n\r\n\tit('should find phone numbers in text (with default country and options)', () => {\r\n\t\tfindPhoneNumbersInText('+7 (800) 555-35-35', 'US', {}, metadata)[0].number.number.should.equal('+78005553535')\r\n\t})\r\n\r\n\tit('should find phone numbers in text (without default country, with options)', () => {\r\n\t\tfindPhoneNumbersInText('+7 (800) 555-35-35', undefined, {}, metadata)[0].number.number.should.equal('+78005553535')\r\n\t})\r\n\r\n\tit('should find phone numbers in text (with default country, without options)', () => {\r\n\t\tfindPhoneNumbersInText('+7 (800) 555-35-35', 'US', undefined, metadata)[0].number.number.should.equal('+78005553535')\r\n\t})\r\n\r\n\tit('should find phone numbers in text (with empty default country)', () => {\r\n\t\tfindPhoneNumbersInText('+7 (800) 555-35-35', undefined, metadata)[0].number.number.should.equal('+78005553535')\r\n\t})\r\n\r\n\tit('should find phone numbers in text', () => {\r\n\t\tconst NUMBERS = ['+78005553535', '+12133734253']\r\n\t\tconst results = findPhoneNumbersInText('The number is +7 (800) 555-35-35 and not (************* as written in the document.', metadata)\r\n\t\tlet i = 0\r\n\t\twhile (i < results.length) {\r\n\t\t\tresults[i].number.number.should.equal(NUMBERS[i])\r\n\t\t\ti++\r\n\t\t}\r\n\t})\r\n\r\n\tit('should find phone numbers in text (default country calling code)', () => {\r\n\t\tconst NUMBERS = ['+870773111632']\r\n\t\tconst results = findPhoneNumbersInText('The number is 773 111 632', { defaultCallingCode: '870' }, metadata)\r\n\t\tlet i = 0\r\n\t\twhile (i < results.length) {\r\n\t\t\tresults[i].number.number.should.equal(NUMBERS[i])\r\n\t\t\ti++\r\n\t\t}\r\n\t})\r\n\r\n\tit('should find numbers', () => {\r\n\t\tfindPhoneNumbersInTextWithResults('2133734253', { defaultCountry: 'US' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 0,\r\n\t\t\tendsAt   : 10\r\n\t\t}])\r\n\r\n\t\tfindPhoneNumbersInTextWithResults('(*************', { defaultCountry: 'US' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 0,\r\n\t\t\tendsAt   : 14\r\n\t\t}])\r\n\r\n\t\tfindPhoneNumbersInTextWithResults('The number is +7 (800) 555-35-35 and not (************* as written in the document.', { defaultCountry: 'US' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}, {\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t}])\r\n\r\n\t\t// Opening parenthesis issue.\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/252\r\n\t\tfindPhoneNumbersInTextWithResults('The number is +7 (800) 555-35-35 and not (************* (that\\'s not even in the same country!) as written in the document.', { defaultCountry: 'US' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}, {\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t}])\r\n\r\n\t\t// No default country.\r\n\t\tfindPhoneNumbersInTextWithResults('The number is +7 (800) 555-35-35 as written in the document.', undefined, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}])\r\n\r\n\t\t// Passing `options` and default country.\r\n\t\tfindPhoneNumbersInTextWithResults('The number is +7 (800) 555-35-35 as written in the document.', { defaultCountry: 'US', leniency: 'VALID' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}])\r\n\r\n\t\t// Passing `options`.\r\n\t\tfindPhoneNumbersInTextWithResults('The number is +7 (800) 555-35-35 as written in the document.', { leniency: 'VALID' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}])\r\n\r\n\t\t// Not a phone number and a phone number.\r\n\t\tfindPhoneNumbersInTextWithResults('Digits 12 are not a number, but +7 (800) 555-35-35 is.', { leniency: 'VALID' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 32,\r\n\t\t\tendsAt   : 50\r\n\t\t}])\r\n\r\n\t\t// Phone number extension.\r\n\t\tfindPhoneNumbersInTextWithResults('Date 02/17/2018 is not a number, but +7 (800) 555-35-35 ext. 123 is.', { leniency: 'VALID' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\text      : '123',\r\n\t\t\tstartsAt : 37,\r\n\t\t\tendsAt   : 64\r\n\t\t}])\r\n\t})\r\n\r\n\tit('should find numbers (v2)', () => {\r\n\t\tconst phoneNumbers = findPhoneNumbersInText('The number is +7 (800) 555-35-35 ext. 1234 and not (************* as written in the document.', { defaultCountry: 'US', v2: true }, metadata)\r\n\r\n\t\tphoneNumbers.length.should.equal(2)\r\n\r\n\t\tphoneNumbers[0].startsAt.should.equal(14)\r\n\t\tphoneNumbers[0].endsAt.should.equal(42)\r\n\r\n\t\tphoneNumbers[0].number.number.should.equal('+78005553535')\r\n\t\tphoneNumbers[0].number.nationalNumber.should.equal('8005553535')\r\n\t\tphoneNumbers[0].number.country.should.equal('RU')\r\n\t\tphoneNumbers[0].number.countryCallingCode.should.equal('7')\r\n\t\tphoneNumbers[0].number.ext.should.equal('1234')\r\n\r\n\t\tphoneNumbers[1].startsAt.should.equal(51)\r\n\t\tphoneNumbers[1].endsAt.should.equal(65)\r\n\r\n\t\tphoneNumbers[1].number.number.should.equal('+12133734253')\r\n\t\tphoneNumbers[1].number.nationalNumber.should.equal('2133734253')\r\n\t\tphoneNumbers[1].number.country.should.equal('US')\r\n\t\tphoneNumbers[1].number.countryCallingCode.should.equal('1')\r\n\t})\r\n\r\n\tit('shouldn\\'t find non-valid numbers', () => {\r\n\t\t// Not a valid phone number for US.\r\n\t\tfindPhoneNumbersInTextWithResults('1111111111', { defaultCountry: 'US' }, metadata).should.deep.equal([])\r\n\t})\r\n\r\n\tit('should find non-European digits', () => {\r\n\t\t// E.g. in Iraq they don't write `+442323234` but rather `+٤٤٢٣٢٣٢٣٤`.\r\n\t\tfindPhoneNumbersInTextWithResults('العَرَبِيَّة‎ +٤٤٣٣٣٣٣٣٣٣٣٣عَرَبِيّ‎', undefined, metadata).should.deep.equal([{\r\n\t\t\tcountry  : 'GB',\r\n\t\t\tphone    : '3333333333',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 27\r\n\t\t}])\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\tlet thrower\r\n\r\n\t\t// No input\r\n\t\tfindPhoneNumbersInTextWithResults('', undefined, metadata).should.deep.equal([])\r\n\r\n\t\t// // No country metadata for this `require` country code\r\n\t\t// thrower = () => findPhoneNumbersInTextWithResults('123', { defaultCountry: 'ZZ' }, metadata)\r\n\t\t// thrower.should.throw('Unknown country')\r\n\r\n\t\t// Numerical `value`\r\n\t\tthrower = () => findPhoneNumbersInTextWithResults(2141111111, { defaultCountry: 'US' })\r\n\t\tthrower.should.throw('A text for parsing must be a string.')\r\n\r\n\t\t// // No metadata\r\n\t\t// thrower = () => findPhoneNumbersInTextWithResults('')\r\n\t\t// thrower.should.throw('`metadata` argument not passed')\r\n\r\n\t\t// No metadata, no default country, no phone numbers.\r\n\t\tfindPhoneNumbersInTextWithResults('').should.deep.equal([])\r\n\t})\r\n\r\n\tit('should find international numbers when passed a non-existent default country', () => {\r\n\t\tconst numbers = findPhoneNumbersInText('Phone: +7 (800) 555 35 35. National: 8 (800) 555-55-55', { defaultCountry: 'XX', v2: true }, metadata)\r\n\t\tnumbers.length.should.equal(1)\r\n\t\tnumbers[0].number.nationalNumber.should.equal('8005553535')\r\n\t})\r\n\r\n\tit('shouldn\\'t find phone numbers which are not phone numbers', () => {\r\n\t\t// A timestamp.\r\n\t\tfindPhoneNumbersInTextWithResults('2012-01-02 08:00', { defaultCountry: 'US' }, metadata).should.deep.equal([])\r\n\r\n\t\t// A valid number (not a complete timestamp).\r\n\t\tfindPhoneNumbersInTextWithResults('2012-01-02 08', { defaultCountry: 'US' }, metadata).should.deep.equal([{\r\n\t\t\tcountry  : 'US',\r\n\t\t\tphone    : '2012010208',\r\n\t\t\tstartsAt : 0,\r\n\t\t\tendsAt   : 13\r\n\t\t}])\r\n\r\n\t\t// Invalid parens.\r\n\t\tfindPhoneNumbersInTextWithResults('213(3734253', { defaultCountry: 'US' }, metadata).should.deep.equal([])\r\n\r\n\t\t// Letters after phone number.\r\n\t\tfindPhoneNumbersInTextWithResults('2133734253a', { defaultCountry: 'US' }, metadata).should.deep.equal([])\r\n\r\n\t\t// Valid phone (same as the one found in the UUID below).\r\n\t\tfindPhoneNumbersInTextWithResults('The phone number is 231354125.', { defaultCountry: 'FR' }, metadata).should.deep.equal([{\r\n\t\t\tcountry  : 'FR',\r\n\t\t\tphone    : '231354125',\r\n\t\t\tstartsAt : 20,\r\n\t\t\tendsAt   : 29\r\n\t\t}])\r\n\r\n\t\t// Not a phone number (part of a UUID).\r\n\t\t// Should parse in `{ extended: true }` mode.\r\n\t\tconst possibleNumbers = findPhoneNumbersInTextWithResults('The UUID is CA801c26f98cd16e231354125ad046e40b.', { defaultCountry: 'FR', extended: true }, metadata)\r\n\t\tpossibleNumbers.length.should.equal(1)\r\n\t\tpossibleNumbers[0].country.should.equal('FR')\r\n\t\tpossibleNumbers[0].phone.should.equal('231354125')\r\n\r\n\t\t// Not a phone number (part of a UUID).\r\n\t\t// Shouldn't parse by default.\r\n\t\tfindPhoneNumbersInTextWithResults('The UUID is CA801c26f98cd16e231354125ad046e40b.', { defaultCountry: 'FR' }, metadata).should.deep.equal([])\r\n\t})\r\n\r\n\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/merge_requests/4\r\n\tit('should return correct `startsAt` and `endsAt` when matching \"inner\" candidates in a could-be-a-candidate substring', () => {\r\n\t\tfindPhoneNumbersInTextWithResults('39945926 77200596 16533084', { defaultCountry: 'ID' }, metadataMax)\r\n\t\t\t.should\r\n\t\t\t.deep\r\n\t\t\t.equal([{\r\n\t\t\t\tcountry: 'ID',\r\n\t\t\t\tphone: '77200596',\r\n\t\t\t\tstartsAt: 9,\r\n\t\t\t\tendsAt: 17\r\n\t\t\t}])\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,sBAAP,MAAmC,6BAAnC;AACA,OAAOC,QAAP,MAAqB,sBAArB,UAAqDC,IAAI,EAAE,MAA3D;AACA,OAAOC,WAAP,MAAwB,sBAAxB,UAAwDD,IAAI,EAAE,MAA9D;;AAEA,SAASE,iCAAT,CAA2CC,KAA3C,EAAkDC,OAAlD,EAA2DL,QAA3D,EAAqE;EACpE,IAAMM,OAAO,GAAGP,sBAAsB,CAACK,KAAD,EAAQC,OAAR,EAAiBL,QAAjB,CAAtC;EACA,OAAOM,OAAO,CAACC,GAAR,CAAY,UAACC,MAAD,EAAY;IAC9B,IAAQC,QAAR,GAAqCD,MAArC,CAAQC,QAAR;IAAA,IAAkBC,MAAlB,GAAqCF,MAArC,CAAkBE,MAAlB;IAAA,IAA0BC,MAA1B,GAAqCH,MAArC,CAA0BG,MAA1B;IACA,IAAMC,IAAI,GAAG;MACZC,KAAK,EAAEF,MAAM,CAACG,cADF;MAEZL,QAAQ,EAARA,QAFY;MAGZC,MAAM,EAANA;IAHY,CAAb;;IAKA,IAAIC,MAAM,CAACI,OAAX,EAAoB;MACnBH,IAAI,CAACG,OAAL,GAAeJ,MAAM,CAACI,OAAtB;IACA;;IACD,IAAIJ,MAAM,CAACK,GAAX,EAAgB;MACfJ,IAAI,CAACI,GAAL,GAAWL,MAAM,CAACK,GAAlB;IACA;;IACD,OAAOJ,IAAP;EACA,CAdM,CAAP;AAeA;;AAEDK,QAAQ,CAAC,wBAAD,EAA2B,YAAM;EACxCC,EAAE,CAAC,0DAAD,EAA6D,YAAM;IACpEnB,sBAAsB,CAAC,oBAAD,EAAuB,IAAvB,EAA6BC,QAA7B,CAAtB,CAA6D,CAA7D,EAAgEW,MAAhE,CAAuEA,MAAvE,CAA8EQ,MAA9E,CAAqFC,KAArF,CAA2F,cAA3F;EACA,CAFC,CAAF;EAIAF,EAAE,CAAC,qEAAD,EAAwE,YAAM;IAC/EnB,sBAAsB,CAAC,oBAAD,EAAuB;MAAEsB,cAAc,EAAE;IAAlB,CAAvB,EAAiDrB,QAAjD,CAAtB,CAAiF,CAAjF,EAAoFW,MAApF,CAA2FA,MAA3F,CAAkGQ,MAAlG,CAAyGC,KAAzG,CAA+G,cAA/G;EACA,CAFC,CAAF;EAIAF,EAAE,CAAC,sEAAD,EAAyE,YAAM;IAChFnB,sBAAsB,CAAC,oBAAD,EAAuB,IAAvB,EAA6B,EAA7B,EAAiCC,QAAjC,CAAtB,CAAiE,CAAjE,EAAoEW,MAApE,CAA2EA,MAA3E,CAAkFQ,MAAlF,CAAyFC,KAAzF,CAA+F,cAA/F;EACA,CAFC,CAAF;EAIAF,EAAE,CAAC,2EAAD,EAA8E,YAAM;IACrFnB,sBAAsB,CAAC,oBAAD,EAAuBuB,SAAvB,EAAkC,EAAlC,EAAsCtB,QAAtC,CAAtB,CAAsE,CAAtE,EAAyEW,MAAzE,CAAgFA,MAAhF,CAAuFQ,MAAvF,CAA8FC,KAA9F,CAAoG,cAApG;EACA,CAFC,CAAF;EAIAF,EAAE,CAAC,2EAAD,EAA8E,YAAM;IACrFnB,sBAAsB,CAAC,oBAAD,EAAuB,IAAvB,EAA6BuB,SAA7B,EAAwCtB,QAAxC,CAAtB,CAAwE,CAAxE,EAA2EW,MAA3E,CAAkFA,MAAlF,CAAyFQ,MAAzF,CAAgGC,KAAhG,CAAsG,cAAtG;EACA,CAFC,CAAF;EAIAF,EAAE,CAAC,gEAAD,EAAmE,YAAM;IAC1EnB,sBAAsB,CAAC,oBAAD,EAAuBuB,SAAvB,EAAkCtB,QAAlC,CAAtB,CAAkE,CAAlE,EAAqEW,MAArE,CAA4EA,MAA5E,CAAmFQ,MAAnF,CAA0FC,KAA1F,CAAgG,cAAhG;EACA,CAFC,CAAF;EAIAF,EAAE,CAAC,mCAAD,EAAsC,YAAM;IAC7C,IAAMK,OAAO,GAAG,CAAC,cAAD,EAAiB,cAAjB,CAAhB;IACA,IAAMjB,OAAO,GAAGP,sBAAsB,CAAC,qFAAD,EAAwFC,QAAxF,CAAtC;IACA,IAAIwB,CAAC,GAAG,CAAR;;IACA,OAAOA,CAAC,GAAGlB,OAAO,CAACmB,MAAnB,EAA2B;MAC1BnB,OAAO,CAACkB,CAAD,CAAP,CAAWb,MAAX,CAAkBA,MAAlB,CAAyBQ,MAAzB,CAAgCC,KAAhC,CAAsCG,OAAO,CAACC,CAAD,CAA7C;MACAA,CAAC;IACD;EACD,CARC,CAAF;EAUAN,EAAE,CAAC,kEAAD,EAAqE,YAAM;IAC5E,IAAMK,OAAO,GAAG,CAAC,eAAD,CAAhB;IACA,IAAMjB,OAAO,GAAGP,sBAAsB,CAAC,2BAAD,EAA8B;MAAE2B,kBAAkB,EAAE;IAAtB,CAA9B,EAA6D1B,QAA7D,CAAtC;IACA,IAAIwB,CAAC,GAAG,CAAR;;IACA,OAAOA,CAAC,GAAGlB,OAAO,CAACmB,MAAnB,EAA2B;MAC1BnB,OAAO,CAACkB,CAAD,CAAP,CAAWb,MAAX,CAAkBA,MAAlB,CAAyBQ,MAAzB,CAAgCC,KAAhC,CAAsCG,OAAO,CAACC,CAAD,CAA7C;MACAA,CAAC;IACD;EACD,CARC,CAAF;EAUAN,EAAE,CAAC,qBAAD,EAAwB,YAAM;IAC/Bf,iCAAiC,CAAC,YAAD,EAAe;MAAEkB,cAAc,EAAE;IAAlB,CAAf,EAAyCrB,QAAzC,CAAjC,CAAoFmB,MAApF,CAA2FQ,IAA3F,CAAgGP,KAAhG,CAAsG,CAAC;MACtGP,KAAK,EAAM,YAD2F;MAEtGE,OAAO,EAAI,IAF2F;MAGtGN,QAAQ,EAAG,CAH2F;MAItGC,MAAM,EAAK;IAJ2F,CAAD,CAAtG;IAOAP,iCAAiC,CAAC,gBAAD,EAAmB;MAAEkB,cAAc,EAAE;IAAlB,CAAnB,EAA6CrB,QAA7C,CAAjC,CAAwFmB,MAAxF,CAA+FQ,IAA/F,CAAoGP,KAApG,CAA0G,CAAC;MAC1GP,KAAK,EAAM,YAD+F;MAE1GE,OAAO,EAAI,IAF+F;MAG1GN,QAAQ,EAAG,CAH+F;MAI1GC,MAAM,EAAK;IAJ+F,CAAD,CAA1G;IAOAP,iCAAiC,CAAC,qFAAD,EAAwF;MAAEkB,cAAc,EAAE;IAAlB,CAAxF,EAAkHrB,QAAlH,CAAjC,CAA6JmB,MAA7J,CAAoKQ,IAApK,CAAyKP,KAAzK,CAA+K,CAAC;MAC/KP,KAAK,EAAM,YADoK;MAE/KE,OAAO,EAAI,IAFoK;MAG/KN,QAAQ,EAAG,EAHoK;MAI/KC,MAAM,EAAK;IAJoK,CAAD,EAK5K;MACFG,KAAK,EAAM,YADT;MAEFE,OAAO,EAAI,IAFT;MAGFN,QAAQ,EAAG,EAHT;MAIFC,MAAM,EAAK;IAJT,CAL4K,CAA/K,EAf+B,CA2B/B;IACA;;IACAP,iCAAiC,CAAC,6HAAD,EAAgI;MAAEkB,cAAc,EAAE;IAAlB,CAAhI,EAA0JrB,QAA1J,CAAjC,CAAqMmB,MAArM,CAA4MQ,IAA5M,CAAiNP,KAAjN,CAAuN,CAAC;MACvNP,KAAK,EAAM,YAD4M;MAEvNE,OAAO,EAAI,IAF4M;MAGvNN,QAAQ,EAAG,EAH4M;MAIvNC,MAAM,EAAK;IAJ4M,CAAD,EAKpN;MACFG,KAAK,EAAM,YADT;MAEFE,OAAO,EAAI,IAFT;MAGFN,QAAQ,EAAG,EAHT;MAIFC,MAAM,EAAK;IAJT,CALoN,CAAvN,EA7B+B,CAyC/B;;IACAP,iCAAiC,CAAC,8DAAD,EAAiEmB,SAAjE,EAA4EtB,QAA5E,CAAjC,CAAuHmB,MAAvH,CAA8HQ,IAA9H,CAAmIP,KAAnI,CAAyI,CAAC;MACzIP,KAAK,EAAM,YAD8H;MAEzIE,OAAO,EAAI,IAF8H;MAGzIN,QAAQ,EAAG,EAH8H;MAIzIC,MAAM,EAAK;IAJ8H,CAAD,CAAzI,EA1C+B,CAiD/B;;IACAP,iCAAiC,CAAC,8DAAD,EAAiE;MAAEkB,cAAc,EAAE,IAAlB;MAAwBO,QAAQ,EAAE;IAAlC,CAAjE,EAA8G5B,QAA9G,CAAjC,CAAyJmB,MAAzJ,CAAgKQ,IAAhK,CAAqKP,KAArK,CAA2K,CAAC;MAC3KP,KAAK,EAAM,YADgK;MAE3KE,OAAO,EAAI,IAFgK;MAG3KN,QAAQ,EAAG,EAHgK;MAI3KC,MAAM,EAAK;IAJgK,CAAD,CAA3K,EAlD+B,CAyD/B;;IACAP,iCAAiC,CAAC,8DAAD,EAAiE;MAAEyB,QAAQ,EAAE;IAAZ,CAAjE,EAAwF5B,QAAxF,CAAjC,CAAmImB,MAAnI,CAA0IQ,IAA1I,CAA+IP,KAA/I,CAAqJ,CAAC;MACrJP,KAAK,EAAM,YAD0I;MAErJE,OAAO,EAAI,IAF0I;MAGrJN,QAAQ,EAAG,EAH0I;MAIrJC,MAAM,EAAK;IAJ0I,CAAD,CAArJ,EA1D+B,CAiE/B;;IACAP,iCAAiC,CAAC,wDAAD,EAA2D;MAAEyB,QAAQ,EAAE;IAAZ,CAA3D,EAAkF5B,QAAlF,CAAjC,CAA6HmB,MAA7H,CAAoIQ,IAApI,CAAyIP,KAAzI,CAA+I,CAAC;MAC/IP,KAAK,EAAM,YADoI;MAE/IE,OAAO,EAAI,IAFoI;MAG/IN,QAAQ,EAAG,EAHoI;MAI/IC,MAAM,EAAK;IAJoI,CAAD,CAA/I,EAlE+B,CAyE/B;;IACAP,iCAAiC,CAAC,sEAAD,EAAyE;MAAEyB,QAAQ,EAAE;IAAZ,CAAzE,EAAgG5B,QAAhG,CAAjC,CAA2ImB,MAA3I,CAAkJQ,IAAlJ,CAAuJP,KAAvJ,CAA6J,CAAC;MAC7JP,KAAK,EAAM,YADkJ;MAE7JE,OAAO,EAAI,IAFkJ;MAG7JC,GAAG,EAAQ,KAHkJ;MAI7JP,QAAQ,EAAG,EAJkJ;MAK7JC,MAAM,EAAK;IALkJ,CAAD,CAA7J;EAOA,CAjFC,CAAF;EAmFAQ,EAAE,CAAC,0BAAD,EAA6B,YAAM;IACpC,IAAMW,YAAY,GAAG9B,sBAAsB,CAAC,+FAAD,EAAkG;MAAEsB,cAAc,EAAE,IAAlB;MAAwBS,EAAE,EAAE;IAA5B,CAAlG,EAAsI9B,QAAtI,CAA3C;IAEA6B,YAAY,CAACJ,MAAb,CAAoBN,MAApB,CAA2BC,KAA3B,CAAiC,CAAjC;IAEAS,YAAY,CAAC,CAAD,CAAZ,CAAgBpB,QAAhB,CAAyBU,MAAzB,CAAgCC,KAAhC,CAAsC,EAAtC;IACAS,YAAY,CAAC,CAAD,CAAZ,CAAgBnB,MAAhB,CAAuBS,MAAvB,CAA8BC,KAA9B,CAAoC,EAApC;IAEAS,YAAY,CAAC,CAAD,CAAZ,CAAgBlB,MAAhB,CAAuBA,MAAvB,CAA8BQ,MAA9B,CAAqCC,KAArC,CAA2C,cAA3C;IACAS,YAAY,CAAC,CAAD,CAAZ,CAAgBlB,MAAhB,CAAuBG,cAAvB,CAAsCK,MAAtC,CAA6CC,KAA7C,CAAmD,YAAnD;IACAS,YAAY,CAAC,CAAD,CAAZ,CAAgBlB,MAAhB,CAAuBI,OAAvB,CAA+BI,MAA/B,CAAsCC,KAAtC,CAA4C,IAA5C;IACAS,YAAY,CAAC,CAAD,CAAZ,CAAgBlB,MAAhB,CAAuBoB,kBAAvB,CAA0CZ,MAA1C,CAAiDC,KAAjD,CAAuD,GAAvD;IACAS,YAAY,CAAC,CAAD,CAAZ,CAAgBlB,MAAhB,CAAuBK,GAAvB,CAA2BG,MAA3B,CAAkCC,KAAlC,CAAwC,MAAxC;IAEAS,YAAY,CAAC,CAAD,CAAZ,CAAgBpB,QAAhB,CAAyBU,MAAzB,CAAgCC,KAAhC,CAAsC,EAAtC;IACAS,YAAY,CAAC,CAAD,CAAZ,CAAgBnB,MAAhB,CAAuBS,MAAvB,CAA8BC,KAA9B,CAAoC,EAApC;IAEAS,YAAY,CAAC,CAAD,CAAZ,CAAgBlB,MAAhB,CAAuBA,MAAvB,CAA8BQ,MAA9B,CAAqCC,KAArC,CAA2C,cAA3C;IACAS,YAAY,CAAC,CAAD,CAAZ,CAAgBlB,MAAhB,CAAuBG,cAAvB,CAAsCK,MAAtC,CAA6CC,KAA7C,CAAmD,YAAnD;IACAS,YAAY,CAAC,CAAD,CAAZ,CAAgBlB,MAAhB,CAAuBI,OAAvB,CAA+BI,MAA/B,CAAsCC,KAAtC,CAA4C,IAA5C;IACAS,YAAY,CAAC,CAAD,CAAZ,CAAgBlB,MAAhB,CAAuBoB,kBAAvB,CAA0CZ,MAA1C,CAAiDC,KAAjD,CAAuD,GAAvD;EACA,CArBC,CAAF;EAuBAF,EAAE,CAAC,mCAAD,EAAsC,YAAM;IAC7C;IACAf,iCAAiC,CAAC,YAAD,EAAe;MAAEkB,cAAc,EAAE;IAAlB,CAAf,EAAyCrB,QAAzC,CAAjC,CAAoFmB,MAApF,CAA2FQ,IAA3F,CAAgGP,KAAhG,CAAsG,EAAtG;EACA,CAHC,CAAF;EAKAF,EAAE,CAAC,iCAAD,EAAoC,YAAM;IAC3C;IACAf,iCAAiC,CAAC,sCAAD,EAAyCmB,SAAzC,EAAoDtB,QAApD,CAAjC,CAA+FmB,MAA/F,CAAsGQ,IAAtG,CAA2GP,KAA3G,CAAiH,CAAC;MACjHL,OAAO,EAAI,IADsG;MAEjHF,KAAK,EAAM,YAFsG;MAGjHJ,QAAQ,EAAG,EAHsG;MAIjHC,MAAM,EAAK;IAJsG,CAAD,CAAjH;EAMA,CARC,CAAF;EAUAQ,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC,IAAIc,OAAJ,CADqC,CAGrC;;IACA7B,iCAAiC,CAAC,EAAD,EAAKmB,SAAL,EAAgBtB,QAAhB,CAAjC,CAA2DmB,MAA3D,CAAkEQ,IAAlE,CAAuEP,KAAvE,CAA6E,EAA7E,EAJqC,CAMrC;IACA;IACA;IAEA;;IACAY,OAAO,GAAG;MAAA,OAAM7B,iCAAiC,CAAC,UAAD,EAAa;QAAEkB,cAAc,EAAE;MAAlB,CAAb,CAAvC;IAAA,CAAV;;IACAW,OAAO,CAACb,MAAR,UAAqB,sCAArB,EAZqC,CAcrC;IACA;IACA;IAEA;;IACAhB,iCAAiC,CAAC,EAAD,CAAjC,CAAsCgB,MAAtC,CAA6CQ,IAA7C,CAAkDP,KAAlD,CAAwD,EAAxD;EACA,CApBC,CAAF;EAsBAF,EAAE,CAAC,8EAAD,EAAiF,YAAM;IACxF,IAAMe,OAAO,GAAGlC,sBAAsB,CAAC,wDAAD,EAA2D;MAAEsB,cAAc,EAAE,IAAlB;MAAwBS,EAAE,EAAE;IAA5B,CAA3D,EAA+F9B,QAA/F,CAAtC;IACAiC,OAAO,CAACR,MAAR,CAAeN,MAAf,CAAsBC,KAAtB,CAA4B,CAA5B;IACAa,OAAO,CAAC,CAAD,CAAP,CAAWtB,MAAX,CAAkBG,cAAlB,CAAiCK,MAAjC,CAAwCC,KAAxC,CAA8C,YAA9C;EACA,CAJC,CAAF;EAMAF,EAAE,CAAC,2DAAD,EAA8D,YAAM;IACrE;IACAf,iCAAiC,CAAC,kBAAD,EAAqB;MAAEkB,cAAc,EAAE;IAAlB,CAArB,EAA+CrB,QAA/C,CAAjC,CAA0FmB,MAA1F,CAAiGQ,IAAjG,CAAsGP,KAAtG,CAA4G,EAA5G,EAFqE,CAIrE;;IACAjB,iCAAiC,CAAC,eAAD,EAAkB;MAAEkB,cAAc,EAAE;IAAlB,CAAlB,EAA4CrB,QAA5C,CAAjC,CAAuFmB,MAAvF,CAA8FQ,IAA9F,CAAmGP,KAAnG,CAAyG,CAAC;MACzGL,OAAO,EAAI,IAD8F;MAEzGF,KAAK,EAAM,YAF8F;MAGzGJ,QAAQ,EAAG,CAH8F;MAIzGC,MAAM,EAAK;IAJ8F,CAAD,CAAzG,EALqE,CAYrE;;IACAP,iCAAiC,CAAC,aAAD,EAAgB;MAAEkB,cAAc,EAAE;IAAlB,CAAhB,EAA0CrB,QAA1C,CAAjC,CAAqFmB,MAArF,CAA4FQ,IAA5F,CAAiGP,KAAjG,CAAuG,EAAvG,EAbqE,CAerE;;IACAjB,iCAAiC,CAAC,aAAD,EAAgB;MAAEkB,cAAc,EAAE;IAAlB,CAAhB,EAA0CrB,QAA1C,CAAjC,CAAqFmB,MAArF,CAA4FQ,IAA5F,CAAiGP,KAAjG,CAAuG,EAAvG,EAhBqE,CAkBrE;;IACAjB,iCAAiC,CAAC,gCAAD,EAAmC;MAAEkB,cAAc,EAAE;IAAlB,CAAnC,EAA6DrB,QAA7D,CAAjC,CAAwGmB,MAAxG,CAA+GQ,IAA/G,CAAoHP,KAApH,CAA0H,CAAC;MAC1HL,OAAO,EAAI,IAD+G;MAE1HF,KAAK,EAAM,WAF+G;MAG1HJ,QAAQ,EAAG,EAH+G;MAI1HC,MAAM,EAAK;IAJ+G,CAAD,CAA1H,EAnBqE,CA0BrE;IACA;;IACA,IAAMwB,eAAe,GAAG/B,iCAAiC,CAAC,iDAAD,EAAoD;MAAEkB,cAAc,EAAE,IAAlB;MAAwBc,QAAQ,EAAE;IAAlC,CAApD,EAA8FnC,QAA9F,CAAzD;IACAkC,eAAe,CAACT,MAAhB,CAAuBN,MAAvB,CAA8BC,KAA9B,CAAoC,CAApC;IACAc,eAAe,CAAC,CAAD,CAAf,CAAmBnB,OAAnB,CAA2BI,MAA3B,CAAkCC,KAAlC,CAAwC,IAAxC;IACAc,eAAe,CAAC,CAAD,CAAf,CAAmBrB,KAAnB,CAAyBM,MAAzB,CAAgCC,KAAhC,CAAsC,WAAtC,EA/BqE,CAiCrE;IACA;;IACAjB,iCAAiC,CAAC,iDAAD,EAAoD;MAAEkB,cAAc,EAAE;IAAlB,CAApD,EAA8ErB,QAA9E,CAAjC,CAAyHmB,MAAzH,CAAgIQ,IAAhI,CAAqIP,KAArI,CAA2I,EAA3I;EACA,CApCC,CAAF,CAlMwC,CAwOxC;;EACAF,EAAE,CAAC,oHAAD,EAAuH,YAAM;IAC9Hf,iCAAiC,CAAC,4BAAD,EAA+B;MAAEkB,cAAc,EAAE;IAAlB,CAA/B,EAAyDnB,WAAzD,CAAjC,CACEiB,MADF,CAEEQ,IAFF,CAGEP,KAHF,CAGQ,CAAC;MACPL,OAAO,EAAE,IADF;MAEPF,KAAK,EAAE,UAFA;MAGPJ,QAAQ,EAAE,CAHH;MAIPC,MAAM,EAAE;IAJD,CAAD,CAHR;EASA,CAVC,CAAF;AAWA,CApPO,CAAR"}