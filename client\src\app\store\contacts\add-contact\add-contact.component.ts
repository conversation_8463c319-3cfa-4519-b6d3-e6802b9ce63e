import { Component, OnInit,ViewChild} from '@angular/core';
import { Router } from '@angular/router';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import {
  Subject,
  takeUntil,
  Observable,
  concat,
  map,
  of,
  forkJoin,
} from 'rxjs';
import {
  distinctUntilChanged,
  switchMap,
  tap,
  catchError,
} from 'rxjs/operators';
import { MessageService } from 'primeng/api';
import { ContactsService } from '../contacts.service';
import { ProspectsService } from '../../prospects/prospects.service';
import { Country, State } from 'country-state-city';
import { CountryWiseMobileComponent } from '../../common-form/country-wise-mobile/country-wise-mobile.component';

@Component({
  selector: 'app-add-contact',
  templateUrl: './add-contact.component.html',
  styleUrl: './add-contact.component.scss',
})
export class AddContactComponent implements OnInit {
  @ViewChild(CountryWiseMobileComponent)
  countryMobileComponent!: CountryWiseMobileComponent;
  private unsubscribe$ = new Subject<void>();
  public submitted = false;
  public cpDepartments: { name: string; value: string }[] = [];
  public cpFunctions: { name: string; value: string }[] = [];
  public accounts$?: Observable<any[]>;
  public accountLoading = false;
  public accountInput$ = new Subject<string>();
  private defaultOptions: any = [];
  public saving = false;
  public countries: any[] = [];
  public selectedCountry: string = '';
  public phoneValidationMessage: string | null = null;
  public mobileValidationMessage: string | null = null;
  

  public ContactForm: FormGroup = this.formBuilder.group({
    first_name: ['', [Validators.required]],
    last_name: ['', [Validators.required]],
    bp_id: [null, [Validators.required]],
    title: [''],
    job_title: [''],
    contact_person_function_name: [''],
    contact_person_department_name: [''],
    destination_location_country: ['', [Validators.required]],
    phone_number: [''],
    fax_number: [''],
    mobile: ['', [Validators.required]],
    email_address: ['', [Validators.required, Validators.email]],
  });

  public selectedCountryForMobile: string =
  this.ContactForm.get('destination_location_country')?.value;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private messageservice: MessageService,
    private contactsservice: ContactsService,
    private prospectsservice: ProspectsService
  ) {}

  ngOnInit(): void {
    this.ContactForm.get('destination_location_country')?.valueChanges.subscribe((countryCode) => {
      this.selectedCountryForMobile = countryCode;
    });
    this.loadAccounts();
    this.loadCountries();
    forkJoin({
      departments: this.contactsservice.getCPDepartment(),
      functions: this.contactsservice.getCPFunction(),
    })
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(({ departments, functions }) => {
        // Load departments
        this.cpDepartments = (departments?.data || []).map((item: any) => ({
          name: item.description,
          value: item.code,
        }));

        // Load functions
        this.cpFunctions = (functions?.data || []).map((item: any) => ({
          name: item.description,
          value: item.code,
        }));
      });
  }

  private loadCountries() {
    const allCountries = Country.getAllCountries()
      .map((country: any) => ({
        name: country.name,
        isoCode: country.isoCode,
      }))
      .filter(
        (country) => State.getStatesOfCountry(country.isoCode).length > 0
      );

    const unitedStates = allCountries.find((c) => c.isoCode === 'US');
    const canada = allCountries.find((c) => c.isoCode === 'CA');
    const others = allCountries
      .filter((c) => c.isoCode !== 'US' && c.isoCode !== 'CA')
      .sort((a, b) => a.name.localeCompare(b.name)); // sort others alphabetically

    this.countries = [unitedStates, canada, ...others].filter(Boolean);
  }

  triggerMobileValidation() {
    this.countryMobileComponent.validatePhone();
  }

  private loadAccounts() {
    this.accounts$ = concat(
      of(this.defaultOptions), // Default empty options
      this.accountInput$.pipe(
        distinctUntilChanged(),
        tap(() => (this.accountLoading = true)),
        switchMap((term: string) => {
          const params: any = {
            [`filters[roles][bp_role][$eq][0]`]: 'FLCU01',
            [`filters[roles][bp_role][$eq][1]`]: 'FLCU00',
            [`fields[0]`]: 'bp_id',
            [`fields[1]`]: 'first_name',
            [`fields[2]`]: 'last_name',
            [`fields[3]`]: 'bp_full_name',
          };

          if (term) {
            params[`filters[$or][0][bp_id][$containsi]`] = term;
            params[`filters[$or][1][bp_full_name][$containsi]`] = term;
          }

          return this.contactsservice.getAccounts(params).pipe(
            map((data: any) => {
              return data || []; // Make sure to return correct data structure
            }),
            tap(() => (this.accountLoading = false)),
            catchError((error) => {
              this.accountLoading = false;
              return of([]);
            })
          );
        })
      )
    );
  }

  async onSubmit() {
    this.submitted = true;

    if (this.ContactForm.invalid) {
      console.log('Form is invalid:', this.ContactForm.errors);
      return;
    }

    this.saving = true;
    const value = { ...this.ContactForm.value };

    const selectedcodewisecountry = this.countries.find(
      (c) => c.isoCode === this.selectedCountry
    );

    const data = {
      first_name: value?.first_name || '',
      middle_name: value?.middle_name,
      last_name: value?.last_name || '',
      bp_id: value?.bp_id || '',
      title: value?.title || '',
      job_title: value?.job_title || '',
      contact_person_function_name:
        value?.contact_person_function_name?.name || '',
      contact_person_function: value?.contact_person_function_name?.value || '',
      contact_person_department_name:
        value?.contact_person_department_name?.name || '',
      contact_person_department:
        value?.contact_person_department_name?.value || '',
      destination_location_country: selectedcodewisecountry?.isoCode,
      email_address: value?.email_address,
      phone_number: value?.phone_number,
      fax_number: value?.fax_number,
      mobile: value?.mobile,
    };

    this.contactsservice
      .createContact(data)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe({
        next: (response: any) => {
          if (response?.data?.documentId) {
            sessionStorage.setItem(
              'contactMessage',
              'Contact created successfully!'
            );
            window.location.href = `${window.location.origin}#/store/contacts/${response?.data?.documentId}/overview`;
          } else {
            console.error('Missing documentId in response:', response);
          }
        },
        error: (res: any) => {
          this.saving = false;
          this.messageservice.add({
            severity: 'error',
            detail: 'Error while processing your request.',
          });
        },
      });
  }
  onCancel() {
    this.router.navigate(['/store/prospects']);
  }

  get f(): any {
    return this.ContactForm.controls;
  }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
