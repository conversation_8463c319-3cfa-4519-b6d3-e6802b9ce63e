import { Component, OnInit, ViewChild } from '@angular/core';
import { OrganizationalService } from '../../organizational.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';
import {
  distinctUntilChanged,
  switchMap,
  tap,
  catchError,
  debounceTime,
  finalize,
} from 'rxjs/operators';
import { MessageService, ConfirmationService } from 'primeng/api';
import { ActivatedRoute } from '@angular/router';
import { Country, State } from 'country-state-city';
import { CountryWiseMobileComponent } from 'src/app/store/common-form/country-wise-mobile/country-wise-mobile.component';

interface Column {
  field: string;
  header: string;
}

@Component({
  selector: 'app-general',
  templateUrl: './general.component.html',
  styleUrl: './general.component.scss',
})
export class GeneralComponent implements OnInit {
  @ViewChild(CountryWiseMobileComponent)
  countryMobileComponent!: CountryWiseMobileComponent;
  private unsubscribe$ = new Subject<void>();
  public parentunitDetails: any[] = [];
  public addressDetails: any[] = [];
  public units$?: Observable<any[]>;
  public unitLoading = false;
  public unitInput$ = new Subject<string>();
  public organisational_unit_id: string = '';
  public addParentDialogVisible: boolean = false;
  public addAddressDialogVisible: boolean = false;
  public visible: boolean = false;
  public position: string = 'right';
  public submitted = false;
  public editid: string = '';
  public editaddressid: string = '';
  public saving = false;
  private defaultOptions: any = [];
  public countries: any[] = [];
  public states: any[] = [];
  public selectedCountry: string = '';
  public selectedState: string = '';
  public phoneValidationMessage: string | null = null;
  public mobileValidationMessage: string | null = null;

  public ParentUnitForm: FormGroup = this.formBuilder.group({
    start_date: ['', [Validators.required]],
    end_date: ['', [Validators.required]],
    parent_organisational_unit_id: [''],
  });

  public AddressForm: FormGroup = this.formBuilder.group({
    name: ['', [Validators.required]],
    email_uri: [''],
    web_uri: [''],
    street_name: [''],
    country_code: ['', [Validators.required]],
    region_code: ['', [Validators.required]],
    city_name: [''],
    street_postal_code: [''],
    mobile_formatted_number_description: [''],
    conventional_phone_formatted_number_description: [''],
    start_date: ['', [Validators.required]],
    end_date: ['', [Validators.required]],
  });

  public selectedCountryForMobile: string =
    this.AddressForm.get('country_code')?.value;

  constructor(
    private route: ActivatedRoute,
    private organizationalservice: OrganizationalService,
    private formBuilder: FormBuilder,
    private messageservice: MessageService,
    private confirmationservice: ConfirmationService
  ) {}

  private _selectedColumnsMap: { [key: string]: Column[] } = {
    employee: [],
    manager: [],
  };

  public cols: Column[] = [
    { field: 'end_date', header: 'Valid To' },
    { field: 'parent_organisational_unit_id', header: 'Parent Unit ID' },
    { field: 'parent_organisational_unit.name', header: 'Parent Unit Name' },
  ];

  public colsaddress: Column[] = [
    { field: 'end_date', header: 'Valid To' },
    { field: 'name', header: 'Name' },
    { field: 'street_name', header: 'Address' },
    {
      field: 'conventional_phone_formatted_number_description',
      header: 'Phone',
    },
    { field: 'mobile_formatted_number_description', header: 'Mobile' },
    { field: 'email_uri', header: 'E-Mail' },
    { field: 'web_uri', header: 'WebSite' },
  ];

  sortFieldMap: { [key: string]: string } = {
    employee: '',
    manager: '',
  };
  sortOrderMap: { [key: string]: number } = {
    employee: 1,
    manager: 1,
  };

  // Sorting method
  customSort(field: string, module: 'parent' | 'address'): void {
    let sortdetails;
    if (module === 'parent') {
      sortdetails = this.parentunitDetails;
    } else if (module === 'address') {
      sortdetails = this.addressDetails;
    } else {
      console.warn('Unknown module:', module);
      return;
    }

    let currentField = this.sortFieldMap[module];
    let currentOrder = this.sortOrderMap[module];

    if (currentField === field) {
      currentOrder = -currentOrder;
    } else {
      currentField = field;
      currentOrder = 1;
    }

    this.sortFieldMap[module] = currentField;
    this.sortOrderMap[module] = currentOrder;

    sortdetails.sort((a, b) => {
      const value1 = this.resolveFieldData(a, field);
      const value2 = this.resolveFieldData(b, field);

      let result = 0;
      if (value1 == null && value2 != null) result = -1;
      else if (value1 != null && value2 == null) result = 1;
      else if (value1 == null && value2 == null) result = 0;
      else if (typeof value1 === 'string' && typeof value2 === 'string') {
        result = value1.localeCompare(value2);
      } else {
        result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;
      }

      return currentOrder * result;
    });
  }

  // Utility to resolve nested field values
  resolveFieldData(data: any, field: string): any {
    if (!data || !field) return null;
    return field.indexOf('.') === -1
      ? data[field]
      : field.split('.').reduce((obj, key) => obj?.[key], data);
  }

  // Dynamic selected columns getter/setter
  getSelectedColumns(module: 'parent' | 'address'): Column[] {
    return this._selectedColumnsMap[module];
  }

  setSelectedColumns(module: 'parent' | 'address', val: Column[]) {
    const baseCols = module === 'parent' ? this.cols : this.colsaddress;
    this._selectedColumnsMap[module] = baseCols.filter((col) =>
      val.includes(col)
    );
  }

  // Column reorder handler (per module)
  onColumnReorder(event: any, module: 'parent' | 'address') {
    const draggedCol = this._selectedColumnsMap[module][event.dragIndex];
    this._selectedColumnsMap[module].splice(event.dragIndex, 1);
    this._selectedColumnsMap[module].splice(event.dropIndex, 0, draggedCol);
  }

  ngOnInit(): void {
    setTimeout(() => {
      const successMessage = sessionStorage.getItem('organizationMessage');
      if (successMessage) {
        this.messageservice.add({
          severity: 'success',
          detail: successMessage,
        });
        sessionStorage.removeItem('organizationMessage');
      }
    }, 100);
    this.AddressForm.get('country_code')?.valueChanges.subscribe(
      (countryCode) => {
        this.selectedCountryForMobile = countryCode;
      }
    );
    this.loadParentUnit();
    this.loadCountries();
    this.organisational_unit_id =
      this.route.parent?.snapshot.paramMap.get('id') || '';
    this.organizationalservice.organizational
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((response: any) => {
        if (response) {
          this.parentunitDetails = [].concat(response ?? []);
          this.addressDetails = Array.isArray(response)
            ? response
            : response?.addresses || [];
        }
      });

    this._selectedColumnsMap['parent'] = this.cols;
    this._selectedColumnsMap['address'] = this.colsaddress;
  }

  loadCountries() {
    const allCountries = Country.getAllCountries()
      .map((country: any) => ({
        name: country.name,
        isoCode: country.isoCode,
      }))
      .filter(
        (country) => State.getStatesOfCountry(country.isoCode).length > 0
      );

    const unitedStates = allCountries.find((c) => c.isoCode === 'US');
    const canada = allCountries.find((c) => c.isoCode === 'CA');
    const others = allCountries
      .filter((c) => c.isoCode !== 'US' && c.isoCode !== 'CA')
      .sort((a, b) => a.name.localeCompare(b.name)); // sort others alphabetically

    this.countries = [unitedStates, canada, ...others].filter(Boolean);
  }

  onCountryChange() {
    this.states = State.getStatesOfCountry(this.selectedCountry).map(
      (state) => ({
        name: state.name,
        isoCode: state.isoCode,
      })
    );
    this.selectedState = ''; // Reset state
  }

  triggerMobileValidation() {
    this.countryMobileComponent.validatePhone();
  }

  private loadParentUnit(): void {
    this.units$ = concat(
      of(this.defaultOptions), // Emit default empty options first
      this.unitInput$.pipe(
        debounceTime(300), // Add debounce to reduce API calls
        distinctUntilChanged(),
        tap(() => (this.unitLoading = true)),
        switchMap((term: string) => {
          const params: any = {
            'fields[0]': 'organisational_unit_id',
            'fields[2]': 'name',
          };

          if (term) {
            params['filters[$or][0][organisational_unit_id][$containsi]'] =
              term;
            params['filters[$or][1][name][$containsi]'] = term;
          }

          return this.organizationalservice.getParentUnit(params).pipe(
            map((response: any) => response ?? []), // Ensure non-null
            catchError((error) => {
              console.error('Parent Unit fetch error:', error);
              return of([]); // Return empty list on error
            }),
            finalize(() => (this.unitLoading = false)) // Always turn off loading
          );
        })
      )
    );
  }

  editUnit(unit: any) {
    this.addParentDialogVisible = true;
    this.editid = unit?.documentId;

    this.defaultOptions = [];
    this.defaultOptions.push({
      name: unit?.parent_organisational_unit?.name,
      organisational_unit_id: unit?.organisational_unit_id,
    });
    this.loadParentUnit();

    this.ParentUnitForm.patchValue({
      start_date: unit?.start_date ? new Date(unit?.start_date) : null,
      end_date: unit?.end_date ? new Date(unit?.end_date) : null,
      parent_organisational_unit_id: unit?.parent_organisational_unit_id,
    });
  }

  editAddress(address: any) {
    this.addAddressDialogVisible = true;
    this.editaddressid = address?.documentId;

    this.AddressForm.patchValue({
      start_date: address?.start_date ? new Date(address?.start_date) : null,
      end_date: address?.end_date ? new Date(address?.end_date) : null,
      name: address?.name,
      email_uri: address?.email_uri,
      web_uri: address?.web_uri,
      street_name: address?.street_name,
      country_code: address?.country_code,
      region_code: address?.region_code,
      city_name: address?.city_name,
      street_postal_code: address?.street_postal_code,
      mobile_formatted_number_description:
        address?.mobile_formatted_number_description,
      conventional_phone_formatted_number_description:
        address?.conventional_phone_formatted_number_description,
    });
  }

  async onSubmit() {
    this.submitted = true;
    this.visible = true;

    if (this.ParentUnitForm.invalid) {
      console.log('Form is invalid:', this.ParentUnitForm.errors);
      this.visible = true;
      return;
    }

    this.saving = true;
    const value = { ...this.ParentUnitForm.value };

    const data = {
      start_date: value?.start_date ? this.formatDate(value.start_date) : null,
      end_date: value?.end_date ? this.formatDate(value.end_date) : null,
      parent_organisational_unit_id: value?.parent_organisational_unit_id,
      organisational_unit_id: this.organisational_unit_id,
    };

    let unitRequest$: Observable<any>;

    if (this.editid) {
      unitRequest$ = this.organizationalservice.updateParentUnit(
        this.editid,
        data
      );
    } else {
      unitRequest$ = this.organizationalservice.createParentUnit(data);
    }

    unitRequest$.pipe(takeUntil(this.unsubscribe$)).subscribe({
      complete: () => {
        this.saving = false;
        this.addParentDialogVisible = false;
        this.ParentUnitForm.reset();
        this.messageservice.add({
          severity: 'success',
          detail: this.editid
            ? 'Parent Unit updated successfully!'
            : 'Parent Unit created successfully!',
        });
        this.organizationalservice
          .getOrganizationByID(this.organisational_unit_id)
          .pipe(takeUntil(this.unsubscribe$))
          .subscribe();
      },
      error: () => {
        this.saving = false;
        this.addParentDialogVisible = false;
        this.messageservice.add({
          severity: 'error',
          detail: 'Error while processing your request.',
        });
      },
    });
  }

  async onSubmitAddress() {
    this.submitted = true;
    this.visible = true;

    if (this.AddressForm.invalid) {
      console.log('Form is invalid:', this.AddressForm.errors);
      this.visible = true;
      return;
    }

    this.saving = true;
    const value = { ...this.AddressForm.value };

    const data = {
      name: value?.name,
      email_uri: value?.email_uri,
      web_uri: value?.web_uri,
      street_name: value?.street_name,
      country_code: value?.country_code,
      region_code: value?.region_code,
      city_name: value?.city_name,
      street_postal_code: value?.street_postal_code,
      mobile_formatted_number_description:
        value?.mobile_formatted_number_description,
      conventional_phone_formatted_number_description:
        value?.conventional_phone_formatted_number_description,
      start_date: value?.start_date ? this.formatDate(value.start_date) : null,
      end_date: value?.end_date ? this.formatDate(value.end_date) : null,
      organisational_unit_id: this.organisational_unit_id,
    };

    let addressRequest$: Observable<any>;

    if (this.editaddressid) {
      addressRequest$ = this.organizationalservice.updateAddress(
        this.editaddressid,
        data
      );
    } else {
      addressRequest$ = this.organizationalservice.createAddress(data);
    }

    addressRequest$.pipe(takeUntil(this.unsubscribe$)).subscribe({
      complete: () => {
        this.saving = false;
        this.addAddressDialogVisible = false;
        this.AddressForm.reset();
        this.messageservice.add({
          severity: 'success',
          detail: 'Address created successfully!.',
        });
        this.organizationalservice
          .getOrganizationByID(this.organisational_unit_id)
          .pipe(takeUntil(this.unsubscribe$))
          .subscribe();
      },
      error: () => {
        this.saving = false;
        this.addAddressDialogVisible = false;
        this.messageservice.add({
          severity: 'error',
          detail: 'Error while processing your request.',
        });
      },
    });
  }

  formatDate(date: Date): string {
    if (!date) return '';
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, '0');
    const dd = String(date.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  }

  confirmRemove(item: any, module: string) {
    this.confirmationservice.confirm({
      message: 'Are you sure you want to delete the selected records?',
      header: 'Confirm',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.remove(item, module);
      },
    });
  }

  remove(item: any, module: string) {
    let deleteObservable;

    if (module === 'parent') {
      const data = {
        organisational_unit_id: this.organisational_unit_id,
        start_date: item.start_date,
        end_date: item.end_date,
        parent_organisational_unit_id: '',
      };
      deleteObservable = this.organizationalservice.updateParentUnit(
        item.documentId,
        data
      );
    } else if (module === 'address') {
      deleteObservable = this.organizationalservice.deleteAddress(
        item.documentId
      );
    } else {
      console.warn('Unknown module:', module);
      return;
    }
    deleteObservable.pipe(takeUntil(this.unsubscribe$)).subscribe({
      next: () => {
        this.messageservice.add({
          severity: 'success',
          detail: 'Record Deleted Successfully!',
        });
        this.organizationalservice
          .getOrganizationByID(this.organisational_unit_id)
          .pipe(takeUntil(this.unsubscribe$))
          .subscribe();
      },
      error: () => {
        this.messageservice.add({
          severity: 'error',
          detail: 'Error while processing your request.',
        });
      },
    });
  }

  showNewDialog(position: string, dialog: string) {
    this.position = position;
    this.submitted = false;

    if (dialog === 'parent') {
      this.addParentDialogVisible = true;
      this.ParentUnitForm.reset();
    } else if (dialog === 'address') {
      this.addAddressDialogVisible = true;
      this.AddressForm.reset();
    }
  }

  get f(): any {
    return this.ParentUnitForm.controls;
  }

  get faddress(): any {
    return this.AddressForm.controls;
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
