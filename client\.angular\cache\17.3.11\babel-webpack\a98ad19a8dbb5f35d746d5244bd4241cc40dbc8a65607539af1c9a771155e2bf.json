{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Country } from 'country-state-city';\nimport { parsePhoneNumberFromString } from 'libphonenumber-js/max';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@ng-select/ng-select\";\nfunction CountryWiseMobileComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"img\", 5);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.item;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.getFlagUrl(item_r1.isoCode), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.name);\n  }\n}\nfunction CountryWiseMobileComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 5);\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.item;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r1.getFlagUrl(item_r3.isoCode), i0.ɵɵsanitizeUrl);\n  }\n}\nexport class CountryWiseMobileComponent {\n  constructor() {\n    this.ngUnsubscribe = new Subject();\n    this.validationResult = new EventEmitter();\n    this.countryList = [];\n  }\n  ngOnInit() {\n    this.countryList = Country.getAllCountries().map(c => ({\n      isoCode: c.isoCode,\n      name: c.name\n    }));\n    this.setupValueChangeListener();\n  }\n  ngOnChanges(changes) {\n    if (changes['selectedCountry'] && this.selectedCountry) {\n      this.formGroup.get(this.countryControlName)?.setValue(this.selectedCountry, {\n        emitEvent: false\n      });\n    }\n    if (changes['phoneFieldName']) {\n      this.setupValueChangeListener();\n    }\n  }\n  setupValueChangeListener() {\n    this.phoneSubscription?.unsubscribe();\n    const control = this.formGroup.get(this.phoneFieldName);\n    if (control) {\n      this.phoneSubscription = control.valueChanges.subscribe(() => {\n        this.validatePhone();\n      });\n    }\n  }\n  getFlagUrl(isoCode) {\n    if (!isoCode) {\n      return '';\n    }\n    const flagUrl = `https://flagcdn.com/w40/${isoCode.toLowerCase()}.png`;\n    return flagUrl;\n  }\n  validatePhone() {\n    const countryCode = this.formGroup.get(this.controlName)?.value;\n    const phone = this.formGroup.get(this.phoneFieldName)?.value;\n    let message = null;\n    if (!phone) {\n      this.validationResult.emit(null);\n      return;\n    }\n    const countryName = this.countryList.find(c => c.isoCode === countryCode)?.name || countryCode;\n    if (!countryCode) {\n      message = `Please select a country for ${this.phoneFieldName}.`;\n    } else if (!phone) {\n      message = `Please enter ${this.phoneFieldName} for ${countryName}.`;\n    } else {\n      const phoneData = parsePhoneNumberFromString(phone, countryCode);\n      if (!phoneData?.isValid()) {\n        message = `Please enter a valid ${this.phoneFieldName} for ${countryName}.`;\n      } else if (phoneData?.nationalNumber?.length < 5 || phoneData?.nationalNumber?.length > 15) {\n        message = `Mobile number length invalid for ${countryName}.`;\n      }\n    }\n    this.validationResult.emit(message);\n  }\n  ngOnDestroy() {\n    this.phoneSubscription?.unsubscribe();\n    this.ngUnsubscribe.next();\n    this.ngUnsubscribe.complete();\n  }\n  static {\n    this.ɵfac = function CountryWiseMobileComponent_Factory(t) {\n      return new (t || CountryWiseMobileComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CountryWiseMobileComponent,\n      selectors: [[\"app-country-wise-mobile\"]],\n      inputs: {\n        formGroup: \"formGroup\",\n        countryControlName: \"countryControlName\",\n        controlName: \"controlName\",\n        selectedCountry: \"selectedCountry\",\n        phoneFieldName: \"phoneFieldName\"\n      },\n      outputs: {\n        validationResult: \"validationResult\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 4,\n      vars: 3,\n      consts: [[1, \"flex\", \"items-center\", \"gap-2\"], [\"bindLabel\", \"name\", \"bindValue\", \"isoCode\", 3, \"items\", \"ngModel\", \"disabled\"], [\"ng-option-tmp\", \"\"], [\"ng-label-tmp\", \"\"], [1, \"flex\", \"items-center\", \"gap-2\", \"w-full\"], [\"width\", \"20\", 3, \"src\"]],\n      template: function CountryWiseMobileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"ng-select\", 1);\n          i0.ɵɵtemplate(2, CountryWiseMobileComponent_ng_template_2_Template, 4, 2, \"ng-template\", 2)(3, CountryWiseMobileComponent_ng_template_3_Template, 1, 1, \"ng-template\", 3);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"items\", ctx.countryList)(\"ngModel\", ctx.selectedCountry)(\"disabled\", true);\n        }\n      },\n      dependencies: [i1.NgControlStatus, i1.NgModel, i2.NgSelectComponent, i2.NgOptionTemplateDirective, i2.NgLabelTemplateDirective],\n      styles: [\".ng-select .ng-dropdown-panel {\\n  width: 300px !important;\\n  \\n\\n}\\n\\n  .ng-select .ng-select-container {\\n  min-width: 70px;\\n  \\n\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvY29tbW9uLWZvcm0vY291bnRyeS13aXNlLW1vYmlsZS9jb3VudHJ5LXdpc2UtbW9iaWxlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksdUJBQUE7RUFDQSxxQkFBQTtBQUNKOztBQUVBO0VBQ0ksZUFBQTtFQUNBLHNCQUFBO0FBQ0oiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAgLm5nLXNlbGVjdCAubmctZHJvcGRvd24tcGFuZWwge1xyXG4gICAgd2lkdGg6IDMwMHB4ICFpbXBvcnRhbnQ7XHJcbiAgICAvKiBBZGp1c3QgYXMgbmVlZGVkICovXHJcbn1cclxuXHJcbjo6bmctZGVlcCAubmctc2VsZWN0IC5uZy1zZWxlY3QtY29udGFpbmVyIHtcclxuICAgIG1pbi13aWR0aDogNzBweDtcclxuICAgIC8qIFNtYWxsIHdoZW4gY2xvc2VkICovXHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Country", "parsePhoneNumberFromString", "Subject", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "getFlagUrl", "item_r1", "isoCode", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "name", "item_r3", "CountryWiseMobileComponent", "constructor", "ngUnsubscribe", "validationResult", "countryList", "ngOnInit", "getAllCountries", "map", "c", "setupValueChangeListener", "ngOnChanges", "changes", "selectedCountry", "formGroup", "get", "countryControlName", "setValue", "emitEvent", "phoneSubscription", "unsubscribe", "control", "phoneFieldName", "valueChanges", "subscribe", "validatePhone", "flagUrl", "toLowerCase", "countryCode", "controlName", "value", "phone", "message", "emit", "countryName", "find", "phoneData", "<PERSON><PERSON><PERSON><PERSON>", "nationalNumber", "length", "ngOnDestroy", "next", "complete", "selectors", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "CountryWiseMobileComponent_Template", "rf", "ctx", "ɵɵtemplate", "CountryWiseMobileComponent_ng_template_2_Template", "CountryWiseMobileComponent_ng_template_3_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\common-form\\country-wise-mobile\\country-wise-mobile.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\common-form\\country-wise-mobile\\country-wise-mobile.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  Input,\r\n  Output,\r\n  EventEmitter,\r\n  OnInit,\r\n  SimpleChanges,\r\n  OnChanges,\r\n} from '@angular/core';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { Country } from 'country-state-city';\r\nimport { parsePhoneNumberFromString } from 'libphonenumber-js/max';\r\nimport { Subject, Subscription } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-country-wise-mobile',\r\n  templateUrl: './country-wise-mobile.component.html',\r\n  styleUrl: './country-wise-mobile.component.scss',\r\n})\r\nexport class CountryWiseMobileComponent implements OnInit, OnChanges {\r\n  private ngUnsubscribe = new Subject<void>();\r\n  @Output() validationResult = new EventEmitter<string | null>();\r\n  @Input() formGroup!: FormGroup;\r\n  @Input() countryControlName!: string;\r\n  @Input() controlName!: string;\r\n  @Input() selectedCountry!: string;\r\n  @Input() phoneFieldName!: string;\r\n\r\n  public countryList: { isoCode: string; name: string }[] = [];\r\n  private phoneSubscription?: Subscription;\r\n\r\n  ngOnInit(): void {\r\n    this.countryList = Country.getAllCountries().map((c) => ({\r\n      isoCode: c.isoCode,\r\n      name: c.name,\r\n    }));\r\n    this.setupValueChangeListener();\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes['selectedCountry'] && this.selectedCountry) {\r\n      this.formGroup\r\n        .get(this.countryControlName)\r\n        ?.setValue(this.selectedCountry, { emitEvent: false });\r\n    }\r\n\r\n    if (changes['phoneFieldName']) {\r\n      this.setupValueChangeListener();\r\n    }\r\n  }\r\n\r\n  setupValueChangeListener(): void {\r\n    this.phoneSubscription?.unsubscribe();\r\n\r\n    const control = this.formGroup.get(this.phoneFieldName);\r\n    if (control) {\r\n      this.phoneSubscription = control.valueChanges.subscribe(() => {\r\n        this.validatePhone();\r\n      });\r\n    }\r\n  }\r\n\r\n  getFlagUrl(isoCode: string): string {\r\n    if (!isoCode) {\r\n      return '';\r\n    }\r\n    const flagUrl = `https://flagcdn.com/w40/${isoCode.toLowerCase()}.png`;\r\n    return flagUrl;\r\n  }\r\n\r\n  validatePhone(): void {\r\n    const countryCode = this.formGroup.get(this.controlName)?.value;\r\n    const phone = this.formGroup.get(this.phoneFieldName)?.value;\r\n\r\n    let message: string | null = null;\r\n\r\n    if (!phone) {\r\n      this.validationResult.emit(null);\r\n      return;\r\n    }\r\n\r\n    const countryName =\r\n      this.countryList.find((c) => c.isoCode === countryCode)?.name ||\r\n      countryCode;\r\n\r\n    if (!countryCode) {\r\n      message = `Please select a country for ${this.phoneFieldName}.`;\r\n    } else if (!phone) {\r\n      message = `Please enter ${this.phoneFieldName} for ${countryName}.`;\r\n    } else {\r\n      const phoneData = parsePhoneNumberFromString(phone, countryCode);\r\n\r\n      if (!phoneData?.isValid()) {\r\n        message = `Please enter a valid ${this.phoneFieldName} for ${countryName}.`;\r\n      } else if (\r\n        phoneData?.nationalNumber?.length < 5 ||\r\n        phoneData?.nationalNumber?.length > 15\r\n      ) {\r\n        message = `Mobile number length invalid for ${countryName}.`;\r\n      }\r\n    }\r\n\r\n    this.validationResult.emit(message);\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.phoneSubscription?.unsubscribe();\r\n    this.ngUnsubscribe.next();\r\n    this.ngUnsubscribe.complete();\r\n  }\r\n}\r\n", "<div class=\"flex items-center gap-2\">\r\n    <ng-select [items]=\"countryList\" [ngModel]=\"selectedCountry\" bindLabel=\"name\" bindValue=\"isoCode\" [disabled]=\"true\">\r\n\r\n        <ng-template ng-option-tmp let-item=\"item\">\r\n            <div class=\"flex items-center gap-2 w-full\">\r\n                <img [src]=\"getFlagUrl(item.isoCode)\" width=\"20\" />\r\n                <span>{{ item.name }}</span>\r\n            </div>\r\n        </ng-template>\r\n\r\n        <ng-template ng-label-tmp let-item=\"item\">\r\n            <img [src]=\"getFlagUrl(item.isoCode)\" width=\"20\"  />\r\n        </ng-template>\r\n\r\n    </ng-select>\r\n</div>"], "mappings": "AAAA,SAIEA,YAAY,QAIP,eAAe;AAEtB,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,0BAA0B,QAAQ,uBAAuB;AAClE,SAASC,OAAO,QAAsB,MAAM;;;;;;ICRhCC,EAAA,CAAAC,cAAA,aAA4C;IACxCD,EAAA,CAAAE,SAAA,aAAmD;IACnDF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAe;IACzBH,EADyB,CAAAI,YAAA,EAAO,EAC1B;;;;;IAFGJ,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAM,UAAA,QAAAC,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAC,OAAA,GAAAV,EAAA,CAAAW,aAAA,CAAgC;IAC/BX,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAY,iBAAA,CAAAH,OAAA,CAAAI,IAAA,CAAe;;;;;IAKzBb,EAAA,CAAAE,SAAA,aAAoD;;;;;IAA/CF,EAAA,CAAAM,UAAA,QAAAC,MAAA,CAAAC,UAAA,CAAAM,OAAA,CAAAJ,OAAA,GAAAV,EAAA,CAAAW,aAAA,CAAgC;;;ADQjD,OAAM,MAAOI,0BAA0B;EALvCC,YAAA;IAMU,KAAAC,aAAa,GAAG,IAAIlB,OAAO,EAAQ;IACjC,KAAAmB,gBAAgB,GAAG,IAAItB,YAAY,EAAiB;IAOvD,KAAAuB,WAAW,GAAwC,EAAE;;EAG5DC,QAAQA,CAAA;IACN,IAAI,CAACD,WAAW,GAAGtB,OAAO,CAACwB,eAAe,EAAE,CAACC,GAAG,CAAEC,CAAC,KAAM;MACvDb,OAAO,EAAEa,CAAC,CAACb,OAAO;MAClBG,IAAI,EAAEU,CAAC,CAACV;KACT,CAAC,CAAC;IACH,IAAI,CAACW,wBAAwB,EAAE;EACjC;EAEAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,iBAAiB,CAAC,IAAI,IAAI,CAACC,eAAe,EAAE;MACtD,IAAI,CAACC,SAAS,CACXC,GAAG,CAAC,IAAI,CAACC,kBAAkB,CAAC,EAC3BC,QAAQ,CAAC,IAAI,CAACJ,eAAe,EAAE;QAAEK,SAAS,EAAE;MAAK,CAAE,CAAC;IAC1D;IAEA,IAAIN,OAAO,CAAC,gBAAgB,CAAC,EAAE;MAC7B,IAAI,CAACF,wBAAwB,EAAE;IACjC;EACF;EAEAA,wBAAwBA,CAAA;IACtB,IAAI,CAACS,iBAAiB,EAAEC,WAAW,EAAE;IAErC,MAAMC,OAAO,GAAG,IAAI,CAACP,SAAS,CAACC,GAAG,CAAC,IAAI,CAACO,cAAc,CAAC;IACvD,IAAID,OAAO,EAAE;MACX,IAAI,CAACF,iBAAiB,GAAGE,OAAO,CAACE,YAAY,CAACC,SAAS,CAAC,MAAK;QAC3D,IAAI,CAACC,aAAa,EAAE;MACtB,CAAC,CAAC;IACJ;EACF;EAEA/B,UAAUA,CAACE,OAAe;IACxB,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO,EAAE;IACX;IACA,MAAM8B,OAAO,GAAG,2BAA2B9B,OAAO,CAAC+B,WAAW,EAAE,MAAM;IACtE,OAAOD,OAAO;EAChB;EAEAD,aAAaA,CAAA;IACX,MAAMG,WAAW,GAAG,IAAI,CAACd,SAAS,CAACC,GAAG,CAAC,IAAI,CAACc,WAAW,CAAC,EAAEC,KAAK;IAC/D,MAAMC,KAAK,GAAG,IAAI,CAACjB,SAAS,CAACC,GAAG,CAAC,IAAI,CAACO,cAAc,CAAC,EAAEQ,KAAK;IAE5D,IAAIE,OAAO,GAAkB,IAAI;IAEjC,IAAI,CAACD,KAAK,EAAE;MACV,IAAI,CAAC3B,gBAAgB,CAAC6B,IAAI,CAAC,IAAI,CAAC;MAChC;IACF;IAEA,MAAMC,WAAW,GACf,IAAI,CAAC7B,WAAW,CAAC8B,IAAI,CAAE1B,CAAC,IAAKA,CAAC,CAACb,OAAO,KAAKgC,WAAW,CAAC,EAAE7B,IAAI,IAC7D6B,WAAW;IAEb,IAAI,CAACA,WAAW,EAAE;MAChBI,OAAO,GAAG,+BAA+B,IAAI,CAACV,cAAc,GAAG;IACjE,CAAC,MAAM,IAAI,CAACS,KAAK,EAAE;MACjBC,OAAO,GAAG,gBAAgB,IAAI,CAACV,cAAc,QAAQY,WAAW,GAAG;IACrE,CAAC,MAAM;MACL,MAAME,SAAS,GAAGpD,0BAA0B,CAAC+C,KAAK,EAAEH,WAAW,CAAC;MAEhE,IAAI,CAACQ,SAAS,EAAEC,OAAO,EAAE,EAAE;QACzBL,OAAO,GAAG,wBAAwB,IAAI,CAACV,cAAc,QAAQY,WAAW,GAAG;MAC7E,CAAC,MAAM,IACLE,SAAS,EAAEE,cAAc,EAAEC,MAAM,GAAG,CAAC,IACrCH,SAAS,EAAEE,cAAc,EAAEC,MAAM,GAAG,EAAE,EACtC;QACAP,OAAO,GAAG,oCAAoCE,WAAW,GAAG;MAC9D;IACF;IAEA,IAAI,CAAC9B,gBAAgB,CAAC6B,IAAI,CAACD,OAAO,CAAC;EACrC;EAEAQ,WAAWA,CAAA;IACT,IAAI,CAACrB,iBAAiB,EAAEC,WAAW,EAAE;IACrC,IAAI,CAACjB,aAAa,CAACsC,IAAI,EAAE;IACzB,IAAI,CAACtC,aAAa,CAACuC,QAAQ,EAAE;EAC/B;;;uBA1FWzC,0BAA0B;IAAA;EAAA;;;YAA1BA,0BAA0B;MAAA0C,SAAA;MAAAC,MAAA;QAAA9B,SAAA;QAAAE,kBAAA;QAAAa,WAAA;QAAAhB,eAAA;QAAAS,cAAA;MAAA;MAAAuB,OAAA;QAAAzC,gBAAA;MAAA;MAAA0C,QAAA,GAAA5D,EAAA,CAAA6D,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClBnCnE,EADJ,CAAAC,cAAA,aAAqC,mBACmF;UAShHD,EAPA,CAAAqE,UAAA,IAAAC,iDAAA,yBAA2C,IAAAC,iDAAA,yBAOD;UAKlDvE,EADI,CAAAI,YAAA,EAAY,EACV;;;UAdSJ,EAAA,CAAAK,SAAA,EAAqB;UAAkEL,EAAvF,CAAAM,UAAA,UAAA8D,GAAA,CAAAjD,WAAA,CAAqB,YAAAiD,GAAA,CAAAzC,eAAA,CAA4B,kBAAuD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}