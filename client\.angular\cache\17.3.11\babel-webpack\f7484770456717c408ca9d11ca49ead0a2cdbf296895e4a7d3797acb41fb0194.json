{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { DialogModule } from 'primeng/dialog';\nimport { TableModule } from 'primeng/table';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { ButtonModule } from 'primeng/button';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { CalendarModule } from 'primeng/calendar';\nimport { ActivitiesFormComponent } from './activities-form/activities-form.component';\nimport { EditorModule } from 'primeng/editor';\nimport { ActivitiesTaskFollowupFormComponent } from './activities-task-followup-form/activities-task-followup-form.component';\nimport { ActivitiesCallFollowupFormComponent } from './activities-call-followup-form/activities-call-followup-form.component';\nimport { OpportunitiesFollowupFormComponent } from './opportunities-followup-form/opportunities-followup-form.component';\nimport { CountryWiseMobileComponent } from './country-wise-mobile/country-wise-mobile.component';\nimport * as i0 from \"@angular/core\";\nexport class CommonFormModule {\n  static {\n    this.ɵfac = function CommonFormModule_Factory(t) {\n      return new (t || CommonFormModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CommonFormModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, DialogModule, TableModule, NgSelectModule, ButtonModule, EditorModule, DropdownModule, CalendarModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CommonFormModule, {\n    declarations: [ActivitiesFormComponent, ActivitiesTaskFollowupFormComponent, ActivitiesCallFollowupFormComponent, OpportunitiesFollowupFormComponent, CountryWiseMobileComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, DialogModule, TableModule, NgSelectModule, ButtonModule, EditorModule, DropdownModule, CalendarModule],\n    exports: [ActivitiesFormComponent, ActivitiesTaskFollowupFormComponent, ActivitiesCallFollowupFormComponent, OpportunitiesFollowupFormComponent, CountryWiseMobileComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "DialogModule", "TableModule", "NgSelectModule", "ButtonModule", "DropdownModule", "CalendarModule", "ActivitiesFormComponent", "EditorModule", "ActivitiesTaskFollowupFormComponent", "ActivitiesCallFollowupFormComponent", "OpportunitiesFollowupFormComponent", "CountryWiseMobileComponent", "CommonFormModule", "declarations", "imports", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-CRM\\client\\src\\app\\store\\common-form\\common-form.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { TableModule } from 'primeng/table';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { ActivitiesFormComponent } from './activities-form/activities-form.component';\r\nimport { EditorModule } from 'primeng/editor';\r\nimport { ActivitiesTaskFollowupFormComponent } from './activities-task-followup-form/activities-task-followup-form.component';\r\nimport { ActivitiesCallFollowupFormComponent } from './activities-call-followup-form/activities-call-followup-form.component';\r\nimport { OpportunitiesFollowupFormComponent } from './opportunities-followup-form/opportunities-followup-form.component';\r\nimport { CountryWiseMobileComponent } from './country-wise-mobile/country-wise-mobile.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ActivitiesFormComponent,\r\n    ActivitiesTaskFollowupFormComponent,\r\n    ActivitiesCallFollowupFormComponent,\r\n    OpportunitiesFollowupFormComponent,\r\n    CountryWiseMobileComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    DialogModule,\r\n    TableModule,\r\n    NgSelectModule,\r\n    ButtonModule,\r\n    EditorModule,\r\n    DropdownModule,\r\n    CalendarModule,\r\n  ],\r\n  exports: [\r\n    ActivitiesFormComponent,\r\n    ActivitiesTaskFollowupFormComponent,\r\n    ActivitiesCallFollowupFormComponent,\r\n    OpportunitiesFollowupFormComponent,\r\n    CountryWiseMobileComponent\r\n  ],\r\n})\r\nexport class CommonFormModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,mCAAmC,QAAQ,yEAAyE;AAC7H,SAASC,mCAAmC,QAAQ,yEAAyE;AAC7H,SAASC,kCAAkC,QAAQ,qEAAqE;AACxH,SAASC,0BAA0B,QAAQ,qDAAqD;;AA8BhG,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAnBzBf,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,YAAY,EACZI,YAAY,EACZH,cAAc,EACdC,cAAc;IAAA;EAAA;;;2EAULO,gBAAgB;IAAAC,YAAA,GA1BzBP,uBAAuB,EACvBE,mCAAmC,EACnCC,mCAAmC,EACnCC,kCAAkC,EAClCC,0BAA0B;IAAAG,OAAA,GAG1BjB,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,YAAY,EACZI,YAAY,EACZH,cAAc,EACdC,cAAc;IAAAU,OAAA,GAGdT,uBAAuB,EACvBE,mCAAmC,EACnCC,mCAAmC,EACnCC,kCAAkC,EAClCC,0BAA0B;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}