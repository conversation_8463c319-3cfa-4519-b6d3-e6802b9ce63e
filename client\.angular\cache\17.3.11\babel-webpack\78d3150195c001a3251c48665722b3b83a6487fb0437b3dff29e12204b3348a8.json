{"ast": null, "code": "import withMetadataArgument from './withMetadataArgument.js';\nimport { default as _parsePhoneNumber } from '../../core/index.js';\nexport function parsePhoneNumber() {\n  return withMetadataArgument(_parsePhoneNumber, arguments);\n}", "map": {"version": 3, "names": ["withMetadataArgument", "default", "_parsePhoneNumber", "parsePhoneNumber", "arguments"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/max/exports/parsePhoneNumber.js"], "sourcesContent": ["import withMetadataArgument from './withMetadataArgument.js'\r\nimport { default as _parsePhoneNumber } from '../../core/index.js'\r\n\r\nexport function parsePhoneNumber() {\r\n\treturn withMetadataArgument(_parsePhoneNumber, arguments)\r\n}"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,2BAA2B;AAC5D,SAASC,OAAO,IAAIC,iBAAiB,QAAQ,qBAAqB;AAElE,OAAO,SAASC,gBAAgBA,CAAA,EAAG;EAClC,OAAOH,oBAAoB,CAACE,iBAAiB,EAAEE,SAAS,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}