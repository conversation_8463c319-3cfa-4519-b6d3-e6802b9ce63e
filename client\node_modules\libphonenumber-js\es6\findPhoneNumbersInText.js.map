{"version": 3, "file": "findPhoneNumbersInText.js", "names": ["PhoneNumberMatcher", "normalizeArguments", "findPhoneNumbersInText", "arguments", "text", "options", "metadata", "matcher", "v2", "results", "hasNext", "push", "next"], "sources": ["../source/findPhoneNumbersInText.js"], "sourcesContent": ["import PhoneNumberMatcher from './PhoneNumberMatcher.js'\r\nimport normalizeArguments from './normalizeArguments.js'\r\n\r\nexport default function findPhoneNumbersInText() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\tconst matcher = new PhoneNumberMatcher(text, { ...options, v2: true }, metadata)\r\n\tconst results = []\r\n\twhile (matcher.hasNext()) {\r\n\t\tresults.push(matcher.next())\r\n\t}\r\n\treturn results\r\n}"], "mappings": ";;;;;;AAAA,OAAOA,kBAAP,MAA+B,yBAA/B;AACA,OAAOC,kBAAP,MAA+B,yBAA/B;AAEA,eAAe,SAASC,sBAAT,GAAkC;EAChD,0BAAoCD,kBAAkB,CAACE,SAAD,CAAtD;EAAA,IAAQC,IAAR,uBAAQA,IAAR;EAAA,IAAcC,OAAd,uBAAcA,OAAd;EAAA,IAAuBC,QAAvB,uBAAuBA,QAAvB;;EACA,IAAMC,OAAO,GAAG,IAAIP,kBAAJ,CAAuBI,IAAvB,kCAAkCC,OAAlC;IAA2CG,EAAE,EAAE;EAA/C,IAAuDF,QAAvD,CAAhB;EACA,IAAMG,OAAO,GAAG,EAAhB;;EACA,OAAOF,OAAO,CAACG,OAAR,EAAP,EAA0B;IACzBD,OAAO,CAACE,IAAR,CAAaJ,OAAO,CAACK,IAAR,EAAb;EACA;;EACD,OAAOH,OAAP;AACA"}