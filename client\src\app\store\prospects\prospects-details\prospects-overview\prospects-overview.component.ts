import { Component, OnInit, ViewChild } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { ProspectsService } from '../../prospects.service';
import { MessageService } from 'primeng/api';
import { Router } from '@angular/router';
import { Country, State } from 'country-state-city';
import { CountryWiseMobileComponent } from 'src/app/store/common-form/country-wise-mobile/country-wise-mobile.component';

@Component({
  selector: 'app-prospects-overview',
  templateUrl: './prospects-overview.component.html',
  styleUrl: './prospects-overview.component.scss',
})
export class ProspectsOverviewComponent implements OnInit {
  @ViewChild(CountryWiseMobileComponent)
  countryMobileComponent!: CountryWiseMobileComponent;
  private ngUnsubscribe = new Subject<void>();
  public prospectDetails: any = null;
  public marketingDetails: any = null;
  public bpextensionDetails: any = null;
  public customer: any = null;
  public chain_scale: { label: string; value: string }[] = [];
  public size_unit: { label: string; value: string }[] = [];
  public months = [
    { label: 'January', value: 'January' },
    { label: 'February', value: 'February' },
    { label: 'March', value: 'March' },
    { label: 'April', value: 'April' },
    { label: 'May', value: 'May' },
    { label: 'June', value: 'June' },
    { label: 'July', value: 'July' },
    { label: 'August', value: 'August' },
    { label: 'September', value: 'September' },
    { label: 'October', value: 'October' },
    { label: 'November', value: 'November' },
    { label: 'December', value: 'December' },
  ];
  public marketingoptions = [
    { label: 'Yes', value: 'Yes' },
    { label: 'No', value: 'No' },
  ];
  public ProspectOverviewForm: FormGroup = this.formBuilder.group({
    bp_full_name: ['', [Validators.required]],
    email_address: ['', [Validators.required, Validators.email]],
    website_url: [
      '',
      [
        Validators.pattern(
          /^(https?:\/\/)?([\w\-]+\.)+[\w\-]+(\/[\w\-./?%&=]*)?$/
        ),
      ],
    ],
    owner: [''],
    additional_street_prefix_name: [''],
    additional_street_suffix_name: [''],
    house_number: [''],
    street_name: [''],
    city_name: [''],
    region: ['', [Validators.required]],
    country: ['', [Validators.required]],
    postal_code: [
      '',
      [Validators.required, Validators.pattern(/^[A-Za-z0-9]{5}$/)],
    ],
    fax_number: [''],
    phone_number: [
      '',
      [Validators.required],
    ],
    mobile: [''],
  });

  public ProspectAttributeForm: FormGroup = this.formBuilder.group({
    pool: [''],
    restaurant: [''],
    conference_room: [''],
    fitness_center: [''],
    renovation_date: [''],
    date_opened: [''],
    seasonal_open_date: [''],
    seasonal_close_date: [''],
  });

  public submitted = false;
  public saving = false;
  public existingProspect: any;
  public bp_id: string = '';
  public editid: string = '';
  public isEditMode = false;
  public isAttributeEditMode = false;
  public countries: any[] = [];
  public states: any[] = [];
  public selectedCountry: string = '';
  public selectedState: string = '';
  public phoneValidationMessage: string | null = null;
  public mobileValidationMessage: string | null = null;
  public selectedCountryForMobile: string =
    this.ProspectOverviewForm.get('country')?.value;

  constructor(
    private formBuilder: FormBuilder,
    private prospectsservice: ProspectsService,
    private messageservice: MessageService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.ProspectOverviewForm.get('country')?.valueChanges.subscribe(
      (countryCode) => {
        this.selectedCountryForMobile = countryCode;
      }
    );
    this.loadChainScale();
    this.loadSizeUnit();
    this.loadCountries();
    // prospect successfully added message.
    setTimeout(() => {
      const successMessage = sessionStorage.getItem('prospectMessage');
      if (successMessage) {
        this.messageservice.add({
          severity: 'success',
          detail: successMessage,
        });
        sessionStorage.removeItem('prospectMessage');
      }
    }, 100);
    this.prospectsservice.prospect
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((response: any) => {
        if (!response?.addresses) return;
        this.bp_id = response?.bp_id;
        this.marketingDetails = response?.marketing_attributes;
        this.bpextensionDetails = response?.bp_extension;
        this.customer = response?.customer;
        this.prospectDetails = response.addresses
          .filter((address: { address_usages?: { address_usage: string }[] }) =>
            address?.address_usages?.some(
              (usage) => usage.address_usage === 'XXDEFAULT'
            )
          )
          .map((address: any) => ({
            ...address,
            updated_id: response?.documentId || '-',
            bp_full_name: response?.bp_full_name || '-',
            city_name: address?.city_name,
            country: address?.country || '-',
            postal_code: address?.postal_code || '-',
            region: address?.region || '-',
            state:
              this.getStateNameByCode(address?.region, address?.county_code) ||
              '-',
            street_name: address?.street_name,
            house_number: address?.house_number,
            email_address: address?.emails?.[0]?.email_address || '-',
            website_url: address?.home_page_urls?.[0]?.website_url,
            fax_number: address?.fax_numbers?.[0]?.fax_number,
            phone_number: (() => {
              const phone = (address?.phone_numbers ?? []).find(
                (p: any) => p.phone_number_type === '1'
              );
              if (!phone || !phone.phone_number) {
                return '-';
              }
              const countryCode = phone.destination_location_country;
              const rawNumber = phone.phone_number;
              return this.prospectsservice.getDialCode(countryCode, rawNumber);
            })(),
            mobile: (() => {
              const phone = (address?.phone_numbers ?? []).find(
                (p: any) => p.phone_number_type === '3'
              );
              if (!phone || !phone.phone_number) {
                return '-';
              }
              const countryCode = phone.destination_location_country;
              const rawNumber = phone.phone_number;
              return this.prospectsservice.getDialCode(countryCode, rawNumber);
            })(),

            country_phone_number: (address?.phone_numbers || []).find(
              (item: any) => item.phone_number_type === '1'
            )?.phone_number,
            country_mobile: (address?.phone_numbers || []).find(
              (item: any) => item.phone_number_type === '3'
            )?.phone_number,
            additional_street_prefix_name:
              address?.additional_street_prefix_name || '-',
            additional_street_suffix_name:
              address?.additional_street_suffix_name || '-',
            status: response?.is_marked_for_archiving ? 'Obsolete' : 'Active',
          }));

        if (this.prospectDetails.length > 0) {
          this.fetchProspectData(this.prospectDetails[0]);
        }

        if (this.marketingDetails) {
          this.ProspectAttributeForm.patchValue({
            pool: this.marketingDetails.pool || '',
            restaurant: this.marketingDetails.restaurant || '',
            conference_room: this.marketingDetails.conference_room || '',
            fitness_center: this.marketingDetails.fitness_center || '',
            str_chain_scale: this.marketingDetails.str_chain_scale || '',
            size: this.marketingDetails.size || '',
            size_unit: this.marketingDetails.size_unit || '',
            renovation_date: this.marketingDetails.renovation_date
              ? new Date(this.marketingDetails.renovation_date)
              : null,
            date_opened: this.marketingDetails.date_opened
              ? new Date(this.marketingDetails.date_opened)
              : null,
            seasonal_open_date: this.marketingDetails.seasonal_open_date || '',
            seasonal_close_date:
              this.marketingDetails.seasonal_close_date || '',
          });
        }
      });
  }

  fetchProspectData(prospect: any) {
    const selectedCountryObj = this.countries.find(
      (c) => c.name === prospect.country || c.isoCode === prospect.country
    );
    this.selectedCountry = selectedCountryObj ? selectedCountryObj.isoCode : '';
    this.onCountryChange(); // Load states based on the selected country
    setTimeout(() => {
      this.selectedState =
        this.states.find(
          (s) => s.name === prospect.region || s.isoCode === prospect.region
        )?.isoCode || '';
    }, 100);
    this.ProspectOverviewForm.patchValue({
      ...prospect,
      country: this.selectedCountry,
    });
    this.existingProspect = {
      bp_full_name: prospect.bp_full_name,
      email_address: prospect.email_address,
      website_url: prospect.website_url,
      house_number: prospect.house_number,
      fax_number: prospect.fax_number,
      additional_street_prefix_name: prospect.additional_street_prefix_name,
      additional_street_suffix_name: prospect.additional_street_suffix_name,
      country: prospect.country,
      region: prospect.region,
      city_name: prospect.city_name,
      street_name: prospect.street_name,
      postal_code: prospect.postal_code,
      phone_number: prospect.country_phone_number,
      mobile: prospect.country_mobile,
    };

    this.editid = prospect.updated_id;
    this.ProspectOverviewForm.patchValue(this.existingProspect);
  }

  loadCountries() {
    const allCountries = Country.getAllCountries()
      .map((country: any) => ({
        name: country.name,
        isoCode: country.isoCode,
      }))
      .filter(
        (country) => State.getStatesOfCountry(country.isoCode).length > 0
      );

    const unitedStates = allCountries.find((c) => c.isoCode === 'US');
    const canada = allCountries.find((c) => c.isoCode === 'CA');
    const others = allCountries
      .filter((c) => c.isoCode !== 'US' && c.isoCode !== 'CA')
      .sort((a, b) => a.name.localeCompare(b.name)); // sort others alphabetically

    this.countries = [unitedStates, canada, ...others].filter(Boolean);
  }

  onCountryChange() {
    this.states = State.getStatesOfCountry(this.selectedCountry).map(
      (state) => ({
        name: state.name,
        isoCode: state.isoCode,
      })
    );
    this.selectedState = ''; // Reset state
  }

  getStateNameByCode(stateCode: string, countryCode: string): string {
    const states = State.getStatesOfCountry(countryCode);
    const match = states.find((state) => state.isoCode === stateCode);
    return match ? match.name : 'Unknown State';
  }

  triggerMobileValidation() {
    this.countryMobileComponent.validatePhone();
  }

  public loadSizeUnit(): void {
    this.prospectsservice
      .getSizeUnit()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((response: any) => {
        if (response && response.data) {
          this.size_unit = response.data.map((item: any) => ({
            label: item.description,
            value: item.code,
          }));
        }
      });
  }

  public loadChainScale(): void {
    this.prospectsservice
      .getChainScale()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((response: any) => {
        if (response && response.data) {
          this.chain_scale = response.data.map((item: any) => ({
            label: item.description,
            value: item.code,
          }));
        }
      });
  }

  async onSubmit() {
    this.submitted = true;

    if (this.ProspectOverviewForm.invalid) {
      return;
    }

    this.saving = true;
    const value = { ...this.ProspectOverviewForm.value };

    const selectedcodewisecountry = this.countries.find(
      (c) => c.isoCode === this.selectedCountry
    );

    const selectedState = this.states.find(
      (state) => state.isoCode === value?.region
    );

    const data = {
      bp_full_name: value?.bp_full_name,
      email_address: value?.email_address,
      website_url: value?.website_url,
      house_number: value?.house_number,
      fax_number: value?.fax_number,
      additional_street_prefix_name: value?.additional_street_prefix_name,
      additional_street_suffix_name: value?.additional_street_suffix_name,
      country: selectedcodewisecountry?.name,
      county_code: selectedcodewisecountry?.isoCode,
      destination_location_country: selectedcodewisecountry?.isoCode,
      region: selectedState?.isoCode,
      city_name: value?.city_name,
      street_name: value?.street_name,
      postal_code: value?.postal_code,
      phone_number: value?.phone_number,
      mobile: value?.mobile,
    };

    this.prospectsservice
      .updateProspect(this.editid, data)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (response: any) => {
          this.messageservice.add({
            severity: 'success',
            detail: 'Prospect Updated successFully!',
          });
          this.prospectsservice
            .getProspectByID(this.bp_id)
            .pipe(takeUntil(this.ngUnsubscribe))
            .subscribe();
          this.isEditMode = false;
        },
        error: (res: any) => {
          this.saving = false;
          this.isEditMode = true;
          this.messageservice.add({
            severity: 'error',
            detail: 'Error while processing your request.',
          });
        },
      });
  }

  async onAttributeSubmit() {
    this.submitted = true;

    if (this.ProspectAttributeForm.invalid) {
      return;
    }

    this.saving = true;
    const value = { ...this.ProspectAttributeForm.value };

    const data = {
      pool: value?.pool,
      restaurant: value?.restaurant,
      conference_room: value?.conference_room,
      fitness_center: value?.fitness_center,
      date_opened: value?.date_opened
        ? this.formatDate(value.date_opened)
        : null,
      renovation_date: value?.renovation_date
        ? this.formatDate(value.renovation_date)
        : null,
      seasonal_open_date: value?.seasonal_open_date,
      seasonal_close_date: value?.seasonal_close_date,
      bp_id: this?.bp_id,
    };

    const apiCall = this.marketingDetails
      ? this.prospectsservice.updateMarketing(
          this.marketingDetails.documentId,
          data
        ) // Update if exists
      : this.prospectsservice.createMarketing(data); // Create if not exists
    apiCall.pipe(takeUntil(this.ngUnsubscribe)).subscribe({
      next: () => {
        this.messageservice.add({
          severity: 'success',
          detail: 'Prospect Attributes Updated successFully!',
        });
        this.prospectsservice
          .getProspectByID(this.bp_id)
          .pipe(takeUntil(this.ngUnsubscribe))
          .subscribe();
        this.isAttributeEditMode = false;
      },
      error: () => {
        this.saving = false;
        this.isAttributeEditMode = true;
        this.messageservice.add({
          severity: 'error',
          detail: 'Error while processing your request.',
        });
      },
    });
  }

  formatDate(date: Date): string {
    if (!date) return '';
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, '0');
    const dd = String(date.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  }

  getChainScaleLabel(value: string): string | undefined {
    return this.chain_scale.find((opt) => opt.value === value)?.label;
  }

  getSizeUnitLabel(value: string): string | undefined {
    return this.size_unit.find((opt) => opt.value === value)?.label;
  }

  get f(): any {
    return this.ProspectOverviewForm.controls;
  }

  toggleEdit() {
    this.isEditMode = !this.isEditMode;
  }

  toggleAttributeEdit() {
    this.isAttributeEditMode = !this.isAttributeEditMode;
  }

  onReset(): void {
    this.submitted = false;
    this.ProspectOverviewForm.reset();
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
