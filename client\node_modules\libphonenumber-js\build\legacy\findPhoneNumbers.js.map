{"version": 3, "file": "findPhoneNumbers.js", "names": ["findPhoneNumbers", "normalizeArguments", "arguments", "text", "options", "metadata", "_findPhoneNumbers", "searchPhoneNumbers", "_searchPhoneNumbers"], "sources": ["../../source/legacy/findPhoneNumbers.js"], "sourcesContent": ["// This is a legacy function.\r\n// Use `findNumbers()` instead.\r\n\r\nimport _findPhoneNumbers, { searchPhoneNumbers as _searchPhoneNumbers } from './findPhoneNumbersInitialImplementation.js'\r\nimport normalizeArguments from '../normalizeArguments.js'\r\n\r\nexport default function findPhoneNumbers()\r\n{\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn _findPhoneNumbers(text, options, metadata)\r\n}\r\n\r\n/**\r\n * @return ES6 `for ... of` iterator.\r\n */\r\nexport function searchPhoneNumbers()\r\n{\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn _searchPhoneNumbers(text, options, metadata)\r\n}"], "mappings": ";;;;;;;;;;AAGA;;AACA;;;;;;;;AAJA;AACA;AAKe,SAASA,gBAAT,GACf;EACC,0BAAoC,IAAAC,+BAAA,EAAmBC,SAAnB,CAApC;EAAA,IAAQC,IAAR,uBAAQA,IAAR;EAAA,IAAcC,OAAd,uBAAcA,OAAd;EAAA,IAAuBC,QAAvB,uBAAuBA,QAAvB;;EACA,OAAO,IAAAC,iDAAA,EAAkBH,IAAlB,EAAwBC,OAAxB,EAAiCC,QAAjC,CAAP;AACA;AAED;AACA;AACA;;;AACO,SAASE,kBAAT,GACP;EACC,2BAAoC,IAAAN,+BAAA,EAAmBC,SAAnB,CAApC;EAAA,IAAQC,IAAR,wBAAQA,IAAR;EAAA,IAAcC,OAAd,wBAAcA,OAAd;EAAA,IAAuBC,QAAvB,wBAAuBA,QAAvB;;EACA,OAAO,IAAAG,yDAAA,EAAoBL,IAApB,EAA0BC,OAA1B,EAAmCC,QAAnC,CAAP;AACA"}