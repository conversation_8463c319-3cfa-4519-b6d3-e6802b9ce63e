<p-toast position="top-right" [life]="3000"></p-toast>
<form [formGroup]="ContactForm">
    <div class="card shadow-1 border-round-xl surface-0 p-5 mb-4 border-1 border-solid border-50">
        <h3 class="mb-2 flex align-items-center h-3rem">Create Contact</h3>
        <div class="p-fluid p-formgrid grid mt-0">
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">person</span>
                        First Name
                        <span class="text-red-500">*</span>
                    </label>
                    <input pInputText id="first_name" type="text" formControlName="first_name" placeholder="First Name"
                        class="h-3rem w-full" [ngClass]="{ 'is-invalid': submitted && f['first_name'].errors }" />
                    <div *ngIf="submitted && f['first_name'].errors" class="invalid-feedback">
                        <div *ngIf="f['first_name'].errors['required']">
                            First Name is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">person</span>
                        Last Name
                        <span class="text-red-500">*</span>
                    </label>
                    <input pInputText id="last_name" type="text" formControlName="last_name" placeholder="Last Name"
                        class="h-3rem w-full" [ngClass]="{ 'is-invalid': submitted && f['last_name'].errors }" />
                    <div *ngIf="submitted && f['last_name'].errors" class="invalid-feedback">
                        <div *ngIf="f['last_name'].errors['required']">
                            Last Name is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">account_circle</span>
                        Account
                        <span class="text-red-500">*</span>
                    </label>
                    <ng-select pInputText [items]="accounts$ | async" bindLabel="bp_full_name" bindValue="bp_id"
                        [hideSelected]="true" [loading]="accountLoading" [minTermLength]="0" formControlName="bp_id"
                        [typeahead]="accountInput$" [maxSelectedItems]="10" appendTo="body"
                        [ngClass]="{ 'is-invalid': submitted && f['bp_id'].errors }"
                        [class]="'multiselect-dropdown p-inputtext p-component p-element'"
                        placeholder="Search for an account">
                        <ng-template ng-option-tmp let-item="item">
                            <span>{{ item.bp_id }}</span>
                            <span *ngIf="item.bp_full_name"> : {{ item.bp_full_name }}</span>
                        </ng-template>
                    </ng-select>
                    <div *ngIf="submitted && f['bp_id'].errors" class="invalid-feedback">
                        <div *ngIf="f['bp_id'].errors['required']">
                            Account is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">title</span>
                        Title
                    </label>
                    <input pInputText id="title" type="text" formControlName="title" placeholder="Title"
                        class="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">work</span>
                        Job Title
                    </label>
                    <input pInputText id="job_title" type="text" formControlName="job_title" placeholder="Job Title"
                        class="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">functions</span>
                        Function
                    </label>
                    <p-dropdown [options]="cpFunctions" formControlName="contact_person_function_name"
                        optionLabel="name" dataKey="value" placeholder="Select Function"
                        [styleClass]="'h-3rem w-full'"></p-dropdown>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">inbox_text_person</span>
                        Department
                    </label>
                    <p-dropdown [options]="cpDepartments" formControlName="contact_person_department_name"
                        optionLabel="name" dataKey="value" placeholder="Select Department"
                        [styleClass]="'h-3rem w-full'">
                    </p-dropdown>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">map</span>
                        Country <span class="text-red-500">*</span>
                    </label>
                    <p-dropdown [options]="countries" optionLabel="name" optionValue="isoCode"
                        [(ngModel)]="selectedCountry" [filter]="true" formControlName="destination_location_country"
                        [styleClass]="'h-3rem w-full'" placeholder="Select Country"
                        [ngClass]="{ 'is-invalid': submitted && f['destination_location_country'].errors }">
                    </p-dropdown>
                    <div *ngIf="submitted && f['destination_location_country'].errors" class="invalid-feedback">
                        <div *ngIf="
                submitted &&
                f['destination_location_country'].errors &&
                f['destination_location_country'].errors['required']
              ">
                            Country is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">phone</span>
                        Phone
                    </label>
                    <div class="flex align-items-center gap-2">
                        <app-country-wise-mobile [formGroup]="ContactForm" controlName="destination_location_country"
                            phoneFieldName="phone_number" [selectedCountry]="selectedCountryForMobile"
                            (validationResult)="phoneValidationMessage = $event"></app-country-wise-mobile>
                        <input pInputText id="phone_number" type="text" formControlName="phone_number"
                            placeholder="Phone" class="h-3rem w-full" (input)="triggerMobileValidation()" />
                    </div>
                    <div class="p-error" *ngIf="ContactForm.get('phone_number')?.touched">
                        <div *ngIf="phoneValidationMessage">{{ phoneValidationMessage }}</div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">fax</span>
                        Fax
                    </label>
                    <input pInputText id="fax_number" type="text" formControlName="fax_number" placeholder="Fax"
                        class="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">phone_iphone</span>
                        Mobile
                        <span class="text-red-500">*</span>
                    </label>
                    <div class="flex align-items-center gap-2">
                        <app-country-wise-mobile [formGroup]="ContactForm" controlName="destination_location_country"
                            phoneFieldName="mobile" [selectedCountry]="selectedCountryForMobile"
                            (validationResult)="mobileValidationMessage = $event"></app-country-wise-mobile>
                        <input pInputText id="mobile" type="text" formControlName="mobile" placeholder="Mobile"
                            class="h-3rem w-full" [ngClass]="{ 'is-invalid': submitted && f['mobile'].errors }"
                            (input)="triggerMobileValidation()" />
                    </div>
                    <div class="p-error">
                        <div *ngIf="(ContactForm.get('mobile')?.touched || submitted) && mobileValidationMessage">
                            {{ mobileValidationMessage }}
                        </div>
                        <div *ngIf="(ContactForm.get('mobile')?.touched || submitted) && f['mobile'].errors?.required">
                            Mobile is required.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-300">mail</span>
                        E-mail
                        <span class="text-red-500">*</span>
                    </label>
                    <input pInputText id="email_address" type="text" formControlName="email_address"
                        placeholder="E-mail" class="h-3rem w-full"
                        [ngClass]="{ 'is-invalid': submitted && f['email_address'].errors }" />
                    <div *ngIf="submitted && f['email_address'].errors" class="invalid-feedback">
                        <div *ngIf="
              submitted &&
              f['email_address'].errors &&
              f['email_address'].errors['required']
            ">
                            Email is required.
                        </div>
                        <div *ngIf="f['email_address'].errors['email']">
                            Email is invalid.
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6"></div>
            <div class="flex align-items-center gap-3 mt-4 ml-auto">
                <button pButton type="button" label="Cancel"
                    class="p-button-rounded bg-light-blue border-none text-primary-700 justify-content-center w-9rem h-3rem"
                    (click)="onCancel()"></button>
                <button pButton type="submit" label="Create"
                    class="p-button-rounded justify-content-center w-9rem h-3rem" (click)="onSubmit()"></button>
            </div>
        </div>
    </div>
</form>