<div class="flex items-center gap-2">
    <ng-select [items]="countryList" [ngModel]="selectedCountry" bindLabel="name" bindValue="isoCode" [disabled]="true">

        <ng-template ng-option-tmp let-item="item">
            <div class="flex items-center gap-2 w-full">
                <img [src]="getFlagUrl(item.isoCode)" width="20" />
                <span>{{ item.name }}</span>
            </div>
        </ng-template>

        <ng-template ng-label-tmp let-item="item">
            <img [src]="getFlagUrl(item.isoCode)" width="20"  />
        </ng-template>

    </ng-select>
</div>