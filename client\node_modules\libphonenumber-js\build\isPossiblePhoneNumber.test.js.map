{"version": 3, "file": "isPossiblePhoneNumber.test.js", "names": ["isPossiblePhoneNumber", "parameters", "push", "metadata", "_isPossiblePhoneNumber", "apply", "describe", "it", "should", "equal", "defaultCountry", "expect", "oldMetadata", "to"], "sources": ["../source/isPossiblePhoneNumber.test.js"], "sourcesContent": ["import _isPossiblePhoneNumber from './isPossiblePhoneNumber.js'\r\nimport metadata from '../metadata.min.json' assert { type: 'json' }\r\nimport oldMetadata from '../test/metadata/1.0.0/metadata.min.json' assert { type: 'json' }\r\n\r\nfunction isPossiblePhoneNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _isPossiblePhoneNumber.apply(this, parameters)\r\n}\r\n\r\ndescribe('isPossiblePhoneNumber', () => {\r\n\tit('should detect whether a phone number is possible', () => {\r\n\t\tisPossiblePhoneNumber('8 (800) 555 35 35', 'RU').should.equal(true)\r\n\t\tisPossiblePhoneNumber('8 (800) 555 35 35 0', 'RU').should.equal(false)\r\n\t\tisPossiblePhoneNumber('Call: 8 (800) 555 35 35', 'RU').should.equal(false)\r\n\t\tisPossiblePhoneNumber('8 (800) 555 35 35', { defaultCountry: 'RU' }).should.equal(true)\r\n\t\tisPossiblePhoneNumber('+7 (800) 555 35 35').should.equal(true)\r\n\t\tisPossiblePhoneNumber('**** (800) 555 35 35').should.equal(false)\r\n\t\tisPossiblePhoneNumber(' +7 (800) 555 35 35').should.equal(false)\r\n\t\tisPossiblePhoneNumber(' ').should.equal(false)\r\n\t})\r\n\r\n\tit('should detect whether a phone number is possible when using old metadata', () => {\r\n\t\texpect(() => _isPossiblePhoneNumber('8 (800) 555 35 35', 'RU', oldMetadata))\r\n\t\t\t.to.throw('Missing \"possibleLengths\" in metadata.')\r\n\t\t_isPossiblePhoneNumber('+888 123 456 78901', oldMetadata).should.equal(true)\r\n\t})\r\n})"], "mappings": ";;AAAA;;AACA;;AACA;;;;AAEA,SAASA,qBAAT,GAA8C;EAAA,kCAAZC,UAAY;IAAZA,UAAY;EAAA;;EAC7CA,UAAU,CAACC,IAAX,CAAgBC,uBAAhB;EACA,OAAOC,kCAAA,CAAuBC,KAAvB,CAA6B,IAA7B,EAAmCJ,UAAnC,CAAP;AACA;;AAEDK,QAAQ,CAAC,uBAAD,EAA0B,YAAM;EACvCC,EAAE,CAAC,kDAAD,EAAqD,YAAM;IAC5DP,qBAAqB,CAAC,mBAAD,EAAsB,IAAtB,CAArB,CAAiDQ,MAAjD,CAAwDC,KAAxD,CAA8D,IAA9D;IACAT,qBAAqB,CAAC,qBAAD,EAAwB,IAAxB,CAArB,CAAmDQ,MAAnD,CAA0DC,KAA1D,CAAgE,KAAhE;IACAT,qBAAqB,CAAC,yBAAD,EAA4B,IAA5B,CAArB,CAAuDQ,MAAvD,CAA8DC,KAA9D,CAAoE,KAApE;IACAT,qBAAqB,CAAC,mBAAD,EAAsB;MAAEU,cAAc,EAAE;IAAlB,CAAtB,CAArB,CAAqEF,MAArE,CAA4EC,KAA5E,CAAkF,IAAlF;IACAT,qBAAqB,CAAC,oBAAD,CAArB,CAA4CQ,MAA5C,CAAmDC,KAAnD,CAAyD,IAAzD;IACAT,qBAAqB,CAAC,sBAAD,CAArB,CAA8CQ,MAA9C,CAAqDC,KAArD,CAA2D,KAA3D;IACAT,qBAAqB,CAAC,qBAAD,CAArB,CAA6CQ,MAA7C,CAAoDC,KAApD,CAA0D,KAA1D;IACAT,qBAAqB,CAAC,GAAD,CAArB,CAA2BQ,MAA3B,CAAkCC,KAAlC,CAAwC,KAAxC;EACA,CATC,CAAF;EAWAF,EAAE,CAAC,0EAAD,EAA6E,YAAM;IACpFI,MAAM,CAAC;MAAA,OAAM,IAAAP,kCAAA,EAAuB,mBAAvB,EAA4C,IAA5C,EAAkDQ,wBAAlD,CAAN;IAAA,CAAD,CAAN,CACEC,EADF,UACW,wCADX;IAEA,IAAAT,kCAAA,EAAuB,oBAAvB,EAA6CQ,wBAA7C,EAA0DJ,MAA1D,CAAiEC,KAAjE,CAAuE,IAAvE;EACA,CAJC,CAAF;AAKA,CAjBO,CAAR"}