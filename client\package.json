{"name": "chs-crm", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build-qa": "ng build --configuration=qa", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^17.3.12", "@angular/common": "^17.3.12", "@angular/compiler": "^17.3.12", "@angular/core": "^17.3.12", "@angular/forms": "^17.3.12", "@angular/platform-browser": "^17.3.12", "@angular/platform-browser-dynamic": "^17.3.12", "@angular/router": "^17.3.12", "@ng-select/ng-select": "^12.0.0", "country-state-city": "^3.2.1", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.12.10", "moment": "^2.30.1", "ngx-mask": "^19.0.7", "primeflex": "^3.3.1", "primeicons": "6.0.1", "primeng": "17.2.0", "qs": "^6.13.1", "quill": "^1.3.7", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.11", "@angular/cli": "~17.3.11", "@angular/compiler-cli": "^17.3.12", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.4.5"}}