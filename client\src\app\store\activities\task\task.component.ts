import { Component, OnInit, ViewChild } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { Subject } from 'rxjs';
import { ActivitiesService } from '../activities.service';
import { Table } from 'primeng/table';
import { Router } from '@angular/router';
import { State } from 'country-state-city';

interface Actions {
  name: string;
  code: string;
}
interface Column {
  field: string;
  header: string;
}

@Component({
  selector: 'app-tasks',
  templateUrl: './task.component.html',
  styleUrl: './task.component.scss',
})
export class TaskComponent implements OnInit {
  private unsubscribe$ = new Subject<void>();
  @ViewChild('dt1') dt1!: Table;
  public breadcrumbitems: MenuItem[] | any;
  public home: MenuItem | any;
  public tasks: any[] = [];
  public totalRecords: number = 0;
  public loading: boolean = true;
  public globalSearchTerm: string = '';
  public Actions: Actions[] | undefined;
  public selectedActions: Actions | undefined;

  public dropdowns: Record<string, any[]> = {
    activityCategory: [],
    activityStatus: [],
    activityPriority: [],
  };

  constructor(
    private activitiesservice: ActivitiesService,
    private router: Router
  ) {}

  private _selectedColumns: Column[] = [];

  public cols: Column[] = [
    { field: 'subject', header: 'Subject' },
    { field: 'business_partner.bp_full_name', header: 'Account' },
    {
      field: 'business_partner_contact.bp_full_name',
      header: 'Primary Contact',
    },
    { field: 'activity_status', header: 'Status' },
    { field: 'notes.note', header: 'Notes' },
    { field: 'start_date', header: 'Start Date/Time' },
    { field: 'end_date', header: 'Due Date/Time' },
    { field: 'priority', header: 'Priority' },
    { field: 'business_partner_processor.bp_full_name', header: 'Processor' },
    { field: 'task_category', header: 'Category' },
    { field: 'business_partner_owner.bp_full_name', header: 'Owner' },
  ];

  sortField: string = '';
  sortOrder: number = 1;

  customSort(field: string): void {
    if (this.sortField === field) {
      this.sortOrder = -this.sortOrder;
    } else {
      this.sortField = field;
      this.sortOrder = 1;
    }

    this.tasks.sort((a, b) => {
      const value1 = this.resolveFieldData(a, field);
      const value2 = this.resolveFieldData(b, field);

      let result = 0;

      if (value1 == null && value2 != null) result = -1;
      else if (value1 != null && value2 == null) result = 1;
      else if (value1 == null && value2 == null) result = 0;
      else if (typeof value1 === 'string' && typeof value2 === 'string')
        result = value1.localeCompare(value2);
      else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;

      return this.sortOrder * result;
    });
  }

  // Utility to resolve nested fields
  resolveFieldData(data: any, field: string): any {
    if (!data || !field) return null;
    if (field.indexOf('.') === -1) {
      return data[field];
    } else {
      return field.split('.').reduce((obj, key) => obj?.[key], data);
    }
  }

  ngOnInit() {
    this.loadActivityDropDown('activityCategory', 'CRM_ACTIVITY_TASK_CATEGORY');
    this.loadActivityDropDown('activityStatus', 'CRM_ACTIVITY_STATUS');
    this.loadActivityDropDown('activityPriority', 'CRM_ACTIVITY_PRIORITY');
    this.breadcrumbitems = [
      { label: 'Tasks', routerLink: ['/store/activities/tasks'] },
    ];
    this.home = { icon: 'pi pi-home text-lg', routerLink: ['/'] };
    this.Actions = [
      { name: 'All', code: 'ALL' },
      { name: 'My Tasks', code: 'MT' },
      { name: 'My Open Tasks', code: 'MOT' },
      { name: 'My Completed Tasks', code: 'MCT' },
      { name: 'My Team`s Tasks', code: 'MTST' },
      { name: 'My Tasks Today', code: 'MTT' },
      { name: 'My Tasks This Week', code: 'MTTW' },
      { name: 'My Tasks This Month', code: 'MTTM' },
    ];

    this.selectedActions = { name: 'All', code: 'ALL' };

    this._selectedColumns = this.cols;
  }

  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    // optional: preserve user reorder logic instead
    this._selectedColumns = this.cols.filter((col) => val.includes(col));
  }

  onColumnReorder(event: any) {
    const draggedCol = this._selectedColumns[event.dragIndex];
    this._selectedColumns.splice(event.dragIndex, 1);
    this._selectedColumns.splice(event.dropIndex, 0, draggedCol);
  }

  loadActivityDropDown(target: string, type: string): void {
    this.activitiesservice
      .getActivityDropdownOptions(type)
      .subscribe((res: any) => {
        this.dropdowns[target] =
          res?.data?.map((attr: any) => ({
            label: attr.description,
            value: attr.code,
          })) ?? [];
      });
  }

  getLabelFromDropdown(dropdownKey: string, value: string): string {
    const item = this.dropdowns[dropdownKey]?.find(
      (opt) => opt.value === value
    );
    return item?.label || value;
  }

  loadTasks(event: any) {
    this.loading = true;
    const page = event.first / event.rows + 1;
    const pageSize = event.rows;
    const sortField = event.sortField;
    const sortOrder = event.sortOrder;
    const filter = this.selectedActions?.code;

    this.activitiesservice
      .getTasks(
        page,
        pageSize,
        sortField,
        sortOrder,
        this.globalSearchTerm,
        filter
      )
      .subscribe({
        next: (response: any) => {
          this.tasks = (response?.data || []).map((call: any) => {
            // 🔍 Find the global note from the call.notes array
            const globalNote = call.notes?.find(
              (n: any) => n.is_global_note === true
            );
            return {
              ...call,
              globalNote: globalNote || null, // 📝 Attach the global note (if found)
            };
          });

          this.totalRecords = response?.meta?.pagination.total;
          this.loading = false;
        },
        error: (error: any) => {
          console.error('Error fetching tasks', error);
          this.loading = false;
        },
      });
  }

  onActionChange() {
    // Re-trigger the lazy load with current dt1 state
    const dt1State = this.dt1?.createLazyLoadMetadata?.() ?? {
      first: 0,
      rows: 14,
    };
    this.loadTasks(dt1State);
  }

  stripHtml(html: string): string {
    const temp = document.createElement('div');
    temp.innerHTML = html;
    return temp.textContent || temp.innerText || '';
  }

  getStateNameByCode(stateCode: string, countryCode: string): string {
    const states = State.getStatesOfCountry(countryCode);
    const match = states.find((state) => state.isoCode === stateCode);
    return match ? match.name : '-';
  }

  signup() {
    this.router.navigate(['/store/activities/tasks/create']);
  }

  onGlobalFilter(table: Table, event: Event) {
    this.loadTasks({ first: 0, rows: 14 });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
