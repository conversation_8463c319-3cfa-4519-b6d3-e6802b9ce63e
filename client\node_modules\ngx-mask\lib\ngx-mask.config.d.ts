import { EventEmitter, InjectionToken } from '@angular/core';
export type InputTransformFn = (value: unknown) => string | number;
export type OutputTransformFn = (value: string | number | undefined | null) => unknown;
export type NgxMaskConfig = {
    suffix: string;
    prefix: string;
    thousandSeparator: string;
    decimalMarker: '.' | ',' | ['.', ','];
    clearIfNotMatch: boolean;
    showMaskTyped: boolean;
    placeHolderCharacter: string;
    shownMaskExpression: string;
    specialCharacters: string[] | readonly string[];
    dropSpecialCharacters: boolean | string[] | readonly string[];
    hiddenInput: boolean;
    validation: boolean;
    instantPrefix: boolean;
    separatorLimit: string;
    apm: boolean;
    allowNegativeNumbers: boolean;
    leadZeroDateTime: boolean;
    leadZero: boolean;
    triggerOnMaskChange: boolean;
    keepCharacterPositions: boolean;
    inputTransformFn: InputTransformFn;
    outputTransformFn: OutputTransformFn;
    maskFilled: EventEmitter<void>;
    patterns: Record<string, {
        pattern: RegExp;
        optional?: boolean;
        symbol?: string;
    }>;
};
export type NgxMaskOptions = Partial<NgxMaskConfig>;
export declare const NGX_MASK_CONFIG: InjectionToken<NgxMaskConfig>;
export declare const NEW_CONFIG: InjectionToken<NgxMaskConfig>;
export declare const INITIAL_CONFIG: InjectionToken<NgxMaskConfig>;
export declare const initialConfig: NgxMaskConfig;
export declare const timeMasks: string[];
export declare const withoutValidation: string[];
