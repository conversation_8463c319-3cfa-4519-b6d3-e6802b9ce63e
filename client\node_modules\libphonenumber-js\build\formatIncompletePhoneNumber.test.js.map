{"version": 3, "file": "formatIncompletePhoneNumber.test.js", "names": ["describe", "it", "result", "formatIncompletePhoneNumber", "metadata", "should", "equal", "defaultCountry", "defaultCallingCode"], "sources": ["../source/formatIncompletePhoneNumber.test.js"], "sourcesContent": ["import formatIncompletePhoneNumber from './formatIncompletePhoneNumber.js'\r\n\r\nimport metadata from '../metadata.min.json' assert { type: 'json' }\r\n\r\ndescribe('formatIncompletePhoneNumber', () => {\r\n\tit('should format parsed input value', () => {\r\n\t\tlet result\r\n\r\n\t\t// National input.\r\n\t\tformatIncompletePhoneNumber('880055535', 'RU', metadata).should.equal('8 (800) 555-35')\r\n\r\n\t\t// International input, no country.\r\n\t\tformatIncompletePhoneNumber('+780055535', null, metadata).should.equal('****** 555 35')\r\n\r\n\t\t// International input, no country argument.\r\n\t\tformatIncompletePhoneNumber('+780055535', metadata).should.equal('****** 555 35')\r\n\r\n\t\t// International input, with country.\r\n\t\tformatIncompletePhoneNumber('+780055535', 'RU', metadata).should.equal('****** 555 35')\r\n\t})\r\n\r\n\tit('should support an object argument', () => {\r\n\t\tformatIncompletePhoneNumber('880055535', { defaultCountry: 'RU' }, metadata).should.equal('8 (800) 555-35')\r\n\t\tformatIncompletePhoneNumber('880055535', { defaultCallingCode: '7' }, metadata).should.equal('8 (800) 555-35')\r\n\t})\r\n})"], "mappings": ";;AAAA;;AAEA;;;;AAEAA,QAAQ,CAAC,6BAAD,EAAgC,YAAM;EAC7CC,EAAE,CAAC,kCAAD,EAAqC,YAAM;IAC5C,IAAIC,MAAJ,CAD4C,CAG5C;;IACA,IAAAC,uCAAA,EAA4B,WAA5B,EAAyC,IAAzC,EAA+CC,uBAA/C,EAAyDC,MAAzD,CAAgEC,KAAhE,CAAsE,gBAAtE,EAJ4C,CAM5C;;IACA,IAAAH,uCAAA,EAA4B,YAA5B,EAA0C,IAA1C,EAAgDC,uBAAhD,EAA0DC,MAA1D,CAAiEC,KAAjE,CAAuE,eAAvE,EAP4C,CAS5C;;IACA,IAAAH,uCAAA,EAA4B,YAA5B,EAA0CC,uBAA1C,EAAoDC,MAApD,CAA2DC,KAA3D,CAAiE,eAAjE,EAV4C,CAY5C;;IACA,IAAAH,uCAAA,EAA4B,YAA5B,EAA0C,IAA1C,EAAgDC,uBAAhD,EAA0DC,MAA1D,CAAiEC,KAAjE,CAAuE,eAAvE;EACA,CAdC,CAAF;EAgBAL,EAAE,CAAC,mCAAD,EAAsC,YAAM;IAC7C,IAAAE,uCAAA,EAA4B,WAA5B,EAAyC;MAAEI,cAAc,EAAE;IAAlB,CAAzC,EAAmEH,uBAAnE,EAA6EC,MAA7E,CAAoFC,KAApF,CAA0F,gBAA1F;IACA,IAAAH,uCAAA,EAA4B,WAA5B,EAAyC;MAAEK,kBAAkB,EAAE;IAAtB,CAAzC,EAAsEJ,uBAAtE,EAAgFC,MAAhF,CAAuFC,KAAvF,CAA6F,gBAA7F;EACA,CAHC,CAAF;AAIA,CArBO,CAAR"}