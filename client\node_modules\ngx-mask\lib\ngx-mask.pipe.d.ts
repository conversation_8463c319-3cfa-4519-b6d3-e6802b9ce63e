import type { PipeTransform } from '@angular/core';
import type { NgxMaskConfig } from './ngx-mask.config';
import * as i0 from "@angular/core";
export declare class NgxMaskPipe implements PipeTransform {
    private readonly defaultOptions;
    private readonly _maskService;
    private _maskExpressionArray;
    private mask;
    transform(value: string | number, mask: string, { patterns, ...config }?: Partial<NgxMaskConfig>): string;
    private _setMask;
    static ɵfac: i0.ɵɵFactoryDeclaration<NgxMaskPipe, never>;
    static ɵpipe: i0.ɵɵPipeDeclaration<NgxMaskPipe, "mask", true>;
}
