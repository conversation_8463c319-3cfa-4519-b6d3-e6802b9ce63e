# Azure Pipeline for Building and Deploying Angular and Strapi Applications

resources:
  containers:
    - container: cfdockercli
      image: "nirmaltodev/node20cfdockercli:v1"
      options: --user 0:0 --privileged

trigger:
  - qa

variables:
  - group: CF_CHS_SecretKeys

stages:
  - stage: deploy
    displayName: Build and Deploy to SAP Cloud Platform
    jobs:
      - job: build_and_deploy
        displayName: Build and Deploy
        pool:
          vmImage: "ubuntu-latest"
        container: cfdockercli

        steps:
          # Step 1: Checkout the current repository
          - checkout: self
            displayName: "Checkout Repository"

          # Step 2: Configure Git User Identity
          - script: |
              git config --global user.email "<EMAIL>"
              git config --global user.name "Azure DevOps Pipeline"
            displayName: "Configure Git User Identity"

          # Step 3: Add and Pull from Central Repository
          - script: |
              git remote add chscentral $(CHS_CENTRAL_REPO_URL)
              git config pull.rebase false  # Set merge strategy
              git pull chscentral staging --allow-unrelated-histories
            displayName: "Pull Changes from Central Repository"

          # Step 4: Build Angular Client
          - script: |
              cd ./client
              npm ci --omit=dev
              npm audit fix --force
              npm run build
            displayName: "Build Angular Client"

          # Step 5: Copy Angular Build to Public Folder
          - script: |
              # Copy built files to public folder
              cp -r ./client/dist/chs-crm/* ./public/
            displayName: "Copy Angular Build to Public Folder"

          # Step 6: Build Strapi Backend
          - script: |
              rm -f package-lock.json
              npm install
              npm run build
            displayName: "Build Strapi Backend"

          # Step 7: Verify Docker is available
          - script: docker --version
            displayName: "Verify Docker Installation"

          # Step 8: Login to Docker Hub
          - script: |
              echo "$(DOCKER_HUB_ACCESS_TOKEN)" | docker login -u "$(DOCKER_HUB_USERNAME)" --password-stdin
            displayName: "Login to Docker Hub"

          # Step 9: Build Docker Image and tag it for Docker Hub
          - script: |
              docker build --no-cache -t snjya/chs:qa-crm-ch-supplies .
            displayName: "Build Docker Image"

          # Step 10: Push Docker Image to Docker Hub
          - script: |
              docker push snjya/chs:qa-crm-ch-supplies
            displayName: "Push Docker Image to Docker Hub"

          # Step 11: Login to Cloud Foundry
          - script: |
              cf login -a $(API_ENDPOINT) -u $(USER) -p $(PASSWORD) -o "CHS_snjya-portal-development-5bvz19un" -s SNJYA_STAGE
            displayName: "Cloud Foundry Login"

          # Step 12: Deploy Docker Image to SAP BTP via Cloud Foundry
          - script: |
              export CF_DOCKER_PASSWORD=$(DOCKER_HUB_ACCESS_TOKEN)
              cf push qa-crm-ch-supplies --docker-image snjya/chs:qa-crm-ch-supplies --docker-username $(DOCKER_HUB_USERNAME)
            displayName: "Deploy Docker Image to Cloud Foundry"
