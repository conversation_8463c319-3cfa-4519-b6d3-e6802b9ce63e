import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  SimpleChanges,
  OnChanges,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Country } from 'country-state-city';
import { parsePhoneNumberFromString } from 'libphonenumber-js/max';
import { Subject, Subscription } from 'rxjs';

@Component({
  selector: 'app-country-wise-mobile',
  templateUrl: './country-wise-mobile.component.html',
  styleUrl: './country-wise-mobile.component.scss',
})
export class CountryWiseMobileComponent implements OnInit, OnChanges {
  private ngUnsubscribe = new Subject<void>();
  @Output() validationResult = new EventEmitter<string | null>();
  @Input() formGroup!: FormGroup;
  @Input() countryControlName!: string;
  @Input() controlName!: string;
  @Input() selectedCountry!: string;
  @Input() phoneFieldName!: string;

  public countryList: { isoCode: string; name: string }[] = [];
  private phoneSubscription?: Subscription;

  ngOnInit(): void {
    this.countryList = Country.getAllCountries().map((c) => ({
      isoCode: c.isoCode,
      name: c.name,
    }));
    this.setupValueChangeListener();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedCountry'] && this.selectedCountry) {
      this.formGroup
        .get(this.countryControlName)
        ?.setValue(this.selectedCountry, { emitEvent: false });
    }

    if (changes['phoneFieldName']) {
      this.setupValueChangeListener();
    }
  }

  setupValueChangeListener(): void {
    this.phoneSubscription?.unsubscribe();

    const control = this.formGroup.get(this.phoneFieldName);
    if (control) {
      this.phoneSubscription = control.valueChanges.subscribe(() => {
        this.validatePhone();
      });
    }
  }

  getFlagUrl(isoCode: string): string {
    if (!isoCode) {
      return '';
    }
    const flagUrl = `https://flagcdn.com/w40/${isoCode.toLowerCase()}.png`;
    return flagUrl;
  }

  validatePhone(): void {
    const countryCode = this.formGroup.get(this.controlName)?.value;
    const phone = this.formGroup.get(this.phoneFieldName)?.value;

    let message: string | null = null;

    if (!phone) {
      this.validationResult.emit(null);
      return;
    }

    const countryName =
      this.countryList.find((c) => c.isoCode === countryCode)?.name ||
      countryCode;

    if (!countryCode) {
      message = `Please select a country for ${this.phoneFieldName}.`;
    } else if (!phone) {
      message = `Please enter ${this.phoneFieldName} for ${countryName}.`;
    } else {
      const phoneData = parsePhoneNumberFromString(phone, countryCode);

      if (!phoneData?.isValid()) {
        message = `Please enter a valid ${this.phoneFieldName} for ${countryName}.`;
      } else if (
        phoneData?.nationalNumber?.length < 5 ||
        phoneData?.nationalNumber?.length > 15
      ) {
        message = `Mobile number length invalid for ${countryName}.`;
      }
    }

    this.validationResult.emit(message);
  }

  ngOnDestroy() {
    this.phoneSubscription?.unsubscribe();
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
