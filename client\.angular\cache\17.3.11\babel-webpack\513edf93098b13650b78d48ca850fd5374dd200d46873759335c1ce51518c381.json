{"ast": null, "code": "// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\n// importing system which is even uncapable of importing \"*.json\" files.\nimport metadata from '../../metadata.max.json.js';\nimport { PhoneNumber as _PhoneNumber } from '../../core/index.js';\nexport function PhoneNumber(number) {\n  return _PhoneNumber.call(this, number, metadata);\n}\nPhoneNumber.prototype = Object.create(_PhoneNumber.prototype, {});\nPhoneNumber.prototype.constructor = PhoneNumber;", "map": {"version": 3, "names": ["metadata", "PhoneNumber", "_PhoneNumber", "number", "call", "prototype", "Object", "create", "constructor"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/max/exports/PhoneNumber.js"], "sourcesContent": ["// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\r\n// importing system which is even uncapable of importing \"*.json\" files.\r\nimport metadata from '../../metadata.max.json.js'\r\n\r\nimport { PhoneNumber as _PhoneNumber } from '../../core/index.js'\r\n\r\nexport function PhoneNumber(number) {\r\n\treturn _PhoneNumber.call(this, number, metadata)\r\n}\r\nPhoneNumber.prototype = Object.create(_PhoneNumber.prototype, {})\r\nPhoneNumber.prototype.constructor = PhoneNumber\r\n"], "mappings": "AAAA;AACA;AACA,OAAOA,QAAQ,MAAM,4BAA4B;AAEjD,SAASC,WAAW,IAAIC,YAAY,QAAQ,qBAAqB;AAEjE,OAAO,SAASD,WAAWA,CAACE,MAAM,EAAE;EACnC,OAAOD,YAAY,CAACE,IAAI,CAAC,IAAI,EAAED,MAAM,EAAEH,QAAQ,CAAC;AACjD;AACAC,WAAW,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACL,YAAY,CAACG,SAAS,EAAE,CAAC,CAAC,CAAC;AACjEJ,WAAW,CAACI,SAAS,CAACG,WAAW,GAAGP,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}