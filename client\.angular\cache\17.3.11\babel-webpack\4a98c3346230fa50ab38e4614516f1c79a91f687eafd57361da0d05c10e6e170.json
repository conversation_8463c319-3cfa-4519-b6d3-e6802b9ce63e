{"ast": null, "code": "// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\n// importing system which is even uncapable of importing \"*.json\" files.\nimport metadata from '../../metadata.max.json.js';\nimport { AsYouType as _AsYouType } from '../../core/index.js';\nexport function AsYouType(country) {\n  return _AsYouType.call(this, country, metadata);\n}\nAsYouType.prototype = Object.create(_AsYouType.prototype, {});\nAsYouType.prototype.constructor = AsYouType;", "map": {"version": 3, "names": ["metadata", "AsYouType", "_AsYouType", "country", "call", "prototype", "Object", "create", "constructor"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/max/exports/AsYouType.js"], "sourcesContent": ["// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\r\n// importing system which is even uncapable of importing \"*.json\" files.\r\nimport metadata from '../../metadata.max.json.js'\r\n\r\nimport { AsYouType as _AsYouType } from '../../core/index.js'\r\n\r\nexport function AsYouType(country) {\r\n\treturn _AsYouType.call(this, country, metadata)\r\n}\r\n\r\nAsYouType.prototype = Object.create(_AsYouType.prototype, {})\r\nAsYouType.prototype.constructor = AsYouType"], "mappings": "AAAA;AACA;AACA,OAAOA,QAAQ,MAAM,4BAA4B;AAEjD,SAASC,SAAS,IAAIC,UAAU,QAAQ,qBAAqB;AAE7D,OAAO,SAASD,SAASA,CAACE,OAAO,EAAE;EAClC,OAAOD,UAAU,CAACE,IAAI,CAAC,IAAI,EAAED,OAAO,EAAEH,QAAQ,CAAC;AAChD;AAEAC,SAAS,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACL,UAAU,CAACG,SAAS,EAAE,CAAC,CAAC,CAAC;AAC7DJ,SAAS,CAACI,SAAS,CAACG,WAAW,GAAGP,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}