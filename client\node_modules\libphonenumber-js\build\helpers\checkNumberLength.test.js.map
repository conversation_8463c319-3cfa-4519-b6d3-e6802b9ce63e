{"version": 3, "file": "checkNumberLength.test.js", "names": ["describe", "it", "checkNumberLength", "should", "equal", "undefined", "_oldMetadata", "<PERSON><PERSON><PERSON>", "oldMetadata", "country", "checkNumberLengthForType", "number", "type", "_metadata", "metadata"], "sources": ["../../source/helpers/checkNumberLength.test.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\nimport metadata from '../../metadata.max.json' assert { type: 'json' }\r\nimport oldMetadata from '../../test/metadata/1.0.0/metadata.min.json' assert { type: 'json' }\r\n\r\nimport { checkNumberLengthForType } from './checkNumberLength.js'\r\n\r\ndescribe('checkNumberLength', () => {\r\n\tit('should check phone number length', () => {\r\n\t\t// Too short.\r\n\t\tcheckNumberLength('800555353', 'FIXED_LINE', 'RU').should.equal('TOO_SHORT')\r\n\t\t// Normal.\r\n\t\tcheckNumberLength('8005553535', 'FIXED_LINE', 'RU').should.equal('IS_POSSIBLE')\r\n\t\t// Too long.\r\n\t\tcheckNumberLength('80055535355', 'FIXED_LINE', 'RU').should.equal('TOO_LONG')\r\n\r\n\t\t// No such type.\r\n\t\tcheckNumberLength('169454850', 'VOIP', 'AC').should.equal('INVALID_LENGTH')\r\n\t\t// No such possible length.\r\n\t\tcheckNumberLength('1694548', undefined, 'AD').should.equal('INVALID_LENGTH')\r\n\r\n\t\t// FIXED_LINE_OR_MOBILE\r\n\t\tcheckNumberLength('1694548', 'FIXED_LINE_OR_MOBILE', 'AD').should.equal('INVALID_LENGTH')\r\n\t\t// No mobile phones.\r\n\t\tcheckNumberLength('8123', 'FIXED_LINE_OR_MOBILE', 'TA').should.equal('IS_POSSIBLE')\r\n\t\t// No \"possible lengths\" for \"mobile\".\r\n\t\tcheckNumberLength('81234567', 'FIXED_LINE_OR_MOBILE', 'SZ').should.equal('IS_POSSIBLE')\r\n\t})\r\n\r\n\tit('should work for old metadata', function() {\r\n\t\tconst _oldMetadata = new Metadata(oldMetadata)\r\n\t\t_oldMetadata.country('RU')\r\n\t\tcheckNumberLengthForType('8005553535', 'FIXED_LINE', _oldMetadata).should.equal('IS_POSSIBLE')\r\n\t})\r\n})\r\n\r\nfunction checkNumberLength(number, type, country) {\r\n\tconst _metadata = new Metadata(metadata)\r\n\t_metadata.country(country)\r\n\treturn checkNumberLengthForType(number, type, _metadata)\r\n}"], "mappings": ";;AAAA;;AACA;;AACA;;AAEA;;;;AAEAA,QAAQ,CAAC,mBAAD,EAAsB,YAAM;EACnCC,EAAE,CAAC,kCAAD,EAAqC,YAAM;IAC5C;IACAC,iBAAiB,CAAC,WAAD,EAAc,YAAd,EAA4B,IAA5B,CAAjB,CAAmDC,MAAnD,CAA0DC,KAA1D,CAAgE,WAAhE,EAF4C,CAG5C;;IACAF,iBAAiB,CAAC,YAAD,EAAe,YAAf,EAA6B,IAA7B,CAAjB,CAAoDC,MAApD,CAA2DC,KAA3D,CAAiE,aAAjE,EAJ4C,CAK5C;;IACAF,iBAAiB,CAAC,aAAD,EAAgB,YAAhB,EAA8B,IAA9B,CAAjB,CAAqDC,MAArD,CAA4DC,KAA5D,CAAkE,UAAlE,EAN4C,CAQ5C;;IACAF,iBAAiB,CAAC,WAAD,EAAc,MAAd,EAAsB,IAAtB,CAAjB,CAA6CC,MAA7C,CAAoDC,KAApD,CAA0D,gBAA1D,EAT4C,CAU5C;;IACAF,iBAAiB,CAAC,SAAD,EAAYG,SAAZ,EAAuB,IAAvB,CAAjB,CAA8CF,MAA9C,CAAqDC,KAArD,CAA2D,gBAA3D,EAX4C,CAa5C;;IACAF,iBAAiB,CAAC,SAAD,EAAY,sBAAZ,EAAoC,IAApC,CAAjB,CAA2DC,MAA3D,CAAkEC,KAAlE,CAAwE,gBAAxE,EAd4C,CAe5C;;IACAF,iBAAiB,CAAC,MAAD,EAAS,sBAAT,EAAiC,IAAjC,CAAjB,CAAwDC,MAAxD,CAA+DC,KAA/D,CAAqE,aAArE,EAhB4C,CAiB5C;;IACAF,iBAAiB,CAAC,UAAD,EAAa,sBAAb,EAAqC,IAArC,CAAjB,CAA4DC,MAA5D,CAAmEC,KAAnE,CAAyE,aAAzE;EACA,CAnBC,CAAF;EAqBAH,EAAE,CAAC,8BAAD,EAAiC,YAAW;IAC7C,IAAMK,YAAY,GAAG,IAAIC,qBAAJ,CAAaC,uBAAb,CAArB;;IACAF,YAAY,CAACG,OAAb,CAAqB,IAArB;;IACA,IAAAC,2CAAA,EAAyB,YAAzB,EAAuC,YAAvC,EAAqDJ,YAArD,EAAmEH,MAAnE,CAA0EC,KAA1E,CAAgF,aAAhF;EACA,CAJC,CAAF;AAKA,CA3BO,CAAR;;AA6BA,SAASF,iBAAT,CAA2BS,MAA3B,EAAmCC,IAAnC,EAAyCH,OAAzC,EAAkD;EACjD,IAAMI,SAAS,GAAG,IAAIN,qBAAJ,CAAaO,uBAAb,CAAlB;;EACAD,SAAS,CAACJ,OAAV,CAAkBA,OAAlB;;EACA,OAAO,IAAAC,2CAAA,EAAyBC,MAAzB,EAAiCC,IAAjC,EAAuCC,SAAvC,CAAP;AACA"}