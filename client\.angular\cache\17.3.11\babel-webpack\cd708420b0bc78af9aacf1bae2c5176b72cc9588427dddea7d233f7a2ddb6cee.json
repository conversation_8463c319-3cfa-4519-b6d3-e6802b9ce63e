{"ast": null, "code": "// This file is a workaround for a bug in web browsers' \"native\"\n// ES6 importing system which is uncapable of importing \"*.json\" files.\n// https://github.com/catamphetamine/libphonenumber-js/issues/239\nexport default {\n  \"version\": 4,\n  \"country_calling_codes\": {\n    \"1\": [\"US\", \"AG\", \"AI\", \"AS\", \"BB\", \"BM\", \"BS\", \"CA\", \"DM\", \"DO\", \"GD\", \"GU\", \"JM\", \"KN\", \"KY\", \"LC\", \"MP\", \"MS\", \"PR\", \"SX\", \"TC\", \"TT\", \"VC\", \"VG\", \"VI\"],\n    \"7\": [\"RU\", \"KZ\"],\n    \"20\": [\"EG\"],\n    \"27\": [\"ZA\"],\n    \"30\": [\"GR\"],\n    \"31\": [\"NL\"],\n    \"32\": [\"BE\"],\n    \"33\": [\"FR\"],\n    \"34\": [\"ES\"],\n    \"36\": [\"HU\"],\n    \"39\": [\"IT\", \"VA\"],\n    \"40\": [\"RO\"],\n    \"41\": [\"CH\"],\n    \"43\": [\"AT\"],\n    \"44\": [\"GB\", \"GG\", \"IM\", \"JE\"],\n    \"45\": [\"DK\"],\n    \"46\": [\"SE\"],\n    \"47\": [\"NO\", \"SJ\"],\n    \"48\": [\"PL\"],\n    \"49\": [\"DE\"],\n    \"51\": [\"PE\"],\n    \"52\": [\"MX\"],\n    \"53\": [\"CU\"],\n    \"54\": [\"AR\"],\n    \"55\": [\"BR\"],\n    \"56\": [\"CL\"],\n    \"57\": [\"CO\"],\n    \"58\": [\"VE\"],\n    \"60\": [\"MY\"],\n    \"61\": [\"AU\", \"CC\", \"CX\"],\n    \"62\": [\"ID\"],\n    \"63\": [\"PH\"],\n    \"64\": [\"NZ\"],\n    \"65\": [\"SG\"],\n    \"66\": [\"TH\"],\n    \"81\": [\"JP\"],\n    \"82\": [\"KR\"],\n    \"84\": [\"VN\"],\n    \"86\": [\"CN\"],\n    \"90\": [\"TR\"],\n    \"91\": [\"IN\"],\n    \"92\": [\"PK\"],\n    \"93\": [\"AF\"],\n    \"94\": [\"LK\"],\n    \"95\": [\"MM\"],\n    \"98\": [\"IR\"],\n    \"211\": [\"SS\"],\n    \"212\": [\"MA\", \"EH\"],\n    \"213\": [\"DZ\"],\n    \"216\": [\"TN\"],\n    \"218\": [\"LY\"],\n    \"220\": [\"GM\"],\n    \"221\": [\"SN\"],\n    \"222\": [\"MR\"],\n    \"223\": [\"ML\"],\n    \"224\": [\"GN\"],\n    \"225\": [\"CI\"],\n    \"226\": [\"BF\"],\n    \"227\": [\"NE\"],\n    \"228\": [\"TG\"],\n    \"229\": [\"BJ\"],\n    \"230\": [\"MU\"],\n    \"231\": [\"LR\"],\n    \"232\": [\"SL\"],\n    \"233\": [\"GH\"],\n    \"234\": [\"NG\"],\n    \"235\": [\"TD\"],\n    \"236\": [\"CF\"],\n    \"237\": [\"CM\"],\n    \"238\": [\"CV\"],\n    \"239\": [\"ST\"],\n    \"240\": [\"GQ\"],\n    \"241\": [\"GA\"],\n    \"242\": [\"CG\"],\n    \"243\": [\"CD\"],\n    \"244\": [\"AO\"],\n    \"245\": [\"GW\"],\n    \"246\": [\"IO\"],\n    \"247\": [\"AC\"],\n    \"248\": [\"SC\"],\n    \"249\": [\"SD\"],\n    \"250\": [\"RW\"],\n    \"251\": [\"ET\"],\n    \"252\": [\"SO\"],\n    \"253\": [\"DJ\"],\n    \"254\": [\"KE\"],\n    \"255\": [\"TZ\"],\n    \"256\": [\"UG\"],\n    \"257\": [\"BI\"],\n    \"258\": [\"MZ\"],\n    \"260\": [\"ZM\"],\n    \"261\": [\"MG\"],\n    \"262\": [\"RE\", \"YT\"],\n    \"263\": [\"ZW\"],\n    \"264\": [\"NA\"],\n    \"265\": [\"MW\"],\n    \"266\": [\"LS\"],\n    \"267\": [\"BW\"],\n    \"268\": [\"SZ\"],\n    \"269\": [\"KM\"],\n    \"290\": [\"SH\", \"TA\"],\n    \"291\": [\"ER\"],\n    \"297\": [\"AW\"],\n    \"298\": [\"FO\"],\n    \"299\": [\"GL\"],\n    \"350\": [\"GI\"],\n    \"351\": [\"PT\"],\n    \"352\": [\"LU\"],\n    \"353\": [\"IE\"],\n    \"354\": [\"IS\"],\n    \"355\": [\"AL\"],\n    \"356\": [\"MT\"],\n    \"357\": [\"CY\"],\n    \"358\": [\"FI\", \"AX\"],\n    \"359\": [\"BG\"],\n    \"370\": [\"LT\"],\n    \"371\": [\"LV\"],\n    \"372\": [\"EE\"],\n    \"373\": [\"MD\"],\n    \"374\": [\"AM\"],\n    \"375\": [\"BY\"],\n    \"376\": [\"AD\"],\n    \"377\": [\"MC\"],\n    \"378\": [\"SM\"],\n    \"380\": [\"UA\"],\n    \"381\": [\"RS\"],\n    \"382\": [\"ME\"],\n    \"383\": [\"XK\"],\n    \"385\": [\"HR\"],\n    \"386\": [\"SI\"],\n    \"387\": [\"BA\"],\n    \"389\": [\"MK\"],\n    \"420\": [\"CZ\"],\n    \"421\": [\"SK\"],\n    \"423\": [\"LI\"],\n    \"500\": [\"FK\"],\n    \"501\": [\"BZ\"],\n    \"502\": [\"GT\"],\n    \"503\": [\"SV\"],\n    \"504\": [\"HN\"],\n    \"505\": [\"NI\"],\n    \"506\": [\"CR\"],\n    \"507\": [\"PA\"],\n    \"508\": [\"PM\"],\n    \"509\": [\"HT\"],\n    \"590\": [\"GP\", \"BL\", \"MF\"],\n    \"591\": [\"BO\"],\n    \"592\": [\"GY\"],\n    \"593\": [\"EC\"],\n    \"594\": [\"GF\"],\n    \"595\": [\"PY\"],\n    \"596\": [\"MQ\"],\n    \"597\": [\"SR\"],\n    \"598\": [\"UY\"],\n    \"599\": [\"CW\", \"BQ\"],\n    \"670\": [\"TL\"],\n    \"672\": [\"NF\"],\n    \"673\": [\"BN\"],\n    \"674\": [\"NR\"],\n    \"675\": [\"PG\"],\n    \"676\": [\"TO\"],\n    \"677\": [\"SB\"],\n    \"678\": [\"VU\"],\n    \"679\": [\"FJ\"],\n    \"680\": [\"PW\"],\n    \"681\": [\"WF\"],\n    \"682\": [\"CK\"],\n    \"683\": [\"NU\"],\n    \"685\": [\"WS\"],\n    \"686\": [\"KI\"],\n    \"687\": [\"NC\"],\n    \"688\": [\"TV\"],\n    \"689\": [\"PF\"],\n    \"690\": [\"TK\"],\n    \"691\": [\"FM\"],\n    \"692\": [\"MH\"],\n    \"850\": [\"KP\"],\n    \"852\": [\"HK\"],\n    \"853\": [\"MO\"],\n    \"855\": [\"KH\"],\n    \"856\": [\"LA\"],\n    \"880\": [\"BD\"],\n    \"886\": [\"TW\"],\n    \"960\": [\"MV\"],\n    \"961\": [\"LB\"],\n    \"962\": [\"JO\"],\n    \"963\": [\"SY\"],\n    \"964\": [\"IQ\"],\n    \"965\": [\"KW\"],\n    \"966\": [\"SA\"],\n    \"967\": [\"YE\"],\n    \"968\": [\"OM\"],\n    \"970\": [\"PS\"],\n    \"971\": [\"AE\"],\n    \"972\": [\"IL\"],\n    \"973\": [\"BH\"],\n    \"974\": [\"QA\"],\n    \"975\": [\"BT\"],\n    \"976\": [\"MN\"],\n    \"977\": [\"NP\"],\n    \"992\": [\"TJ\"],\n    \"993\": [\"TM\"],\n    \"994\": [\"AZ\"],\n    \"995\": [\"GE\"],\n    \"996\": [\"KG\"],\n    \"998\": [\"UZ\"]\n  },\n  \"countries\": {\n    \"AC\": [\"247\", \"00\", \"(?:[01589]\\\\d|[46])\\\\d{4}\", [5, 6]],\n    \"AD\": [\"376\", \"00\", \"(?:1|6\\\\d)\\\\d{7}|[135-9]\\\\d{5}\", [6, 8, 9], [[\"(\\\\d{3})(\\\\d{3})\", \"$1 $2\", [\"[135-9]\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"1\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"6\"]]]],\n    \"AE\": [\"971\", \"00\", \"(?:[4-7]\\\\d|9[0-689])\\\\d{7}|800\\\\d{2,9}|[2-4679]\\\\d{7}\", [5, 6, 7, 8, 9, 10, 11, 12], [[\"(\\\\d{3})(\\\\d{2,9})\", \"$1 $2\", [\"60|8\"]], [\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[236]|[479][2-8]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d)(\\\\d{5})\", \"$1 $2 $3\", [\"[479]\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"5\"], \"0$1\"]], \"0\"],\n    \"AF\": [\"93\", \"00\", \"[2-7]\\\\d{8}\", [9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[2-7]\"], \"0$1\"]], \"0\"],\n    \"AG\": [\"1\", \"011\", \"(?:268|[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([457]\\\\d{6})$|1\", \"268$1\", 0, \"268\"],\n    \"AI\": [\"1\", \"011\", \"(?:264|[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2457]\\\\d{6})$|1\", \"264$1\", 0, \"264\"],\n    \"AL\": [\"355\", \"00\", \"(?:700\\\\d\\\\d|900)\\\\d{3}|8\\\\d{5,7}|(?:[2-5]|6\\\\d)\\\\d{7}\", [6, 7, 8, 9], [[\"(\\\\d{3})(\\\\d{3,4})\", \"$1 $2\", [\"80|9\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"4[2-6]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[2358][2-5]|4\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"[23578]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"6\"], \"0$1\"]], \"0\"],\n    \"AM\": [\"374\", \"00\", \"(?:[1-489]\\\\d|55|60|77)\\\\d{6}\", [8], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"[89]0\"], \"0 $1\"], [\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"2|3[12]\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{6})\", \"$1 $2\", [\"1|47\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{6})\", \"$1 $2\", [\"[3-9]\"], \"0$1\"]], \"0\"],\n    \"AO\": [\"244\", \"00\", \"[29]\\\\d{8}\", [9], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[29]\"]]]],\n    \"AR\": [\"54\", \"00\", \"(?:11|[89]\\\\d\\\\d)\\\\d{8}|[2368]\\\\d{9}\", [10, 11], [[\"(\\\\d{4})(\\\\d{2})(\\\\d{4})\", \"$1 $2-$3\", [\"2(?:2[024-9]|3[0-59]|47|6[245]|9[02-8])|3(?:3[28]|4[03-9]|5[2-46-8]|7[1-578]|8[2-9])\", \"2(?:[23]02|6(?:[25]|4[6-8])|9(?:[02356]|4[02568]|72|8[23]))|3(?:3[28]|4(?:[04679]|3[5-8]|5[4-68]|8[2379])|5(?:[2467]|3[237]|8[2-5])|7[1-578]|8(?:[2469]|3[2578]|5[4-8]|7[36-8]|8[5-8]))|2(?:2[24-9]|3[1-59]|47)\", \"2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3[78]|5(?:4[46]|8)|8[2379])|5(?:[2467]|3[237]|8[23])|7[1-578]|8(?:[2469]|3[278]|5[56][46]|86[3-6]))|2(?:2[24-9]|3[1-59]|47)|38(?:[58][78]|7[378])|3(?:4[35][56]|58[45]|8(?:[38]5|54|76))[4-6]\", \"2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3(?:5(?:4[0-25689]|[56])|[78])|58|8[2379])|5(?:[2467]|3[237]|8(?:[23]|4(?:[45]|60)|5(?:4[0-39]|5|64)))|7[1-578]|8(?:[2469]|3[278]|54(?:4|5[13-7]|6[89])|86[3-6]))|2(?:2[24-9]|3[1-59]|47)|38(?:[58][78]|7[378])|3(?:454|85[56])[46]|3(?:4(?:36|5[56])|8(?:[38]5|76))[4-6]\"], \"0$1\", 1], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2-$3\", [\"1\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1-$2-$3\", [\"[68]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2-$3\", [\"[23]\"], \"0$1\", 1], [\"(\\\\d)(\\\\d{4})(\\\\d{2})(\\\\d{4})\", \"$2 15-$3-$4\", [\"9(?:2[2-469]|3[3-578])\", \"9(?:2(?:2[024-9]|3[0-59]|47|6[245]|9[02-8])|3(?:3[28]|4[03-9]|5[2-46-8]|7[1-578]|8[2-9]))\", \"9(?:2(?:[23]02|6(?:[25]|4[6-8])|9(?:[02356]|4[02568]|72|8[23]))|3(?:3[28]|4(?:[04679]|3[5-8]|5[4-68]|8[2379])|5(?:[2467]|3[237]|8[2-5])|7[1-578]|8(?:[2469]|3[2578]|5[4-8]|7[36-8]|8[5-8])))|92(?:2[24-9]|3[1-59]|47)\", \"9(?:2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3[78]|5(?:4[46]|8)|8[2379])|5(?:[2467]|3[237]|8[23])|7[1-578]|8(?:[2469]|3[278]|5(?:[56][46]|[78])|7[378]|8(?:6[3-6]|[78]))))|92(?:2[24-9]|3[1-59]|47)|93(?:4[35][56]|58[45]|8(?:[38]5|54|76))[4-6]\", \"9(?:2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3(?:5(?:4[0-25689]|[56])|[78])|5(?:4[46]|8)|8[2379])|5(?:[2467]|3[237]|8(?:[23]|4(?:[45]|60)|5(?:4[0-39]|5|64)))|7[1-578]|8(?:[2469]|3[278]|5(?:4(?:4|5[13-7]|6[89])|[56][46]|[78])|7[378]|8(?:6[3-6]|[78]))))|92(?:2[24-9]|3[1-59]|47)|93(?:4(?:36|5[56])|8(?:[38]5|76))[4-6]\"], \"0$1\", 0, \"$1 $2 $3-$4\"], [\"(\\\\d)(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$2 15-$3-$4\", [\"91\"], \"0$1\", 0, \"$1 $2 $3-$4\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{5})\", \"$1-$2-$3\", [\"8\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$2 15-$3-$4\", [\"9\"], \"0$1\", 0, \"$1 $2 $3-$4\"]], \"0\", 0, \"0?(?:(11|2(?:2(?:02?|[13]|2[13-79]|4[1-6]|5[2457]|6[124-8]|7[1-4]|8[13-6]|9[1267])|3(?:02?|1[467]|2[03-6]|3[13-8]|[49][2-6]|5[2-8]|[67])|4(?:7[3-578]|9)|6(?:[0136]|2[24-6]|4[6-8]?|5[15-8])|80|9(?:0[1-3]|[19]|2\\\\d|3[1-6]|4[02568]?|5[2-4]|6[2-46]|72?|8[23]?))|3(?:3(?:2[79]|6|8[2578])|4(?:0[0-24-9]|[12]|3[5-8]?|4[24-7]|5[4-68]?|6[02-9]|7[126]|8[2379]?|9[1-36-8])|5(?:1|2[1245]|3[237]?|4[1-46-9]|6[2-4]|7[1-6]|8[2-5]?)|6[24]|7(?:[069]|1[1568]|2[15]|3[145]|4[13]|5[14-8]|7[2-57]|8[126])|8(?:[01]|2[15-7]|3[2578]?|4[13-6]|5[4-8]?|6[1-357-9]|7[36-8]?|8[5-8]?|9[124])))15)?\", \"9$1\"],\n    \"AS\": [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|684|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([267]\\\\d{6})$|1\", \"684$1\", 0, \"684\"],\n    \"AT\": [\"43\", \"00\", \"1\\\\d{3,12}|2\\\\d{6,12}|43(?:(?:0\\\\d|5[02-9])\\\\d{3,9}|2\\\\d{4,5}|[3467]\\\\d{4}|8\\\\d{4,6}|9\\\\d{4,7})|5\\\\d{4,12}|8\\\\d{7,12}|9\\\\d{8,12}|(?:[367]\\\\d|4[0-24-9])\\\\d{4,11}\", [4, 5, 6, 7, 8, 9, 10, 11, 12, 13], [[\"(\\\\d)(\\\\d{3,12})\", \"$1 $2\", [\"1(?:11|[2-9])\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})\", \"$1 $2\", [\"517\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3,5})\", \"$1 $2\", [\"5[079]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3,10})\", \"$1 $2\", [\"(?:31|4)6|51|6(?:48|5[0-3579]|[6-9])|7(?:20|32|8)|[89]\", \"(?:31|4)6|51|6(?:485|5[0-3579]|[6-9])|7(?:20|32|8)|[89]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3,9})\", \"$1 $2\", [\"[2-467]|5[2-6]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"5\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4,7})\", \"$1 $2 $3\", [\"5\"], \"0$1\"]], \"0\"],\n    \"AU\": [\"61\", \"001[14-689]|14(?:1[14]|34|4[17]|[56]6|7[47]|88)0011\", \"1(?:[0-79]\\\\d{7}(?:\\\\d(?:\\\\d{2})?)?|8[0-24-9]\\\\d{7})|[2-478]\\\\d{8}|1\\\\d{4,7}\", [5, 6, 7, 8, 9, 10, 12], [[\"(\\\\d{2})(\\\\d{3,4})\", \"$1 $2\", [\"16\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2,4})\", \"$1 $2 $3\", [\"16\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"14|4\"], \"0$1\"], [\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"[2378]\"], \"(0$1)\"], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"1(?:30|[89])\"]]], \"0\", 0, \"(183[12])|0\", 0, 0, 0, [[\"(?:(?:2(?:(?:[0-26-9]\\\\d|3[0-8]|5[0135-9])\\\\d|4(?:[02-9]\\\\d|10))|3(?:(?:[0-3589]\\\\d|6[1-9]|7[0-35-9])\\\\d|4(?:[0-578]\\\\d|90))|7(?:[013-57-9]\\\\d|2[0-8])\\\\d)\\\\d\\\\d|8(?:51(?:0(?:0[03-9]|[12479]\\\\d|3[2-9]|5[0-8]|6[1-9]|8[0-7])|1(?:[0235689]\\\\d|1[0-69]|4[0-589]|7[0-47-9])|2(?:0[0-79]|[18][13579]|2[14-9]|3[0-46-9]|[4-6]\\\\d|7[89]|9[0-4])|[34]\\\\d\\\\d)|(?:6[0-8]|[78]\\\\d)\\\\d{3}|9(?:[02-9]\\\\d{3}|1(?:(?:[0-58]\\\\d|6[0135-9])\\\\d|7(?:0[0-24-9]|[1-9]\\\\d)|9(?:[0-46-9]\\\\d|5[0-79])))))\\\\d{3}\", [9]], [\"4(?:79[01]|83[0-389]|94[0-478])\\\\d{5}|4(?:[0-36]\\\\d|4[047-9]|5[0-25-9]|7[02-8]|8[0-24-9]|9[0-37-9])\\\\d{6}\", [9]], [\"180(?:0\\\\d{3}|2)\\\\d{3}\", [7, 10]], [\"190[0-26]\\\\d{6}\", [10]], 0, 0, 0, [\"163\\\\d{2,6}\", [5, 6, 7, 8, 9]], [\"14(?:5(?:1[0458]|[23][458])|71\\\\d)\\\\d{4}\", [9]], [\"13(?:00\\\\d{6}(?:\\\\d{2})?|45[0-4]\\\\d{3})|13\\\\d{4}\", [6, 8, 10, 12]]], \"0011\"],\n    \"AW\": [\"297\", \"00\", \"(?:[25-79]\\\\d\\\\d|800)\\\\d{4}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[25-9]\"]]]],\n    \"AX\": [\"358\", \"00|99(?:[01469]|5(?:[14]1|3[23]|5[59]|77|88|9[09]))\", \"2\\\\d{4,9}|35\\\\d{4,5}|(?:60\\\\d\\\\d|800)\\\\d{4,6}|7\\\\d{5,11}|(?:[14]\\\\d|3[0-46-9]|50)\\\\d{4,8}\", [5, 6, 7, 8, 9, 10, 11, 12], 0, \"0\", 0, 0, 0, 0, \"18\", 0, \"00\"],\n    \"AZ\": [\"994\", \"00\", \"365\\\\d{6}|(?:[124579]\\\\d|60|88)\\\\d{7}\", [9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"90\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"1[28]|2|365|46\", \"1[28]|2|365[45]|46\", \"1[28]|2|365(?:4|5[02])|46\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[13-9]\"], \"0$1\"]], \"0\"],\n    \"BA\": [\"387\", \"00\", \"6\\\\d{8}|(?:[35689]\\\\d|49|70)\\\\d{6}\", [8, 9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"6[1-3]|[7-9]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2-$3\", [\"[3-5]|6[56]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"6\"], \"0$1\"]], \"0\"],\n    \"BB\": [\"1\", \"011\", \"(?:246|[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-9]\\\\d{6})$|1\", \"246$1\", 0, \"246\"],\n    \"BD\": [\"880\", \"00\", \"[1-469]\\\\d{9}|8[0-79]\\\\d{7,8}|[2-79]\\\\d{8}|[2-9]\\\\d{7}|[3-9]\\\\d{6}|[57-9]\\\\d{5}\", [6, 7, 8, 9, 10], [[\"(\\\\d{2})(\\\\d{4,6})\", \"$1-$2\", [\"31[5-8]|[459]1\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3,7})\", \"$1-$2\", [\"3(?:[67]|8[013-9])|4(?:6[168]|7|[89][18])|5(?:6[128]|9)|6(?:[15]|28|4[14])|7[2-589]|8(?:0[014-9]|[12])|9[358]|(?:3[2-5]|4[235]|5[2-578]|6[0389]|76|8[3-7]|9[24])1|(?:44|66)[01346-9]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3,6})\", \"$1-$2\", [\"[13-9]|2[23]\"], \"0$1\"], [\"(\\\\d)(\\\\d{7,8})\", \"$1-$2\", [\"2\"], \"0$1\"]], \"0\"],\n    \"BE\": [\"32\", \"00\", \"4\\\\d{8}|[1-9]\\\\d{7}\", [8, 9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"(?:80|9)0\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[239]|4[23]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[15-8]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"4\"], \"0$1\"]], \"0\"],\n    \"BF\": [\"226\", \"00\", \"(?:[025-7]\\\\d|44)\\\\d{6}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[024-7]\"]]]],\n    \"BG\": [\"359\", \"00\", \"00800\\\\d{7}|[2-7]\\\\d{6,7}|[89]\\\\d{6,8}|2\\\\d{5}\", [6, 7, 8, 9, 12], [[\"(\\\\d)(\\\\d)(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"2\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"43[1-6]|70[1-9]\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"2\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2,3})\", \"$1 $2 $3\", [\"[356]|4[124-7]|7[1-9]|8[1-6]|9[1-7]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"(?:70|8)0\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{2})\", \"$1 $2 $3\", [\"43[1-7]|7\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[48]|9[08]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"9\"], \"0$1\"]], \"0\"],\n    \"BH\": [\"973\", \"00\", \"[136-9]\\\\d{7}\", [8], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[13679]|8[02-4679]\"]]]],\n    \"BI\": [\"257\", \"00\", \"(?:[267]\\\\d|31)\\\\d{6}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[2367]\"]]]],\n    \"BJ\": [\"229\", \"00\", \"(?:01\\\\d|[24-689])\\\\d{7}\", [8, 10], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[24-689]\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4 $5\", [\"0\"]]]],\n    \"BL\": [\"590\", \"00\", \"(?:590\\\\d|7090)\\\\d{5}|(?:69|80|9\\\\d)\\\\d{7}\", [9], 0, \"0\", 0, 0, 0, 0, 0, [[\"590(?:2[7-9]|3[3-7]|5[12]|87)\\\\d{4}\"], [\"(?:69(?:0\\\\d\\\\d|1(?:2[2-9]|3[0-5])|4(?:0[89]|1[2-6]|9\\\\d)|6(?:1[016-9]|5[0-4]|[67]\\\\d))|7090[0-4])\\\\d{4}\"], [\"80[0-5]\\\\d{6}\"], 0, 0, 0, 0, 0, [\"9(?:(?:39[5-7]|76[018])\\\\d|475[0-6])\\\\d{4}\"]]],\n    \"BM\": [\"1\", \"011\", \"(?:441|[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-9]\\\\d{6})$|1\", \"441$1\", 0, \"441\"],\n    \"BN\": [\"673\", \"00\", \"[2-578]\\\\d{6}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[2-578]\"]]]],\n    \"BO\": [\"591\", \"00(?:1\\\\d)?\", \"8001\\\\d{5}|(?:[2-467]\\\\d|50)\\\\d{6}\", [8, 9], [[\"(\\\\d)(\\\\d{7})\", \"$1 $2\", [\"[235]|4[46]\"]], [\"(\\\\d{8})\", \"$1\", [\"[67]\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{4})\", \"$1 $2 $3\", [\"8\"]]], \"0\", 0, \"0(1\\\\d)?\"],\n    \"BQ\": [\"599\", \"00\", \"(?:[34]1|7\\\\d)\\\\d{5}\", [7], 0, 0, 0, 0, 0, 0, \"[347]\"],\n    \"BR\": [\"55\", \"00(?:1[245]|2[1-35]|31|4[13]|[56]5|99)\", \"[1-467]\\\\d{9,10}|55[0-46-9]\\\\d{8}|[34]\\\\d{7}|55\\\\d{7,8}|(?:5[0-46-9]|[89]\\\\d)\\\\d{7,9}\", [8, 9, 10, 11], [[\"(\\\\d{4})(\\\\d{4})\", \"$1-$2\", [\"300|4(?:0[02]|37|86)\", \"300|4(?:0(?:0|20)|370|864)\"]], [\"(\\\\d{3})(\\\\d{2,3})(\\\\d{4})\", \"$1 $2 $3\", [\"(?:[358]|90)0\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2-$3\", [\"(?:[14689][1-9]|2[12478]|3[1-578]|5[13-5]|7[13-579])[2-57]\"], \"($1)\"], [\"(\\\\d{2})(\\\\d{5})(\\\\d{4})\", \"$1 $2-$3\", [\"[16][1-9]|[2-57-9]\"], \"($1)\"]], \"0\", 0, \"(?:0|90)(?:(1[245]|2[1-35]|31|4[13]|[56]5|99)(\\\\d{10,11}))?\", \"$2\"],\n    \"BS\": [\"1\", \"011\", \"(?:242|[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([3-8]\\\\d{6})$|1\", \"242$1\", 0, \"242\"],\n    \"BT\": [\"975\", \"00\", \"[17]\\\\d{7}|[2-8]\\\\d{6}\", [7, 8], [[\"(\\\\d)(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[2-68]|7[246]\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"1[67]|7\"]]]],\n    \"BW\": [\"267\", \"00\", \"(?:0800|(?:[37]|800)\\\\d)\\\\d{6}|(?:[2-6]\\\\d|90)\\\\d{5}\", [7, 8, 10], [[\"(\\\\d{2})(\\\\d{5})\", \"$1 $2\", [\"90\"]], [\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[24-6]|3[15-9]\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[37]\"]], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"0\"]], [\"(\\\\d{3})(\\\\d{4})(\\\\d{3})\", \"$1 $2 $3\", [\"8\"]]]],\n    \"BY\": [\"375\", \"810\", \"(?:[12]\\\\d|33|44|902)\\\\d{7}|8(?:0[0-79]\\\\d{5,7}|[1-7]\\\\d{9})|8(?:1[0-489]|[5-79]\\\\d)\\\\d{7}|8[1-79]\\\\d{6,7}|8[0-79]\\\\d{5}|8\\\\d{5}\", [6, 7, 8, 9, 10, 11], [[\"(\\\\d{3})(\\\\d{3})\", \"$1 $2\", [\"800\"], \"8 $1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2,4})\", \"$1 $2 $3\", [\"800\"], \"8 $1\"], [\"(\\\\d{4})(\\\\d{2})(\\\\d{3})\", \"$1 $2-$3\", [\"1(?:5[169]|6[3-5]|7[179])|2(?:1[35]|2[34]|3[3-5])\", \"1(?:5[169]|6(?:3[1-3]|4|5[125])|7(?:1[3-9]|7[0-24-6]|9[2-7]))|2(?:1[35]|2[34]|3[3-5])\"], \"8 0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2-$3-$4\", [\"1(?:[56]|7[467])|2[1-3]\"], \"8 0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2-$3-$4\", [\"[1-4]\"], \"8 0$1\"], [\"(\\\\d{3})(\\\\d{3,4})(\\\\d{4})\", \"$1 $2 $3\", [\"[89]\"], \"8 $1\"]], \"8\", 0, \"0|80?\", 0, 0, 0, 0, \"8~10\"],\n    \"BZ\": [\"501\", \"00\", \"(?:0800\\\\d|[2-8])\\\\d{6}\", [7, 11], [[\"(\\\\d{3})(\\\\d{4})\", \"$1-$2\", [\"[2-8]\"]], [\"(\\\\d)(\\\\d{3})(\\\\d{4})(\\\\d{3})\", \"$1-$2-$3-$4\", [\"0\"]]]],\n    \"CA\": [\"1\", \"011\", \"[2-9]\\\\d{9}|3\\\\d{6}\", [7, 10], 0, \"1\", 0, 0, 0, 0, 0, [[\"(?:2(?:04|[23]6|[48]9|5[07]|63)|3(?:06|43|54|6[578]|82)|4(?:03|1[68]|[26]8|3[178]|50|74)|5(?:06|1[49]|48|79|8[147])|6(?:04|[18]3|39|47|72)|7(?:0[59]|42|53|78|8[02])|8(?:[06]7|19|25|7[39])|9(?:0[25]|42))[2-9]\\\\d{6}\", [10]], [\"\", [10]], [\"8(?:00|33|44|55|66|77|88)[2-9]\\\\d{6}\", [10]], [\"900[2-9]\\\\d{6}\", [10]], [\"52(?:3(?:[2-46-9][02-9]\\\\d|5(?:[02-46-9]\\\\d|5[0-46-9]))|4(?:[2-478][02-9]\\\\d|5(?:[034]\\\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\\\d)|9(?:[05-9]\\\\d|2[0-5]|49)))\\\\d{4}|52[34][2-9]1[02-9]\\\\d{4}|(?:5(?:2[125-9]|33|44|66|77|88)|6(?:22|33))[2-9]\\\\d{6}\", [10]], 0, [\"310\\\\d{4}\", [7]], 0, [\"600[2-9]\\\\d{6}\", [10]]]],\n    \"CC\": [\"61\", \"001[14-689]|14(?:1[14]|34|4[17]|[56]6|7[47]|88)0011\", \"1(?:[0-79]\\\\d{8}(?:\\\\d{2})?|8[0-24-9]\\\\d{7})|[148]\\\\d{8}|1\\\\d{5,7}\", [6, 7, 8, 9, 10, 12], 0, \"0\", 0, \"([59]\\\\d{7})$|0\", \"8$1\", 0, 0, [[\"8(?:51(?:0(?:02|31|60|89)|1(?:18|76)|223)|91(?:0(?:1[0-2]|29)|1(?:[28]2|50|79)|2(?:10|64)|3(?:[06]8|22)|4[29]8|62\\\\d|70[23]|959))\\\\d{3}\", [9]], [\"4(?:79[01]|83[0-389]|94[0-478])\\\\d{5}|4(?:[0-36]\\\\d|4[047-9]|5[0-25-9]|7[02-8]|8[0-24-9]|9[0-37-9])\\\\d{6}\", [9]], [\"180(?:0\\\\d{3}|2)\\\\d{3}\", [7, 10]], [\"190[0-26]\\\\d{6}\", [10]], 0, 0, 0, 0, [\"14(?:5(?:1[0458]|[23][458])|71\\\\d)\\\\d{4}\", [9]], [\"13(?:00\\\\d{6}(?:\\\\d{2})?|45[0-4]\\\\d{3})|13\\\\d{4}\", [6, 8, 10, 12]]], \"0011\"],\n    \"CD\": [\"243\", \"00\", \"(?:(?:[189]|5\\\\d)\\\\d|2)\\\\d{7}|[1-68]\\\\d{6}\", [7, 8, 9, 10], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"88\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{5})\", \"$1 $2\", [\"[1-6]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\", \"$1 $2 $3\", [\"2\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[89]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"5\"], \"0$1\"]], \"0\"],\n    \"CF\": [\"236\", \"00\", \"(?:[27]\\\\d{3}|8776)\\\\d{4}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[278]\"]]]],\n    \"CG\": [\"242\", \"00\", \"222\\\\d{6}|(?:0\\\\d|80)\\\\d{7}\", [9], [[\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"8\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[02]\"]]]],\n    \"CH\": [\"41\", \"00\", \"8\\\\d{11}|[2-9]\\\\d{8}\", [9, 12], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"8[047]|90\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[2-79]|81\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4 $5\", [\"8\"], \"0$1\"]], \"0\"],\n    \"CI\": [\"225\", \"00\", \"[02]\\\\d{9}\", [10], [[\"(\\\\d{2})(\\\\d{2})(\\\\d)(\\\\d{5})\", \"$1 $2 $3 $4\", [\"2\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{4})\", \"$1 $2 $3 $4\", [\"0\"]]]],\n    \"CK\": [\"682\", \"00\", \"[2-578]\\\\d{4}\", [5], [[\"(\\\\d{2})(\\\\d{3})\", \"$1 $2\", [\"[2-578]\"]]]],\n    \"CL\": [\"56\", \"(?:0|1(?:1[0-69]|2[02-5]|5[13-58]|69|7[0167]|8[018]))0\", \"12300\\\\d{6}|6\\\\d{9,10}|[2-9]\\\\d{8}\", [9, 10, 11], [[\"(\\\\d{5})(\\\\d{4})\", \"$1 $2\", [\"219\", \"2196\"], \"($1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"44\"]], [\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"2[1-36]\"], \"($1)\"], [\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"9[2-9]\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"3[2-5]|[47]|5[1-3578]|6[13-57]|8(?:0[1-9]|[1-9])\"], \"($1)\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"60|8\"]], [\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"60\"]]]],\n    \"CM\": [\"237\", \"00\", \"[26]\\\\d{8}|88\\\\d{6,7}\", [8, 9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"88\"]], [\"(\\\\d)(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4 $5\", [\"[26]|88\"]]]],\n    \"CN\": [\"86\", \"00|1(?:[12]\\\\d|79)\\\\d\\\\d00\", \"(?:(?:1[03-689]|2\\\\d)\\\\d\\\\d|6)\\\\d{8}|1\\\\d{10}|[126]\\\\d{6}(?:\\\\d(?:\\\\d{2})?)?|86\\\\d{5,6}|(?:[3-579]\\\\d|8[0-57-9])\\\\d{5,9}\", [7, 8, 9, 10, 11, 12], [[\"(\\\\d{2})(\\\\d{5,6})\", \"$1 $2\", [\"(?:10|2[0-57-9])[19]|3(?:[157]|35|49|9[1-68])|4(?:1[124-9]|2[179]|6[47-9]|7|8[23])|5(?:[1357]|2[37]|4[36]|6[1-46]|80)|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:07|1[236-8]|2[5-7]|[37]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|3|4[13]|5[1-5]|7[0-79]|9[0-35-9])|(?:4[35]|59|85)[1-9]\", \"(?:10|2[0-57-9])(?:1[02]|9[56])|8078|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:1[124-9]|2[179]|[35][1-9]|6[47-9]|7\\\\d|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[1-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|3\\\\d|4[13]|5[1-5]|7[0-79]|9[0-35-9]))1\", \"10(?:1(?:0|23)|9[56])|2[0-57-9](?:1(?:00|23)|9[56])|80781|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:1[124-9]|2[179]|[35][1-9]|6[47-9]|7\\\\d|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[1-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|3\\\\d|4[13]|5[1-5]|7[0-79]|9[0-35-9]))12\", \"10(?:1(?:0|23)|9[56])|2[0-57-9](?:1(?:00|23)|9[56])|807812|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:1[124-9]|2[179]|[35][1-9]|6[47-9]|7\\\\d|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[1-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|3\\\\d|4[13]|5[1-5]|7[0-79]|9[0-35-9]))123\", \"10(?:1(?:0|23)|9[56])|2[0-57-9](?:1(?:00|23)|9[56])|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:1[124-9]|2[179]|[35][1-9]|6[47-9]|7\\\\d|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:078|1[236-8]|2[5-7]|[37]\\\\d|5[1-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|3\\\\d|4[13]|5[1-5]|7[0-79]|9[0-35-9]))123\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{5,6})\", \"$1 $2\", [\"3(?:[157]|35|49|9[1-68])|4(?:[17]|2[179]|6[47-9]|8[23])|5(?:[1357]|2[37]|4[36]|6[1-46]|80)|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]|4[13]|5[1-5])|(?:4[35]|59|85)[1-9]\", \"(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:[17]\\\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[1-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\\\d|4[13]|5[1-5]))[19]\", \"85[23](?:10|95)|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:[17]\\\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[14-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\\\d|4[13]|5[1-5]))(?:10|9[56])\", \"85[23](?:100|95)|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:[17]\\\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[14-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\\\d|4[13]|5[1-5]))(?:100|9[56])\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"(?:4|80)0\"]], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"10|2(?:[02-57-9]|1[1-9])\", \"10|2(?:[02-57-9]|1[1-9])\", \"10[0-79]|2(?:[02-57-9]|1[1-79])|(?:10|21)8(?:0[1-9]|[1-9])\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"3(?:[3-59]|7[02-68])|4(?:[26-8]|3[3-9]|5[2-9])|5(?:3[03-9]|[468]|7[028]|9[2-46-9])|6|7(?:[0-247]|3[04-9]|5[0-4689]|6[2368])|8(?:[1-358]|9[1-7])|9(?:[013479]|5[1-5])|(?:[34]1|55|79|87)[02-9]\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{7,8})\", \"$1 $2\", [\"9\"]], [\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"80\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"[3-578]\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"1[3-9]\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3 $4\", [\"[12]\"], \"0$1\", 1]], \"0\", 0, \"(1(?:[12]\\\\d|79)\\\\d\\\\d)|0\", 0, 0, 0, 0, \"00\"],\n    \"CO\": [\"57\", \"00(?:4(?:[14]4|56)|[579])\", \"(?:46|60\\\\d\\\\d)\\\\d{6}|(?:1\\\\d|[39])\\\\d{9}\", [8, 10, 11], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"46\"]], [\"(\\\\d{3})(\\\\d{7})\", \"$1 $2\", [\"6|90\"], \"($1)\"], [\"(\\\\d{3})(\\\\d{7})\", \"$1 $2\", [\"3[0-357]|9[14]\"]], [\"(\\\\d)(\\\\d{3})(\\\\d{7})\", \"$1-$2-$3\", [\"1\"], \"0$1\", 0, \"$1 $2 $3\"]], \"0\", 0, \"0([3579]|4(?:[14]4|56))?\"],\n    \"CR\": [\"506\", \"00\", \"(?:8\\\\d|90)\\\\d{8}|(?:[24-8]\\\\d{3}|3005)\\\\d{4}\", [8, 10], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[2-7]|8[3-9]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1-$2-$3\", [\"[89]\"]]], 0, 0, \"(19(?:0[0-2468]|1[09]|20|66|77|99))\"],\n    \"CU\": [\"53\", \"119\", \"(?:[2-7]|8\\\\d\\\\d)\\\\d{7}|[2-47]\\\\d{6}|[34]\\\\d{5}\", [6, 7, 8, 10], [[\"(\\\\d{2})(\\\\d{4,6})\", \"$1 $2\", [\"2[1-4]|[34]\"], \"(0$1)\"], [\"(\\\\d)(\\\\d{6,7})\", \"$1 $2\", [\"7\"], \"(0$1)\"], [\"(\\\\d)(\\\\d{7})\", \"$1 $2\", [\"[56]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{7})\", \"$1 $2\", [\"8\"], \"0$1\"]], \"0\"],\n    \"CV\": [\"238\", \"0\", \"(?:[2-59]\\\\d\\\\d|800)\\\\d{4}\", [7], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3\", [\"[2-589]\"]]]],\n    \"CW\": [\"599\", \"00\", \"(?:[34]1|60|(?:7|9\\\\d)\\\\d)\\\\d{5}\", [7, 8], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[3467]\"]], [\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"9[4-8]\"]]], 0, 0, 0, 0, 0, \"[69]\"],\n    \"CX\": [\"61\", \"001[14-689]|14(?:1[14]|34|4[17]|[56]6|7[47]|88)0011\", \"1(?:[0-79]\\\\d{8}(?:\\\\d{2})?|8[0-24-9]\\\\d{7})|[148]\\\\d{8}|1\\\\d{5,7}\", [6, 7, 8, 9, 10, 12], 0, \"0\", 0, \"([59]\\\\d{7})$|0\", \"8$1\", 0, 0, [[\"8(?:51(?:0(?:01|30|59|88)|1(?:17|46|75)|2(?:22|35))|91(?:00[6-9]|1(?:[28]1|49|78)|2(?:09|63)|3(?:12|26|75)|4(?:56|97)|64\\\\d|7(?:0[01]|1[0-2])|958))\\\\d{3}\", [9]], [\"4(?:79[01]|83[0-389]|94[0-478])\\\\d{5}|4(?:[0-36]\\\\d|4[047-9]|5[0-25-9]|7[02-8]|8[0-24-9]|9[0-37-9])\\\\d{6}\", [9]], [\"180(?:0\\\\d{3}|2)\\\\d{3}\", [7, 10]], [\"190[0-26]\\\\d{6}\", [10]], 0, 0, 0, 0, [\"14(?:5(?:1[0458]|[23][458])|71\\\\d)\\\\d{4}\", [9]], [\"13(?:00\\\\d{6}(?:\\\\d{2})?|45[0-4]\\\\d{3})|13\\\\d{4}\", [6, 8, 10, 12]]], \"0011\"],\n    \"CY\": [\"357\", \"00\", \"(?:[279]\\\\d|[58]0)\\\\d{6}\", [8], [[\"(\\\\d{2})(\\\\d{6})\", \"$1 $2\", [\"[257-9]\"]]]],\n    \"CZ\": [\"420\", \"00\", \"(?:[2-578]\\\\d|60)\\\\d{7}|9\\\\d{8,11}\", [9, 10, 11, 12], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[2-8]|9[015-7]\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"96\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"9\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"9\"]]]],\n    \"DE\": [\"49\", \"00\", \"[2579]\\\\d{5,14}|49(?:[34]0|69|8\\\\d)\\\\d\\\\d?|49(?:37|49|60|7[089]|9\\\\d)\\\\d{1,3}|49(?:2[024-9]|3[2-689]|7[1-7])\\\\d{1,8}|(?:1|[368]\\\\d|4[0-8])\\\\d{3,13}|49(?:[015]\\\\d|2[13]|31|[46][1-8])\\\\d{1,9}\", [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], [[\"(\\\\d{2})(\\\\d{3,13})\", \"$1 $2\", [\"3[02]|40|[68]9\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3,12})\", \"$1 $2\", [\"2(?:0[1-389]|1[124]|2[18]|3[14])|3(?:[35-9][15]|4[015])|906|(?:2[4-9]|4[2-9]|[579][1-9]|[68][1-8])1\", \"2(?:0[1-389]|12[0-8])|3(?:[35-9][15]|4[015])|906|2(?:[13][14]|2[18])|(?:2[4-9]|4[2-9]|[579][1-9]|[68][1-8])1\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{2,11})\", \"$1 $2\", [\"[24-6]|3(?:[3569][02-46-9]|4[2-4679]|7[2-467]|8[2-46-8])|70[2-8]|8(?:0[2-9]|[1-8])|90[7-9]|[79][1-9]\", \"[24-6]|3(?:3(?:0[1-467]|2[127-9]|3[124578]|7[1257-9]|8[1256]|9[145])|4(?:2[135]|4[13578]|9[1346])|5(?:0[14]|2[1-3589]|6[1-4]|7[13468]|8[13568])|6(?:2[1-489]|3[124-6]|6[13]|7[12579]|8[1-356]|9[135])|7(?:2[1-7]|4[145]|6[1-5]|7[1-4])|8(?:21|3[1468]|6|7[1467]|8[136])|9(?:0[12479]|2[1358]|4[134679]|6[1-9]|7[136]|8[147]|9[1468]))|70[2-8]|8(?:0[2-9]|[1-8])|90[7-9]|[79][1-9]|3[68]4[1347]|3(?:47|60)[1356]|3(?:3[46]|46|5[49])[1246]|3[4579]3[1357]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"138\"], \"0$1\"], [\"(\\\\d{5})(\\\\d{2,10})\", \"$1 $2\", [\"3\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{5,11})\", \"$1 $2\", [\"181\"], \"0$1\"], [\"(\\\\d{3})(\\\\d)(\\\\d{4,10})\", \"$1 $2 $3\", [\"1(?:3|80)|9\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{7,8})\", \"$1 $2\", [\"1[67]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{7,12})\", \"$1 $2\", [\"8\"], \"0$1\"], [\"(\\\\d{5})(\\\\d{6})\", \"$1 $2\", [\"185\", \"1850\", \"18500\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"7\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{7})\", \"$1 $2\", [\"18[68]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{7})\", \"$1 $2\", [\"15[1279]\"], \"0$1\"], [\"(\\\\d{5})(\\\\d{6})\", \"$1 $2\", [\"15[03568]\", \"15(?:[0568]|31)\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{8})\", \"$1 $2\", [\"18\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{7,8})\", \"$1 $2 $3\", [\"1(?:6[023]|7)\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{2})(\\\\d{7})\", \"$1 $2 $3\", [\"15[279]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{8})\", \"$1 $2 $3\", [\"15\"], \"0$1\"]], \"0\"],\n    \"DJ\": [\"253\", \"00\", \"(?:2\\\\d|77)\\\\d{6}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[27]\"]]]],\n    \"DK\": [\"45\", \"00\", \"[2-9]\\\\d{7}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[2-9]\"]]]],\n    \"DM\": [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|767|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-7]\\\\d{6})$|1\", \"767$1\", 0, \"767\"],\n    \"DO\": [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, 0, 0, 0, \"8001|8[024]9\"],\n    \"DZ\": [\"213\", \"00\", \"(?:[1-4]|[5-79]\\\\d|80)\\\\d{7}\", [8, 9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[1-4]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"9\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[5-8]\"], \"0$1\"]], \"0\"],\n    \"EC\": [\"593\", \"00\", \"1\\\\d{9,10}|(?:[2-7]|9\\\\d)\\\\d{7}\", [8, 9, 10, 11], [[\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2-$3\", [\"[2-7]\"], \"(0$1)\", 0, \"$1-$2-$3\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"9\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"1\"]]], \"0\"],\n    \"EE\": [\"372\", \"00\", \"8\\\\d{9}|[4578]\\\\d{7}|(?:[3-8]\\\\d|90)\\\\d{5}\", [7, 8, 10], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[369]|4[3-8]|5(?:[0-2]|5[0-478]|6[45])|7[1-9]|88\", \"[369]|4[3-8]|5(?:[02]|1(?:[0-8]|95)|5[0-478]|6(?:4[0-4]|5[1-589]))|7[1-9]|88\"]], [\"(\\\\d{4})(\\\\d{3,4})\", \"$1 $2\", [\"[45]|8(?:00|[1-49])\", \"[45]|8(?:00[1-9]|[1-49])\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\", \"$1 $2 $3\", [\"7\"]], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"8\"]]]],\n    \"EG\": [\"20\", \"00\", \"[189]\\\\d{8,9}|[24-6]\\\\d{8}|[135]\\\\d{7}\", [8, 9, 10], [[\"(\\\\d)(\\\\d{7,8})\", \"$1 $2\", [\"[23]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{6,7})\", \"$1 $2\", [\"1[35]|[4-6]|8[2468]|9[235-7]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[89]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{8})\", \"$1 $2\", [\"1\"], \"0$1\"]], \"0\"],\n    \"EH\": [\"212\", \"00\", \"[5-8]\\\\d{8}\", [9], 0, \"0\", 0, 0, 0, 0, \"528[89]\"],\n    \"ER\": [\"291\", \"00\", \"[178]\\\\d{6}\", [7], [[\"(\\\\d)(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[178]\"], \"0$1\"]], \"0\"],\n    \"ES\": [\"34\", \"00\", \"[5-9]\\\\d{8}\", [9], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[89]00\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[5-9]\"]]]],\n    \"ET\": [\"251\", \"00\", \"(?:11|[2-579]\\\\d)\\\\d{7}\", [9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[1-579]\"], \"0$1\"]], \"0\"],\n    \"FI\": [\"358\", \"00|99(?:[01469]|5(?:[14]1|3[23]|5[59]|77|88|9[09]))\", \"[1-35689]\\\\d{4}|7\\\\d{10,11}|(?:[124-7]\\\\d|3[0-46-9])\\\\d{8}|[1-9]\\\\d{5,8}\", [5, 6, 7, 8, 9, 10, 11, 12], [[\"(\\\\d{5})\", \"$1\", [\"20[2-59]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3,7})\", \"$1 $2\", [\"(?:[1-3]0|[68])0|70[07-9]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4,8})\", \"$1 $2\", [\"[14]|2[09]|50|7[135]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{6,10})\", \"$1 $2\", [\"7\"], \"0$1\"], [\"(\\\\d)(\\\\d{4,9})\", \"$1 $2\", [\"(?:19|[2568])[1-8]|3(?:0[1-9]|[1-9])|9\"], \"0$1\"]], \"0\", 0, 0, 0, 0, \"1[03-79]|[2-9]\", 0, \"00\"],\n    \"FJ\": [\"679\", \"0(?:0|52)\", \"45\\\\d{5}|(?:0800\\\\d|[235-9])\\\\d{6}\", [7, 11], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[235-9]|45\"]], [\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"0\"]]], 0, 0, 0, 0, 0, 0, 0, \"00\"],\n    \"FK\": [\"500\", \"00\", \"[2-7]\\\\d{4}\", [5]],\n    \"FM\": [\"691\", \"00\", \"(?:[39]\\\\d\\\\d|820)\\\\d{4}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[389]\"]]]],\n    \"FO\": [\"298\", \"00\", \"[2-9]\\\\d{5}\", [6], [[\"(\\\\d{6})\", \"$1\", [\"[2-9]\"]]], 0, 0, \"(10(?:01|[12]0|88))\"],\n    \"FR\": [\"33\", \"00\", \"[1-9]\\\\d{8}\", [9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"8\"], \"0 $1\"], [\"(\\\\d)(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4 $5\", [\"[1-79]\"], \"0$1\"]], \"0\"],\n    \"GA\": [\"241\", \"00\", \"(?:[067]\\\\d|11)\\\\d{6}|[2-7]\\\\d{6}\", [7, 8], [[\"(\\\\d)(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[2-7]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"0\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"11|[67]\"], \"0$1\"]], 0, 0, \"0(11\\\\d{6}|60\\\\d{6}|61\\\\d{6}|6[256]\\\\d{6}|7[467]\\\\d{6})\", \"$1\"],\n    \"GB\": [\"44\", \"00\", \"[1-357-9]\\\\d{9}|[18]\\\\d{8}|8\\\\d{6}\", [7, 9, 10], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"800\", \"8001\", \"80011\", \"800111\", \"8001111\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3\", [\"845\", \"8454\", \"84546\", \"845464\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{6})\", \"$1 $2\", [\"800\"], \"0$1\"], [\"(\\\\d{5})(\\\\d{4,5})\", \"$1 $2\", [\"1(?:38|5[23]|69|76|94)\", \"1(?:(?:38|69)7|5(?:24|39)|768|946)\", \"1(?:3873|5(?:242|39[4-6])|(?:697|768)[347]|9467)\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{5,6})\", \"$1 $2\", [\"1(?:[2-69][02-9]|[78])\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"[25]|7(?:0|6[02-9])\", \"[25]|7(?:0|6(?:[03-9]|2[356]))\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{6})\", \"$1 $2\", [\"7\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[1389]\"], \"0$1\"]], \"0\", 0, 0, 0, 0, 0, [[\"(?:1(?:1(?:3(?:[0-58]\\\\d\\\\d|73[0-35])|4(?:(?:[0-5]\\\\d|70)\\\\d|69[7-9])|(?:(?:5[0-26-9]|[78][0-49])\\\\d|6(?:[0-4]\\\\d|50))\\\\d)|(?:2(?:(?:0[024-9]|2[3-9]|3[3-79]|4[1-689]|[58][02-9]|6[0-47-9]|7[013-9]|9\\\\d)\\\\d|1(?:[0-7]\\\\d|8[0-3]))|(?:3(?:0\\\\d|1[0-8]|[25][02-9]|3[02-579]|[468][0-46-9]|7[1-35-79]|9[2-578])|4(?:0[03-9]|[137]\\\\d|[28][02-57-9]|4[02-69]|5[0-8]|[69][0-79])|5(?:0[1-35-9]|[16]\\\\d|2[024-9]|3[015689]|4[02-9]|5[03-9]|7[0-35-9]|8[0-468]|9[0-57-9])|6(?:0[034689]|1\\\\d|2[0-35689]|[38][013-9]|4[1-467]|5[0-69]|6[13-9]|7[0-8]|9[0-24578])|7(?:0[0246-9]|2\\\\d|3[0236-8]|4[03-9]|5[0-46-9]|6[013-9]|7[0-35-9]|8[024-9]|9[02-9])|8(?:0[35-9]|2[1-57-9]|3[02-578]|4[0-578]|5[124-9]|6[2-69]|7\\\\d|8[02-9]|9[02569])|9(?:0[02-589]|[18]\\\\d|2[02-689]|3[1-57-9]|4[2-9]|5[0-579]|6[2-47-9]|7[0-24578]|9[2-57]))\\\\d)\\\\d)|2(?:0[013478]|3[0189]|4[017]|8[0-46-9]|9[0-2])\\\\d{3})\\\\d{4}|1(?:2(?:0(?:46[1-4]|87[2-9])|545[1-79]|76(?:2\\\\d|3[1-8]|6[1-6])|9(?:7(?:2[0-4]|3[2-5])|8(?:2[2-8]|7[0-47-9]|8[3-5])))|3(?:6(?:38[2-5]|47[23])|8(?:47[04-9]|64[0157-9]))|4(?:044[1-7]|20(?:2[23]|8\\\\d)|6(?:0(?:30|5[2-57]|6[1-8]|7[2-8])|140)|8(?:052|87[1-3]))|5(?:2(?:4(?:3[2-79]|6\\\\d)|76\\\\d)|6(?:26[06-9]|686))|6(?:06(?:4\\\\d|7[4-79])|295[5-7]|35[34]\\\\d|47(?:24|61)|59(?:5[08]|6[67]|74)|9(?:55[0-4]|77[23]))|7(?:26(?:6[13-9]|7[0-7])|(?:442|688)\\\\d|50(?:2[0-3]|[3-68]2|76))|8(?:27[56]\\\\d|37(?:5[2-5]|8[239])|843[2-58])|9(?:0(?:0(?:6[1-8]|85)|52\\\\d)|3583|4(?:66[1-8]|9(?:2[01]|81))|63(?:23|3[1-4])|9561))\\\\d{3}\", [9, 10]], [\"7(?:457[0-57-9]|700[01]|911[028])\\\\d{5}|7(?:[1-3]\\\\d\\\\d|4(?:[0-46-9]\\\\d|5[0-689])|5(?:0[0-8]|[13-9]\\\\d|2[0-35-9])|7(?:0[1-9]|[1-7]\\\\d|8[02-9]|9[0-689])|8(?:[014-9]\\\\d|[23][0-8])|9(?:[024-9]\\\\d|1[02-9]|3[0-689]))\\\\d{6}\", [10]], [\"80[08]\\\\d{7}|800\\\\d{6}|8001111\"], [\"(?:8(?:4[2-5]|7[0-3])|9(?:[01]\\\\d|8[2-49]))\\\\d{7}|845464\\\\d\", [7, 10]], [\"70\\\\d{8}\", [10]], 0, [\"(?:3[0347]|55)\\\\d{8}\", [10]], [\"76(?:464|652)\\\\d{5}|76(?:0[0-28]|2[356]|34|4[01347]|5[49]|6[0-369]|77|8[14]|9[139])\\\\d{6}\", [10]], [\"56\\\\d{8}\", [10]]], 0, \" x\"],\n    \"GD\": [\"1\", \"011\", \"(?:473|[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-9]\\\\d{6})$|1\", \"473$1\", 0, \"473\"],\n    \"GE\": [\"995\", \"00\", \"(?:[3-57]\\\\d\\\\d|800)\\\\d{6}\", [9], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"70\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"32\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[57]\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[348]\"], \"0$1\"]], \"0\"],\n    \"GF\": [\"594\", \"00\", \"(?:[56]94\\\\d|7093)\\\\d{5}|(?:80|9\\\\d)\\\\d{7}\", [9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[5-7]|9[47]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[89]\"], \"0$1\"]], \"0\"],\n    \"GG\": [\"44\", \"00\", \"(?:1481|[357-9]\\\\d{3})\\\\d{6}|8\\\\d{6}(?:\\\\d{2})?\", [7, 9, 10], 0, \"0\", 0, \"([25-9]\\\\d{5})$|0\", \"1481$1\", 0, 0, [[\"1481[25-9]\\\\d{5}\", [10]], [\"7(?:(?:781|839)\\\\d|911[17])\\\\d{5}\", [10]], [\"80[08]\\\\d{7}|800\\\\d{6}|8001111\"], [\"(?:8(?:4[2-5]|7[0-3])|9(?:[01]\\\\d|8[0-3]))\\\\d{7}|845464\\\\d\", [7, 10]], [\"70\\\\d{8}\", [10]], 0, [\"(?:3[0347]|55)\\\\d{8}\", [10]], [\"76(?:464|652)\\\\d{5}|76(?:0[0-28]|2[356]|34|4[01347]|5[49]|6[0-369]|77|8[14]|9[139])\\\\d{6}\", [10]], [\"56\\\\d{8}\", [10]]]],\n    \"GH\": [\"233\", \"00\", \"(?:[235]\\\\d{3}|800)\\\\d{5}\", [8, 9], [[\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"8\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[235]\"], \"0$1\"]], \"0\"],\n    \"GI\": [\"350\", \"00\", \"(?:[25]\\\\d|60)\\\\d{6}\", [8], [[\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"2\"]]]],\n    \"GL\": [\"299\", \"00\", \"(?:19|[2-689]\\\\d|70)\\\\d{4}\", [6], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3\", [\"19|[2-9]\"]]]],\n    \"GM\": [\"220\", \"00\", \"[2-9]\\\\d{6}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[2-9]\"]]]],\n    \"GN\": [\"224\", \"00\", \"722\\\\d{6}|(?:3|6\\\\d)\\\\d{7}\", [8, 9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"3\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[67]\"]]]],\n    \"GP\": [\"590\", \"00\", \"(?:590\\\\d|7090)\\\\d{5}|(?:69|80|9\\\\d)\\\\d{7}\", [9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[5-79]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"8\"], \"0$1\"]], \"0\", 0, 0, 0, 0, 0, [[\"590(?:0[1-68]|[14][0-24-9]|2[0-68]|3[1-9]|5[3-579]|[68][0-689]|7[08]|9\\\\d)\\\\d{4}\"], [\"(?:69(?:0\\\\d\\\\d|1(?:2[2-9]|3[0-5])|4(?:0[89]|1[2-6]|9\\\\d)|6(?:1[016-9]|5[0-4]|[67]\\\\d))|7090[0-4])\\\\d{4}\"], [\"80[0-5]\\\\d{6}\"], 0, 0, 0, 0, 0, [\"9(?:(?:39[5-7]|76[018])\\\\d|475[0-6])\\\\d{4}\"]]],\n    \"GQ\": [\"240\", \"00\", \"222\\\\d{6}|(?:3\\\\d|55|[89]0)\\\\d{7}\", [9], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[235]\"]], [\"(\\\\d{3})(\\\\d{6})\", \"$1 $2\", [\"[89]\"]]]],\n    \"GR\": [\"30\", \"00\", \"5005000\\\\d{3}|8\\\\d{9,11}|(?:[269]\\\\d|70)\\\\d{8}\", [10, 11, 12], [[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"21|7\"]], [\"(\\\\d{4})(\\\\d{6})\", \"$1 $2\", [\"2(?:2|3[2-57-9]|4[2-469]|5[2-59]|6[2-9]|7[2-69]|8[2-49])|5\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[2689]\"]], [\"(\\\\d{3})(\\\\d{3,4})(\\\\d{5})\", \"$1 $2 $3\", [\"8\"]]]],\n    \"GT\": [\"502\", \"00\", \"80\\\\d{6}|(?:1\\\\d{3}|[2-7])\\\\d{7}\", [8, 11], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[2-8]\"]], [\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"]]]],\n    \"GU\": [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|671|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-9]\\\\d{6})$|1\", \"671$1\", 0, \"671\"],\n    \"GW\": [\"245\", \"00\", \"[49]\\\\d{8}|4\\\\d{6}\", [7, 9], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"40\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[49]\"]]]],\n    \"GY\": [\"592\", \"001\", \"(?:[2-8]\\\\d{3}|9008)\\\\d{3}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[2-9]\"]]]],\n    \"HK\": [\"852\", \"00(?:30|5[09]|[126-9]?)\", \"8[0-46-9]\\\\d{6,7}|9\\\\d{4,7}|(?:[2-7]|9\\\\d{3})\\\\d{7}\", [5, 6, 7, 8, 9, 11], [[\"(\\\\d{3})(\\\\d{2,5})\", \"$1 $2\", [\"900\", \"9003\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[2-7]|8[1-4]|9(?:0[1-9]|[1-8])\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"8\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"9\"]]], 0, 0, 0, 0, 0, 0, 0, \"00\"],\n    \"HN\": [\"504\", \"00\", \"8\\\\d{10}|[237-9]\\\\d{7}\", [8, 11], [[\"(\\\\d{4})(\\\\d{4})\", \"$1-$2\", [\"[237-9]\"]]]],\n    \"HR\": [\"385\", \"00\", \"[2-69]\\\\d{8}|80\\\\d{5,7}|[1-79]\\\\d{7}|6\\\\d{6}\", [7, 8, 9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"6[01]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2,3})\", \"$1 $2 $3\", [\"8\"], \"0$1\"], [\"(\\\\d)(\\\\d{4})(\\\\d{3})\", \"$1 $2 $3\", [\"1\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"6|7[245]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"9\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[2-57]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"8\"], \"0$1\"]], \"0\"],\n    \"HT\": [\"509\", \"00\", \"[2-589]\\\\d{7}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\", \"$1 $2 $3\", [\"[2-589]\"]]]],\n    \"HU\": [\"36\", \"00\", \"[235-7]\\\\d{8}|[1-9]\\\\d{7}\", [8, 9], [[\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"], \"(06 $1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[27][2-9]|3[2-7]|4[24-9]|5[2-79]|6|8[2-57-9]|9[2-69]\"], \"(06 $1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[2-9]\"], \"06 $1\"]], \"06\"],\n    \"ID\": [\"62\", \"00[89]\", \"00[1-9]\\\\d{9,14}|(?:[1-36]|8\\\\d{5})\\\\d{6}|00\\\\d{9}|[1-9]\\\\d{8,10}|[2-9]\\\\d{7}\", [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17], [[\"(\\\\d)(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"15\"]], [\"(\\\\d{2})(\\\\d{5,9})\", \"$1 $2\", [\"2[124]|[36]1\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{5,7})\", \"$1 $2\", [\"800\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{5,8})\", \"$1 $2\", [\"[2-79]\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{3,4})(\\\\d{3})\", \"$1-$2-$3\", [\"8[1-35-9]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{6,8})\", \"$1 $2\", [\"1\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"804\"], \"0$1\"], [\"(\\\\d{3})(\\\\d)(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"80\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{4})(\\\\d{4,5})\", \"$1-$2-$3\", [\"8\"], \"0$1\"]], \"0\"],\n    \"IE\": [\"353\", \"00\", \"(?:1\\\\d|[2569])\\\\d{6,8}|4\\\\d{6,9}|7\\\\d{8}|8\\\\d{8,9}\", [7, 8, 9, 10], [[\"(\\\\d{2})(\\\\d{5})\", \"$1 $2\", [\"2[24-9]|47|58|6[237-9]|9[35-9]\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"[45]0\"], \"(0$1)\"], [\"(\\\\d)(\\\\d{3,4})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[2569]|4[1-69]|7[14]\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"70\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"81\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[78]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"1\"]], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"4\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3 $4\", [\"8\"], \"0$1\"]], \"0\"],\n    \"IL\": [\"972\", \"0(?:0|1[2-9])\", \"1\\\\d{6}(?:\\\\d{3,5})?|[57]\\\\d{8}|[1-489]\\\\d{7}\", [7, 8, 9, 10, 11, 12], [[\"(\\\\d{4})(\\\\d{3})\", \"$1-$2\", [\"125\"]], [\"(\\\\d{4})(\\\\d{2})(\\\\d{2})\", \"$1-$2-$3\", [\"121\"]], [\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1-$2-$3\", [\"[2-489]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1-$2-$3\", [\"[57]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1-$2-$3\", [\"12\"]], [\"(\\\\d{4})(\\\\d{6})\", \"$1-$2\", [\"159\"]], [\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1-$2-$3-$4\", [\"1[7-9]\"]], [\"(\\\\d{3})(\\\\d{1,2})(\\\\d{3})(\\\\d{4})\", \"$1-$2 $3-$4\", [\"15\"]]], \"0\"],\n    \"IM\": [\"44\", \"00\", \"1624\\\\d{6}|(?:[3578]\\\\d|90)\\\\d{8}\", [10], 0, \"0\", 0, \"([25-8]\\\\d{5})$|0\", \"1624$1\", 0, \"74576|(?:16|7[56])24\"],\n    \"IN\": [\"91\", \"00\", \"(?:000800|[2-9]\\\\d\\\\d)\\\\d{7}|1\\\\d{7,12}\", [8, 9, 10, 11, 12, 13], [[\"(\\\\d{8})\", \"$1\", [\"5(?:0|2[23]|3[03]|[67]1|88)\", \"5(?:0|2(?:21|3)|3(?:0|3[23])|616|717|888)\", \"5(?:0|2(?:21|3)|3(?:0|3[23])|616|717|8888)\"], 0, 1], [\"(\\\\d{4})(\\\\d{4,5})\", \"$1 $2\", [\"180\", \"1800\"], 0, 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"140\"], 0, 1], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"11|2[02]|33|4[04]|79[1-7]|80[2-46]\", \"11|2[02]|33|4[04]|79(?:[1-6]|7[19])|80(?:[2-4]|6[0-589])\", \"11|2[02]|33|4[04]|79(?:[124-6]|3(?:[02-9]|1[0-24-9])|7(?:1|9[1-6]))|80(?:[2-4]|6[0-589])\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1(?:2[0-249]|3[0-25]|4[145]|[68]|7[1257])|2(?:1[257]|3[013]|4[01]|5[0137]|6[0158]|78|8[1568])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|5[12]|[78]1)|6(?:12|[2-4]1|5[17]|6[13]|80)|7(?:12|3[134]|4[47]|61|88)|8(?:16|2[014]|3[126]|6[136]|7[078]|8[34]|91)|(?:43|59|75)[15]|(?:1[59]|29|67|72)[14]\", \"1(?:2[0-24]|3[0-25]|4[145]|[59][14]|6[1-9]|7[1257]|8[1-57-9])|2(?:1[257]|3[013]|4[01]|5[0137]|6[058]|78|8[1568]|9[14])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|3[15]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|[578]1|9[15])|674|7(?:(?:2[14]|3[34]|5[15])[2-6]|61[346]|88[0-8])|8(?:70[2-6]|84[235-7]|91[3-7])|(?:1(?:29|60|8[06])|261|552|6(?:12|[2-47]1|5[17]|6[13]|80)|7(?:12|31|4[47])|8(?:16|2[014]|3[126]|6[136]|7[78]|83))[2-7]\", \"1(?:2[0-24]|3[0-25]|4[145]|[59][14]|6[1-9]|7[1257]|8[1-57-9])|2(?:1[257]|3[013]|4[01]|5[0137]|6[058]|78|8[1568]|9[14])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|3[15]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|[578]1|9[15])|6(?:12(?:[2-6]|7[0-8])|74[2-7])|7(?:(?:2[14]|5[15])[2-6]|3171|61[346]|88(?:[2-7]|82))|8(?:70[2-6]|84(?:[2356]|7[19])|91(?:[3-6]|7[19]))|73[134][2-6]|(?:74[47]|8(?:16|2[014]|3[126]|6[136]|7[78]|83))(?:[2-6]|7[19])|(?:1(?:29|60|8[06])|261|552|6(?:[2-4]1|5[17]|6[13]|7(?:1|4[0189])|80)|7(?:12|88[01]))[2-7]\"], \"0$1\", 1], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"1(?:[2-479]|5[0235-9])|[2-5]|6(?:1[1358]|2[2457-9]|3[2-5]|4[235-7]|5[2-689]|6[24578]|7[235689]|8[1-6])|7(?:1[013-9]|28|3[129]|4[1-35689]|5[29]|6[02-5]|70)|807\", \"1(?:[2-479]|5[0235-9])|[2-5]|6(?:1[1358]|2(?:[2457]|84|95)|3(?:[2-4]|55)|4[235-7]|5[2-689]|6[24578]|7[235689]|8[1-6])|7(?:1(?:[013-8]|9[6-9])|28[6-8]|3(?:17|2[0-49]|9[2-57])|4(?:1[2-4]|[29][0-7]|3[0-8]|[56]|8[0-24-7])|5(?:2[1-3]|9[0-6])|6(?:0[5689]|2[5-9]|3[02-8]|4|5[0-367])|70[13-7])|807[19]\", \"1(?:[2-479]|5(?:[0236-9]|5[013-9]))|[2-5]|6(?:2(?:84|95)|355|83)|73179|807(?:1|9[1-3])|(?:1552|6(?:1[1358]|2[2457]|3[2-4]|4[235-7]|5[2-689]|6[24578]|7[235689]|8[124-6])\\\\d|7(?:1(?:[013-8]\\\\d|9[6-9])|28[6-8]|3(?:2[0-49]|9[2-57])|4(?:1[2-4]|[29][0-7]|3[0-8]|[56]\\\\d|8[0-24-7])|5(?:2[1-3]|9[0-6])|6(?:0[5689]|2[5-9]|3[02-8]|4\\\\d|5[0-367])|70[13-7]))[2-7]\"], \"0$1\", 1], [\"(\\\\d{5})(\\\\d{5})\", \"$1 $2\", [\"[6-9]\"], \"0$1\", 1], [\"(\\\\d{4})(\\\\d{2,4})(\\\\d{4})\", \"$1 $2 $3\", [\"1(?:6|8[06])\", \"1(?:6|8[06]0)\"], 0, 1], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"18\"], 0, 1]], \"0\"],\n    \"IO\": [\"246\", \"00\", \"3\\\\d{6}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"3\"]]]],\n    \"IQ\": [\"964\", \"00\", \"(?:1|7\\\\d\\\\d)\\\\d{7}|[2-6]\\\\d{7,8}\", [8, 9, 10], [[\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[2-6]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"7\"], \"0$1\"]], \"0\"],\n    \"IR\": [\"98\", \"00\", \"[1-9]\\\\d{9}|(?:[1-8]\\\\d\\\\d|9)\\\\d{3,4}\", [4, 5, 6, 7, 10], [[\"(\\\\d{4,5})\", \"$1\", [\"96\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4,5})\", \"$1 $2\", [\"(?:1[137]|2[13-68]|3[1458]|4[145]|5[1468]|6[16]|7[1467]|8[13467])[12689]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"9\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"[1-8]\"], \"0$1\"]], \"0\"],\n    \"IS\": [\"354\", \"00|1(?:0(?:01|[12]0)|100)\", \"(?:38\\\\d|[4-9])\\\\d{6}\", [7, 9], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[4-9]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"3\"]]], 0, 0, 0, 0, 0, 0, 0, \"00\"],\n    \"IT\": [\"39\", \"00\", \"0\\\\d{5,10}|1\\\\d{8,10}|3(?:[0-8]\\\\d{7,10}|9\\\\d{7,8})|(?:43|55|70)\\\\d{8}|8\\\\d{5}(?:\\\\d{2,4})?\", [6, 7, 8, 9, 10, 11, 12], [[\"(\\\\d{2})(\\\\d{4,6})\", \"$1 $2\", [\"0[26]\"]], [\"(\\\\d{3})(\\\\d{3,6})\", \"$1 $2\", [\"0[13-57-9][0159]|8(?:03|4[17]|9[2-5])\", \"0[13-57-9][0159]|8(?:03|4[17]|9(?:2|3[04]|[45][0-4]))\"]], [\"(\\\\d{4})(\\\\d{2,6})\", \"$1 $2\", [\"0(?:[13-579][2-46-8]|8[236-8])\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"894\"]], [\"(\\\\d{2})(\\\\d{3,4})(\\\\d{4})\", \"$1 $2 $3\", [\"0[26]|5\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"1(?:44|[679])|[378]|43\"]], [\"(\\\\d{3})(\\\\d{3,4})(\\\\d{4})\", \"$1 $2 $3\", [\"0[13-57-9][0159]|14\"]], [\"(\\\\d{2})(\\\\d{4})(\\\\d{5})\", \"$1 $2 $3\", [\"0[26]\"]], [\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"0\"]], [\"(\\\\d{3})(\\\\d{4})(\\\\d{4,5})\", \"$1 $2 $3\", [\"3\"]]], 0, 0, 0, 0, 0, 0, [[\"0669[0-79]\\\\d{1,6}|0(?:1(?:[0159]\\\\d|[27][1-5]|31|4[1-4]|6[1356]|8[2-57])|2\\\\d\\\\d|3(?:[0159]\\\\d|2[1-4]|3[12]|[48][1-6]|6[2-59]|7[1-7])|4(?:[0159]\\\\d|[23][1-9]|4[245]|6[1-5]|7[1-4]|81)|5(?:[0159]\\\\d|2[1-5]|3[2-6]|4[1-79]|6[4-6]|7[1-578]|8[3-8])|6(?:[0-57-9]\\\\d|6[0-8])|7(?:[0159]\\\\d|2[12]|3[1-7]|4[2-46]|6[13569]|7[13-6]|8[1-59])|8(?:[0159]\\\\d|2[3-578]|3[1-356]|[6-8][1-5])|9(?:[0159]\\\\d|[238][1-5]|4[12]|6[1-8]|7[1-6]))\\\\d{2,7}\", [6, 7, 8, 9, 10, 11]], [\"3[2-9]\\\\d{7,8}|(?:31|43)\\\\d{8}\", [9, 10]], [\"80(?:0\\\\d{3}|3)\\\\d{3}\", [6, 9]], [\"(?:0878\\\\d{3}|89(?:2\\\\d|3[04]|4(?:[0-4]|[5-9]\\\\d\\\\d)|5[0-4]))\\\\d\\\\d|(?:1(?:44|6[346])|89(?:38|5[5-9]|9))\\\\d{6}\", [6, 8, 9, 10]], [\"1(?:78\\\\d|99)\\\\d{6}\", [9, 10]], [\"3[2-8]\\\\d{9,10}\", [11, 12]], 0, 0, [\"55\\\\d{8}\", [10]], [\"84(?:[08]\\\\d{3}|[17])\\\\d{3}\", [6, 9]]]],\n    \"JE\": [\"44\", \"00\", \"1534\\\\d{6}|(?:[3578]\\\\d|90)\\\\d{8}\", [10], 0, \"0\", 0, \"([0-24-8]\\\\d{5})$|0\", \"1534$1\", 0, 0, [[\"1534[0-24-8]\\\\d{5}\"], [\"7(?:(?:(?:50|82)9|937)\\\\d|7(?:00[378]|97\\\\d))\\\\d{5}\"], [\"80(?:07(?:35|81)|8901)\\\\d{4}\"], [\"(?:8(?:4(?:4(?:4(?:05|42|69)|703)|5(?:041|800))|7(?:0002|1206))|90(?:066[59]|1810|71(?:07|55)))\\\\d{4}\"], [\"701511\\\\d{4}\"], 0, [\"(?:3(?:0(?:07(?:35|81)|8901)|3\\\\d{4}|4(?:4(?:4(?:05|42|69)|703)|5(?:041|800))|7(?:0002|1206))|55\\\\d{4})\\\\d{4}\"], [\"76(?:464|652)\\\\d{5}|76(?:0[0-28]|2[356]|34|4[01347]|5[49]|6[0-369]|77|8[14]|9[139])\\\\d{6}\"], [\"56\\\\d{8}\"]]],\n    \"JM\": [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|658|900)\\\\d{7}\", [10], 0, \"1\", 0, 0, 0, 0, \"658|876\"],\n    \"JO\": [\"962\", \"00\", \"(?:(?:[2689]|7\\\\d)\\\\d|32|53)\\\\d{6}\", [8, 9], [[\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[2356]|87\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{5,6})\", \"$1 $2\", [\"[89]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{7})\", \"$1 $2\", [\"70\"], \"0$1\"], [\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"7\"], \"0$1\"]], \"0\"],\n    \"JP\": [\"81\", \"010\", \"00[1-9]\\\\d{6,14}|[257-9]\\\\d{9}|(?:00|[1-9]\\\\d\\\\d)\\\\d{6}\", [8, 9, 10, 11, 12, 13, 14, 15, 16, 17], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1-$2-$3\", [\"(?:12|57|99)0\"], \"0$1\"], [\"(\\\\d{4})(\\\\d)(\\\\d{4})\", \"$1-$2-$3\", [\"1(?:26|3[79]|4[56]|5[4-68]|6[3-5])|499|5(?:76|97)|746|8(?:3[89]|47|51)|9(?:80|9[16])\", \"1(?:267|3(?:7[247]|9[278])|466|5(?:47|58|64)|6(?:3[245]|48|5[4-68]))|499[2468]|5(?:76|97)9|7468|8(?:3(?:8[7-9]|96)|477|51[2-9])|9(?:802|9(?:1[23]|69))|1(?:45|58)[67]\", \"1(?:267|3(?:7[247]|9[278])|466|5(?:47|58|64)|6(?:3[245]|48|5[4-68]))|499[2468]|5(?:769|979[2-69])|7468|8(?:3(?:8[7-9]|96[2457-9])|477|51[2-9])|9(?:802|9(?:1[23]|69))|1(?:45|58)[67]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1-$2-$3\", [\"60\"], \"0$1\"], [\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1-$2-$3\", [\"[36]|4(?:2[09]|7[01])\", \"[36]|4(?:2(?:0|9[02-69])|7(?:0[019]|1))\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1-$2-$3\", [\"1(?:1|5[45]|77|88|9[69])|2(?:2[1-37]|3[0-269]|4[59]|5|6[24]|7[1-358]|8[1369]|9[0-38])|4(?:[28][1-9]|3[0-57]|[45]|6[248]|7[2-579]|9[29])|5(?:2|3[0459]|4[0-369]|5[29]|8[02389]|9[0-389])|7(?:2[02-46-9]|34|[58]|6[0249]|7[57]|9[2-6])|8(?:2[124589]|3[26-9]|49|51|6|7[0-468]|8[68]|9[019])|9(?:[23][1-9]|4[15]|5[138]|6[1-3]|7[156]|8[189]|9[1-489])\", \"1(?:1|5(?:4[018]|5[017])|77|88|9[69])|2(?:2(?:[127]|3[014-9])|3[0-269]|4[59]|5(?:[1-3]|5[0-69]|9[19])|62|7(?:[1-35]|8[0189])|8(?:[16]|3[0134]|9[0-5])|9(?:[028]|17))|4(?:2(?:[13-79]|8[014-6])|3[0-57]|[45]|6[248]|7[2-47]|8[1-9]|9[29])|5(?:2|3(?:[045]|9[0-8])|4[0-369]|5[29]|8[02389]|9[0-3])|7(?:2[02-46-9]|34|[58]|6[0249]|7[57]|9(?:[23]|4[0-59]|5[01569]|6[0167]))|8(?:2(?:[1258]|4[0-39]|9[0-2469])|3(?:[29]|60)|49|51|6(?:[0-24]|36|5[0-3589]|7[23]|9[01459])|7[0-468]|8[68])|9(?:[23][1-9]|4[15]|5[138]|6[1-3]|7[156]|8[189]|9(?:[1289]|3[34]|4[0178]))|(?:264|837)[016-9]|2(?:57|93)[015-9]|(?:25[0468]|422|838)[01]|(?:47[59]|59[89]|8(?:6[68]|9))[019]\", \"1(?:1|5(?:4[018]|5[017])|77|88|9[69])|2(?:2[127]|3[0-269]|4[59]|5(?:[1-3]|5[0-69]|9(?:17|99))|6(?:2|4[016-9])|7(?:[1-35]|8[0189])|8(?:[16]|3[0134]|9[0-5])|9(?:[028]|17))|4(?:2(?:[13-79]|8[014-6])|3[0-57]|[45]|6[248]|7[2-47]|9[29])|5(?:2|3(?:[045]|9(?:[0-58]|6[4-9]|7[0-35689]))|4[0-369]|5[29]|8[02389]|9[0-3])|7(?:2[02-46-9]|34|[58]|6[0249]|7[57]|9(?:[23]|4[0-59]|5[01569]|6[0167]))|8(?:2(?:[1258]|4[0-39]|9[0169])|3(?:[29]|60|7(?:[017-9]|6[6-8]))|49|51|6(?:[0-24]|36[2-57-9]|5(?:[0-389]|5[23])|6(?:[01]|9[178])|7(?:2[2-468]|3[78])|9[0145])|7[0-468]|8[68])|9(?:4[15]|5[138]|7[156]|8[189]|9(?:[1289]|3(?:31|4[357])|4[0178]))|(?:8294|96)[1-3]|2(?:57|93)[015-9]|(?:223|8699)[014-9]|(?:25[0468]|422|838)[01]|(?:48|8292|9[23])[1-9]|(?:47[59]|59[89]|8(?:68|9))[019]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{4})\", \"$1-$2-$3\", [\"[14]|[289][2-9]|5[3-9]|7[2-4679]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1-$2-$3\", [\"800\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1-$2-$3\", [\"[257-9]\"], \"0$1\"]], \"0\", 0, \"(000[259]\\\\d{6})$|(?:(?:003768)0?)|0\", \"$1\"],\n    \"KE\": [\"254\", \"000\", \"(?:[17]\\\\d\\\\d|900)\\\\d{6}|(?:2|80)0\\\\d{6,7}|[4-6]\\\\d{6,8}\", [7, 8, 9, 10], [[\"(\\\\d{2})(\\\\d{5,7})\", \"$1 $2\", [\"[24-6]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{6})\", \"$1 $2\", [\"[17]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[89]\"], \"0$1\"]], \"0\"],\n    \"KG\": [\"996\", \"00\", \"8\\\\d{9}|[235-9]\\\\d{8}\", [9, 10], [[\"(\\\\d{4})(\\\\d{5})\", \"$1 $2\", [\"3(?:1[346]|[24-79])\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[235-79]|88\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d)(\\\\d{2,3})\", \"$1 $2 $3 $4\", [\"8\"], \"0$1\"]], \"0\"],\n    \"KH\": [\"855\", \"00[14-9]\", \"1\\\\d{9}|[1-9]\\\\d{7,8}\", [8, 9, 10], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[1-9]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"1\"]]], \"0\"],\n    \"KI\": [\"686\", \"00\", \"(?:[37]\\\\d|6[0-79])\\\\d{6}|(?:[2-48]\\\\d|50)\\\\d{3}\", [5, 8], 0, \"0\"],\n    \"KM\": [\"269\", \"00\", \"[3478]\\\\d{6}\", [7], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3\", [\"[3478]\"]]]],\n    \"KN\": [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-7]\\\\d{6})$|1\", \"869$1\", 0, \"869\"],\n    \"KP\": [\"850\", \"00|99\", \"85\\\\d{6}|(?:19\\\\d|[2-7])\\\\d{7}\", [8, 10], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"8\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[2-7]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"], \"0$1\"]], \"0\"],\n    \"KR\": [\"82\", \"00(?:[125689]|3(?:[46]5|91)|7(?:00|27|3|55|6[126]))\", \"00[1-9]\\\\d{8,11}|(?:[12]|5\\\\d{3})\\\\d{7}|[13-6]\\\\d{9}|(?:[1-6]\\\\d|80)\\\\d{7}|[3-6]\\\\d{4,5}|(?:00|7)0\\\\d{8}\", [5, 6, 8, 9, 10, 11, 12, 13, 14], [[\"(\\\\d{2})(\\\\d{3,4})\", \"$1-$2\", [\"(?:3[1-3]|[46][1-4]|5[1-5])1\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{4})\", \"$1-$2\", [\"1\"]], [\"(\\\\d)(\\\\d{3,4})(\\\\d{4})\", \"$1-$2-$3\", [\"2\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1-$2-$3\", [\"[36]0|8\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3,4})(\\\\d{4})\", \"$1-$2-$3\", [\"[1346]|5[1-5]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1-$2-$3\", [\"[57]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{5})(\\\\d{4})\", \"$1-$2-$3\", [\"5\"], \"0$1\"]], \"0\", 0, \"0(8(?:[1-46-8]|5\\\\d\\\\d))?\"],\n    \"KW\": [\"965\", \"00\", \"18\\\\d{5}|(?:[2569]\\\\d|41)\\\\d{6}\", [7, 8], [[\"(\\\\d{4})(\\\\d{3,4})\", \"$1 $2\", [\"[169]|2(?:[235]|4[1-35-9])|52\"]], [\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"[245]\"]]]],\n    \"KY\": [\"1\", \"011\", \"(?:345|[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-9]\\\\d{6})$|1\", \"345$1\", 0, \"345\"],\n    \"KZ\": [\"7\", \"810\", \"(?:33622|8\\\\d{8})\\\\d{5}|[78]\\\\d{9}\", [10, 14], 0, \"8\", 0, 0, 0, 0, \"33622|7\", 0, \"8~10\"],\n    \"LA\": [\"856\", \"00\", \"[23]\\\\d{9}|3\\\\d{8}|(?:[235-8]\\\\d|41)\\\\d{6}\", [8, 9, 10], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"2[13]|3[14]|[4-8]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"3\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"[23]\"], \"0$1\"]], \"0\"],\n    \"LB\": [\"961\", \"00\", \"[27-9]\\\\d{7}|[13-9]\\\\d{6}\", [7, 8], [[\"(\\\\d)(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[13-69]|7(?:[2-57]|62|8[0-6]|9[04-9])|8[02-9]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[27-9]\"]]], \"0\"],\n    \"LC\": [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|758|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-8]\\\\d{6})$|1\", \"758$1\", 0, \"758\"],\n    \"LI\": [\"423\", \"00\", \"[68]\\\\d{8}|(?:[2378]\\\\d|90)\\\\d{5}\", [7, 9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3\", [\"[2379]|8(?:0[09]|7)\", \"[2379]|8(?:0(?:02|9)|7)\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"8\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"69\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"6\"]]], \"0\", 0, \"(1001)|0\"],\n    \"LK\": [\"94\", \"00\", \"[1-9]\\\\d{8}\", [9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"7\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[1-689]\"], \"0$1\"]], \"0\"],\n    \"LR\": [\"231\", \"00\", \"(?:[2457]\\\\d|33|88)\\\\d{7}|(?:2\\\\d|[4-6])\\\\d{6}\", [7, 8, 9], [[\"(\\\\d)(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"4[67]|[56]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"2\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[2-578]\"], \"0$1\"]], \"0\"],\n    \"LS\": [\"266\", \"00\", \"(?:[256]\\\\d\\\\d|800)\\\\d{5}\", [8], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[2568]\"]]]],\n    \"LT\": [\"370\", \"00\", \"(?:[3469]\\\\d|52|[78]0)\\\\d{6}\", [8], [[\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"52[0-7]\"], \"(0-$1)\", 1], [\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"[7-9]\"], \"0 $1\", 1], [\"(\\\\d{2})(\\\\d{6})\", \"$1 $2\", [\"37|4(?:[15]|6[1-8])\"], \"(0-$1)\", 1], [\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"[3-6]\"], \"(0-$1)\", 1]], \"0\", 0, \"[08]\"],\n    \"LU\": [\"352\", \"00\", \"35[013-9]\\\\d{4,8}|6\\\\d{8}|35\\\\d{2,4}|(?:[2457-9]\\\\d|3[0-46-9])\\\\d{2,9}\", [4, 5, 6, 7, 8, 9, 10, 11], [[\"(\\\\d{2})(\\\\d{3})\", \"$1 $2\", [\"2(?:0[2-689]|[2-9])|[3-57]|8(?:0[2-9]|[13-9])|9(?:0[89]|[2-579])\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3\", [\"2(?:0[2-689]|[2-9])|[3-57]|8(?:0[2-9]|[13-9])|9(?:0[89]|[2-579])\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"20[2-689]\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{1,2})\", \"$1 $2 $3 $4\", [\"2(?:[0367]|4[3-8])\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"80[01]|90[015]\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"20\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"6\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{1,2})\", \"$1 $2 $3 $4 $5\", [\"2(?:[0367]|4[3-8])\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{1,5})\", \"$1 $2 $3 $4\", [\"[3-57]|8[13-9]|9(?:0[89]|[2-579])|(?:2|80)[2-9]\"]]], 0, 0, \"(15(?:0[06]|1[12]|[35]5|4[04]|6[26]|77|88|99)\\\\d)\"],\n    \"LV\": [\"371\", \"00\", \"(?:[268]\\\\d|90)\\\\d{6}\", [8], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[269]|8[01]\"]]]],\n    \"LY\": [\"218\", \"00\", \"[2-9]\\\\d{8}\", [9], [[\"(\\\\d{2})(\\\\d{7})\", \"$1-$2\", [\"[2-9]\"], \"0$1\"]], \"0\"],\n    \"MA\": [\"212\", \"00\", \"[5-8]\\\\d{8}\", [9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"5[45]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{5})\", \"$1-$2\", [\"5(?:2[2-46-9]|3[3-9]|9)|8(?:0[89]|92)\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{7})\", \"$1-$2\", [\"8\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{6})\", \"$1-$2\", [\"[5-7]\"], \"0$1\"]], \"0\", 0, 0, 0, 0, 0, [[\"5(?:2(?:[0-25-79]\\\\d|3[1-578]|4[02-46-8]|8[0235-7])|3(?:[0-47]\\\\d|5[02-9]|6[02-8]|8[014-9]|9[3-9])|(?:4[067]|5[03])\\\\d)\\\\d{5}\"], [\"(?:6(?:[0-79]\\\\d|8[0-247-9])|7(?:[0167]\\\\d|2[0-8]|5[0-3]|8[0-7]))\\\\d{6}\"], [\"80[0-7]\\\\d{6}\"], [\"89\\\\d{7}\"], 0, 0, 0, 0, [\"(?:592(?:4[0-2]|93)|80[89]\\\\d\\\\d)\\\\d{4}\"]]],\n    \"MC\": [\"377\", \"00\", \"(?:[3489]|6\\\\d)\\\\d{7}\", [8, 9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"4\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[389]\"]], [\"(\\\\d)(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4 $5\", [\"6\"], \"0$1\"]], \"0\"],\n    \"MD\": [\"373\", \"00\", \"(?:[235-7]\\\\d|[89]0)\\\\d{6}\", [8], [[\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"[89]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"22|3\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"[25-7]\"], \"0$1\"]], \"0\"],\n    \"ME\": [\"382\", \"00\", \"(?:20|[3-79]\\\\d)\\\\d{6}|80\\\\d{6,7}\", [8, 9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[2-9]\"], \"0$1\"]], \"0\"],\n    \"MF\": [\"590\", \"00\", \"(?:590\\\\d|7090)\\\\d{5}|(?:69|80|9\\\\d)\\\\d{7}\", [9], 0, \"0\", 0, 0, 0, 0, 0, [[\"590(?:0[079]|[14]3|[27][79]|3[03-7]|5[0-268]|87)\\\\d{4}\"], [\"(?:69(?:0\\\\d\\\\d|1(?:2[2-9]|3[0-5])|4(?:0[89]|1[2-6]|9\\\\d)|6(?:1[016-9]|5[0-4]|[67]\\\\d))|7090[0-4])\\\\d{4}\"], [\"80[0-5]\\\\d{6}\"], 0, 0, 0, 0, 0, [\"9(?:(?:39[5-7]|76[018])\\\\d|475[0-6])\\\\d{4}\"]]],\n    \"MG\": [\"261\", \"00\", \"[23]\\\\d{8}\", [9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[23]\"], \"0$1\"]], \"0\", 0, \"([24-9]\\\\d{6})$|0\", \"20$1\"],\n    \"MH\": [\"692\", \"011\", \"329\\\\d{4}|(?:[256]\\\\d|45)\\\\d{5}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1-$2\", [\"[2-6]\"]]], \"1\"],\n    \"MK\": [\"389\", \"00\", \"[2-578]\\\\d{7}\", [8], [[\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"2|34[47]|4(?:[37]7|5[47]|64)\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[347]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d)(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[58]\"], \"0$1\"]], \"0\"],\n    \"ML\": [\"223\", \"00\", \"[24-9]\\\\d{7}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[24-9]\"]]]],\n    \"MM\": [\"95\", \"00\", \"1\\\\d{5,7}|95\\\\d{6}|(?:[4-7]|9[0-46-9])\\\\d{6,8}|(?:2|8\\\\d)\\\\d{5,8}\", [6, 7, 8, 9, 10], [[\"(\\\\d)(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"16|2\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"4(?:[2-46]|5[3-5])|5|6(?:[1-689]|7[235-7])|7(?:[0-4]|5[2-7])|8[1-5]|(?:60|86)[23]\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[12]|452|678|86\", \"[12]|452|6788|86\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[4-7]|8[1-35]\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{4,6})\", \"$1 $2 $3\", [\"9(?:2[0-4]|[35-9]|4[137-9])\"], \"0$1\"], [\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"2\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"8\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"92\"], \"0$1\"], [\"(\\\\d)(\\\\d{5})(\\\\d{4})\", \"$1 $2 $3\", [\"9\"], \"0$1\"]], \"0\"],\n    \"MN\": [\"976\", \"001\", \"[12]\\\\d{7,9}|[5-9]\\\\d{7}\", [8, 9, 10], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\", \"$1 $2 $3\", [\"[12]1\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[5-9]\"]], [\"(\\\\d{3})(\\\\d{5,6})\", \"$1 $2\", [\"[12]2[1-3]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{5,6})\", \"$1 $2\", [\"[12](?:27|3[2-8]|4[2-68]|5[1-4689])\", \"[12](?:27|3[2-8]|4[2-68]|5[1-4689])[0-3]\"], \"0$1\"], [\"(\\\\d{5})(\\\\d{4,5})\", \"$1 $2\", [\"[12]\"], \"0$1\"]], \"0\"],\n    \"MO\": [\"853\", \"00\", \"0800\\\\d{3}|(?:28|[68]\\\\d)\\\\d{6}\", [7, 8], [[\"(\\\\d{4})(\\\\d{3})\", \"$1 $2\", [\"0\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[268]\"]]]],\n    \"MP\": [\"1\", \"011\", \"[58]\\\\d{9}|(?:67|90)0\\\\d{7}\", [10], 0, \"1\", 0, \"([2-9]\\\\d{6})$|1\", \"670$1\", 0, \"670\"],\n    \"MQ\": [\"596\", \"00\", \"(?:596\\\\d|7091)\\\\d{5}|(?:69|[89]\\\\d)\\\\d{7}\", [9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[5-79]|8(?:0[6-9]|[36])\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"8\"], \"0$1\"]], \"0\"],\n    \"MR\": [\"222\", \"00\", \"(?:[2-4]\\\\d\\\\d|800)\\\\d{5}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[2-48]\"]]]],\n    \"MS\": [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|664|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([34]\\\\d{6})$|1\", \"664$1\", 0, \"664\"],\n    \"MT\": [\"356\", \"00\", \"3550\\\\d{4}|(?:[2579]\\\\d\\\\d|800)\\\\d{5}\", [8], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[2357-9]\"]]]],\n    \"MU\": [\"230\", \"0(?:0|[24-7]0|3[03])\", \"(?:[57]|8\\\\d\\\\d)\\\\d{7}|[2-468]\\\\d{6}\", [7, 8, 10], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[2-46]|8[013]\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[57]\"]], [\"(\\\\d{5})(\\\\d{5})\", \"$1 $2\", [\"8\"]]], 0, 0, 0, 0, 0, 0, 0, \"020\"],\n    \"MV\": [\"960\", \"0(?:0|19)\", \"(?:800|9[0-57-9]\\\\d)\\\\d{7}|[34679]\\\\d{6}\", [7, 10], [[\"(\\\\d{3})(\\\\d{4})\", \"$1-$2\", [\"[34679]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[89]\"]]], 0, 0, 0, 0, 0, 0, 0, \"00\"],\n    \"MW\": [\"265\", \"00\", \"(?:[1289]\\\\d|31|77)\\\\d{7}|1\\\\d{6}\", [7, 9], [[\"(\\\\d)(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"1[2-9]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"2\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[137-9]\"], \"0$1\"]], \"0\"],\n    \"MX\": [\"52\", \"0[09]\", \"[2-9]\\\\d{9}\", [10], [[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"33|5[56]|81\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[2-9]\"]]], 0, 0, 0, 0, 0, 0, 0, \"00\"],\n    \"MY\": [\"60\", \"00\", \"1\\\\d{8,9}|(?:3\\\\d|[4-9])\\\\d{7}\", [8, 9, 10], [[\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1-$2 $3\", [\"[4-79]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1-$2 $3\", [\"1(?:[02469]|[378][1-9]|53)|8\", \"1(?:[02469]|[37][1-9]|53|8(?:[1-46-9]|5[7-9]))|8\"], \"0$1\"], [\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1-$2 $3\", [\"3\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{2})(\\\\d{4})\", \"$1-$2-$3-$4\", [\"1(?:[367]|80)\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1-$2 $3\", [\"15\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1-$2 $3\", [\"1\"], \"0$1\"]], \"0\"],\n    \"MZ\": [\"258\", \"00\", \"(?:2|8\\\\d)\\\\d{7}\", [8, 9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"2|8[2-79]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"8\"]]]],\n    \"NA\": [\"264\", \"00\", \"[68]\\\\d{7,8}\", [8, 9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"88\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"6\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"87\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"8\"], \"0$1\"]], \"0\"],\n    \"NC\": [\"687\", \"00\", \"(?:050|[2-57-9]\\\\d\\\\d)\\\\d{3}\", [6], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1.$2.$3\", [\"[02-57-9]\"]]]],\n    \"NE\": [\"227\", \"00\", \"[027-9]\\\\d{7}\", [8], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"08\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[089]|2[013]|7[0467]\"]]]],\n    \"NF\": [\"672\", \"00\", \"[13]\\\\d{5}\", [6], [[\"(\\\\d{2})(\\\\d{4})\", \"$1 $2\", [\"1[0-3]\"]], [\"(\\\\d)(\\\\d{5})\", \"$1 $2\", [\"[13]\"]]], 0, 0, \"([0-258]\\\\d{4})$\", \"3$1\"],\n    \"NG\": [\"234\", \"009\", \"(?:20|9\\\\d)\\\\d{8}|[78]\\\\d{9,13}\", [10, 11, 12, 13, 14], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[7-9]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"20[129]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{2})(\\\\d{4})\", \"$1 $2 $3\", [\"2\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{4})(\\\\d{4,5})\", \"$1 $2 $3\", [\"[78]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{5})(\\\\d{5,6})\", \"$1 $2 $3\", [\"[78]\"], \"0$1\"]], \"0\"],\n    \"NI\": [\"505\", \"00\", \"(?:1800|[25-8]\\\\d{3})\\\\d{4}\", [8], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[125-8]\"]]]],\n    \"NL\": [\"31\", \"00\", \"(?:[124-7]\\\\d\\\\d|3(?:[02-9]\\\\d|1[0-8]))\\\\d{6}|8\\\\d{6,9}|9\\\\d{6,10}|1\\\\d{4,5}\", [5, 6, 7, 8, 9, 10, 11], [[\"(\\\\d{3})(\\\\d{4,7})\", \"$1 $2\", [\"[89]0\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{7})\", \"$1 $2\", [\"66\"], \"0$1\"], [\"(\\\\d)(\\\\d{8})\", \"$1 $2\", [\"6\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"1[16-8]|2[259]|3[124]|4[17-9]|5[124679]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[1-578]|91\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{5})\", \"$1 $2 $3\", [\"9\"], \"0$1\"]], \"0\"],\n    \"NO\": [\"47\", \"00\", \"(?:0|[2-9]\\\\d{3})\\\\d{4}\", [5, 8], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"8\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[2-79]\"]]], 0, 0, 0, 0, 0, \"[02-689]|7[0-8]\"],\n    \"NP\": [\"977\", \"00\", \"(?:1\\\\d|9)\\\\d{9}|[1-9]\\\\d{7}\", [8, 10, 11], [[\"(\\\\d)(\\\\d{7})\", \"$1-$2\", [\"1[2-6]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{6})\", \"$1-$2\", [\"1[01]|[2-8]|9(?:[1-59]|[67][2-6])\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{7})\", \"$1-$2\", [\"9\"]]], \"0\"],\n    \"NR\": [\"674\", \"00\", \"(?:222|444|(?:55|8\\\\d)\\\\d|666|777|999)\\\\d{4}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[24-9]\"]]]],\n    \"NU\": [\"683\", \"00\", \"(?:[4-7]|888\\\\d)\\\\d{3}\", [4, 7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"8\"]]]],\n    \"NZ\": [\"64\", \"0(?:0|161)\", \"[1289]\\\\d{9}|50\\\\d{5}(?:\\\\d{2,3})?|[27-9]\\\\d{7,8}|(?:[34]\\\\d|6[0-35-9])\\\\d{6}|8\\\\d{4,6}\", [5, 6, 7, 8, 9, 10], [[\"(\\\\d{2})(\\\\d{3,8})\", \"$1 $2\", [\"8[1-79]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2,3})\", \"$1 $2 $3\", [\"50[036-8]|8|90\", \"50(?:[0367]|88)|8|90\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"24|[346]|7[2-57-9]|9[2-9]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"2(?:10|74)|[589]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3,4})(\\\\d{4})\", \"$1 $2 $3\", [\"1|2[028]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,5})\", \"$1 $2 $3\", [\"2(?:[169]|7[0-35-9])|7\"], \"0$1\"]], \"0\", 0, 0, 0, 0, 0, 0, \"00\"],\n    \"OM\": [\"968\", \"00\", \"(?:1505|[279]\\\\d{3}|500)\\\\d{4}|800\\\\d{5,6}\", [7, 8, 9], [[\"(\\\\d{3})(\\\\d{4,6})\", \"$1 $2\", [\"[58]\"]], [\"(\\\\d{2})(\\\\d{6})\", \"$1 $2\", [\"2\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[179]\"]]]],\n    \"PA\": [\"507\", \"00\", \"(?:00800|8\\\\d{3})\\\\d{6}|[68]\\\\d{7}|[1-57-9]\\\\d{6}\", [7, 8, 10, 11], [[\"(\\\\d{3})(\\\\d{4})\", \"$1-$2\", [\"[1-57-9]\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1-$2\", [\"[68]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"8\"]]]],\n    \"PE\": [\"51\", \"00|19(?:1[124]|77|90)00\", \"(?:[14-8]|9\\\\d)\\\\d{7}\", [8, 9], [[\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"80\"], \"(0$1)\"], [\"(\\\\d)(\\\\d{7})\", \"$1 $2\", [\"1\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{6})\", \"$1 $2\", [\"[4-8]\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"9\"]]], \"0\", 0, 0, 0, 0, 0, 0, \"00\", \" Anexo \"],\n    \"PF\": [\"689\", \"00\", \"4\\\\d{5}(?:\\\\d{2})?|8\\\\d{7,8}\", [6, 8, 9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3\", [\"44\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"4|8[7-9]\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"8\"]]]],\n    \"PG\": [\"675\", \"00|140[1-3]\", \"(?:180|[78]\\\\d{3})\\\\d{4}|(?:[2-589]\\\\d|64)\\\\d{5}\", [7, 8], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"18|[2-69]|85\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[78]\"]]], 0, 0, 0, 0, 0, 0, 0, \"00\"],\n    \"PH\": [\"63\", \"00\", \"(?:[2-7]|9\\\\d)\\\\d{8}|2\\\\d{5}|(?:1800|8)\\\\d{7,9}\", [6, 8, 9, 10, 11, 12, 13], [[\"(\\\\d)(\\\\d{5})\", \"$1 $2\", [\"2\"], \"(0$1)\"], [\"(\\\\d{4})(\\\\d{4,6})\", \"$1 $2\", [\"3(?:23|39|46)|4(?:2[3-6]|[35]9|4[26]|76)|544|88[245]|(?:52|64|86)2\", \"3(?:230|397|461)|4(?:2(?:35|[46]4|51)|396|4(?:22|63)|59[347]|76[15])|5(?:221|446)|642[23]|8(?:622|8(?:[24]2|5[13]))\"], \"(0$1)\"], [\"(\\\\d{5})(\\\\d{4})\", \"$1 $2\", [\"346|4(?:27|9[35])|883\", \"3469|4(?:279|9(?:30|56))|8834\"], \"(0$1)\"], [\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"2\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[3-7]|8[2-8]\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[89]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"]], [\"(\\\\d{4})(\\\\d{1,2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3 $4\", [\"1\"]]], \"0\"],\n    \"PK\": [\"92\", \"00\", \"122\\\\d{6}|[24-8]\\\\d{10,11}|9(?:[013-9]\\\\d{8,10}|2(?:[01]\\\\d\\\\d|2(?:[06-8]\\\\d|1[01]))\\\\d{7})|(?:[2-8]\\\\d{3}|92(?:[0-7]\\\\d|8[1-9]))\\\\d{6}|[24-9]\\\\d{8}|[89]\\\\d{7}\", [8, 9, 10, 11, 12], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{2,7})\", \"$1 $2 $3\", [\"[89]0\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{5})\", \"$1 $2\", [\"1\"]], [\"(\\\\d{3})(\\\\d{6,7})\", \"$1 $2\", [\"2(?:3[2358]|4[2-4]|9[2-8])|45[3479]|54[2-467]|60[468]|72[236]|8(?:2[2-689]|3[23578]|4[3478]|5[2356])|9(?:2[2-8]|3[27-9]|4[2-6]|6[3569]|9[25-8])\", \"9(?:2[3-8]|98)|(?:2(?:3[2358]|4[2-4]|9[2-8])|45[3479]|54[2-467]|60[468]|72[236]|8(?:2[2-689]|3[23578]|4[3478]|5[2356])|9(?:22|3[27-9]|4[2-6]|6[3569]|9[25-7]))[2-9]\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{7,8})\", \"$1 $2\", [\"(?:2[125]|4[0-246-9]|5[1-35-7]|6[1-8]|7[14]|8[16]|91)[2-9]\"], \"(0$1)\"], [\"(\\\\d{5})(\\\\d{5})\", \"$1 $2\", [\"58\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{7})\", \"$1 $2\", [\"3\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"2[125]|4[0-246-9]|5[1-35-7]|6[1-8]|7[14]|8[16]|91\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"[24-9]\"], \"(0$1)\"]], \"0\"],\n    \"PL\": [\"48\", \"00\", \"(?:6|8\\\\d\\\\d)\\\\d{7}|[1-9]\\\\d{6}(?:\\\\d{2})?|[26]\\\\d{5}\", [6, 7, 8, 9, 10], [[\"(\\\\d{5})\", \"$1\", [\"19\"]], [\"(\\\\d{3})(\\\\d{3})\", \"$1 $2\", [\"11|20|64\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\", \"$1 $2 $3\", [\"(?:1[2-8]|2[2-69]|3[2-4]|4[1-468]|5[24-689]|6[1-3578]|7[14-7]|8[1-79]|9[145])1\", \"(?:1[2-8]|2[2-69]|3[2-4]|4[1-468]|5[24-689]|6[1-3578]|7[14-7]|8[1-79]|9[145])19\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2,3})\", \"$1 $2 $3\", [\"64\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"21|39|45|5[0137]|6[0469]|7[02389]|8(?:0[14]|8)\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"1[2-8]|[2-7]|8[1-79]|9[145]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"8\"]]]],\n    \"PM\": [\"508\", \"00\", \"[45]\\\\d{5}|(?:708|8\\\\d\\\\d)\\\\d{6}\", [6, 9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3\", [\"[45]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"7\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"8\"], \"0$1\"]], \"0\"],\n    \"PR\": [\"1\", \"011\", \"(?:[589]\\\\d\\\\d|787)\\\\d{7}\", [10], 0, \"1\", 0, 0, 0, 0, \"787|939\"],\n    \"PS\": [\"970\", \"00\", \"[2489]2\\\\d{6}|(?:1\\\\d|5)\\\\d{8}\", [8, 9, 10], [[\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[2489]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"5\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"1\"]]], \"0\"],\n    \"PT\": [\"351\", \"00\", \"1693\\\\d{5}|(?:[26-9]\\\\d|30)\\\\d{7}\", [9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"2[12]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"16|[236-9]\"]]]],\n    \"PW\": [\"680\", \"01[12]\", \"(?:[24-8]\\\\d\\\\d|345|900)\\\\d{4}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[2-9]\"]]]],\n    \"PY\": [\"595\", \"00\", \"59\\\\d{4,6}|9\\\\d{5,10}|(?:[2-46-8]\\\\d|5[0-8])\\\\d{4,7}\", [6, 7, 8, 9, 10, 11], [[\"(\\\\d{3})(\\\\d{3,6})\", \"$1 $2\", [\"[2-9]0\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{5})\", \"$1 $2\", [\"[26]1|3[289]|4[1246-8]|7[1-3]|8[1-36]\"], \"(0$1)\"], [\"(\\\\d{3})(\\\\d{4,5})\", \"$1 $2\", [\"2[279]|3[13-5]|4[359]|5|6(?:[34]|7[1-46-8])|7[46-8]|85\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"2[14-68]|3[26-9]|4[1246-8]|6(?:1|75)|7[1-35]|8[1-36]\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"87\"]], [\"(\\\\d{3})(\\\\d{6})\", \"$1 $2\", [\"9(?:[5-79]|8[1-7])\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[2-8]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"9\"]]], \"0\"],\n    \"QA\": [\"974\", \"00\", \"800\\\\d{4}|(?:2|800)\\\\d{6}|(?:0080|[3-7])\\\\d{7}\", [7, 8, 9, 11], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"2[136]|8\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[3-7]\"]]]],\n    \"RE\": [\"262\", \"00\", \"709\\\\d{6}|(?:26|[689]\\\\d)\\\\d{7}\", [9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[26-9]\"], \"0$1\"]], \"0\", 0, 0, 0, 0, 0, [[\"26(?:2\\\\d\\\\d|3(?:0\\\\d|1[0-6]))\\\\d{4}\"], [\"(?:69(?:2\\\\d\\\\d|3(?:[06][0-6]|1[0-3]|2[0-2]|3[0-39]|4\\\\d|5[0-5]|7[0-37]|8[0-8]|9[0-479]))|7092[0-3])\\\\d{4}\"], [\"80\\\\d{7}\"], [\"89[1-37-9]\\\\d{6}\"], 0, 0, 0, 0, [\"9(?:399[0-3]|479[0-6]|76(?:2[278]|3[0-37]))\\\\d{4}\"], [\"8(?:1[019]|2[0156]|84|90)\\\\d{6}\"]]],\n    \"RO\": [\"40\", \"00\", \"(?:[236-8]\\\\d|90)\\\\d{7}|[23]\\\\d{5}\", [6, 9], [[\"(\\\\d{3})(\\\\d{3})\", \"$1 $2\", [\"2[3-6]\", \"2[3-6]\\\\d9\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4})\", \"$1 $2\", [\"219|31\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[23]1\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[236-9]\"], \"0$1\"]], \"0\", 0, 0, 0, 0, 0, 0, 0, \" int \"],\n    \"RS\": [\"381\", \"00\", \"38[02-9]\\\\d{6,9}|6\\\\d{7,9}|90\\\\d{4,8}|38\\\\d{5,6}|(?:7\\\\d\\\\d|800)\\\\d{3,9}|(?:[12]\\\\d|3[0-79])\\\\d{5,10}\", [6, 7, 8, 9, 10, 11, 12], [[\"(\\\\d{3})(\\\\d{3,9})\", \"$1 $2\", [\"(?:2[389]|39)0|[7-9]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{5,10})\", \"$1 $2\", [\"[1-36]\"], \"0$1\"]], \"0\"],\n    \"RU\": [\"7\", \"810\", \"8\\\\d{13}|[347-9]\\\\d{9}\", [10, 14], [[\"(\\\\d{4})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"7(?:1[0-8]|2[1-9])\", \"7(?:1(?:[0-356]2|4[29]|7|8[27])|2(?:1[23]|[2-9]2))\", \"7(?:1(?:[0-356]2|4[29]|7|8[27])|2(?:13[03-69]|62[013-9]))|72[1-57-9]2\"], \"8 ($1)\", 1], [\"(\\\\d{5})(\\\\d)(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"7(?:1[0-68]|2[1-9])\", \"7(?:1(?:[06][3-6]|[18]|2[35]|[3-5][3-5])|2(?:[13][3-5]|[24-689]|7[457]))\", \"7(?:1(?:0(?:[356]|4[023])|[18]|2(?:3[013-9]|5)|3[45]|43[013-79]|5(?:3[1-8]|4[1-7]|5)|6(?:3[0-35-9]|[4-6]))|2(?:1(?:3[178]|[45])|[24-689]|3[35]|7[457]))|7(?:14|23)4[0-8]|71(?:33|45)[1-79]\"], \"8 ($1)\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"7\"], \"8 ($1)\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2-$3-$4\", [\"[349]|8(?:[02-7]|1[1-8])\"], \"8 ($1)\", 1], [\"(\\\\d{4})(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"8\"], \"8 ($1)\"]], \"8\", 0, 0, 0, 0, 0, [[\"336(?:[013-9]\\\\d|2[013-9])\\\\d{5}|(?:3(?:0[12]|4[1-35-79]|5[1-3]|65|8[1-58]|9[0145])|4(?:01|1[1356]|2[13467]|7[1-5]|8[1-7]|9[1-689])|8(?:1[1-8]|2[01]|3[13-6]|4[0-8]|5[15-7]|6[0-35-79]|7[1-37-9]))\\\\d{7}\", [10]], [\"9\\\\d{9}\", [10]], [\"8(?:0[04]|108\\\\d{3})\\\\d{7}\"], [\"80[39]\\\\d{7}\", [10]], [\"808\\\\d{7}\", [10]]], \"8~10\"],\n    \"RW\": [\"250\", \"00\", \"(?:06|[27]\\\\d\\\\d|[89]00)\\\\d{6}\", [8, 9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"0\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"2\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[7-9]\"], \"0$1\"]], \"0\"],\n    \"SA\": [\"966\", \"00\", \"(?:[15]\\\\d|800|92)\\\\d{7}\", [9, 10], [[\"(\\\\d{4})(\\\\d{5})\", \"$1 $2\", [\"9\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"5\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"8\"]]], \"0\"],\n    \"SB\": [\"677\", \"0[01]\", \"[6-9]\\\\d{6}|[1-6]\\\\d{4}\", [5, 7], [[\"(\\\\d{2})(\\\\d{5})\", \"$1 $2\", [\"6[89]|7|8[4-9]|9(?:[1-8]|9[0-8])\"]]]],\n    \"SC\": [\"248\", \"010|0[0-2]\", \"(?:[2489]\\\\d|64)\\\\d{5}\", [7], [[\"(\\\\d)(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[246]|9[57]\"]]], 0, 0, 0, 0, 0, 0, 0, \"00\"],\n    \"SD\": [\"249\", \"00\", \"[19]\\\\d{8}\", [9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[19]\"], \"0$1\"]], \"0\"],\n    \"SE\": [\"46\", \"00\", \"(?:[26]\\\\d\\\\d|9)\\\\d{9}|[1-9]\\\\d{8}|[1-689]\\\\d{7}|[1-4689]\\\\d{6}|2\\\\d{5}\", [6, 7, 8, 9, 10, 12], [[\"(\\\\d{2})(\\\\d{2,3})(\\\\d{2})\", \"$1-$2 $3\", [\"20\"], \"0$1\", 0, \"$1 $2 $3\"], [\"(\\\\d{3})(\\\\d{4})\", \"$1-$2\", [\"9(?:00|39|44|9)\"], \"0$1\", 0, \"$1 $2\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})\", \"$1-$2 $3\", [\"[12][136]|3[356]|4[0246]|6[03]|90[1-9]\"], \"0$1\", 0, \"$1 $2 $3\"], [\"(\\\\d)(\\\\d{2,3})(\\\\d{2})(\\\\d{2})\", \"$1-$2 $3 $4\", [\"8\"], \"0$1\", 0, \"$1 $2 $3 $4\"], [\"(\\\\d{3})(\\\\d{2,3})(\\\\d{2})\", \"$1-$2 $3\", [\"1[2457]|2(?:[247-9]|5[0138])|3[0247-9]|4[1357-9]|5[0-35-9]|6(?:[125689]|4[02-57]|7[0-2])|9(?:[125-8]|3[02-5]|4[0-3])\"], \"0$1\", 0, \"$1 $2 $3\"], [\"(\\\\d{3})(\\\\d{2,3})(\\\\d{3})\", \"$1-$2 $3\", [\"9(?:00|39|44)\"], \"0$1\", 0, \"$1 $2 $3\"], [\"(\\\\d{2})(\\\\d{2,3})(\\\\d{2})(\\\\d{2})\", \"$1-$2 $3 $4\", [\"1[13689]|2[0136]|3[1356]|4[0246]|54|6[03]|90[1-9]\"], \"0$1\", 0, \"$1 $2 $3 $4\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1-$2 $3 $4\", [\"10|7\"], \"0$1\", 0, \"$1 $2 $3 $4\"], [\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{2})\", \"$1-$2 $3 $4\", [\"8\"], \"0$1\", 0, \"$1 $2 $3 $4\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1-$2 $3 $4\", [\"[13-5]|2(?:[247-9]|5[0138])|6(?:[124-689]|7[0-2])|9(?:[125-8]|3[02-5]|4[0-3])\"], \"0$1\", 0, \"$1 $2 $3 $4\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{3})\", \"$1-$2 $3 $4\", [\"9\"], \"0$1\", 0, \"$1 $2 $3 $4\"], [\"(\\\\d{3})(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1-$2 $3 $4 $5\", [\"[26]\"], \"0$1\", 0, \"$1 $2 $3 $4 $5\"]], \"0\"],\n    \"SG\": [\"65\", \"0[0-3]\\\\d\", \"(?:(?:1\\\\d|8)\\\\d\\\\d|7000)\\\\d{7}|[3689]\\\\d{7}\", [8, 10, 11], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[369]|8(?:0[1-9]|[1-9])\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"8\"]], [\"(\\\\d{4})(\\\\d{4})(\\\\d{3})\", \"$1 $2 $3\", [\"7\"]], [\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"1\"]]]],\n    \"SH\": [\"290\", \"00\", \"(?:[256]\\\\d|8)\\\\d{3}\", [4, 5], 0, 0, 0, 0, 0, 0, \"[256]\"],\n    \"SI\": [\"386\", \"00|10(?:22|66|88|99)\", \"[1-7]\\\\d{7}|8\\\\d{4,7}|90\\\\d{4,6}\", [5, 6, 7, 8], [[\"(\\\\d{2})(\\\\d{3,6})\", \"$1 $2\", [\"8[09]|9\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"59|8\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[37][01]|4[0139]|51|6\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[1-57]\"], \"(0$1)\"]], \"0\", 0, 0, 0, 0, 0, 0, \"00\"],\n    \"SJ\": [\"47\", \"00\", \"0\\\\d{4}|(?:[489]\\\\d|79)\\\\d{6}\", [5, 8], 0, 0, 0, 0, 0, 0, \"79\"],\n    \"SK\": [\"421\", \"00\", \"[2-689]\\\\d{8}|[2-59]\\\\d{6}|[2-5]\\\\d{5}\", [6, 7, 9], [[\"(\\\\d)(\\\\d{2})(\\\\d{3,4})\", \"$1 $2 $3\", [\"21\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{2})(\\\\d{2,3})\", \"$1 $2 $3\", [\"[3-5][1-8]1\", \"[3-5][1-8]1[67]\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{2})\", \"$1/$2 $3 $4\", [\"2\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[689]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1/$2 $3 $4\", [\"[3-5]\"], \"0$1\"]], \"0\"],\n    \"SL\": [\"232\", \"00\", \"(?:[237-9]\\\\d|66)\\\\d{6}\", [8], [[\"(\\\\d{2})(\\\\d{6})\", \"$1 $2\", [\"[236-9]\"], \"(0$1)\"]], \"0\"],\n    \"SM\": [\"378\", \"00\", \"(?:0549|[5-7]\\\\d)\\\\d{6}\", [8, 10], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[5-7]\"]], [\"(\\\\d{4})(\\\\d{6})\", \"$1 $2\", [\"0\"]]], 0, 0, \"([89]\\\\d{5})$\", \"0549$1\"],\n    \"SN\": [\"221\", \"00\", \"(?:[378]\\\\d|93)\\\\d{7}\", [9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"8\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[379]\"]]]],\n    \"SO\": [\"252\", \"00\", \"[346-9]\\\\d{8}|[12679]\\\\d{7}|[1-5]\\\\d{6}|[1348]\\\\d{5}\", [6, 7, 8, 9], [[\"(\\\\d{2})(\\\\d{4})\", \"$1 $2\", [\"8[125]\"]], [\"(\\\\d{6})\", \"$1\", [\"[134]\"]], [\"(\\\\d)(\\\\d{6})\", \"$1 $2\", [\"[15]|2[0-79]|3[0-46-8]|4[0-7]\"]], [\"(\\\\d)(\\\\d{7})\", \"$1 $2\", [\"(?:2|90)4|[67]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[348]|64|79|90\"]], [\"(\\\\d{2})(\\\\d{5,7})\", \"$1 $2\", [\"1|28|6[0-35-9]|7[67]|9[2-9]\"]]], \"0\"],\n    \"SR\": [\"597\", \"00\", \"(?:[2-5]|68|[78]\\\\d|90)\\\\d{5}\", [6, 7], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1-$2-$3\", [\"56\"]], [\"(\\\\d{3})(\\\\d{3})\", \"$1-$2\", [\"[2-5]\"]], [\"(\\\\d{3})(\\\\d{4})\", \"$1-$2\", [\"[6-9]\"]]]],\n    \"SS\": [\"211\", \"00\", \"[19]\\\\d{8}\", [9], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[19]\"], \"0$1\"]], \"0\"],\n    \"ST\": [\"239\", \"00\", \"(?:22|9\\\\d)\\\\d{5}\", [7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[29]\"]]]],\n    \"SV\": [\"503\", \"00\", \"[267]\\\\d{7}|(?:80\\\\d|900)\\\\d{4}(?:\\\\d{4})?\", [7, 8, 11], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[89]\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[267]\"]], [\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"[89]\"]]]],\n    \"SX\": [\"1\", \"011\", \"7215\\\\d{6}|(?:[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"(5\\\\d{6})$|1\", \"721$1\", 0, \"721\"],\n    \"SY\": [\"963\", \"00\", \"[1-39]\\\\d{8}|[1-5]\\\\d{7}\", [8, 9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[1-5]\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"9\"], \"0$1\", 1]], \"0\"],\n    \"SZ\": [\"268\", \"00\", \"0800\\\\d{4}|(?:[237]\\\\d|900)\\\\d{6}\", [8, 9], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[0237]\"]], [\"(\\\\d{5})(\\\\d{4})\", \"$1 $2\", [\"9\"]]]],\n    \"TA\": [\"290\", \"00\", \"8\\\\d{3}\", [4], 0, 0, 0, 0, 0, 0, \"8\"],\n    \"TC\": [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|649|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-479]\\\\d{6})$|1\", \"649$1\", 0, \"649\"],\n    \"TD\": [\"235\", \"00|16\", \"(?:22|30|[689]\\\\d|77)\\\\d{6}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[236-9]\"]]], 0, 0, 0, 0, 0, 0, 0, \"00\"],\n    \"TG\": [\"228\", \"00\", \"[279]\\\\d{7}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[279]\"]]]],\n    \"TH\": [\"66\", \"00[1-9]\", \"(?:001800|[2-57]|[689]\\\\d)\\\\d{7}|1\\\\d{7,9}\", [8, 9, 10, 13], [[\"(\\\\d)(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"2\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[13-9]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"1\"]]], \"0\"],\n    \"TJ\": [\"992\", \"810\", \"[0-57-9]\\\\d{8}\", [9], [[\"(\\\\d{6})(\\\\d)(\\\\d{2})\", \"$1 $2 $3\", [\"331\", \"3317\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{4})\", \"$1 $2 $3\", [\"44[02-479]|[34]7\"]], [\"(\\\\d{4})(\\\\d)(\\\\d{4})\", \"$1 $2 $3\", [\"3(?:[1245]|3[12])\"]], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[0-57-9]\"]]], 0, 0, 0, 0, 0, 0, 0, \"8~10\"],\n    \"TK\": [\"690\", \"00\", \"[2-47]\\\\d{3,6}\", [4, 5, 6, 7]],\n    \"TL\": [\"670\", \"00\", \"7\\\\d{7}|(?:[2-47]\\\\d|[89]0)\\\\d{5}\", [7, 8], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[2-489]|70\"]], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"7\"]]]],\n    \"TM\": [\"993\", \"810\", \"(?:[1-6]\\\\d|71)\\\\d{6}\", [8], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2-$3-$4\", [\"12\"], \"(8 $1)\"], [\"(\\\\d{3})(\\\\d)(\\\\d{2})(\\\\d{2})\", \"$1 $2-$3-$4\", [\"[1-5]\"], \"(8 $1)\"], [\"(\\\\d{2})(\\\\d{6})\", \"$1 $2\", [\"[67]\"], \"8 $1\"]], \"8\", 0, 0, 0, 0, 0, 0, \"8~10\"],\n    \"TN\": [\"216\", \"00\", \"[2-57-9]\\\\d{7}\", [8], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[2-57-9]\"]]]],\n    \"TO\": [\"676\", \"00\", \"(?:0800|(?:[5-8]\\\\d\\\\d|999)\\\\d)\\\\d{3}|[2-8]\\\\d{4}\", [5, 7], [[\"(\\\\d{2})(\\\\d{3})\", \"$1-$2\", [\"[2-4]|50|6[09]|7[0-24-69]|8[05]\"]], [\"(\\\\d{4})(\\\\d{3})\", \"$1 $2\", [\"0\"]], [\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[5-9]\"]]]],\n    \"TR\": [\"90\", \"00\", \"4\\\\d{6}|8\\\\d{11,12}|(?:[2-58]\\\\d\\\\d|900)\\\\d{7}\", [7, 10, 12, 13], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"512|8[01589]|90\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"5(?:[0-59]|61)\", \"5(?:[0-59]|61[06])\", \"5(?:[0-59]|61[06]1)\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[24][1-8]|3[1-9]\"], \"(0$1)\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{6,7})\", \"$1 $2 $3\", [\"80\"], \"0$1\", 1]], \"0\"],\n    \"TT\": [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-46-8]\\\\d{6})$|1\", \"868$1\", 0, \"868\"],\n    \"TV\": [\"688\", \"00\", \"(?:2|7\\\\d\\\\d|90)\\\\d{4}\", [5, 6, 7], [[\"(\\\\d{2})(\\\\d{3})\", \"$1 $2\", [\"2\"]], [\"(\\\\d{2})(\\\\d{4})\", \"$1 $2\", [\"90\"]], [\"(\\\\d{2})(\\\\d{5})\", \"$1 $2\", [\"7\"]]]],\n    \"TW\": [\"886\", \"0(?:0[25-79]|19)\", \"[2-689]\\\\d{8}|7\\\\d{9,10}|[2-8]\\\\d{7}|2\\\\d{6}\", [7, 8, 9, 10, 11], [[\"(\\\\d{2})(\\\\d)(\\\\d{4})\", \"$1 $2 $3\", [\"202\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[258]0\"], \"0$1\"], [\"(\\\\d)(\\\\d{3,4})(\\\\d{4})\", \"$1 $2 $3\", [\"[23568]|4(?:0[02-48]|[1-47-9])|7[1-9]\", \"[23568]|4(?:0[2-48]|[1-47-9])|(?:400|7)[1-9]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[49]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4,5})\", \"$1 $2 $3\", [\"7\"], \"0$1\"]], \"0\", 0, 0, 0, 0, 0, 0, 0, \"#\"],\n    \"TZ\": [\"255\", \"00[056]\", \"(?:[25-8]\\\\d|41|90)\\\\d{7}\", [9], [[\"(\\\\d{3})(\\\\d{2})(\\\\d{4})\", \"$1 $2 $3\", [\"[89]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[24]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{7})\", \"$1 $2\", [\"5\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[67]\"], \"0$1\"]], \"0\"],\n    \"UA\": [\"380\", \"00\", \"[89]\\\\d{9}|[3-9]\\\\d{8}\", [9, 10], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"6[12][29]|(?:3[1-8]|4[136-8]|5[12457]|6[49])2|(?:56|65)[24]\", \"6[12][29]|(?:35|4[1378]|5[12457]|6[49])2|(?:56|65)[24]|(?:3[1-46-8]|46)2[013-9]\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{5})\", \"$1 $2\", [\"3[1-8]|4(?:[1367]|[45][6-9]|8[4-6])|5(?:[1-5]|6[0135689]|7[4-6])|6(?:[12][3-7]|[459])\", \"3[1-8]|4(?:[1367]|[45][6-9]|8[4-6])|5(?:[1-5]|6(?:[015689]|3[02389])|7[4-6])|6(?:[12][3-7]|[459])\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[3-7]|89|9[1-9]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[89]\"], \"0$1\"]], \"0\", 0, 0, 0, 0, 0, 0, \"0~0\"],\n    \"UG\": [\"256\", \"00[057]\", \"800\\\\d{6}|(?:[29]0|[347]\\\\d)\\\\d{7}\", [9], [[\"(\\\\d{4})(\\\\d{5})\", \"$1 $2\", [\"202\", \"2024\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{6})\", \"$1 $2\", [\"[27-9]|4(?:6[45]|[7-9])\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{7})\", \"$1 $2\", [\"[34]\"], \"0$1\"]], \"0\"],\n    \"US\": [\"1\", \"011\", \"[2-9]\\\\d{9}|3\\\\d{6}\", [10], [[\"(\\\\d{3})(\\\\d{4})\", \"$1-$2\", [\"310\"], 0, 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"($1) $2-$3\", [\"[2-9]\"], 0, 1, \"$1-$2-$3\"]], \"1\", 0, 0, 0, 0, 0, [[\"(?:3052(?:0[0-8]|[1-9]\\\\d)|5056(?:[0-35-9]\\\\d|4[0-68]))\\\\d{4}|(?:2742|305[3-9]|472[247-9]|505[2-57-9]|983[2-47-9])\\\\d{6}|(?:2(?:0[1-35-9]|1[02-9]|2[03-57-9]|3[1459]|4[08]|5[1-46]|6[0279]|7[0269]|8[13])|3(?:0[1-47-9]|1[02-9]|2[0135-79]|3[0-24679]|4[167]|5[0-2]|6[01349]|8[056])|4(?:0[124-9]|1[02-579]|2[3-5]|3[0245]|4[023578]|58|6[349]|7[0589]|8[04])|5(?:0[1-47-9]|1[0235-8]|20|3[0149]|4[01]|5[179]|6[1-47]|7[0-5]|8[0256])|6(?:0[1-35-9]|1[024-9]|2[03689]|3[016]|4[0156]|5[01679]|6[0-279]|78|8[0-29])|7(?:0[1-46-8]|1[2-9]|2[04-8]|3[0-247]|4[0378]|5[47]|6[02359]|7[0-59]|8[156])|8(?:0[1-68]|1[02-8]|2[0168]|3[0-2589]|4[03578]|5[046-9]|6[02-5]|7[028])|9(?:0[1346-9]|1[02-9]|2[0589]|3[0146-8]|4[01357-9]|5[12469]|7[0-3589]|8[04-69]))[2-9]\\\\d{6}\"], [\"\"], [\"8(?:00|33|44|55|66|77|88)[2-9]\\\\d{6}\"], [\"900[2-9]\\\\d{6}\"], [\"52(?:3(?:[2-46-9][02-9]\\\\d|5(?:[02-46-9]\\\\d|5[0-46-9]))|4(?:[2-478][02-9]\\\\d|5(?:[034]\\\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\\\d)|9(?:[05-9]\\\\d|2[0-5]|49)))\\\\d{4}|52[34][2-9]1[02-9]\\\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\\\d{6}\"], 0, 0, 0, [\"305209\\\\d{4}\"]]],\n    \"UY\": [\"598\", \"0(?:0|1[3-9]\\\\d)\", \"0004\\\\d{2,9}|[1249]\\\\d{7}|(?:[49]\\\\d|80)\\\\d{5}\", [6, 7, 8, 9, 10, 11, 12, 13], [[\"(\\\\d{3})(\\\\d{3,4})\", \"$1 $2\", [\"0\"]], [\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[49]0|8\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"9\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[124]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{2,4})\", \"$1 $2 $3\", [\"0\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})(\\\\d{2,4})\", \"$1 $2 $3 $4\", [\"0\"]]], \"0\", 0, 0, 0, 0, 0, 0, \"00\", \" int. \"],\n    \"UZ\": [\"998\", \"00\", \"(?:20|33|[5-9]\\\\d)\\\\d{7}\", [9], [[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"[235-9]\"]]]],\n    \"VA\": [\"39\", \"00\", \"0\\\\d{5,10}|3[0-8]\\\\d{7,10}|55\\\\d{8}|8\\\\d{5}(?:\\\\d{2,4})?|(?:1\\\\d|39)\\\\d{7,8}\", [6, 7, 8, 9, 10, 11, 12], 0, 0, 0, 0, 0, 0, \"06698\"],\n    \"VC\": [\"1\", \"011\", \"(?:[58]\\\\d\\\\d|784|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-7]\\\\d{6})$|1\", \"784$1\", 0, \"784\"],\n    \"VE\": [\"58\", \"00\", \"[68]00\\\\d{7}|(?:[24]\\\\d|[59]0)\\\\d{8}\", [10], [[\"(\\\\d{3})(\\\\d{7})\", \"$1-$2\", [\"[24-689]\"], \"0$1\"]], \"0\"],\n    \"VG\": [\"1\", \"011\", \"(?:284|[58]\\\\d\\\\d|900)\\\\d{7}\", [10], 0, \"1\", 0, \"([2-578]\\\\d{6})$|1\", \"284$1\", 0, \"284\"],\n    \"VI\": [\"1\", \"011\", \"[58]\\\\d{9}|(?:34|90)0\\\\d{7}\", [10], 0, \"1\", 0, \"([2-9]\\\\d{6})$|1\", \"340$1\", 0, \"340\"],\n    \"VN\": [\"84\", \"00\", \"[12]\\\\d{9}|[135-9]\\\\d{8}|[16]\\\\d{7}|[16-8]\\\\d{6}\", [7, 8, 9, 10], [[\"(\\\\d{2})(\\\\d{5})\", \"$1 $2\", [\"80\"], \"0$1\", 1], [\"(\\\\d{4})(\\\\d{4,6})\", \"$1 $2\", [\"1\"], 0, 1], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"6\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[357-9]\"], \"0$1\", 1], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"2[48]\"], \"0$1\", 1], [\"(\\\\d{3})(\\\\d{4})(\\\\d{3})\", \"$1 $2 $3\", [\"2\"], \"0$1\", 1]], \"0\"],\n    \"VU\": [\"678\", \"00\", \"[57-9]\\\\d{6}|(?:[238]\\\\d|48)\\\\d{3}\", [5, 7], [[\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"[57-9]\"]]]],\n    \"WF\": [\"681\", \"00\", \"(?:40|72|8\\\\d{4})\\\\d{4}|[89]\\\\d{5}\", [6, 9], [[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3\", [\"[47-9]\"]], [\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\", \"$1 $2 $3 $4\", [\"8\"]]]],\n    \"WS\": [\"685\", \"0\", \"(?:[2-6]|8\\\\d{5})\\\\d{4}|[78]\\\\d{6}|[68]\\\\d{5}\", [5, 6, 7, 10], [[\"(\\\\d{5})\", \"$1\", [\"[2-5]|6[1-9]\"]], [\"(\\\\d{3})(\\\\d{3,7})\", \"$1 $2\", [\"[68]\"]], [\"(\\\\d{2})(\\\\d{5})\", \"$1 $2\", [\"7\"]]]],\n    \"XK\": [\"383\", \"00\", \"2\\\\d{7,8}|3\\\\d{7,11}|(?:4\\\\d\\\\d|[89]00)\\\\d{5}\", [8, 9, 10, 11, 12], [[\"(\\\\d{3})(\\\\d{5})\", \"$1 $2\", [\"[89]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[2-4]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"2|39\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{7,10})\", \"$1 $2\", [\"3\"], \"0$1\"]], \"0\"],\n    \"YE\": [\"967\", \"00\", \"(?:1|7\\\\d)\\\\d{7}|[1-7]\\\\d{6}\", [7, 8, 9], [[\"(\\\\d)(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"[1-6]|7(?:[24-6]|8[0-7])\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"7\"], \"0$1\"]], \"0\"],\n    \"YT\": [\"262\", \"00\", \"7093\\\\d{5}|(?:80|9\\\\d)\\\\d{7}|(?:26|63)9\\\\d{6}\", [9], 0, \"0\", 0, 0, 0, 0, 0, [[\"269(?:0[0-467]|15|5[0-4]|6\\\\d|[78]0)\\\\d{4}\"], [\"(?:639(?:0[0-79]|1[019]|[267]\\\\d|3[09]|40|5[05-9]|9[04-79])|7093[5-7])\\\\d{4}\"], [\"80\\\\d{7}\"], 0, 0, 0, 0, 0, [\"9(?:(?:39|47)8[01]|769\\\\d)\\\\d{4}\"]]],\n    \"ZA\": [\"27\", \"00\", \"[1-79]\\\\d{8}|8\\\\d{4,9}\", [5, 6, 7, 8, 9, 10], [[\"(\\\\d{2})(\\\\d{3,4})\", \"$1 $2\", [\"8[1-4]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{2,3})\", \"$1 $2 $3\", [\"8[1-4]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"860\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"[1-9]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"8\"], \"0$1\"]], \"0\"],\n    \"ZM\": [\"260\", \"00\", \"800\\\\d{6}|(?:21|[579]\\\\d|63)\\\\d{7}\", [9], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[28]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{7})\", \"$1 $2\", [\"[579]\"], \"0$1\"]], \"0\"],\n    \"ZW\": [\"263\", \"00\", \"2(?:[0-57-9]\\\\d{6,8}|6[0-24-9]\\\\d{6,7})|[38]\\\\d{9}|[35-8]\\\\d{8}|[3-6]\\\\d{7}|[1-689]\\\\d{6}|[1-3569]\\\\d{5}|[1356]\\\\d{4}\", [5, 6, 7, 8, 9, 10], [[\"(\\\\d{3})(\\\\d{3,5})\", \"$1 $2\", [\"2(?:0[45]|2[278]|[49]8)|3(?:[09]8|17)|6(?:[29]8|37|75)|[23][78]|(?:33|5[15]|6[68])[78]\"], \"0$1\"], [\"(\\\\d)(\\\\d{3})(\\\\d{2,4})\", \"$1 $2 $3\", [\"[49]\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{4})\", \"$1 $2\", [\"80\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{7})\", \"$1 $2\", [\"24|8[13-59]|(?:2[05-79]|39|5[45]|6[15-8])2\", \"2(?:02[014]|4|[56]20|[79]2)|392|5(?:42|525)|6(?:[16-8]21|52[013])|8[13-59]\"], \"(0$1)\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"7\"], \"0$1\"], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"2(?:1[39]|2[0157]|[378]|[56][14])|3(?:12|29)\", \"2(?:1[39]|2[0157]|[378]|[56][14])|3(?:123|29)\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{6})\", \"$1 $2\", [\"8\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3,5})\", \"$1 $2\", [\"1|2(?:0[0-36-9]|12|29|[56])|3(?:1[0-689]|[24-6])|5(?:[0236-9]|1[2-4])|6(?:[013-59]|7[0-46-9])|(?:33|55|6[68])[0-69]|(?:29|3[09]|62)[0-79]\"], \"0$1\"], [\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\", \"$1 $2 $3\", [\"29[013-9]|39|54\"], \"0$1\"], [\"(\\\\d{4})(\\\\d{3,5})\", \"$1 $2\", [\"(?:25|54)8\", \"258|5483\"], \"0$1\"]], \"0\"]\n  },\n  \"nonGeographic\": {\n    \"800\": [\"800\", 0, \"(?:00|[1-9]\\\\d)\\\\d{6}\", [8], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"\\\\d\"]]], 0, 0, 0, 0, 0, 0, [0, 0, [\"(?:00|[1-9]\\\\d)\\\\d{6}\"]]],\n    \"808\": [\"808\", 0, \"[1-9]\\\\d{7}\", [8], [[\"(\\\\d{4})(\\\\d{4})\", \"$1 $2\", [\"[1-9]\"]]], 0, 0, 0, 0, 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, [\"[1-9]\\\\d{7}\"]]],\n    \"870\": [\"870\", 0, \"7\\\\d{11}|[235-7]\\\\d{8}\", [9, 12], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"[235-7]\"]]], 0, 0, 0, 0, 0, 0, [0, [\"(?:[356]|774[45])\\\\d{8}|7[6-8]\\\\d{7}\"], 0, 0, 0, 0, 0, 0, [\"2\\\\d{8}\", [9]]]],\n    \"878\": [\"878\", 0, \"10\\\\d{10}\", [12], [[\"(\\\\d{2})(\\\\d{5})(\\\\d{5})\", \"$1 $2 $3\", [\"1\"]]], 0, 0, 0, 0, 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, [\"10\\\\d{10}\"]]],\n    \"881\": [\"881\", 0, \"6\\\\d{9}|[0-36-9]\\\\d{8}\", [9, 10], [[\"(\\\\d)(\\\\d{3})(\\\\d{5})\", \"$1 $2 $3\", [\"[0-37-9]\"]], [\"(\\\\d)(\\\\d{3})(\\\\d{5,6})\", \"$1 $2 $3\", [\"6\"]]], 0, 0, 0, 0, 0, 0, [0, [\"6\\\\d{9}|[0-36-9]\\\\d{8}\"]]],\n    \"882\": [\"882\", 0, \"[13]\\\\d{6}(?:\\\\d{2,5})?|[19]\\\\d{7}|(?:[25]\\\\d\\\\d|4)\\\\d{7}(?:\\\\d{2})?\", [7, 8, 9, 10, 11, 12], [[\"(\\\\d{2})(\\\\d{5})\", \"$1 $2\", [\"16|342\"]], [\"(\\\\d{2})(\\\\d{6})\", \"$1 $2\", [\"49\"]], [\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\", \"$1 $2 $3\", [\"1[36]|9\"]], [\"(\\\\d{2})(\\\\d{4})(\\\\d{3})\", \"$1 $2 $3\", [\"3[23]\"]], [\"(\\\\d{2})(\\\\d{3,4})(\\\\d{4})\", \"$1 $2 $3\", [\"16\"]], [\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"10|23|3(?:[15]|4[57])|4|51\"]], [\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"34\"]], [\"(\\\\d{2})(\\\\d{4,5})(\\\\d{5})\", \"$1 $2 $3\", [\"[1-35]\"]]], 0, 0, 0, 0, 0, 0, [0, [\"342\\\\d{4}|(?:337|49)\\\\d{6}|(?:3(?:2|47|7\\\\d{3})|50\\\\d{3})\\\\d{7}\", [7, 8, 9, 10, 12]], 0, 0, 0, [\"348[57]\\\\d{7}\", [11]], 0, 0, [\"1(?:3(?:0[0347]|[13][0139]|2[035]|4[013568]|6[0459]|7[06]|8[15-8]|9[0689])\\\\d{4}|6\\\\d{5,10})|(?:345\\\\d|9[89])\\\\d{6}|(?:10|2(?:3|85\\\\d)|3(?:[15]|[69]\\\\d\\\\d)|4[15-8]|51)\\\\d{8}\"]]],\n    \"883\": [\"883\", 0, \"(?:[1-4]\\\\d|51)\\\\d{6,10}\", [8, 9, 10, 11, 12], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{2,8})\", \"$1 $2 $3\", [\"[14]|2[24-689]|3[02-689]|51[24-9]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3\", [\"510\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\", \"$1 $2 $3\", [\"21\"]], [\"(\\\\d{4})(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"51[13]\"]], [\"(\\\\d{3})(\\\\d{3})(\\\\d{3})(\\\\d{3})\", \"$1 $2 $3 $4\", [\"[235]\"]]], 0, 0, 0, 0, 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, [\"(?:2(?:00\\\\d\\\\d|10)|(?:370[1-9]|51\\\\d0)\\\\d)\\\\d{7}|51(?:00\\\\d{5}|[24-9]0\\\\d{4,7})|(?:1[0-79]|2[24-689]|3[02-689]|4[0-4])0\\\\d{5,9}\"]]],\n    \"888\": [\"888\", 0, \"\\\\d{11}\", [11], [[\"(\\\\d{3})(\\\\d{3})(\\\\d{5})\", \"$1 $2 $3\"]], 0, 0, 0, 0, 0, 0, [0, 0, 0, 0, 0, 0, [\"\\\\d{11}\"]]],\n    \"979\": [\"979\", 0, \"[1359]\\\\d{8}\", [9], [[\"(\\\\d)(\\\\d{4})(\\\\d{4})\", \"$1 $2 $3\", [\"[1359]\"]]], 0, 0, 0, 0, 0, 0, [0, 0, 0, [\"[1359]\\\\d{8}\"]]]\n  }\n};", "map": {"version": 3, "names": [], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/metadata.min.json.js"], "sourcesContent": ["// This file is a workaround for a bug in web browsers' \"native\"\n// ES6 importing system which is uncapable of importing \"*.json\" files.\n// https://github.com/catamphetamine/libphonenumber-js/issues/239\nexport default {\"version\":4,\"country_calling_codes\":{\"1\":[\"US\",\"AG\",\"AI\",\"AS\",\"BB\",\"BM\",\"BS\",\"CA\",\"DM\",\"DO\",\"GD\",\"GU\",\"JM\",\"KN\",\"KY\",\"LC\",\"MP\",\"MS\",\"PR\",\"SX\",\"TC\",\"TT\",\"VC\",\"VG\",\"VI\"],\"7\":[\"RU\",\"KZ\"],\"20\":[\"EG\"],\"27\":[\"ZA\"],\"30\":[\"GR\"],\"31\":[\"NL\"],\"32\":[\"BE\"],\"33\":[\"FR\"],\"34\":[\"ES\"],\"36\":[\"HU\"],\"39\":[\"IT\",\"VA\"],\"40\":[\"RO\"],\"41\":[\"CH\"],\"43\":[\"AT\"],\"44\":[\"GB\",\"GG\",\"IM\",\"JE\"],\"45\":[\"DK\"],\"46\":[\"SE\"],\"47\":[\"NO\",\"SJ\"],\"48\":[\"PL\"],\"49\":[\"DE\"],\"51\":[\"PE\"],\"52\":[\"MX\"],\"53\":[\"CU\"],\"54\":[\"AR\"],\"55\":[\"BR\"],\"56\":[\"CL\"],\"57\":[\"CO\"],\"58\":[\"VE\"],\"60\":[\"MY\"],\"61\":[\"AU\",\"CC\",\"CX\"],\"62\":[\"ID\"],\"63\":[\"PH\"],\"64\":[\"NZ\"],\"65\":[\"SG\"],\"66\":[\"TH\"],\"81\":[\"JP\"],\"82\":[\"KR\"],\"84\":[\"VN\"],\"86\":[\"CN\"],\"90\":[\"TR\"],\"91\":[\"IN\"],\"92\":[\"PK\"],\"93\":[\"AF\"],\"94\":[\"LK\"],\"95\":[\"MM\"],\"98\":[\"IR\"],\"211\":[\"SS\"],\"212\":[\"MA\",\"EH\"],\"213\":[\"DZ\"],\"216\":[\"TN\"],\"218\":[\"LY\"],\"220\":[\"GM\"],\"221\":[\"SN\"],\"222\":[\"MR\"],\"223\":[\"ML\"],\"224\":[\"GN\"],\"225\":[\"CI\"],\"226\":[\"BF\"],\"227\":[\"NE\"],\"228\":[\"TG\"],\"229\":[\"BJ\"],\"230\":[\"MU\"],\"231\":[\"LR\"],\"232\":[\"SL\"],\"233\":[\"GH\"],\"234\":[\"NG\"],\"235\":[\"TD\"],\"236\":[\"CF\"],\"237\":[\"CM\"],\"238\":[\"CV\"],\"239\":[\"ST\"],\"240\":[\"GQ\"],\"241\":[\"GA\"],\"242\":[\"CG\"],\"243\":[\"CD\"],\"244\":[\"AO\"],\"245\":[\"GW\"],\"246\":[\"IO\"],\"247\":[\"AC\"],\"248\":[\"SC\"],\"249\":[\"SD\"],\"250\":[\"RW\"],\"251\":[\"ET\"],\"252\":[\"SO\"],\"253\":[\"DJ\"],\"254\":[\"KE\"],\"255\":[\"TZ\"],\"256\":[\"UG\"],\"257\":[\"BI\"],\"258\":[\"MZ\"],\"260\":[\"ZM\"],\"261\":[\"MG\"],\"262\":[\"RE\",\"YT\"],\"263\":[\"ZW\"],\"264\":[\"NA\"],\"265\":[\"MW\"],\"266\":[\"LS\"],\"267\":[\"BW\"],\"268\":[\"SZ\"],\"269\":[\"KM\"],\"290\":[\"SH\",\"TA\"],\"291\":[\"ER\"],\"297\":[\"AW\"],\"298\":[\"FO\"],\"299\":[\"GL\"],\"350\":[\"GI\"],\"351\":[\"PT\"],\"352\":[\"LU\"],\"353\":[\"IE\"],\"354\":[\"IS\"],\"355\":[\"AL\"],\"356\":[\"MT\"],\"357\":[\"CY\"],\"358\":[\"FI\",\"AX\"],\"359\":[\"BG\"],\"370\":[\"LT\"],\"371\":[\"LV\"],\"372\":[\"EE\"],\"373\":[\"MD\"],\"374\":[\"AM\"],\"375\":[\"BY\"],\"376\":[\"AD\"],\"377\":[\"MC\"],\"378\":[\"SM\"],\"380\":[\"UA\"],\"381\":[\"RS\"],\"382\":[\"ME\"],\"383\":[\"XK\"],\"385\":[\"HR\"],\"386\":[\"SI\"],\"387\":[\"BA\"],\"389\":[\"MK\"],\"420\":[\"CZ\"],\"421\":[\"SK\"],\"423\":[\"LI\"],\"500\":[\"FK\"],\"501\":[\"BZ\"],\"502\":[\"GT\"],\"503\":[\"SV\"],\"504\":[\"HN\"],\"505\":[\"NI\"],\"506\":[\"CR\"],\"507\":[\"PA\"],\"508\":[\"PM\"],\"509\":[\"HT\"],\"590\":[\"GP\",\"BL\",\"MF\"],\"591\":[\"BO\"],\"592\":[\"GY\"],\"593\":[\"EC\"],\"594\":[\"GF\"],\"595\":[\"PY\"],\"596\":[\"MQ\"],\"597\":[\"SR\"],\"598\":[\"UY\"],\"599\":[\"CW\",\"BQ\"],\"670\":[\"TL\"],\"672\":[\"NF\"],\"673\":[\"BN\"],\"674\":[\"NR\"],\"675\":[\"PG\"],\"676\":[\"TO\"],\"677\":[\"SB\"],\"678\":[\"VU\"],\"679\":[\"FJ\"],\"680\":[\"PW\"],\"681\":[\"WF\"],\"682\":[\"CK\"],\"683\":[\"NU\"],\"685\":[\"WS\"],\"686\":[\"KI\"],\"687\":[\"NC\"],\"688\":[\"TV\"],\"689\":[\"PF\"],\"690\":[\"TK\"],\"691\":[\"FM\"],\"692\":[\"MH\"],\"850\":[\"KP\"],\"852\":[\"HK\"],\"853\":[\"MO\"],\"855\":[\"KH\"],\"856\":[\"LA\"],\"880\":[\"BD\"],\"886\":[\"TW\"],\"960\":[\"MV\"],\"961\":[\"LB\"],\"962\":[\"JO\"],\"963\":[\"SY\"],\"964\":[\"IQ\"],\"965\":[\"KW\"],\"966\":[\"SA\"],\"967\":[\"YE\"],\"968\":[\"OM\"],\"970\":[\"PS\"],\"971\":[\"AE\"],\"972\":[\"IL\"],\"973\":[\"BH\"],\"974\":[\"QA\"],\"975\":[\"BT\"],\"976\":[\"MN\"],\"977\":[\"NP\"],\"992\":[\"TJ\"],\"993\":[\"TM\"],\"994\":[\"AZ\"],\"995\":[\"GE\"],\"996\":[\"KG\"],\"998\":[\"UZ\"]},\"countries\":{\"AC\":[\"247\",\"00\",\"(?:[01589]\\\\d|[46])\\\\d{4}\",[5,6]],\"AD\":[\"376\",\"00\",\"(?:1|6\\\\d)\\\\d{7}|[135-9]\\\\d{5}\",[6,8,9],[[\"(\\\\d{3})(\\\\d{3})\",\"$1 $2\",[\"[135-9]\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"1\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"6\"]]]],\"AE\":[\"971\",\"00\",\"(?:[4-7]\\\\d|9[0-689])\\\\d{7}|800\\\\d{2,9}|[2-4679]\\\\d{7}\",[5,6,7,8,9,10,11,12],[[\"(\\\\d{3})(\\\\d{2,9})\",\"$1 $2\",[\"60|8\"]],[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[236]|[479][2-8]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d)(\\\\d{5})\",\"$1 $2 $3\",[\"[479]\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"5\"],\"0$1\"]],\"0\"],\"AF\":[\"93\",\"00\",\"[2-7]\\\\d{8}\",[9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[2-7]\"],\"0$1\"]],\"0\"],\"AG\":[\"1\",\"011\",\"(?:268|[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([457]\\\\d{6})$|1\",\"268$1\",0,\"268\"],\"AI\":[\"1\",\"011\",\"(?:264|[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([2457]\\\\d{6})$|1\",\"264$1\",0,\"264\"],\"AL\":[\"355\",\"00\",\"(?:700\\\\d\\\\d|900)\\\\d{3}|8\\\\d{5,7}|(?:[2-5]|6\\\\d)\\\\d{7}\",[6,7,8,9],[[\"(\\\\d{3})(\\\\d{3,4})\",\"$1 $2\",[\"80|9\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"4[2-6]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[2358][2-5]|4\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"[23578]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"6\"],\"0$1\"]],\"0\"],\"AM\":[\"374\",\"00\",\"(?:[1-489]\\\\d|55|60|77)\\\\d{6}\",[8],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"[89]0\"],\"0 $1\"],[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"2|3[12]\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{6})\",\"$1 $2\",[\"1|47\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{6})\",\"$1 $2\",[\"[3-9]\"],\"0$1\"]],\"0\"],\"AO\":[\"244\",\"00\",\"[29]\\\\d{8}\",[9],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[29]\"]]]],\"AR\":[\"54\",\"00\",\"(?:11|[89]\\\\d\\\\d)\\\\d{8}|[2368]\\\\d{9}\",[10,11],[[\"(\\\\d{4})(\\\\d{2})(\\\\d{4})\",\"$1 $2-$3\",[\"2(?:2[024-9]|3[0-59]|47|6[245]|9[02-8])|3(?:3[28]|4[03-9]|5[2-46-8]|7[1-578]|8[2-9])\",\"2(?:[23]02|6(?:[25]|4[6-8])|9(?:[02356]|4[02568]|72|8[23]))|3(?:3[28]|4(?:[04679]|3[5-8]|5[4-68]|8[2379])|5(?:[2467]|3[237]|8[2-5])|7[1-578]|8(?:[2469]|3[2578]|5[4-8]|7[36-8]|8[5-8]))|2(?:2[24-9]|3[1-59]|47)\",\"2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3[78]|5(?:4[46]|8)|8[2379])|5(?:[2467]|3[237]|8[23])|7[1-578]|8(?:[2469]|3[278]|5[56][46]|86[3-6]))|2(?:2[24-9]|3[1-59]|47)|38(?:[58][78]|7[378])|3(?:4[35][56]|58[45]|8(?:[38]5|54|76))[4-6]\",\"2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3(?:5(?:4[0-25689]|[56])|[78])|58|8[2379])|5(?:[2467]|3[237]|8(?:[23]|4(?:[45]|60)|5(?:4[0-39]|5|64)))|7[1-578]|8(?:[2469]|3[278]|54(?:4|5[13-7]|6[89])|86[3-6]))|2(?:2[24-9]|3[1-59]|47)|38(?:[58][78]|7[378])|3(?:454|85[56])[46]|3(?:4(?:36|5[56])|8(?:[38]5|76))[4-6]\"],\"0$1\",1],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2-$3\",[\"1\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1-$2-$3\",[\"[68]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2-$3\",[\"[23]\"],\"0$1\",1],[\"(\\\\d)(\\\\d{4})(\\\\d{2})(\\\\d{4})\",\"$2 15-$3-$4\",[\"9(?:2[2-469]|3[3-578])\",\"9(?:2(?:2[024-9]|3[0-59]|47|6[245]|9[02-8])|3(?:3[28]|4[03-9]|5[2-46-8]|7[1-578]|8[2-9]))\",\"9(?:2(?:[23]02|6(?:[25]|4[6-8])|9(?:[02356]|4[02568]|72|8[23]))|3(?:3[28]|4(?:[04679]|3[5-8]|5[4-68]|8[2379])|5(?:[2467]|3[237]|8[2-5])|7[1-578]|8(?:[2469]|3[2578]|5[4-8]|7[36-8]|8[5-8])))|92(?:2[24-9]|3[1-59]|47)\",\"9(?:2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3[78]|5(?:4[46]|8)|8[2379])|5(?:[2467]|3[237]|8[23])|7[1-578]|8(?:[2469]|3[278]|5(?:[56][46]|[78])|7[378]|8(?:6[3-6]|[78]))))|92(?:2[24-9]|3[1-59]|47)|93(?:4[35][56]|58[45]|8(?:[38]5|54|76))[4-6]\",\"9(?:2(?:[23]02|6(?:[25]|4(?:64|[78]))|9(?:[02356]|4(?:[0268]|5[2-6])|72|8[23]))|3(?:3[28]|4(?:[04679]|3(?:5(?:4[0-25689]|[56])|[78])|5(?:4[46]|8)|8[2379])|5(?:[2467]|3[237]|8(?:[23]|4(?:[45]|60)|5(?:4[0-39]|5|64)))|7[1-578]|8(?:[2469]|3[278]|5(?:4(?:4|5[13-7]|6[89])|[56][46]|[78])|7[378]|8(?:6[3-6]|[78]))))|92(?:2[24-9]|3[1-59]|47)|93(?:4(?:36|5[56])|8(?:[38]5|76))[4-6]\"],\"0$1\",0,\"$1 $2 $3-$4\"],[\"(\\\\d)(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$2 15-$3-$4\",[\"91\"],\"0$1\",0,\"$1 $2 $3-$4\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{5})\",\"$1-$2-$3\",[\"8\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$2 15-$3-$4\",[\"9\"],\"0$1\",0,\"$1 $2 $3-$4\"]],\"0\",0,\"0?(?:(11|2(?:2(?:02?|[13]|2[13-79]|4[1-6]|5[2457]|6[124-8]|7[1-4]|8[13-6]|9[1267])|3(?:02?|1[467]|2[03-6]|3[13-8]|[49][2-6]|5[2-8]|[67])|4(?:7[3-578]|9)|6(?:[0136]|2[24-6]|4[6-8]?|5[15-8])|80|9(?:0[1-3]|[19]|2\\\\d|3[1-6]|4[02568]?|5[2-4]|6[2-46]|72?|8[23]?))|3(?:3(?:2[79]|6|8[2578])|4(?:0[0-24-9]|[12]|3[5-8]?|4[24-7]|5[4-68]?|6[02-9]|7[126]|8[2379]?|9[1-36-8])|5(?:1|2[1245]|3[237]?|4[1-46-9]|6[2-4]|7[1-6]|8[2-5]?)|6[24]|7(?:[069]|1[1568]|2[15]|3[145]|4[13]|5[14-8]|7[2-57]|8[126])|8(?:[01]|2[15-7]|3[2578]?|4[13-6]|5[4-8]?|6[1-357-9]|7[36-8]?|8[5-8]?|9[124])))15)?\",\"9$1\"],\"AS\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|684|900)\\\\d{7}\",[10],0,\"1\",0,\"([267]\\\\d{6})$|1\",\"684$1\",0,\"684\"],\"AT\":[\"43\",\"00\",\"1\\\\d{3,12}|2\\\\d{6,12}|43(?:(?:0\\\\d|5[02-9])\\\\d{3,9}|2\\\\d{4,5}|[3467]\\\\d{4}|8\\\\d{4,6}|9\\\\d{4,7})|5\\\\d{4,12}|8\\\\d{7,12}|9\\\\d{8,12}|(?:[367]\\\\d|4[0-24-9])\\\\d{4,11}\",[4,5,6,7,8,9,10,11,12,13],[[\"(\\\\d)(\\\\d{3,12})\",\"$1 $2\",[\"1(?:11|[2-9])\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})\",\"$1 $2\",[\"517\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3,5})\",\"$1 $2\",[\"5[079]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3,10})\",\"$1 $2\",[\"(?:31|4)6|51|6(?:48|5[0-3579]|[6-9])|7(?:20|32|8)|[89]\",\"(?:31|4)6|51|6(?:485|5[0-3579]|[6-9])|7(?:20|32|8)|[89]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3,9})\",\"$1 $2\",[\"[2-467]|5[2-6]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"5\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4,7})\",\"$1 $2 $3\",[\"5\"],\"0$1\"]],\"0\"],\"AU\":[\"61\",\"001[14-689]|14(?:1[14]|34|4[17]|[56]6|7[47]|88)0011\",\"1(?:[0-79]\\\\d{7}(?:\\\\d(?:\\\\d{2})?)?|8[0-24-9]\\\\d{7})|[2-478]\\\\d{8}|1\\\\d{4,7}\",[5,6,7,8,9,10,12],[[\"(\\\\d{2})(\\\\d{3,4})\",\"$1 $2\",[\"16\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2,4})\",\"$1 $2 $3\",[\"16\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"14|4\"],\"0$1\"],[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"[2378]\"],\"(0$1)\"],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"1(?:30|[89])\"]]],\"0\",0,\"(183[12])|0\",0,0,0,[[\"(?:(?:2(?:(?:[0-26-9]\\\\d|3[0-8]|5[0135-9])\\\\d|4(?:[02-9]\\\\d|10))|3(?:(?:[0-3589]\\\\d|6[1-9]|7[0-35-9])\\\\d|4(?:[0-578]\\\\d|90))|7(?:[013-57-9]\\\\d|2[0-8])\\\\d)\\\\d\\\\d|8(?:51(?:0(?:0[03-9]|[12479]\\\\d|3[2-9]|5[0-8]|6[1-9]|8[0-7])|1(?:[0235689]\\\\d|1[0-69]|4[0-589]|7[0-47-9])|2(?:0[0-79]|[18][13579]|2[14-9]|3[0-46-9]|[4-6]\\\\d|7[89]|9[0-4])|[34]\\\\d\\\\d)|(?:6[0-8]|[78]\\\\d)\\\\d{3}|9(?:[02-9]\\\\d{3}|1(?:(?:[0-58]\\\\d|6[0135-9])\\\\d|7(?:0[0-24-9]|[1-9]\\\\d)|9(?:[0-46-9]\\\\d|5[0-79])))))\\\\d{3}\",[9]],[\"4(?:79[01]|83[0-389]|94[0-478])\\\\d{5}|4(?:[0-36]\\\\d|4[047-9]|5[0-25-9]|7[02-8]|8[0-24-9]|9[0-37-9])\\\\d{6}\",[9]],[\"180(?:0\\\\d{3}|2)\\\\d{3}\",[7,10]],[\"190[0-26]\\\\d{6}\",[10]],0,0,0,[\"163\\\\d{2,6}\",[5,6,7,8,9]],[\"14(?:5(?:1[0458]|[23][458])|71\\\\d)\\\\d{4}\",[9]],[\"13(?:00\\\\d{6}(?:\\\\d{2})?|45[0-4]\\\\d{3})|13\\\\d{4}\",[6,8,10,12]]],\"0011\"],\"AW\":[\"297\",\"00\",\"(?:[25-79]\\\\d\\\\d|800)\\\\d{4}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[25-9]\"]]]],\"AX\":[\"358\",\"00|99(?:[01469]|5(?:[14]1|3[23]|5[59]|77|88|9[09]))\",\"2\\\\d{4,9}|35\\\\d{4,5}|(?:60\\\\d\\\\d|800)\\\\d{4,6}|7\\\\d{5,11}|(?:[14]\\\\d|3[0-46-9]|50)\\\\d{4,8}\",[5,6,7,8,9,10,11,12],0,\"0\",0,0,0,0,\"18\",0,\"00\"],\"AZ\":[\"994\",\"00\",\"365\\\\d{6}|(?:[124579]\\\\d|60|88)\\\\d{7}\",[9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"90\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"1[28]|2|365|46\",\"1[28]|2|365[45]|46\",\"1[28]|2|365(?:4|5[02])|46\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[13-9]\"],\"0$1\"]],\"0\"],\"BA\":[\"387\",\"00\",\"6\\\\d{8}|(?:[35689]\\\\d|49|70)\\\\d{6}\",[8,9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"6[1-3]|[7-9]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2-$3\",[\"[3-5]|6[56]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"6\"],\"0$1\"]],\"0\"],\"BB\":[\"1\",\"011\",\"(?:246|[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-9]\\\\d{6})$|1\",\"246$1\",0,\"246\"],\"BD\":[\"880\",\"00\",\"[1-469]\\\\d{9}|8[0-79]\\\\d{7,8}|[2-79]\\\\d{8}|[2-9]\\\\d{7}|[3-9]\\\\d{6}|[57-9]\\\\d{5}\",[6,7,8,9,10],[[\"(\\\\d{2})(\\\\d{4,6})\",\"$1-$2\",[\"31[5-8]|[459]1\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3,7})\",\"$1-$2\",[\"3(?:[67]|8[013-9])|4(?:6[168]|7|[89][18])|5(?:6[128]|9)|6(?:[15]|28|4[14])|7[2-589]|8(?:0[014-9]|[12])|9[358]|(?:3[2-5]|4[235]|5[2-578]|6[0389]|76|8[3-7]|9[24])1|(?:44|66)[01346-9]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3,6})\",\"$1-$2\",[\"[13-9]|2[23]\"],\"0$1\"],[\"(\\\\d)(\\\\d{7,8})\",\"$1-$2\",[\"2\"],\"0$1\"]],\"0\"],\"BE\":[\"32\",\"00\",\"4\\\\d{8}|[1-9]\\\\d{7}\",[8,9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"(?:80|9)0\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[239]|4[23]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[15-8]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"4\"],\"0$1\"]],\"0\"],\"BF\":[\"226\",\"00\",\"(?:[025-7]\\\\d|44)\\\\d{6}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[024-7]\"]]]],\"BG\":[\"359\",\"00\",\"00800\\\\d{7}|[2-7]\\\\d{6,7}|[89]\\\\d{6,8}|2\\\\d{5}\",[6,7,8,9,12],[[\"(\\\\d)(\\\\d)(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"2\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"43[1-6]|70[1-9]\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"2\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2,3})\",\"$1 $2 $3\",[\"[356]|4[124-7]|7[1-9]|8[1-6]|9[1-7]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"(?:70|8)0\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{2})\",\"$1 $2 $3\",[\"43[1-7]|7\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[48]|9[08]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"9\"],\"0$1\"]],\"0\"],\"BH\":[\"973\",\"00\",\"[136-9]\\\\d{7}\",[8],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[13679]|8[02-4679]\"]]]],\"BI\":[\"257\",\"00\",\"(?:[267]\\\\d|31)\\\\d{6}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[2367]\"]]]],\"BJ\":[\"229\",\"00\",\"(?:01\\\\d|[24-689])\\\\d{7}\",[8,10],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[24-689]\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4 $5\",[\"0\"]]]],\"BL\":[\"590\",\"00\",\"(?:590\\\\d|7090)\\\\d{5}|(?:69|80|9\\\\d)\\\\d{7}\",[9],0,\"0\",0,0,0,0,0,[[\"590(?:2[7-9]|3[3-7]|5[12]|87)\\\\d{4}\"],[\"(?:69(?:0\\\\d\\\\d|1(?:2[2-9]|3[0-5])|4(?:0[89]|1[2-6]|9\\\\d)|6(?:1[016-9]|5[0-4]|[67]\\\\d))|7090[0-4])\\\\d{4}\"],[\"80[0-5]\\\\d{6}\"],0,0,0,0,0,[\"9(?:(?:39[5-7]|76[018])\\\\d|475[0-6])\\\\d{4}\"]]],\"BM\":[\"1\",\"011\",\"(?:441|[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-9]\\\\d{6})$|1\",\"441$1\",0,\"441\"],\"BN\":[\"673\",\"00\",\"[2-578]\\\\d{6}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[2-578]\"]]]],\"BO\":[\"591\",\"00(?:1\\\\d)?\",\"8001\\\\d{5}|(?:[2-467]\\\\d|50)\\\\d{6}\",[8,9],[[\"(\\\\d)(\\\\d{7})\",\"$1 $2\",[\"[235]|4[46]\"]],[\"(\\\\d{8})\",\"$1\",[\"[67]\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3\",[\"8\"]]],\"0\",0,\"0(1\\\\d)?\"],\"BQ\":[\"599\",\"00\",\"(?:[34]1|7\\\\d)\\\\d{5}\",[7],0,0,0,0,0,0,\"[347]\"],\"BR\":[\"55\",\"00(?:1[245]|2[1-35]|31|4[13]|[56]5|99)\",\"[1-467]\\\\d{9,10}|55[0-46-9]\\\\d{8}|[34]\\\\d{7}|55\\\\d{7,8}|(?:5[0-46-9]|[89]\\\\d)\\\\d{7,9}\",[8,9,10,11],[[\"(\\\\d{4})(\\\\d{4})\",\"$1-$2\",[\"300|4(?:0[02]|37|86)\",\"300|4(?:0(?:0|20)|370|864)\"]],[\"(\\\\d{3})(\\\\d{2,3})(\\\\d{4})\",\"$1 $2 $3\",[\"(?:[358]|90)0\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2-$3\",[\"(?:[14689][1-9]|2[12478]|3[1-578]|5[13-5]|7[13-579])[2-57]\"],\"($1)\"],[\"(\\\\d{2})(\\\\d{5})(\\\\d{4})\",\"$1 $2-$3\",[\"[16][1-9]|[2-57-9]\"],\"($1)\"]],\"0\",0,\"(?:0|90)(?:(1[245]|2[1-35]|31|4[13]|[56]5|99)(\\\\d{10,11}))?\",\"$2\"],\"BS\":[\"1\",\"011\",\"(?:242|[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([3-8]\\\\d{6})$|1\",\"242$1\",0,\"242\"],\"BT\":[\"975\",\"00\",\"[17]\\\\d{7}|[2-8]\\\\d{6}\",[7,8],[[\"(\\\\d)(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[2-68]|7[246]\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"1[67]|7\"]]]],\"BW\":[\"267\",\"00\",\"(?:0800|(?:[37]|800)\\\\d)\\\\d{6}|(?:[2-6]\\\\d|90)\\\\d{5}\",[7,8,10],[[\"(\\\\d{2})(\\\\d{5})\",\"$1 $2\",[\"90\"]],[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[24-6]|3[15-9]\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[37]\"]],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"0\"]],[\"(\\\\d{3})(\\\\d{4})(\\\\d{3})\",\"$1 $2 $3\",[\"8\"]]]],\"BY\":[\"375\",\"810\",\"(?:[12]\\\\d|33|44|902)\\\\d{7}|8(?:0[0-79]\\\\d{5,7}|[1-7]\\\\d{9})|8(?:1[0-489]|[5-79]\\\\d)\\\\d{7}|8[1-79]\\\\d{6,7}|8[0-79]\\\\d{5}|8\\\\d{5}\",[6,7,8,9,10,11],[[\"(\\\\d{3})(\\\\d{3})\",\"$1 $2\",[\"800\"],\"8 $1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2,4})\",\"$1 $2 $3\",[\"800\"],\"8 $1\"],[\"(\\\\d{4})(\\\\d{2})(\\\\d{3})\",\"$1 $2-$3\",[\"1(?:5[169]|6[3-5]|7[179])|2(?:1[35]|2[34]|3[3-5])\",\"1(?:5[169]|6(?:3[1-3]|4|5[125])|7(?:1[3-9]|7[0-24-6]|9[2-7]))|2(?:1[35]|2[34]|3[3-5])\"],\"8 0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2-$3-$4\",[\"1(?:[56]|7[467])|2[1-3]\"],\"8 0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2-$3-$4\",[\"[1-4]\"],\"8 0$1\"],[\"(\\\\d{3})(\\\\d{3,4})(\\\\d{4})\",\"$1 $2 $3\",[\"[89]\"],\"8 $1\"]],\"8\",0,\"0|80?\",0,0,0,0,\"8~10\"],\"BZ\":[\"501\",\"00\",\"(?:0800\\\\d|[2-8])\\\\d{6}\",[7,11],[[\"(\\\\d{3})(\\\\d{4})\",\"$1-$2\",[\"[2-8]\"]],[\"(\\\\d)(\\\\d{3})(\\\\d{4})(\\\\d{3})\",\"$1-$2-$3-$4\",[\"0\"]]]],\"CA\":[\"1\",\"011\",\"[2-9]\\\\d{9}|3\\\\d{6}\",[7,10],0,\"1\",0,0,0,0,0,[[\"(?:2(?:04|[23]6|[48]9|5[07]|63)|3(?:06|43|54|6[578]|82)|4(?:03|1[68]|[26]8|3[178]|50|74)|5(?:06|1[49]|48|79|8[147])|6(?:04|[18]3|39|47|72)|7(?:0[59]|42|53|78|8[02])|8(?:[06]7|19|25|7[39])|9(?:0[25]|42))[2-9]\\\\d{6}\",[10]],[\"\",[10]],[\"8(?:00|33|44|55|66|77|88)[2-9]\\\\d{6}\",[10]],[\"900[2-9]\\\\d{6}\",[10]],[\"52(?:3(?:[2-46-9][02-9]\\\\d|5(?:[02-46-9]\\\\d|5[0-46-9]))|4(?:[2-478][02-9]\\\\d|5(?:[034]\\\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\\\d)|9(?:[05-9]\\\\d|2[0-5]|49)))\\\\d{4}|52[34][2-9]1[02-9]\\\\d{4}|(?:5(?:2[125-9]|33|44|66|77|88)|6(?:22|33))[2-9]\\\\d{6}\",[10]],0,[\"310\\\\d{4}\",[7]],0,[\"600[2-9]\\\\d{6}\",[10]]]],\"CC\":[\"61\",\"001[14-689]|14(?:1[14]|34|4[17]|[56]6|7[47]|88)0011\",\"1(?:[0-79]\\\\d{8}(?:\\\\d{2})?|8[0-24-9]\\\\d{7})|[148]\\\\d{8}|1\\\\d{5,7}\",[6,7,8,9,10,12],0,\"0\",0,\"([59]\\\\d{7})$|0\",\"8$1\",0,0,[[\"8(?:51(?:0(?:02|31|60|89)|1(?:18|76)|223)|91(?:0(?:1[0-2]|29)|1(?:[28]2|50|79)|2(?:10|64)|3(?:[06]8|22)|4[29]8|62\\\\d|70[23]|959))\\\\d{3}\",[9]],[\"4(?:79[01]|83[0-389]|94[0-478])\\\\d{5}|4(?:[0-36]\\\\d|4[047-9]|5[0-25-9]|7[02-8]|8[0-24-9]|9[0-37-9])\\\\d{6}\",[9]],[\"180(?:0\\\\d{3}|2)\\\\d{3}\",[7,10]],[\"190[0-26]\\\\d{6}\",[10]],0,0,0,0,[\"14(?:5(?:1[0458]|[23][458])|71\\\\d)\\\\d{4}\",[9]],[\"13(?:00\\\\d{6}(?:\\\\d{2})?|45[0-4]\\\\d{3})|13\\\\d{4}\",[6,8,10,12]]],\"0011\"],\"CD\":[\"243\",\"00\",\"(?:(?:[189]|5\\\\d)\\\\d|2)\\\\d{7}|[1-68]\\\\d{6}\",[7,8,9,10],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"88\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{5})\",\"$1 $2\",[\"[1-6]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3\",[\"2\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[89]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"5\"],\"0$1\"]],\"0\"],\"CF\":[\"236\",\"00\",\"(?:[27]\\\\d{3}|8776)\\\\d{4}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[278]\"]]]],\"CG\":[\"242\",\"00\",\"222\\\\d{6}|(?:0\\\\d|80)\\\\d{7}\",[9],[[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"8\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[02]\"]]]],\"CH\":[\"41\",\"00\",\"8\\\\d{11}|[2-9]\\\\d{8}\",[9,12],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"8[047]|90\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[2-79]|81\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4 $5\",[\"8\"],\"0$1\"]],\"0\"],\"CI\":[\"225\",\"00\",\"[02]\\\\d{9}\",[10],[[\"(\\\\d{2})(\\\\d{2})(\\\\d)(\\\\d{5})\",\"$1 $2 $3 $4\",[\"2\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3 $4\",[\"0\"]]]],\"CK\":[\"682\",\"00\",\"[2-578]\\\\d{4}\",[5],[[\"(\\\\d{2})(\\\\d{3})\",\"$1 $2\",[\"[2-578]\"]]]],\"CL\":[\"56\",\"(?:0|1(?:1[0-69]|2[02-5]|5[13-58]|69|7[0167]|8[018]))0\",\"12300\\\\d{6}|6\\\\d{9,10}|[2-9]\\\\d{8}\",[9,10,11],[[\"(\\\\d{5})(\\\\d{4})\",\"$1 $2\",[\"219\",\"2196\"],\"($1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"44\"]],[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"2[1-36]\"],\"($1)\"],[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"9[2-9]\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"3[2-5]|[47]|5[1-3578]|6[13-57]|8(?:0[1-9]|[1-9])\"],\"($1)\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"60|8\"]],[\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"60\"]]]],\"CM\":[\"237\",\"00\",\"[26]\\\\d{8}|88\\\\d{6,7}\",[8,9],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"88\"]],[\"(\\\\d)(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4 $5\",[\"[26]|88\"]]]],\"CN\":[\"86\",\"00|1(?:[12]\\\\d|79)\\\\d\\\\d00\",\"(?:(?:1[03-689]|2\\\\d)\\\\d\\\\d|6)\\\\d{8}|1\\\\d{10}|[126]\\\\d{6}(?:\\\\d(?:\\\\d{2})?)?|86\\\\d{5,6}|(?:[3-579]\\\\d|8[0-57-9])\\\\d{5,9}\",[7,8,9,10,11,12],[[\"(\\\\d{2})(\\\\d{5,6})\",\"$1 $2\",[\"(?:10|2[0-57-9])[19]|3(?:[157]|35|49|9[1-68])|4(?:1[124-9]|2[179]|6[47-9]|7|8[23])|5(?:[1357]|2[37]|4[36]|6[1-46]|80)|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:07|1[236-8]|2[5-7]|[37]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|3|4[13]|5[1-5]|7[0-79]|9[0-35-9])|(?:4[35]|59|85)[1-9]\",\"(?:10|2[0-57-9])(?:1[02]|9[56])|8078|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:1[124-9]|2[179]|[35][1-9]|6[47-9]|7\\\\d|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[1-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|3\\\\d|4[13]|5[1-5]|7[0-79]|9[0-35-9]))1\",\"10(?:1(?:0|23)|9[56])|2[0-57-9](?:1(?:00|23)|9[56])|80781|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:1[124-9]|2[179]|[35][1-9]|6[47-9]|7\\\\d|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[1-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|3\\\\d|4[13]|5[1-5]|7[0-79]|9[0-35-9]))12\",\"10(?:1(?:0|23)|9[56])|2[0-57-9](?:1(?:00|23)|9[56])|807812|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:1[124-9]|2[179]|[35][1-9]|6[47-9]|7\\\\d|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[1-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|3\\\\d|4[13]|5[1-5]|7[0-79]|9[0-35-9]))123\",\"10(?:1(?:0|23)|9[56])|2[0-57-9](?:1(?:00|23)|9[56])|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:1[124-9]|2[179]|[35][1-9]|6[47-9]|7\\\\d|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:078|1[236-8]|2[5-7]|[37]\\\\d|5[1-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|3\\\\d|4[13]|5[1-5]|7[0-79]|9[0-35-9]))123\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{5,6})\",\"$1 $2\",[\"3(?:[157]|35|49|9[1-68])|4(?:[17]|2[179]|6[47-9]|8[23])|5(?:[1357]|2[37]|4[36]|6[1-46]|80)|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]|4[13]|5[1-5])|(?:4[35]|59|85)[1-9]\",\"(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:[17]\\\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[1-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\\\d|4[13]|5[1-5]))[19]\",\"85[23](?:10|95)|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:[17]\\\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[14-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\\\d|4[13]|5[1-5]))(?:10|9[56])\",\"85[23](?:100|95)|(?:3(?:[157]\\\\d|35|49|9[1-68])|4(?:[17]\\\\d|2[179]|[35][1-9]|6[47-9]|8[23])|5(?:[1357]\\\\d|2[37]|4[36]|6[1-46]|80|9[1-9])|6(?:3[1-5]|6[0238]|9[12])|7(?:01|[1579]\\\\d|2[248]|3[014-9]|4[3-6]|6[023689])|8(?:1[236-8]|2[5-7]|[37]\\\\d|5[14-9]|8[36-8]|9[1-8])|9(?:0[1-3689]|1[1-79]|[379]\\\\d|4[13]|5[1-5]))(?:100|9[56])\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"(?:4|80)0\"]],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"10|2(?:[02-57-9]|1[1-9])\",\"10|2(?:[02-57-9]|1[1-9])\",\"10[0-79]|2(?:[02-57-9]|1[1-79])|(?:10|21)8(?:0[1-9]|[1-9])\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"3(?:[3-59]|7[02-68])|4(?:[26-8]|3[3-9]|5[2-9])|5(?:3[03-9]|[468]|7[028]|9[2-46-9])|6|7(?:[0-247]|3[04-9]|5[0-4689]|6[2368])|8(?:[1-358]|9[1-7])|9(?:[013479]|5[1-5])|(?:[34]1|55|79|87)[02-9]\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{7,8})\",\"$1 $2\",[\"9\"]],[\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"80\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"[3-578]\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"1[3-9]\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3 $4\",[\"[12]\"],\"0$1\",1]],\"0\",0,\"(1(?:[12]\\\\d|79)\\\\d\\\\d)|0\",0,0,0,0,\"00\"],\"CO\":[\"57\",\"00(?:4(?:[14]4|56)|[579])\",\"(?:46|60\\\\d\\\\d)\\\\d{6}|(?:1\\\\d|[39])\\\\d{9}\",[8,10,11],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"46\"]],[\"(\\\\d{3})(\\\\d{7})\",\"$1 $2\",[\"6|90\"],\"($1)\"],[\"(\\\\d{3})(\\\\d{7})\",\"$1 $2\",[\"3[0-357]|9[14]\"]],[\"(\\\\d)(\\\\d{3})(\\\\d{7})\",\"$1-$2-$3\",[\"1\"],\"0$1\",0,\"$1 $2 $3\"]],\"0\",0,\"0([3579]|4(?:[14]4|56))?\"],\"CR\":[\"506\",\"00\",\"(?:8\\\\d|90)\\\\d{8}|(?:[24-8]\\\\d{3}|3005)\\\\d{4}\",[8,10],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[2-7]|8[3-9]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1-$2-$3\",[\"[89]\"]]],0,0,\"(19(?:0[0-2468]|1[09]|20|66|77|99))\"],\"CU\":[\"53\",\"119\",\"(?:[2-7]|8\\\\d\\\\d)\\\\d{7}|[2-47]\\\\d{6}|[34]\\\\d{5}\",[6,7,8,10],[[\"(\\\\d{2})(\\\\d{4,6})\",\"$1 $2\",[\"2[1-4]|[34]\"],\"(0$1)\"],[\"(\\\\d)(\\\\d{6,7})\",\"$1 $2\",[\"7\"],\"(0$1)\"],[\"(\\\\d)(\\\\d{7})\",\"$1 $2\",[\"[56]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{7})\",\"$1 $2\",[\"8\"],\"0$1\"]],\"0\"],\"CV\":[\"238\",\"0\",\"(?:[2-59]\\\\d\\\\d|800)\\\\d{4}\",[7],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3\",[\"[2-589]\"]]]],\"CW\":[\"599\",\"00\",\"(?:[34]1|60|(?:7|9\\\\d)\\\\d)\\\\d{5}\",[7,8],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[3467]\"]],[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"9[4-8]\"]]],0,0,0,0,0,\"[69]\"],\"CX\":[\"61\",\"001[14-689]|14(?:1[14]|34|4[17]|[56]6|7[47]|88)0011\",\"1(?:[0-79]\\\\d{8}(?:\\\\d{2})?|8[0-24-9]\\\\d{7})|[148]\\\\d{8}|1\\\\d{5,7}\",[6,7,8,9,10,12],0,\"0\",0,\"([59]\\\\d{7})$|0\",\"8$1\",0,0,[[\"8(?:51(?:0(?:01|30|59|88)|1(?:17|46|75)|2(?:22|35))|91(?:00[6-9]|1(?:[28]1|49|78)|2(?:09|63)|3(?:12|26|75)|4(?:56|97)|64\\\\d|7(?:0[01]|1[0-2])|958))\\\\d{3}\",[9]],[\"4(?:79[01]|83[0-389]|94[0-478])\\\\d{5}|4(?:[0-36]\\\\d|4[047-9]|5[0-25-9]|7[02-8]|8[0-24-9]|9[0-37-9])\\\\d{6}\",[9]],[\"180(?:0\\\\d{3}|2)\\\\d{3}\",[7,10]],[\"190[0-26]\\\\d{6}\",[10]],0,0,0,0,[\"14(?:5(?:1[0458]|[23][458])|71\\\\d)\\\\d{4}\",[9]],[\"13(?:00\\\\d{6}(?:\\\\d{2})?|45[0-4]\\\\d{3})|13\\\\d{4}\",[6,8,10,12]]],\"0011\"],\"CY\":[\"357\",\"00\",\"(?:[279]\\\\d|[58]0)\\\\d{6}\",[8],[[\"(\\\\d{2})(\\\\d{6})\",\"$1 $2\",[\"[257-9]\"]]]],\"CZ\":[\"420\",\"00\",\"(?:[2-578]\\\\d|60)\\\\d{7}|9\\\\d{8,11}\",[9,10,11,12],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[2-8]|9[015-7]\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"96\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"9\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"9\"]]]],\"DE\":[\"49\",\"00\",\"[2579]\\\\d{5,14}|49(?:[34]0|69|8\\\\d)\\\\d\\\\d?|49(?:37|49|60|7[089]|9\\\\d)\\\\d{1,3}|49(?:2[024-9]|3[2-689]|7[1-7])\\\\d{1,8}|(?:1|[368]\\\\d|4[0-8])\\\\d{3,13}|49(?:[015]\\\\d|2[13]|31|[46][1-8])\\\\d{1,9}\",[4,5,6,7,8,9,10,11,12,13,14,15],[[\"(\\\\d{2})(\\\\d{3,13})\",\"$1 $2\",[\"3[02]|40|[68]9\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3,12})\",\"$1 $2\",[\"2(?:0[1-389]|1[124]|2[18]|3[14])|3(?:[35-9][15]|4[015])|906|(?:2[4-9]|4[2-9]|[579][1-9]|[68][1-8])1\",\"2(?:0[1-389]|12[0-8])|3(?:[35-9][15]|4[015])|906|2(?:[13][14]|2[18])|(?:2[4-9]|4[2-9]|[579][1-9]|[68][1-8])1\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{2,11})\",\"$1 $2\",[\"[24-6]|3(?:[3569][02-46-9]|4[2-4679]|7[2-467]|8[2-46-8])|70[2-8]|8(?:0[2-9]|[1-8])|90[7-9]|[79][1-9]\",\"[24-6]|3(?:3(?:0[1-467]|2[127-9]|3[124578]|7[1257-9]|8[1256]|9[145])|4(?:2[135]|4[13578]|9[1346])|5(?:0[14]|2[1-3589]|6[1-4]|7[13468]|8[13568])|6(?:2[1-489]|3[124-6]|6[13]|7[12579]|8[1-356]|9[135])|7(?:2[1-7]|4[145]|6[1-5]|7[1-4])|8(?:21|3[1468]|6|7[1467]|8[136])|9(?:0[12479]|2[1358]|4[134679]|6[1-9]|7[136]|8[147]|9[1468]))|70[2-8]|8(?:0[2-9]|[1-8])|90[7-9]|[79][1-9]|3[68]4[1347]|3(?:47|60)[1356]|3(?:3[46]|46|5[49])[1246]|3[4579]3[1357]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"138\"],\"0$1\"],[\"(\\\\d{5})(\\\\d{2,10})\",\"$1 $2\",[\"3\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{5,11})\",\"$1 $2\",[\"181\"],\"0$1\"],[\"(\\\\d{3})(\\\\d)(\\\\d{4,10})\",\"$1 $2 $3\",[\"1(?:3|80)|9\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{7,8})\",\"$1 $2\",[\"1[67]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{7,12})\",\"$1 $2\",[\"8\"],\"0$1\"],[\"(\\\\d{5})(\\\\d{6})\",\"$1 $2\",[\"185\",\"1850\",\"18500\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"7\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{7})\",\"$1 $2\",[\"18[68]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{7})\",\"$1 $2\",[\"15[1279]\"],\"0$1\"],[\"(\\\\d{5})(\\\\d{6})\",\"$1 $2\",[\"15[03568]\",\"15(?:[0568]|31)\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{8})\",\"$1 $2\",[\"18\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{7,8})\",\"$1 $2 $3\",[\"1(?:6[023]|7)\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{2})(\\\\d{7})\",\"$1 $2 $3\",[\"15[279]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{8})\",\"$1 $2 $3\",[\"15\"],\"0$1\"]],\"0\"],\"DJ\":[\"253\",\"00\",\"(?:2\\\\d|77)\\\\d{6}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[27]\"]]]],\"DK\":[\"45\",\"00\",\"[2-9]\\\\d{7}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[2-9]\"]]]],\"DM\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|767|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-7]\\\\d{6})$|1\",\"767$1\",0,\"767\"],\"DO\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,0,0,0,\"8001|8[024]9\"],\"DZ\":[\"213\",\"00\",\"(?:[1-4]|[5-79]\\\\d|80)\\\\d{7}\",[8,9],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[1-4]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"9\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[5-8]\"],\"0$1\"]],\"0\"],\"EC\":[\"593\",\"00\",\"1\\\\d{9,10}|(?:[2-7]|9\\\\d)\\\\d{7}\",[8,9,10,11],[[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2-$3\",[\"[2-7]\"],\"(0$1)\",0,\"$1-$2-$3\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"9\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"1\"]]],\"0\"],\"EE\":[\"372\",\"00\",\"8\\\\d{9}|[4578]\\\\d{7}|(?:[3-8]\\\\d|90)\\\\d{5}\",[7,8,10],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[369]|4[3-8]|5(?:[0-2]|5[0-478]|6[45])|7[1-9]|88\",\"[369]|4[3-8]|5(?:[02]|1(?:[0-8]|95)|5[0-478]|6(?:4[0-4]|5[1-589]))|7[1-9]|88\"]],[\"(\\\\d{4})(\\\\d{3,4})\",\"$1 $2\",[\"[45]|8(?:00|[1-49])\",\"[45]|8(?:00[1-9]|[1-49])\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3\",[\"7\"]],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"8\"]]]],\"EG\":[\"20\",\"00\",\"[189]\\\\d{8,9}|[24-6]\\\\d{8}|[135]\\\\d{7}\",[8,9,10],[[\"(\\\\d)(\\\\d{7,8})\",\"$1 $2\",[\"[23]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{6,7})\",\"$1 $2\",[\"1[35]|[4-6]|8[2468]|9[235-7]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[89]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{8})\",\"$1 $2\",[\"1\"],\"0$1\"]],\"0\"],\"EH\":[\"212\",\"00\",\"[5-8]\\\\d{8}\",[9],0,\"0\",0,0,0,0,\"528[89]\"],\"ER\":[\"291\",\"00\",\"[178]\\\\d{6}\",[7],[[\"(\\\\d)(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[178]\"],\"0$1\"]],\"0\"],\"ES\":[\"34\",\"00\",\"[5-9]\\\\d{8}\",[9],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[89]00\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[5-9]\"]]]],\"ET\":[\"251\",\"00\",\"(?:11|[2-579]\\\\d)\\\\d{7}\",[9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[1-579]\"],\"0$1\"]],\"0\"],\"FI\":[\"358\",\"00|99(?:[01469]|5(?:[14]1|3[23]|5[59]|77|88|9[09]))\",\"[1-35689]\\\\d{4}|7\\\\d{10,11}|(?:[124-7]\\\\d|3[0-46-9])\\\\d{8}|[1-9]\\\\d{5,8}\",[5,6,7,8,9,10,11,12],[[\"(\\\\d{5})\",\"$1\",[\"20[2-59]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3,7})\",\"$1 $2\",[\"(?:[1-3]0|[68])0|70[07-9]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4,8})\",\"$1 $2\",[\"[14]|2[09]|50|7[135]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{6,10})\",\"$1 $2\",[\"7\"],\"0$1\"],[\"(\\\\d)(\\\\d{4,9})\",\"$1 $2\",[\"(?:19|[2568])[1-8]|3(?:0[1-9]|[1-9])|9\"],\"0$1\"]],\"0\",0,0,0,0,\"1[03-79]|[2-9]\",0,\"00\"],\"FJ\":[\"679\",\"0(?:0|52)\",\"45\\\\d{5}|(?:0800\\\\d|[235-9])\\\\d{6}\",[7,11],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[235-9]|45\"]],[\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"0\"]]],0,0,0,0,0,0,0,\"00\"],\"FK\":[\"500\",\"00\",\"[2-7]\\\\d{4}\",[5]],\"FM\":[\"691\",\"00\",\"(?:[39]\\\\d\\\\d|820)\\\\d{4}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[389]\"]]]],\"FO\":[\"298\",\"00\",\"[2-9]\\\\d{5}\",[6],[[\"(\\\\d{6})\",\"$1\",[\"[2-9]\"]]],0,0,\"(10(?:01|[12]0|88))\"],\"FR\":[\"33\",\"00\",\"[1-9]\\\\d{8}\",[9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"8\"],\"0 $1\"],[\"(\\\\d)(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4 $5\",[\"[1-79]\"],\"0$1\"]],\"0\"],\"GA\":[\"241\",\"00\",\"(?:[067]\\\\d|11)\\\\d{6}|[2-7]\\\\d{6}\",[7,8],[[\"(\\\\d)(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[2-7]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"0\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"11|[67]\"],\"0$1\"]],0,0,\"0(11\\\\d{6}|60\\\\d{6}|61\\\\d{6}|6[256]\\\\d{6}|7[467]\\\\d{6})\",\"$1\"],\"GB\":[\"44\",\"00\",\"[1-357-9]\\\\d{9}|[18]\\\\d{8}|8\\\\d{6}\",[7,9,10],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"800\",\"8001\",\"80011\",\"800111\",\"8001111\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3\",[\"845\",\"8454\",\"84546\",\"845464\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{6})\",\"$1 $2\",[\"800\"],\"0$1\"],[\"(\\\\d{5})(\\\\d{4,5})\",\"$1 $2\",[\"1(?:38|5[23]|69|76|94)\",\"1(?:(?:38|69)7|5(?:24|39)|768|946)\",\"1(?:3873|5(?:242|39[4-6])|(?:697|768)[347]|9467)\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{5,6})\",\"$1 $2\",[\"1(?:[2-69][02-9]|[78])\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"[25]|7(?:0|6[02-9])\",\"[25]|7(?:0|6(?:[03-9]|2[356]))\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{6})\",\"$1 $2\",[\"7\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[1389]\"],\"0$1\"]],\"0\",0,0,0,0,0,[[\"(?:1(?:1(?:3(?:[0-58]\\\\d\\\\d|73[0-35])|4(?:(?:[0-5]\\\\d|70)\\\\d|69[7-9])|(?:(?:5[0-26-9]|[78][0-49])\\\\d|6(?:[0-4]\\\\d|50))\\\\d)|(?:2(?:(?:0[024-9]|2[3-9]|3[3-79]|4[1-689]|[58][02-9]|6[0-47-9]|7[013-9]|9\\\\d)\\\\d|1(?:[0-7]\\\\d|8[0-3]))|(?:3(?:0\\\\d|1[0-8]|[25][02-9]|3[02-579]|[468][0-46-9]|7[1-35-79]|9[2-578])|4(?:0[03-9]|[137]\\\\d|[28][02-57-9]|4[02-69]|5[0-8]|[69][0-79])|5(?:0[1-35-9]|[16]\\\\d|2[024-9]|3[015689]|4[02-9]|5[03-9]|7[0-35-9]|8[0-468]|9[0-57-9])|6(?:0[034689]|1\\\\d|2[0-35689]|[38][013-9]|4[1-467]|5[0-69]|6[13-9]|7[0-8]|9[0-24578])|7(?:0[0246-9]|2\\\\d|3[0236-8]|4[03-9]|5[0-46-9]|6[013-9]|7[0-35-9]|8[024-9]|9[02-9])|8(?:0[35-9]|2[1-57-9]|3[02-578]|4[0-578]|5[124-9]|6[2-69]|7\\\\d|8[02-9]|9[02569])|9(?:0[02-589]|[18]\\\\d|2[02-689]|3[1-57-9]|4[2-9]|5[0-579]|6[2-47-9]|7[0-24578]|9[2-57]))\\\\d)\\\\d)|2(?:0[013478]|3[0189]|4[017]|8[0-46-9]|9[0-2])\\\\d{3})\\\\d{4}|1(?:2(?:0(?:46[1-4]|87[2-9])|545[1-79]|76(?:2\\\\d|3[1-8]|6[1-6])|9(?:7(?:2[0-4]|3[2-5])|8(?:2[2-8]|7[0-47-9]|8[3-5])))|3(?:6(?:38[2-5]|47[23])|8(?:47[04-9]|64[0157-9]))|4(?:044[1-7]|20(?:2[23]|8\\\\d)|6(?:0(?:30|5[2-57]|6[1-8]|7[2-8])|140)|8(?:052|87[1-3]))|5(?:2(?:4(?:3[2-79]|6\\\\d)|76\\\\d)|6(?:26[06-9]|686))|6(?:06(?:4\\\\d|7[4-79])|295[5-7]|35[34]\\\\d|47(?:24|61)|59(?:5[08]|6[67]|74)|9(?:55[0-4]|77[23]))|7(?:26(?:6[13-9]|7[0-7])|(?:442|688)\\\\d|50(?:2[0-3]|[3-68]2|76))|8(?:27[56]\\\\d|37(?:5[2-5]|8[239])|843[2-58])|9(?:0(?:0(?:6[1-8]|85)|52\\\\d)|3583|4(?:66[1-8]|9(?:2[01]|81))|63(?:23|3[1-4])|9561))\\\\d{3}\",[9,10]],[\"7(?:457[0-57-9]|700[01]|911[028])\\\\d{5}|7(?:[1-3]\\\\d\\\\d|4(?:[0-46-9]\\\\d|5[0-689])|5(?:0[0-8]|[13-9]\\\\d|2[0-35-9])|7(?:0[1-9]|[1-7]\\\\d|8[02-9]|9[0-689])|8(?:[014-9]\\\\d|[23][0-8])|9(?:[024-9]\\\\d|1[02-9]|3[0-689]))\\\\d{6}\",[10]],[\"80[08]\\\\d{7}|800\\\\d{6}|8001111\"],[\"(?:8(?:4[2-5]|7[0-3])|9(?:[01]\\\\d|8[2-49]))\\\\d{7}|845464\\\\d\",[7,10]],[\"70\\\\d{8}\",[10]],0,[\"(?:3[0347]|55)\\\\d{8}\",[10]],[\"76(?:464|652)\\\\d{5}|76(?:0[0-28]|2[356]|34|4[01347]|5[49]|6[0-369]|77|8[14]|9[139])\\\\d{6}\",[10]],[\"56\\\\d{8}\",[10]]],0,\" x\"],\"GD\":[\"1\",\"011\",\"(?:473|[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-9]\\\\d{6})$|1\",\"473$1\",0,\"473\"],\"GE\":[\"995\",\"00\",\"(?:[3-57]\\\\d\\\\d|800)\\\\d{6}\",[9],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"70\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"32\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[57]\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[348]\"],\"0$1\"]],\"0\"],\"GF\":[\"594\",\"00\",\"(?:[56]94\\\\d|7093)\\\\d{5}|(?:80|9\\\\d)\\\\d{7}\",[9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[5-7]|9[47]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[89]\"],\"0$1\"]],\"0\"],\"GG\":[\"44\",\"00\",\"(?:1481|[357-9]\\\\d{3})\\\\d{6}|8\\\\d{6}(?:\\\\d{2})?\",[7,9,10],0,\"0\",0,\"([25-9]\\\\d{5})$|0\",\"1481$1\",0,0,[[\"1481[25-9]\\\\d{5}\",[10]],[\"7(?:(?:781|839)\\\\d|911[17])\\\\d{5}\",[10]],[\"80[08]\\\\d{7}|800\\\\d{6}|8001111\"],[\"(?:8(?:4[2-5]|7[0-3])|9(?:[01]\\\\d|8[0-3]))\\\\d{7}|845464\\\\d\",[7,10]],[\"70\\\\d{8}\",[10]],0,[\"(?:3[0347]|55)\\\\d{8}\",[10]],[\"76(?:464|652)\\\\d{5}|76(?:0[0-28]|2[356]|34|4[01347]|5[49]|6[0-369]|77|8[14]|9[139])\\\\d{6}\",[10]],[\"56\\\\d{8}\",[10]]]],\"GH\":[\"233\",\"00\",\"(?:[235]\\\\d{3}|800)\\\\d{5}\",[8,9],[[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"8\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[235]\"],\"0$1\"]],\"0\"],\"GI\":[\"350\",\"00\",\"(?:[25]\\\\d|60)\\\\d{6}\",[8],[[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"2\"]]]],\"GL\":[\"299\",\"00\",\"(?:19|[2-689]\\\\d|70)\\\\d{4}\",[6],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3\",[\"19|[2-9]\"]]]],\"GM\":[\"220\",\"00\",\"[2-9]\\\\d{6}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[2-9]\"]]]],\"GN\":[\"224\",\"00\",\"722\\\\d{6}|(?:3|6\\\\d)\\\\d{7}\",[8,9],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"3\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[67]\"]]]],\"GP\":[\"590\",\"00\",\"(?:590\\\\d|7090)\\\\d{5}|(?:69|80|9\\\\d)\\\\d{7}\",[9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[5-79]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"8\"],\"0$1\"]],\"0\",0,0,0,0,0,[[\"590(?:0[1-68]|[14][0-24-9]|2[0-68]|3[1-9]|5[3-579]|[68][0-689]|7[08]|9\\\\d)\\\\d{4}\"],[\"(?:69(?:0\\\\d\\\\d|1(?:2[2-9]|3[0-5])|4(?:0[89]|1[2-6]|9\\\\d)|6(?:1[016-9]|5[0-4]|[67]\\\\d))|7090[0-4])\\\\d{4}\"],[\"80[0-5]\\\\d{6}\"],0,0,0,0,0,[\"9(?:(?:39[5-7]|76[018])\\\\d|475[0-6])\\\\d{4}\"]]],\"GQ\":[\"240\",\"00\",\"222\\\\d{6}|(?:3\\\\d|55|[89]0)\\\\d{7}\",[9],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[235]\"]],[\"(\\\\d{3})(\\\\d{6})\",\"$1 $2\",[\"[89]\"]]]],\"GR\":[\"30\",\"00\",\"5005000\\\\d{3}|8\\\\d{9,11}|(?:[269]\\\\d|70)\\\\d{8}\",[10,11,12],[[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"21|7\"]],[\"(\\\\d{4})(\\\\d{6})\",\"$1 $2\",[\"2(?:2|3[2-57-9]|4[2-469]|5[2-59]|6[2-9]|7[2-69]|8[2-49])|5\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[2689]\"]],[\"(\\\\d{3})(\\\\d{3,4})(\\\\d{5})\",\"$1 $2 $3\",[\"8\"]]]],\"GT\":[\"502\",\"00\",\"80\\\\d{6}|(?:1\\\\d{3}|[2-7])\\\\d{7}\",[8,11],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[2-8]\"]],[\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"]]]],\"GU\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|671|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-9]\\\\d{6})$|1\",\"671$1\",0,\"671\"],\"GW\":[\"245\",\"00\",\"[49]\\\\d{8}|4\\\\d{6}\",[7,9],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"40\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[49]\"]]]],\"GY\":[\"592\",\"001\",\"(?:[2-8]\\\\d{3}|9008)\\\\d{3}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[2-9]\"]]]],\"HK\":[\"852\",\"00(?:30|5[09]|[126-9]?)\",\"8[0-46-9]\\\\d{6,7}|9\\\\d{4,7}|(?:[2-7]|9\\\\d{3})\\\\d{7}\",[5,6,7,8,9,11],[[\"(\\\\d{3})(\\\\d{2,5})\",\"$1 $2\",[\"900\",\"9003\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[2-7]|8[1-4]|9(?:0[1-9]|[1-8])\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"8\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"9\"]]],0,0,0,0,0,0,0,\"00\"],\"HN\":[\"504\",\"00\",\"8\\\\d{10}|[237-9]\\\\d{7}\",[8,11],[[\"(\\\\d{4})(\\\\d{4})\",\"$1-$2\",[\"[237-9]\"]]]],\"HR\":[\"385\",\"00\",\"[2-69]\\\\d{8}|80\\\\d{5,7}|[1-79]\\\\d{7}|6\\\\d{6}\",[7,8,9],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"6[01]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2,3})\",\"$1 $2 $3\",[\"8\"],\"0$1\"],[\"(\\\\d)(\\\\d{4})(\\\\d{3})\",\"$1 $2 $3\",[\"1\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"6|7[245]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"9\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[2-57]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"8\"],\"0$1\"]],\"0\"],\"HT\":[\"509\",\"00\",\"[2-589]\\\\d{7}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3\",[\"[2-589]\"]]]],\"HU\":[\"36\",\"00\",\"[235-7]\\\\d{8}|[1-9]\\\\d{7}\",[8,9],[[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"],\"(06 $1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[27][2-9]|3[2-7]|4[24-9]|5[2-79]|6|8[2-57-9]|9[2-69]\"],\"(06 $1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[2-9]\"],\"06 $1\"]],\"06\"],\"ID\":[\"62\",\"00[89]\",\"00[1-9]\\\\d{9,14}|(?:[1-36]|8\\\\d{5})\\\\d{6}|00\\\\d{9}|[1-9]\\\\d{8,10}|[2-9]\\\\d{7}\",[7,8,9,10,11,12,13,14,15,16,17],[[\"(\\\\d)(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"15\"]],[\"(\\\\d{2})(\\\\d{5,9})\",\"$1 $2\",[\"2[124]|[36]1\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{5,7})\",\"$1 $2\",[\"800\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{5,8})\",\"$1 $2\",[\"[2-79]\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{3,4})(\\\\d{3})\",\"$1-$2-$3\",[\"8[1-35-9]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{6,8})\",\"$1 $2\",[\"1\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"804\"],\"0$1\"],[\"(\\\\d{3})(\\\\d)(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"80\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{4})(\\\\d{4,5})\",\"$1-$2-$3\",[\"8\"],\"0$1\"]],\"0\"],\"IE\":[\"353\",\"00\",\"(?:1\\\\d|[2569])\\\\d{6,8}|4\\\\d{6,9}|7\\\\d{8}|8\\\\d{8,9}\",[7,8,9,10],[[\"(\\\\d{2})(\\\\d{5})\",\"$1 $2\",[\"2[24-9]|47|58|6[237-9]|9[35-9]\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"[45]0\"],\"(0$1)\"],[\"(\\\\d)(\\\\d{3,4})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[2569]|4[1-69]|7[14]\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"70\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"81\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[78]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"1\"]],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"4\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3 $4\",[\"8\"],\"0$1\"]],\"0\"],\"IL\":[\"972\",\"0(?:0|1[2-9])\",\"1\\\\d{6}(?:\\\\d{3,5})?|[57]\\\\d{8}|[1-489]\\\\d{7}\",[7,8,9,10,11,12],[[\"(\\\\d{4})(\\\\d{3})\",\"$1-$2\",[\"125\"]],[\"(\\\\d{4})(\\\\d{2})(\\\\d{2})\",\"$1-$2-$3\",[\"121\"]],[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1-$2-$3\",[\"[2-489]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1-$2-$3\",[\"[57]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1-$2-$3\",[\"12\"]],[\"(\\\\d{4})(\\\\d{6})\",\"$1-$2\",[\"159\"]],[\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1-$2-$3-$4\",[\"1[7-9]\"]],[\"(\\\\d{3})(\\\\d{1,2})(\\\\d{3})(\\\\d{4})\",\"$1-$2 $3-$4\",[\"15\"]]],\"0\"],\"IM\":[\"44\",\"00\",\"1624\\\\d{6}|(?:[3578]\\\\d|90)\\\\d{8}\",[10],0,\"0\",0,\"([25-8]\\\\d{5})$|0\",\"1624$1\",0,\"74576|(?:16|7[56])24\"],\"IN\":[\"91\",\"00\",\"(?:000800|[2-9]\\\\d\\\\d)\\\\d{7}|1\\\\d{7,12}\",[8,9,10,11,12,13],[[\"(\\\\d{8})\",\"$1\",[\"5(?:0|2[23]|3[03]|[67]1|88)\",\"5(?:0|2(?:21|3)|3(?:0|3[23])|616|717|888)\",\"5(?:0|2(?:21|3)|3(?:0|3[23])|616|717|8888)\"],0,1],[\"(\\\\d{4})(\\\\d{4,5})\",\"$1 $2\",[\"180\",\"1800\"],0,1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"140\"],0,1],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"11|2[02]|33|4[04]|79[1-7]|80[2-46]\",\"11|2[02]|33|4[04]|79(?:[1-6]|7[19])|80(?:[2-4]|6[0-589])\",\"11|2[02]|33|4[04]|79(?:[124-6]|3(?:[02-9]|1[0-24-9])|7(?:1|9[1-6]))|80(?:[2-4]|6[0-589])\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1(?:2[0-249]|3[0-25]|4[145]|[68]|7[1257])|2(?:1[257]|3[013]|4[01]|5[0137]|6[0158]|78|8[1568])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|5[12]|[78]1)|6(?:12|[2-4]1|5[17]|6[13]|80)|7(?:12|3[134]|4[47]|61|88)|8(?:16|2[014]|3[126]|6[136]|7[078]|8[34]|91)|(?:43|59|75)[15]|(?:1[59]|29|67|72)[14]\",\"1(?:2[0-24]|3[0-25]|4[145]|[59][14]|6[1-9]|7[1257]|8[1-57-9])|2(?:1[257]|3[013]|4[01]|5[0137]|6[058]|78|8[1568]|9[14])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|3[15]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|[578]1|9[15])|674|7(?:(?:2[14]|3[34]|5[15])[2-6]|61[346]|88[0-8])|8(?:70[2-6]|84[235-7]|91[3-7])|(?:1(?:29|60|8[06])|261|552|6(?:12|[2-47]1|5[17]|6[13]|80)|7(?:12|31|4[47])|8(?:16|2[014]|3[126]|6[136]|7[78]|83))[2-7]\",\"1(?:2[0-24]|3[0-25]|4[145]|[59][14]|6[1-9]|7[1257]|8[1-57-9])|2(?:1[257]|3[013]|4[01]|5[0137]|6[058]|78|8[1568]|9[14])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|3[15]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|22|[36][25]|4[28]|[578]1|9[15])|6(?:12(?:[2-6]|7[0-8])|74[2-7])|7(?:(?:2[14]|5[15])[2-6]|3171|61[346]|88(?:[2-7]|82))|8(?:70[2-6]|84(?:[2356]|7[19])|91(?:[3-6]|7[19]))|73[134][2-6]|(?:74[47]|8(?:16|2[014]|3[126]|6[136]|7[78]|83))(?:[2-6]|7[19])|(?:1(?:29|60|8[06])|261|552|6(?:[2-4]1|5[17]|6[13]|7(?:1|4[0189])|80)|7(?:12|88[01]))[2-7]\"],\"0$1\",1],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"1(?:[2-479]|5[0235-9])|[2-5]|6(?:1[1358]|2[2457-9]|3[2-5]|4[235-7]|5[2-689]|6[24578]|7[235689]|8[1-6])|7(?:1[013-9]|28|3[129]|4[1-35689]|5[29]|6[02-5]|70)|807\",\"1(?:[2-479]|5[0235-9])|[2-5]|6(?:1[1358]|2(?:[2457]|84|95)|3(?:[2-4]|55)|4[235-7]|5[2-689]|6[24578]|7[235689]|8[1-6])|7(?:1(?:[013-8]|9[6-9])|28[6-8]|3(?:17|2[0-49]|9[2-57])|4(?:1[2-4]|[29][0-7]|3[0-8]|[56]|8[0-24-7])|5(?:2[1-3]|9[0-6])|6(?:0[5689]|2[5-9]|3[02-8]|4|5[0-367])|70[13-7])|807[19]\",\"1(?:[2-479]|5(?:[0236-9]|5[013-9]))|[2-5]|6(?:2(?:84|95)|355|83)|73179|807(?:1|9[1-3])|(?:1552|6(?:1[1358]|2[2457]|3[2-4]|4[235-7]|5[2-689]|6[24578]|7[235689]|8[124-6])\\\\d|7(?:1(?:[013-8]\\\\d|9[6-9])|28[6-8]|3(?:2[0-49]|9[2-57])|4(?:1[2-4]|[29][0-7]|3[0-8]|[56]\\\\d|8[0-24-7])|5(?:2[1-3]|9[0-6])|6(?:0[5689]|2[5-9]|3[02-8]|4\\\\d|5[0-367])|70[13-7]))[2-7]\"],\"0$1\",1],[\"(\\\\d{5})(\\\\d{5})\",\"$1 $2\",[\"[6-9]\"],\"0$1\",1],[\"(\\\\d{4})(\\\\d{2,4})(\\\\d{4})\",\"$1 $2 $3\",[\"1(?:6|8[06])\",\"1(?:6|8[06]0)\"],0,1],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"18\"],0,1]],\"0\"],\"IO\":[\"246\",\"00\",\"3\\\\d{6}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"3\"]]]],\"IQ\":[\"964\",\"00\",\"(?:1|7\\\\d\\\\d)\\\\d{7}|[2-6]\\\\d{7,8}\",[8,9,10],[[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[2-6]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"7\"],\"0$1\"]],\"0\"],\"IR\":[\"98\",\"00\",\"[1-9]\\\\d{9}|(?:[1-8]\\\\d\\\\d|9)\\\\d{3,4}\",[4,5,6,7,10],[[\"(\\\\d{4,5})\",\"$1\",[\"96\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4,5})\",\"$1 $2\",[\"(?:1[137]|2[13-68]|3[1458]|4[145]|5[1468]|6[16]|7[1467]|8[13467])[12689]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"9\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"[1-8]\"],\"0$1\"]],\"0\"],\"IS\":[\"354\",\"00|1(?:0(?:01|[12]0)|100)\",\"(?:38\\\\d|[4-9])\\\\d{6}\",[7,9],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[4-9]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"3\"]]],0,0,0,0,0,0,0,\"00\"],\"IT\":[\"39\",\"00\",\"0\\\\d{5,10}|1\\\\d{8,10}|3(?:[0-8]\\\\d{7,10}|9\\\\d{7,8})|(?:43|55|70)\\\\d{8}|8\\\\d{5}(?:\\\\d{2,4})?\",[6,7,8,9,10,11,12],[[\"(\\\\d{2})(\\\\d{4,6})\",\"$1 $2\",[\"0[26]\"]],[\"(\\\\d{3})(\\\\d{3,6})\",\"$1 $2\",[\"0[13-57-9][0159]|8(?:03|4[17]|9[2-5])\",\"0[13-57-9][0159]|8(?:03|4[17]|9(?:2|3[04]|[45][0-4]))\"]],[\"(\\\\d{4})(\\\\d{2,6})\",\"$1 $2\",[\"0(?:[13-579][2-46-8]|8[236-8])\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"894\"]],[\"(\\\\d{2})(\\\\d{3,4})(\\\\d{4})\",\"$1 $2 $3\",[\"0[26]|5\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"1(?:44|[679])|[378]|43\"]],[\"(\\\\d{3})(\\\\d{3,4})(\\\\d{4})\",\"$1 $2 $3\",[\"0[13-57-9][0159]|14\"]],[\"(\\\\d{2})(\\\\d{4})(\\\\d{5})\",\"$1 $2 $3\",[\"0[26]\"]],[\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"0\"]],[\"(\\\\d{3})(\\\\d{4})(\\\\d{4,5})\",\"$1 $2 $3\",[\"3\"]]],0,0,0,0,0,0,[[\"0669[0-79]\\\\d{1,6}|0(?:1(?:[0159]\\\\d|[27][1-5]|31|4[1-4]|6[1356]|8[2-57])|2\\\\d\\\\d|3(?:[0159]\\\\d|2[1-4]|3[12]|[48][1-6]|6[2-59]|7[1-7])|4(?:[0159]\\\\d|[23][1-9]|4[245]|6[1-5]|7[1-4]|81)|5(?:[0159]\\\\d|2[1-5]|3[2-6]|4[1-79]|6[4-6]|7[1-578]|8[3-8])|6(?:[0-57-9]\\\\d|6[0-8])|7(?:[0159]\\\\d|2[12]|3[1-7]|4[2-46]|6[13569]|7[13-6]|8[1-59])|8(?:[0159]\\\\d|2[3-578]|3[1-356]|[6-8][1-5])|9(?:[0159]\\\\d|[238][1-5]|4[12]|6[1-8]|7[1-6]))\\\\d{2,7}\",[6,7,8,9,10,11]],[\"3[2-9]\\\\d{7,8}|(?:31|43)\\\\d{8}\",[9,10]],[\"80(?:0\\\\d{3}|3)\\\\d{3}\",[6,9]],[\"(?:0878\\\\d{3}|89(?:2\\\\d|3[04]|4(?:[0-4]|[5-9]\\\\d\\\\d)|5[0-4]))\\\\d\\\\d|(?:1(?:44|6[346])|89(?:38|5[5-9]|9))\\\\d{6}\",[6,8,9,10]],[\"1(?:78\\\\d|99)\\\\d{6}\",[9,10]],[\"3[2-8]\\\\d{9,10}\",[11,12]],0,0,[\"55\\\\d{8}\",[10]],[\"84(?:[08]\\\\d{3}|[17])\\\\d{3}\",[6,9]]]],\"JE\":[\"44\",\"00\",\"1534\\\\d{6}|(?:[3578]\\\\d|90)\\\\d{8}\",[10],0,\"0\",0,\"([0-24-8]\\\\d{5})$|0\",\"1534$1\",0,0,[[\"1534[0-24-8]\\\\d{5}\"],[\"7(?:(?:(?:50|82)9|937)\\\\d|7(?:00[378]|97\\\\d))\\\\d{5}\"],[\"80(?:07(?:35|81)|8901)\\\\d{4}\"],[\"(?:8(?:4(?:4(?:4(?:05|42|69)|703)|5(?:041|800))|7(?:0002|1206))|90(?:066[59]|1810|71(?:07|55)))\\\\d{4}\"],[\"701511\\\\d{4}\"],0,[\"(?:3(?:0(?:07(?:35|81)|8901)|3\\\\d{4}|4(?:4(?:4(?:05|42|69)|703)|5(?:041|800))|7(?:0002|1206))|55\\\\d{4})\\\\d{4}\"],[\"76(?:464|652)\\\\d{5}|76(?:0[0-28]|2[356]|34|4[01347]|5[49]|6[0-369]|77|8[14]|9[139])\\\\d{6}\"],[\"56\\\\d{8}\"]]],\"JM\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|658|900)\\\\d{7}\",[10],0,\"1\",0,0,0,0,\"658|876\"],\"JO\":[\"962\",\"00\",\"(?:(?:[2689]|7\\\\d)\\\\d|32|53)\\\\d{6}\",[8,9],[[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[2356]|87\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{5,6})\",\"$1 $2\",[\"[89]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{7})\",\"$1 $2\",[\"70\"],\"0$1\"],[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"7\"],\"0$1\"]],\"0\"],\"JP\":[\"81\",\"010\",\"00[1-9]\\\\d{6,14}|[257-9]\\\\d{9}|(?:00|[1-9]\\\\d\\\\d)\\\\d{6}\",[8,9,10,11,12,13,14,15,16,17],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1-$2-$3\",[\"(?:12|57|99)0\"],\"0$1\"],[\"(\\\\d{4})(\\\\d)(\\\\d{4})\",\"$1-$2-$3\",[\"1(?:26|3[79]|4[56]|5[4-68]|6[3-5])|499|5(?:76|97)|746|8(?:3[89]|47|51)|9(?:80|9[16])\",\"1(?:267|3(?:7[247]|9[278])|466|5(?:47|58|64)|6(?:3[245]|48|5[4-68]))|499[2468]|5(?:76|97)9|7468|8(?:3(?:8[7-9]|96)|477|51[2-9])|9(?:802|9(?:1[23]|69))|1(?:45|58)[67]\",\"1(?:267|3(?:7[247]|9[278])|466|5(?:47|58|64)|6(?:3[245]|48|5[4-68]))|499[2468]|5(?:769|979[2-69])|7468|8(?:3(?:8[7-9]|96[2457-9])|477|51[2-9])|9(?:802|9(?:1[23]|69))|1(?:45|58)[67]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1-$2-$3\",[\"60\"],\"0$1\"],[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1-$2-$3\",[\"[36]|4(?:2[09]|7[01])\",\"[36]|4(?:2(?:0|9[02-69])|7(?:0[019]|1))\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1-$2-$3\",[\"1(?:1|5[45]|77|88|9[69])|2(?:2[1-37]|3[0-269]|4[59]|5|6[24]|7[1-358]|8[1369]|9[0-38])|4(?:[28][1-9]|3[0-57]|[45]|6[248]|7[2-579]|9[29])|5(?:2|3[0459]|4[0-369]|5[29]|8[02389]|9[0-389])|7(?:2[02-46-9]|34|[58]|6[0249]|7[57]|9[2-6])|8(?:2[124589]|3[26-9]|49|51|6|7[0-468]|8[68]|9[019])|9(?:[23][1-9]|4[15]|5[138]|6[1-3]|7[156]|8[189]|9[1-489])\",\"1(?:1|5(?:4[018]|5[017])|77|88|9[69])|2(?:2(?:[127]|3[014-9])|3[0-269]|4[59]|5(?:[1-3]|5[0-69]|9[19])|62|7(?:[1-35]|8[0189])|8(?:[16]|3[0134]|9[0-5])|9(?:[028]|17))|4(?:2(?:[13-79]|8[014-6])|3[0-57]|[45]|6[248]|7[2-47]|8[1-9]|9[29])|5(?:2|3(?:[045]|9[0-8])|4[0-369]|5[29]|8[02389]|9[0-3])|7(?:2[02-46-9]|34|[58]|6[0249]|7[57]|9(?:[23]|4[0-59]|5[01569]|6[0167]))|8(?:2(?:[1258]|4[0-39]|9[0-2469])|3(?:[29]|60)|49|51|6(?:[0-24]|36|5[0-3589]|7[23]|9[01459])|7[0-468]|8[68])|9(?:[23][1-9]|4[15]|5[138]|6[1-3]|7[156]|8[189]|9(?:[1289]|3[34]|4[0178]))|(?:264|837)[016-9]|2(?:57|93)[015-9]|(?:25[0468]|422|838)[01]|(?:47[59]|59[89]|8(?:6[68]|9))[019]\",\"1(?:1|5(?:4[018]|5[017])|77|88|9[69])|2(?:2[127]|3[0-269]|4[59]|5(?:[1-3]|5[0-69]|9(?:17|99))|6(?:2|4[016-9])|7(?:[1-35]|8[0189])|8(?:[16]|3[0134]|9[0-5])|9(?:[028]|17))|4(?:2(?:[13-79]|8[014-6])|3[0-57]|[45]|6[248]|7[2-47]|9[29])|5(?:2|3(?:[045]|9(?:[0-58]|6[4-9]|7[0-35689]))|4[0-369]|5[29]|8[02389]|9[0-3])|7(?:2[02-46-9]|34|[58]|6[0249]|7[57]|9(?:[23]|4[0-59]|5[01569]|6[0167]))|8(?:2(?:[1258]|4[0-39]|9[0169])|3(?:[29]|60|7(?:[017-9]|6[6-8]))|49|51|6(?:[0-24]|36[2-57-9]|5(?:[0-389]|5[23])|6(?:[01]|9[178])|7(?:2[2-468]|3[78])|9[0145])|7[0-468]|8[68])|9(?:4[15]|5[138]|7[156]|8[189]|9(?:[1289]|3(?:31|4[357])|4[0178]))|(?:8294|96)[1-3]|2(?:57|93)[015-9]|(?:223|8699)[014-9]|(?:25[0468]|422|838)[01]|(?:48|8292|9[23])[1-9]|(?:47[59]|59[89]|8(?:68|9))[019]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{4})\",\"$1-$2-$3\",[\"[14]|[289][2-9]|5[3-9]|7[2-4679]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1-$2-$3\",[\"800\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1-$2-$3\",[\"[257-9]\"],\"0$1\"]],\"0\",0,\"(000[259]\\\\d{6})$|(?:(?:003768)0?)|0\",\"$1\"],\"KE\":[\"254\",\"000\",\"(?:[17]\\\\d\\\\d|900)\\\\d{6}|(?:2|80)0\\\\d{6,7}|[4-6]\\\\d{6,8}\",[7,8,9,10],[[\"(\\\\d{2})(\\\\d{5,7})\",\"$1 $2\",[\"[24-6]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{6})\",\"$1 $2\",[\"[17]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[89]\"],\"0$1\"]],\"0\"],\"KG\":[\"996\",\"00\",\"8\\\\d{9}|[235-9]\\\\d{8}\",[9,10],[[\"(\\\\d{4})(\\\\d{5})\",\"$1 $2\",[\"3(?:1[346]|[24-79])\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[235-79]|88\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d)(\\\\d{2,3})\",\"$1 $2 $3 $4\",[\"8\"],\"0$1\"]],\"0\"],\"KH\":[\"855\",\"00[14-9]\",\"1\\\\d{9}|[1-9]\\\\d{7,8}\",[8,9,10],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[1-9]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"1\"]]],\"0\"],\"KI\":[\"686\",\"00\",\"(?:[37]\\\\d|6[0-79])\\\\d{6}|(?:[2-48]\\\\d|50)\\\\d{3}\",[5,8],0,\"0\"],\"KM\":[\"269\",\"00\",\"[3478]\\\\d{6}\",[7],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3\",[\"[3478]\"]]]],\"KN\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-7]\\\\d{6})$|1\",\"869$1\",0,\"869\"],\"KP\":[\"850\",\"00|99\",\"85\\\\d{6}|(?:19\\\\d|[2-7])\\\\d{7}\",[8,10],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"8\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[2-7]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"],\"0$1\"]],\"0\"],\"KR\":[\"82\",\"00(?:[125689]|3(?:[46]5|91)|7(?:00|27|3|55|6[126]))\",\"00[1-9]\\\\d{8,11}|(?:[12]|5\\\\d{3})\\\\d{7}|[13-6]\\\\d{9}|(?:[1-6]\\\\d|80)\\\\d{7}|[3-6]\\\\d{4,5}|(?:00|7)0\\\\d{8}\",[5,6,8,9,10,11,12,13,14],[[\"(\\\\d{2})(\\\\d{3,4})\",\"$1-$2\",[\"(?:3[1-3]|[46][1-4]|5[1-5])1\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{4})\",\"$1-$2\",[\"1\"]],[\"(\\\\d)(\\\\d{3,4})(\\\\d{4})\",\"$1-$2-$3\",[\"2\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1-$2-$3\",[\"[36]0|8\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3,4})(\\\\d{4})\",\"$1-$2-$3\",[\"[1346]|5[1-5]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1-$2-$3\",[\"[57]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{5})(\\\\d{4})\",\"$1-$2-$3\",[\"5\"],\"0$1\"]],\"0\",0,\"0(8(?:[1-46-8]|5\\\\d\\\\d))?\"],\"KW\":[\"965\",\"00\",\"18\\\\d{5}|(?:[2569]\\\\d|41)\\\\d{6}\",[7,8],[[\"(\\\\d{4})(\\\\d{3,4})\",\"$1 $2\",[\"[169]|2(?:[235]|4[1-35-9])|52\"]],[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"[245]\"]]]],\"KY\":[\"1\",\"011\",\"(?:345|[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-9]\\\\d{6})$|1\",\"345$1\",0,\"345\"],\"KZ\":[\"7\",\"810\",\"(?:33622|8\\\\d{8})\\\\d{5}|[78]\\\\d{9}\",[10,14],0,\"8\",0,0,0,0,\"33622|7\",0,\"8~10\"],\"LA\":[\"856\",\"00\",\"[23]\\\\d{9}|3\\\\d{8}|(?:[235-8]\\\\d|41)\\\\d{6}\",[8,9,10],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"2[13]|3[14]|[4-8]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"3\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"[23]\"],\"0$1\"]],\"0\"],\"LB\":[\"961\",\"00\",\"[27-9]\\\\d{7}|[13-9]\\\\d{6}\",[7,8],[[\"(\\\\d)(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[13-69]|7(?:[2-57]|62|8[0-6]|9[04-9])|8[02-9]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[27-9]\"]]],\"0\"],\"LC\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|758|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-8]\\\\d{6})$|1\",\"758$1\",0,\"758\"],\"LI\":[\"423\",\"00\",\"[68]\\\\d{8}|(?:[2378]\\\\d|90)\\\\d{5}\",[7,9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3\",[\"[2379]|8(?:0[09]|7)\",\"[2379]|8(?:0(?:02|9)|7)\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"8\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"69\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"6\"]]],\"0\",0,\"(1001)|0\"],\"LK\":[\"94\",\"00\",\"[1-9]\\\\d{8}\",[9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"7\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[1-689]\"],\"0$1\"]],\"0\"],\"LR\":[\"231\",\"00\",\"(?:[2457]\\\\d|33|88)\\\\d{7}|(?:2\\\\d|[4-6])\\\\d{6}\",[7,8,9],[[\"(\\\\d)(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"4[67]|[56]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"2\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[2-578]\"],\"0$1\"]],\"0\"],\"LS\":[\"266\",\"00\",\"(?:[256]\\\\d\\\\d|800)\\\\d{5}\",[8],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[2568]\"]]]],\"LT\":[\"370\",\"00\",\"(?:[3469]\\\\d|52|[78]0)\\\\d{6}\",[8],[[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"52[0-7]\"],\"(0-$1)\",1],[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"[7-9]\"],\"0 $1\",1],[\"(\\\\d{2})(\\\\d{6})\",\"$1 $2\",[\"37|4(?:[15]|6[1-8])\"],\"(0-$1)\",1],[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"[3-6]\"],\"(0-$1)\",1]],\"0\",0,\"[08]\"],\"LU\":[\"352\",\"00\",\"35[013-9]\\\\d{4,8}|6\\\\d{8}|35\\\\d{2,4}|(?:[2457-9]\\\\d|3[0-46-9])\\\\d{2,9}\",[4,5,6,7,8,9,10,11],[[\"(\\\\d{2})(\\\\d{3})\",\"$1 $2\",[\"2(?:0[2-689]|[2-9])|[3-57]|8(?:0[2-9]|[13-9])|9(?:0[89]|[2-579])\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3\",[\"2(?:0[2-689]|[2-9])|[3-57]|8(?:0[2-9]|[13-9])|9(?:0[89]|[2-579])\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"20[2-689]\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{1,2})\",\"$1 $2 $3 $4\",[\"2(?:[0367]|4[3-8])\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"80[01]|90[015]\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"20\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"6\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{1,2})\",\"$1 $2 $3 $4 $5\",[\"2(?:[0367]|4[3-8])\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{1,5})\",\"$1 $2 $3 $4\",[\"[3-57]|8[13-9]|9(?:0[89]|[2-579])|(?:2|80)[2-9]\"]]],0,0,\"(15(?:0[06]|1[12]|[35]5|4[04]|6[26]|77|88|99)\\\\d)\"],\"LV\":[\"371\",\"00\",\"(?:[268]\\\\d|90)\\\\d{6}\",[8],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[269]|8[01]\"]]]],\"LY\":[\"218\",\"00\",\"[2-9]\\\\d{8}\",[9],[[\"(\\\\d{2})(\\\\d{7})\",\"$1-$2\",[\"[2-9]\"],\"0$1\"]],\"0\"],\"MA\":[\"212\",\"00\",\"[5-8]\\\\d{8}\",[9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"5[45]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{5})\",\"$1-$2\",[\"5(?:2[2-46-9]|3[3-9]|9)|8(?:0[89]|92)\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{7})\",\"$1-$2\",[\"8\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{6})\",\"$1-$2\",[\"[5-7]\"],\"0$1\"]],\"0\",0,0,0,0,0,[[\"5(?:2(?:[0-25-79]\\\\d|3[1-578]|4[02-46-8]|8[0235-7])|3(?:[0-47]\\\\d|5[02-9]|6[02-8]|8[014-9]|9[3-9])|(?:4[067]|5[03])\\\\d)\\\\d{5}\"],[\"(?:6(?:[0-79]\\\\d|8[0-247-9])|7(?:[0167]\\\\d|2[0-8]|5[0-3]|8[0-7]))\\\\d{6}\"],[\"80[0-7]\\\\d{6}\"],[\"89\\\\d{7}\"],0,0,0,0,[\"(?:592(?:4[0-2]|93)|80[89]\\\\d\\\\d)\\\\d{4}\"]]],\"MC\":[\"377\",\"00\",\"(?:[3489]|6\\\\d)\\\\d{7}\",[8,9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"4\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[389]\"]],[\"(\\\\d)(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4 $5\",[\"6\"],\"0$1\"]],\"0\"],\"MD\":[\"373\",\"00\",\"(?:[235-7]\\\\d|[89]0)\\\\d{6}\",[8],[[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"[89]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"22|3\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"[25-7]\"],\"0$1\"]],\"0\"],\"ME\":[\"382\",\"00\",\"(?:20|[3-79]\\\\d)\\\\d{6}|80\\\\d{6,7}\",[8,9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[2-9]\"],\"0$1\"]],\"0\"],\"MF\":[\"590\",\"00\",\"(?:590\\\\d|7090)\\\\d{5}|(?:69|80|9\\\\d)\\\\d{7}\",[9],0,\"0\",0,0,0,0,0,[[\"590(?:0[079]|[14]3|[27][79]|3[03-7]|5[0-268]|87)\\\\d{4}\"],[\"(?:69(?:0\\\\d\\\\d|1(?:2[2-9]|3[0-5])|4(?:0[89]|1[2-6]|9\\\\d)|6(?:1[016-9]|5[0-4]|[67]\\\\d))|7090[0-4])\\\\d{4}\"],[\"80[0-5]\\\\d{6}\"],0,0,0,0,0,[\"9(?:(?:39[5-7]|76[018])\\\\d|475[0-6])\\\\d{4}\"]]],\"MG\":[\"261\",\"00\",\"[23]\\\\d{8}\",[9],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[23]\"],\"0$1\"]],\"0\",0,\"([24-9]\\\\d{6})$|0\",\"20$1\"],\"MH\":[\"692\",\"011\",\"329\\\\d{4}|(?:[256]\\\\d|45)\\\\d{5}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1-$2\",[\"[2-6]\"]]],\"1\"],\"MK\":[\"389\",\"00\",\"[2-578]\\\\d{7}\",[8],[[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"2|34[47]|4(?:[37]7|5[47]|64)\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[347]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d)(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[58]\"],\"0$1\"]],\"0\"],\"ML\":[\"223\",\"00\",\"[24-9]\\\\d{7}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[24-9]\"]]]],\"MM\":[\"95\",\"00\",\"1\\\\d{5,7}|95\\\\d{6}|(?:[4-7]|9[0-46-9])\\\\d{6,8}|(?:2|8\\\\d)\\\\d{5,8}\",[6,7,8,9,10],[[\"(\\\\d)(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"16|2\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"4(?:[2-46]|5[3-5])|5|6(?:[1-689]|7[235-7])|7(?:[0-4]|5[2-7])|8[1-5]|(?:60|86)[23]\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[12]|452|678|86\",\"[12]|452|6788|86\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[4-7]|8[1-35]\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{4,6})\",\"$1 $2 $3\",[\"9(?:2[0-4]|[35-9]|4[137-9])\"],\"0$1\"],[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"2\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"8\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"92\"],\"0$1\"],[\"(\\\\d)(\\\\d{5})(\\\\d{4})\",\"$1 $2 $3\",[\"9\"],\"0$1\"]],\"0\"],\"MN\":[\"976\",\"001\",\"[12]\\\\d{7,9}|[5-9]\\\\d{7}\",[8,9,10],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3\",[\"[12]1\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[5-9]\"]],[\"(\\\\d{3})(\\\\d{5,6})\",\"$1 $2\",[\"[12]2[1-3]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{5,6})\",\"$1 $2\",[\"[12](?:27|3[2-8]|4[2-68]|5[1-4689])\",\"[12](?:27|3[2-8]|4[2-68]|5[1-4689])[0-3]\"],\"0$1\"],[\"(\\\\d{5})(\\\\d{4,5})\",\"$1 $2\",[\"[12]\"],\"0$1\"]],\"0\"],\"MO\":[\"853\",\"00\",\"0800\\\\d{3}|(?:28|[68]\\\\d)\\\\d{6}\",[7,8],[[\"(\\\\d{4})(\\\\d{3})\",\"$1 $2\",[\"0\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[268]\"]]]],\"MP\":[\"1\",\"011\",\"[58]\\\\d{9}|(?:67|90)0\\\\d{7}\",[10],0,\"1\",0,\"([2-9]\\\\d{6})$|1\",\"670$1\",0,\"670\"],\"MQ\":[\"596\",\"00\",\"(?:596\\\\d|7091)\\\\d{5}|(?:69|[89]\\\\d)\\\\d{7}\",[9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[5-79]|8(?:0[6-9]|[36])\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"8\"],\"0$1\"]],\"0\"],\"MR\":[\"222\",\"00\",\"(?:[2-4]\\\\d\\\\d|800)\\\\d{5}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[2-48]\"]]]],\"MS\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|664|900)\\\\d{7}\",[10],0,\"1\",0,\"([34]\\\\d{6})$|1\",\"664$1\",0,\"664\"],\"MT\":[\"356\",\"00\",\"3550\\\\d{4}|(?:[2579]\\\\d\\\\d|800)\\\\d{5}\",[8],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[2357-9]\"]]]],\"MU\":[\"230\",\"0(?:0|[24-7]0|3[03])\",\"(?:[57]|8\\\\d\\\\d)\\\\d{7}|[2-468]\\\\d{6}\",[7,8,10],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[2-46]|8[013]\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[57]\"]],[\"(\\\\d{5})(\\\\d{5})\",\"$1 $2\",[\"8\"]]],0,0,0,0,0,0,0,\"020\"],\"MV\":[\"960\",\"0(?:0|19)\",\"(?:800|9[0-57-9]\\\\d)\\\\d{7}|[34679]\\\\d{6}\",[7,10],[[\"(\\\\d{3})(\\\\d{4})\",\"$1-$2\",[\"[34679]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[89]\"]]],0,0,0,0,0,0,0,\"00\"],\"MW\":[\"265\",\"00\",\"(?:[1289]\\\\d|31|77)\\\\d{7}|1\\\\d{6}\",[7,9],[[\"(\\\\d)(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"1[2-9]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"2\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[137-9]\"],\"0$1\"]],\"0\"],\"MX\":[\"52\",\"0[09]\",\"[2-9]\\\\d{9}\",[10],[[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"33|5[56]|81\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[2-9]\"]]],0,0,0,0,0,0,0,\"00\"],\"MY\":[\"60\",\"00\",\"1\\\\d{8,9}|(?:3\\\\d|[4-9])\\\\d{7}\",[8,9,10],[[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1-$2 $3\",[\"[4-79]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1-$2 $3\",[\"1(?:[02469]|[378][1-9]|53)|8\",\"1(?:[02469]|[37][1-9]|53|8(?:[1-46-9]|5[7-9]))|8\"],\"0$1\"],[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1-$2 $3\",[\"3\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{2})(\\\\d{4})\",\"$1-$2-$3-$4\",[\"1(?:[367]|80)\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1-$2 $3\",[\"15\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1-$2 $3\",[\"1\"],\"0$1\"]],\"0\"],\"MZ\":[\"258\",\"00\",\"(?:2|8\\\\d)\\\\d{7}\",[8,9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"2|8[2-79]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"8\"]]]],\"NA\":[\"264\",\"00\",\"[68]\\\\d{7,8}\",[8,9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"88\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"6\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"87\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"8\"],\"0$1\"]],\"0\"],\"NC\":[\"687\",\"00\",\"(?:050|[2-57-9]\\\\d\\\\d)\\\\d{3}\",[6],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1.$2.$3\",[\"[02-57-9]\"]]]],\"NE\":[\"227\",\"00\",\"[027-9]\\\\d{7}\",[8],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"08\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[089]|2[013]|7[0467]\"]]]],\"NF\":[\"672\",\"00\",\"[13]\\\\d{5}\",[6],[[\"(\\\\d{2})(\\\\d{4})\",\"$1 $2\",[\"1[0-3]\"]],[\"(\\\\d)(\\\\d{5})\",\"$1 $2\",[\"[13]\"]]],0,0,\"([0-258]\\\\d{4})$\",\"3$1\"],\"NG\":[\"234\",\"009\",\"(?:20|9\\\\d)\\\\d{8}|[78]\\\\d{9,13}\",[10,11,12,13,14],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[7-9]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"20[129]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3\",[\"2\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{4})(\\\\d{4,5})\",\"$1 $2 $3\",[\"[78]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{5})(\\\\d{5,6})\",\"$1 $2 $3\",[\"[78]\"],\"0$1\"]],\"0\"],\"NI\":[\"505\",\"00\",\"(?:1800|[25-8]\\\\d{3})\\\\d{4}\",[8],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[125-8]\"]]]],\"NL\":[\"31\",\"00\",\"(?:[124-7]\\\\d\\\\d|3(?:[02-9]\\\\d|1[0-8]))\\\\d{6}|8\\\\d{6,9}|9\\\\d{6,10}|1\\\\d{4,5}\",[5,6,7,8,9,10,11],[[\"(\\\\d{3})(\\\\d{4,7})\",\"$1 $2\",[\"[89]0\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{7})\",\"$1 $2\",[\"66\"],\"0$1\"],[\"(\\\\d)(\\\\d{8})\",\"$1 $2\",[\"6\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"1[16-8]|2[259]|3[124]|4[17-9]|5[124679]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[1-578]|91\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{5})\",\"$1 $2 $3\",[\"9\"],\"0$1\"]],\"0\"],\"NO\":[\"47\",\"00\",\"(?:0|[2-9]\\\\d{3})\\\\d{4}\",[5,8],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"8\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[2-79]\"]]],0,0,0,0,0,\"[02-689]|7[0-8]\"],\"NP\":[\"977\",\"00\",\"(?:1\\\\d|9)\\\\d{9}|[1-9]\\\\d{7}\",[8,10,11],[[\"(\\\\d)(\\\\d{7})\",\"$1-$2\",[\"1[2-6]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{6})\",\"$1-$2\",[\"1[01]|[2-8]|9(?:[1-59]|[67][2-6])\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{7})\",\"$1-$2\",[\"9\"]]],\"0\"],\"NR\":[\"674\",\"00\",\"(?:222|444|(?:55|8\\\\d)\\\\d|666|777|999)\\\\d{4}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[24-9]\"]]]],\"NU\":[\"683\",\"00\",\"(?:[4-7]|888\\\\d)\\\\d{3}\",[4,7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"8\"]]]],\"NZ\":[\"64\",\"0(?:0|161)\",\"[1289]\\\\d{9}|50\\\\d{5}(?:\\\\d{2,3})?|[27-9]\\\\d{7,8}|(?:[34]\\\\d|6[0-35-9])\\\\d{6}|8\\\\d{4,6}\",[5,6,7,8,9,10],[[\"(\\\\d{2})(\\\\d{3,8})\",\"$1 $2\",[\"8[1-79]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2,3})\",\"$1 $2 $3\",[\"50[036-8]|8|90\",\"50(?:[0367]|88)|8|90\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"24|[346]|7[2-57-9]|9[2-9]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"2(?:10|74)|[589]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3,4})(\\\\d{4})\",\"$1 $2 $3\",[\"1|2[028]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,5})\",\"$1 $2 $3\",[\"2(?:[169]|7[0-35-9])|7\"],\"0$1\"]],\"0\",0,0,0,0,0,0,\"00\"],\"OM\":[\"968\",\"00\",\"(?:1505|[279]\\\\d{3}|500)\\\\d{4}|800\\\\d{5,6}\",[7,8,9],[[\"(\\\\d{3})(\\\\d{4,6})\",\"$1 $2\",[\"[58]\"]],[\"(\\\\d{2})(\\\\d{6})\",\"$1 $2\",[\"2\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[179]\"]]]],\"PA\":[\"507\",\"00\",\"(?:00800|8\\\\d{3})\\\\d{6}|[68]\\\\d{7}|[1-57-9]\\\\d{6}\",[7,8,10,11],[[\"(\\\\d{3})(\\\\d{4})\",\"$1-$2\",[\"[1-57-9]\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1-$2\",[\"[68]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"8\"]]]],\"PE\":[\"51\",\"00|19(?:1[124]|77|90)00\",\"(?:[14-8]|9\\\\d)\\\\d{7}\",[8,9],[[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"80\"],\"(0$1)\"],[\"(\\\\d)(\\\\d{7})\",\"$1 $2\",[\"1\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{6})\",\"$1 $2\",[\"[4-8]\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"9\"]]],\"0\",0,0,0,0,0,0,\"00\",\" Anexo \"],\"PF\":[\"689\",\"00\",\"4\\\\d{5}(?:\\\\d{2})?|8\\\\d{7,8}\",[6,8,9],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3\",[\"44\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"4|8[7-9]\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"8\"]]]],\"PG\":[\"675\",\"00|140[1-3]\",\"(?:180|[78]\\\\d{3})\\\\d{4}|(?:[2-589]\\\\d|64)\\\\d{5}\",[7,8],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"18|[2-69]|85\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[78]\"]]],0,0,0,0,0,0,0,\"00\"],\"PH\":[\"63\",\"00\",\"(?:[2-7]|9\\\\d)\\\\d{8}|2\\\\d{5}|(?:1800|8)\\\\d{7,9}\",[6,8,9,10,11,12,13],[[\"(\\\\d)(\\\\d{5})\",\"$1 $2\",[\"2\"],\"(0$1)\"],[\"(\\\\d{4})(\\\\d{4,6})\",\"$1 $2\",[\"3(?:23|39|46)|4(?:2[3-6]|[35]9|4[26]|76)|544|88[245]|(?:52|64|86)2\",\"3(?:230|397|461)|4(?:2(?:35|[46]4|51)|396|4(?:22|63)|59[347]|76[15])|5(?:221|446)|642[23]|8(?:622|8(?:[24]2|5[13]))\"],\"(0$1)\"],[\"(\\\\d{5})(\\\\d{4})\",\"$1 $2\",[\"346|4(?:27|9[35])|883\",\"3469|4(?:279|9(?:30|56))|8834\"],\"(0$1)\"],[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"2\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[3-7]|8[2-8]\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[89]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"]],[\"(\\\\d{4})(\\\\d{1,2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3 $4\",[\"1\"]]],\"0\"],\"PK\":[\"92\",\"00\",\"122\\\\d{6}|[24-8]\\\\d{10,11}|9(?:[013-9]\\\\d{8,10}|2(?:[01]\\\\d\\\\d|2(?:[06-8]\\\\d|1[01]))\\\\d{7})|(?:[2-8]\\\\d{3}|92(?:[0-7]\\\\d|8[1-9]))\\\\d{6}|[24-9]\\\\d{8}|[89]\\\\d{7}\",[8,9,10,11,12],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{2,7})\",\"$1 $2 $3\",[\"[89]0\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{5})\",\"$1 $2\",[\"1\"]],[\"(\\\\d{3})(\\\\d{6,7})\",\"$1 $2\",[\"2(?:3[2358]|4[2-4]|9[2-8])|45[3479]|54[2-467]|60[468]|72[236]|8(?:2[2-689]|3[23578]|4[3478]|5[2356])|9(?:2[2-8]|3[27-9]|4[2-6]|6[3569]|9[25-8])\",\"9(?:2[3-8]|98)|(?:2(?:3[2358]|4[2-4]|9[2-8])|45[3479]|54[2-467]|60[468]|72[236]|8(?:2[2-689]|3[23578]|4[3478]|5[2356])|9(?:22|3[27-9]|4[2-6]|6[3569]|9[25-7]))[2-9]\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{7,8})\",\"$1 $2\",[\"(?:2[125]|4[0-246-9]|5[1-35-7]|6[1-8]|7[14]|8[16]|91)[2-9]\"],\"(0$1)\"],[\"(\\\\d{5})(\\\\d{5})\",\"$1 $2\",[\"58\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{7})\",\"$1 $2\",[\"3\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"2[125]|4[0-246-9]|5[1-35-7]|6[1-8]|7[14]|8[16]|91\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"[24-9]\"],\"(0$1)\"]],\"0\"],\"PL\":[\"48\",\"00\",\"(?:6|8\\\\d\\\\d)\\\\d{7}|[1-9]\\\\d{6}(?:\\\\d{2})?|[26]\\\\d{5}\",[6,7,8,9,10],[[\"(\\\\d{5})\",\"$1\",[\"19\"]],[\"(\\\\d{3})(\\\\d{3})\",\"$1 $2\",[\"11|20|64\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{3})\",\"$1 $2 $3\",[\"(?:1[2-8]|2[2-69]|3[2-4]|4[1-468]|5[24-689]|6[1-3578]|7[14-7]|8[1-79]|9[145])1\",\"(?:1[2-8]|2[2-69]|3[2-4]|4[1-468]|5[24-689]|6[1-3578]|7[14-7]|8[1-79]|9[145])19\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2,3})\",\"$1 $2 $3\",[\"64\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"21|39|45|5[0137]|6[0469]|7[02389]|8(?:0[14]|8)\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"1[2-8]|[2-7]|8[1-79]|9[145]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"8\"]]]],\"PM\":[\"508\",\"00\",\"[45]\\\\d{5}|(?:708|8\\\\d\\\\d)\\\\d{6}\",[6,9],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3\",[\"[45]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"7\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"8\"],\"0$1\"]],\"0\"],\"PR\":[\"1\",\"011\",\"(?:[589]\\\\d\\\\d|787)\\\\d{7}\",[10],0,\"1\",0,0,0,0,\"787|939\"],\"PS\":[\"970\",\"00\",\"[2489]2\\\\d{6}|(?:1\\\\d|5)\\\\d{8}\",[8,9,10],[[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[2489]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"5\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"1\"]]],\"0\"],\"PT\":[\"351\",\"00\",\"1693\\\\d{5}|(?:[26-9]\\\\d|30)\\\\d{7}\",[9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"2[12]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"16|[236-9]\"]]]],\"PW\":[\"680\",\"01[12]\",\"(?:[24-8]\\\\d\\\\d|345|900)\\\\d{4}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[2-9]\"]]]],\"PY\":[\"595\",\"00\",\"59\\\\d{4,6}|9\\\\d{5,10}|(?:[2-46-8]\\\\d|5[0-8])\\\\d{4,7}\",[6,7,8,9,10,11],[[\"(\\\\d{3})(\\\\d{3,6})\",\"$1 $2\",[\"[2-9]0\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{5})\",\"$1 $2\",[\"[26]1|3[289]|4[1246-8]|7[1-3]|8[1-36]\"],\"(0$1)\"],[\"(\\\\d{3})(\\\\d{4,5})\",\"$1 $2\",[\"2[279]|3[13-5]|4[359]|5|6(?:[34]|7[1-46-8])|7[46-8]|85\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"2[14-68]|3[26-9]|4[1246-8]|6(?:1|75)|7[1-35]|8[1-36]\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"87\"]],[\"(\\\\d{3})(\\\\d{6})\",\"$1 $2\",[\"9(?:[5-79]|8[1-7])\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[2-8]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"9\"]]],\"0\"],\"QA\":[\"974\",\"00\",\"800\\\\d{4}|(?:2|800)\\\\d{6}|(?:0080|[3-7])\\\\d{7}\",[7,8,9,11],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"2[136]|8\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[3-7]\"]]]],\"RE\":[\"262\",\"00\",\"709\\\\d{6}|(?:26|[689]\\\\d)\\\\d{7}\",[9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[26-9]\"],\"0$1\"]],\"0\",0,0,0,0,0,[[\"26(?:2\\\\d\\\\d|3(?:0\\\\d|1[0-6]))\\\\d{4}\"],[\"(?:69(?:2\\\\d\\\\d|3(?:[06][0-6]|1[0-3]|2[0-2]|3[0-39]|4\\\\d|5[0-5]|7[0-37]|8[0-8]|9[0-479]))|7092[0-3])\\\\d{4}\"],[\"80\\\\d{7}\"],[\"89[1-37-9]\\\\d{6}\"],0,0,0,0,[\"9(?:399[0-3]|479[0-6]|76(?:2[278]|3[0-37]))\\\\d{4}\"],[\"8(?:1[019]|2[0156]|84|90)\\\\d{6}\"]]],\"RO\":[\"40\",\"00\",\"(?:[236-8]\\\\d|90)\\\\d{7}|[23]\\\\d{5}\",[6,9],[[\"(\\\\d{3})(\\\\d{3})\",\"$1 $2\",[\"2[3-6]\",\"2[3-6]\\\\d9\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4})\",\"$1 $2\",[\"219|31\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[23]1\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[236-9]\"],\"0$1\"]],\"0\",0,0,0,0,0,0,0,\" int \"],\"RS\":[\"381\",\"00\",\"38[02-9]\\\\d{6,9}|6\\\\d{7,9}|90\\\\d{4,8}|38\\\\d{5,6}|(?:7\\\\d\\\\d|800)\\\\d{3,9}|(?:[12]\\\\d|3[0-79])\\\\d{5,10}\",[6,7,8,9,10,11,12],[[\"(\\\\d{3})(\\\\d{3,9})\",\"$1 $2\",[\"(?:2[389]|39)0|[7-9]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{5,10})\",\"$1 $2\",[\"[1-36]\"],\"0$1\"]],\"0\"],\"RU\":[\"7\",\"810\",\"8\\\\d{13}|[347-9]\\\\d{9}\",[10,14],[[\"(\\\\d{4})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"7(?:1[0-8]|2[1-9])\",\"7(?:1(?:[0-356]2|4[29]|7|8[27])|2(?:1[23]|[2-9]2))\",\"7(?:1(?:[0-356]2|4[29]|7|8[27])|2(?:13[03-69]|62[013-9]))|72[1-57-9]2\"],\"8 ($1)\",1],[\"(\\\\d{5})(\\\\d)(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"7(?:1[0-68]|2[1-9])\",\"7(?:1(?:[06][3-6]|[18]|2[35]|[3-5][3-5])|2(?:[13][3-5]|[24-689]|7[457]))\",\"7(?:1(?:0(?:[356]|4[023])|[18]|2(?:3[013-9]|5)|3[45]|43[013-79]|5(?:3[1-8]|4[1-7]|5)|6(?:3[0-35-9]|[4-6]))|2(?:1(?:3[178]|[45])|[24-689]|3[35]|7[457]))|7(?:14|23)4[0-8]|71(?:33|45)[1-79]\"],\"8 ($1)\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"7\"],\"8 ($1)\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2-$3-$4\",[\"[349]|8(?:[02-7]|1[1-8])\"],\"8 ($1)\",1],[\"(\\\\d{4})(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"8\"],\"8 ($1)\"]],\"8\",0,0,0,0,0,[[\"336(?:[013-9]\\\\d|2[013-9])\\\\d{5}|(?:3(?:0[12]|4[1-35-79]|5[1-3]|65|8[1-58]|9[0145])|4(?:01|1[1356]|2[13467]|7[1-5]|8[1-7]|9[1-689])|8(?:1[1-8]|2[01]|3[13-6]|4[0-8]|5[15-7]|6[0-35-79]|7[1-37-9]))\\\\d{7}\",[10]],[\"9\\\\d{9}\",[10]],[\"8(?:0[04]|108\\\\d{3})\\\\d{7}\"],[\"80[39]\\\\d{7}\",[10]],[\"808\\\\d{7}\",[10]]],\"8~10\"],\"RW\":[\"250\",\"00\",\"(?:06|[27]\\\\d\\\\d|[89]00)\\\\d{6}\",[8,9],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"0\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"2\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[7-9]\"],\"0$1\"]],\"0\"],\"SA\":[\"966\",\"00\",\"(?:[15]\\\\d|800|92)\\\\d{7}\",[9,10],[[\"(\\\\d{4})(\\\\d{5})\",\"$1 $2\",[\"9\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"5\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"8\"]]],\"0\"],\"SB\":[\"677\",\"0[01]\",\"[6-9]\\\\d{6}|[1-6]\\\\d{4}\",[5,7],[[\"(\\\\d{2})(\\\\d{5})\",\"$1 $2\",[\"6[89]|7|8[4-9]|9(?:[1-8]|9[0-8])\"]]]],\"SC\":[\"248\",\"010|0[0-2]\",\"(?:[2489]\\\\d|64)\\\\d{5}\",[7],[[\"(\\\\d)(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[246]|9[57]\"]]],0,0,0,0,0,0,0,\"00\"],\"SD\":[\"249\",\"00\",\"[19]\\\\d{8}\",[9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[19]\"],\"0$1\"]],\"0\"],\"SE\":[\"46\",\"00\",\"(?:[26]\\\\d\\\\d|9)\\\\d{9}|[1-9]\\\\d{8}|[1-689]\\\\d{7}|[1-4689]\\\\d{6}|2\\\\d{5}\",[6,7,8,9,10,12],[[\"(\\\\d{2})(\\\\d{2,3})(\\\\d{2})\",\"$1-$2 $3\",[\"20\"],\"0$1\",0,\"$1 $2 $3\"],[\"(\\\\d{3})(\\\\d{4})\",\"$1-$2\",[\"9(?:00|39|44|9)\"],\"0$1\",0,\"$1 $2\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})\",\"$1-$2 $3\",[\"[12][136]|3[356]|4[0246]|6[03]|90[1-9]\"],\"0$1\",0,\"$1 $2 $3\"],[\"(\\\\d)(\\\\d{2,3})(\\\\d{2})(\\\\d{2})\",\"$1-$2 $3 $4\",[\"8\"],\"0$1\",0,\"$1 $2 $3 $4\"],[\"(\\\\d{3})(\\\\d{2,3})(\\\\d{2})\",\"$1-$2 $3\",[\"1[2457]|2(?:[247-9]|5[0138])|3[0247-9]|4[1357-9]|5[0-35-9]|6(?:[125689]|4[02-57]|7[0-2])|9(?:[125-8]|3[02-5]|4[0-3])\"],\"0$1\",0,\"$1 $2 $3\"],[\"(\\\\d{3})(\\\\d{2,3})(\\\\d{3})\",\"$1-$2 $3\",[\"9(?:00|39|44)\"],\"0$1\",0,\"$1 $2 $3\"],[\"(\\\\d{2})(\\\\d{2,3})(\\\\d{2})(\\\\d{2})\",\"$1-$2 $3 $4\",[\"1[13689]|2[0136]|3[1356]|4[0246]|54|6[03]|90[1-9]\"],\"0$1\",0,\"$1 $2 $3 $4\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1-$2 $3 $4\",[\"10|7\"],\"0$1\",0,\"$1 $2 $3 $4\"],[\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{2})\",\"$1-$2 $3 $4\",[\"8\"],\"0$1\",0,\"$1 $2 $3 $4\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1-$2 $3 $4\",[\"[13-5]|2(?:[247-9]|5[0138])|6(?:[124-689]|7[0-2])|9(?:[125-8]|3[02-5]|4[0-3])\"],\"0$1\",0,\"$1 $2 $3 $4\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{3})\",\"$1-$2 $3 $4\",[\"9\"],\"0$1\",0,\"$1 $2 $3 $4\"],[\"(\\\\d{3})(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1-$2 $3 $4 $5\",[\"[26]\"],\"0$1\",0,\"$1 $2 $3 $4 $5\"]],\"0\"],\"SG\":[\"65\",\"0[0-3]\\\\d\",\"(?:(?:1\\\\d|8)\\\\d\\\\d|7000)\\\\d{7}|[3689]\\\\d{7}\",[8,10,11],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[369]|8(?:0[1-9]|[1-9])\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"8\"]],[\"(\\\\d{4})(\\\\d{4})(\\\\d{3})\",\"$1 $2 $3\",[\"7\"]],[\"(\\\\d{4})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"1\"]]]],\"SH\":[\"290\",\"00\",\"(?:[256]\\\\d|8)\\\\d{3}\",[4,5],0,0,0,0,0,0,\"[256]\"],\"SI\":[\"386\",\"00|10(?:22|66|88|99)\",\"[1-7]\\\\d{7}|8\\\\d{4,7}|90\\\\d{4,6}\",[5,6,7,8],[[\"(\\\\d{2})(\\\\d{3,6})\",\"$1 $2\",[\"8[09]|9\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"59|8\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[37][01]|4[0139]|51|6\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[1-57]\"],\"(0$1)\"]],\"0\",0,0,0,0,0,0,\"00\"],\"SJ\":[\"47\",\"00\",\"0\\\\d{4}|(?:[489]\\\\d|79)\\\\d{6}\",[5,8],0,0,0,0,0,0,\"79\"],\"SK\":[\"421\",\"00\",\"[2-689]\\\\d{8}|[2-59]\\\\d{6}|[2-5]\\\\d{5}\",[6,7,9],[[\"(\\\\d)(\\\\d{2})(\\\\d{3,4})\",\"$1 $2 $3\",[\"21\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{2})(\\\\d{2,3})\",\"$1 $2 $3\",[\"[3-5][1-8]1\",\"[3-5][1-8]1[67]\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{3})(\\\\d{2})\",\"$1/$2 $3 $4\",[\"2\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[689]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1/$2 $3 $4\",[\"[3-5]\"],\"0$1\"]],\"0\"],\"SL\":[\"232\",\"00\",\"(?:[237-9]\\\\d|66)\\\\d{6}\",[8],[[\"(\\\\d{2})(\\\\d{6})\",\"$1 $2\",[\"[236-9]\"],\"(0$1)\"]],\"0\"],\"SM\":[\"378\",\"00\",\"(?:0549|[5-7]\\\\d)\\\\d{6}\",[8,10],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[5-7]\"]],[\"(\\\\d{4})(\\\\d{6})\",\"$1 $2\",[\"0\"]]],0,0,\"([89]\\\\d{5})$\",\"0549$1\"],\"SN\":[\"221\",\"00\",\"(?:[378]\\\\d|93)\\\\d{7}\",[9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"8\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[379]\"]]]],\"SO\":[\"252\",\"00\",\"[346-9]\\\\d{8}|[12679]\\\\d{7}|[1-5]\\\\d{6}|[1348]\\\\d{5}\",[6,7,8,9],[[\"(\\\\d{2})(\\\\d{4})\",\"$1 $2\",[\"8[125]\"]],[\"(\\\\d{6})\",\"$1\",[\"[134]\"]],[\"(\\\\d)(\\\\d{6})\",\"$1 $2\",[\"[15]|2[0-79]|3[0-46-8]|4[0-7]\"]],[\"(\\\\d)(\\\\d{7})\",\"$1 $2\",[\"(?:2|90)4|[67]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[348]|64|79|90\"]],[\"(\\\\d{2})(\\\\d{5,7})\",\"$1 $2\",[\"1|28|6[0-35-9]|7[67]|9[2-9]\"]]],\"0\"],\"SR\":[\"597\",\"00\",\"(?:[2-5]|68|[78]\\\\d|90)\\\\d{5}\",[6,7],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1-$2-$3\",[\"56\"]],[\"(\\\\d{3})(\\\\d{3})\",\"$1-$2\",[\"[2-5]\"]],[\"(\\\\d{3})(\\\\d{4})\",\"$1-$2\",[\"[6-9]\"]]]],\"SS\":[\"211\",\"00\",\"[19]\\\\d{8}\",[9],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[19]\"],\"0$1\"]],\"0\"],\"ST\":[\"239\",\"00\",\"(?:22|9\\\\d)\\\\d{5}\",[7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[29]\"]]]],\"SV\":[\"503\",\"00\",\"[267]\\\\d{7}|(?:80\\\\d|900)\\\\d{4}(?:\\\\d{4})?\",[7,8,11],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[89]\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[267]\"]],[\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"[89]\"]]]],\"SX\":[\"1\",\"011\",\"7215\\\\d{6}|(?:[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"(5\\\\d{6})$|1\",\"721$1\",0,\"721\"],\"SY\":[\"963\",\"00\",\"[1-39]\\\\d{8}|[1-5]\\\\d{7}\",[8,9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[1-5]\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"9\"],\"0$1\",1]],\"0\"],\"SZ\":[\"268\",\"00\",\"0800\\\\d{4}|(?:[237]\\\\d|900)\\\\d{6}\",[8,9],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[0237]\"]],[\"(\\\\d{5})(\\\\d{4})\",\"$1 $2\",[\"9\"]]]],\"TA\":[\"290\",\"00\",\"8\\\\d{3}\",[4],0,0,0,0,0,0,\"8\"],\"TC\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|649|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-479]\\\\d{6})$|1\",\"649$1\",0,\"649\"],\"TD\":[\"235\",\"00|16\",\"(?:22|30|[689]\\\\d|77)\\\\d{6}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[236-9]\"]]],0,0,0,0,0,0,0,\"00\"],\"TG\":[\"228\",\"00\",\"[279]\\\\d{7}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[279]\"]]]],\"TH\":[\"66\",\"00[1-9]\",\"(?:001800|[2-57]|[689]\\\\d)\\\\d{7}|1\\\\d{7,9}\",[8,9,10,13],[[\"(\\\\d)(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"2\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[13-9]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"1\"]]],\"0\"],\"TJ\":[\"992\",\"810\",\"[0-57-9]\\\\d{8}\",[9],[[\"(\\\\d{6})(\\\\d)(\\\\d{2})\",\"$1 $2 $3\",[\"331\",\"3317\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3\",[\"44[02-479]|[34]7\"]],[\"(\\\\d{4})(\\\\d)(\\\\d{4})\",\"$1 $2 $3\",[\"3(?:[1245]|3[12])\"]],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[0-57-9]\"]]],0,0,0,0,0,0,0,\"8~10\"],\"TK\":[\"690\",\"00\",\"[2-47]\\\\d{3,6}\",[4,5,6,7]],\"TL\":[\"670\",\"00\",\"7\\\\d{7}|(?:[2-47]\\\\d|[89]0)\\\\d{5}\",[7,8],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[2-489]|70\"]],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"7\"]]]],\"TM\":[\"993\",\"810\",\"(?:[1-6]\\\\d|71)\\\\d{6}\",[8],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2-$3-$4\",[\"12\"],\"(8 $1)\"],[\"(\\\\d{3})(\\\\d)(\\\\d{2})(\\\\d{2})\",\"$1 $2-$3-$4\",[\"[1-5]\"],\"(8 $1)\"],[\"(\\\\d{2})(\\\\d{6})\",\"$1 $2\",[\"[67]\"],\"8 $1\"]],\"8\",0,0,0,0,0,0,\"8~10\"],\"TN\":[\"216\",\"00\",\"[2-57-9]\\\\d{7}\",[8],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[2-57-9]\"]]]],\"TO\":[\"676\",\"00\",\"(?:0800|(?:[5-8]\\\\d\\\\d|999)\\\\d)\\\\d{3}|[2-8]\\\\d{4}\",[5,7],[[\"(\\\\d{2})(\\\\d{3})\",\"$1-$2\",[\"[2-4]|50|6[09]|7[0-24-69]|8[05]\"]],[\"(\\\\d{4})(\\\\d{3})\",\"$1 $2\",[\"0\"]],[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[5-9]\"]]]],\"TR\":[\"90\",\"00\",\"4\\\\d{6}|8\\\\d{11,12}|(?:[2-58]\\\\d\\\\d|900)\\\\d{7}\",[7,10,12,13],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"512|8[01589]|90\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"5(?:[0-59]|61)\",\"5(?:[0-59]|61[06])\",\"5(?:[0-59]|61[06]1)\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[24][1-8]|3[1-9]\"],\"(0$1)\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{6,7})\",\"$1 $2 $3\",[\"80\"],\"0$1\",1]],\"0\"],\"TT\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-46-8]\\\\d{6})$|1\",\"868$1\",0,\"868\"],\"TV\":[\"688\",\"00\",\"(?:2|7\\\\d\\\\d|90)\\\\d{4}\",[5,6,7],[[\"(\\\\d{2})(\\\\d{3})\",\"$1 $2\",[\"2\"]],[\"(\\\\d{2})(\\\\d{4})\",\"$1 $2\",[\"90\"]],[\"(\\\\d{2})(\\\\d{5})\",\"$1 $2\",[\"7\"]]]],\"TW\":[\"886\",\"0(?:0[25-79]|19)\",\"[2-689]\\\\d{8}|7\\\\d{9,10}|[2-8]\\\\d{7}|2\\\\d{6}\",[7,8,9,10,11],[[\"(\\\\d{2})(\\\\d)(\\\\d{4})\",\"$1 $2 $3\",[\"202\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[258]0\"],\"0$1\"],[\"(\\\\d)(\\\\d{3,4})(\\\\d{4})\",\"$1 $2 $3\",[\"[23568]|4(?:0[02-48]|[1-47-9])|7[1-9]\",\"[23568]|4(?:0[2-48]|[1-47-9])|(?:400|7)[1-9]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[49]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4,5})\",\"$1 $2 $3\",[\"7\"],\"0$1\"]],\"0\",0,0,0,0,0,0,0,\"#\"],\"TZ\":[\"255\",\"00[056]\",\"(?:[25-8]\\\\d|41|90)\\\\d{7}\",[9],[[\"(\\\\d{3})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3\",[\"[89]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[24]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{7})\",\"$1 $2\",[\"5\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[67]\"],\"0$1\"]],\"0\"],\"UA\":[\"380\",\"00\",\"[89]\\\\d{9}|[3-9]\\\\d{8}\",[9,10],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"6[12][29]|(?:3[1-8]|4[136-8]|5[12457]|6[49])2|(?:56|65)[24]\",\"6[12][29]|(?:35|4[1378]|5[12457]|6[49])2|(?:56|65)[24]|(?:3[1-46-8]|46)2[013-9]\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{5})\",\"$1 $2\",[\"3[1-8]|4(?:[1367]|[45][6-9]|8[4-6])|5(?:[1-5]|6[0135689]|7[4-6])|6(?:[12][3-7]|[459])\",\"3[1-8]|4(?:[1367]|[45][6-9]|8[4-6])|5(?:[1-5]|6(?:[015689]|3[02389])|7[4-6])|6(?:[12][3-7]|[459])\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[3-7]|89|9[1-9]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[89]\"],\"0$1\"]],\"0\",0,0,0,0,0,0,\"0~0\"],\"UG\":[\"256\",\"00[057]\",\"800\\\\d{6}|(?:[29]0|[347]\\\\d)\\\\d{7}\",[9],[[\"(\\\\d{4})(\\\\d{5})\",\"$1 $2\",[\"202\",\"2024\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{6})\",\"$1 $2\",[\"[27-9]|4(?:6[45]|[7-9])\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{7})\",\"$1 $2\",[\"[34]\"],\"0$1\"]],\"0\"],\"US\":[\"1\",\"011\",\"[2-9]\\\\d{9}|3\\\\d{6}\",[10],[[\"(\\\\d{3})(\\\\d{4})\",\"$1-$2\",[\"310\"],0,1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"($1) $2-$3\",[\"[2-9]\"],0,1,\"$1-$2-$3\"]],\"1\",0,0,0,0,0,[[\"(?:3052(?:0[0-8]|[1-9]\\\\d)|5056(?:[0-35-9]\\\\d|4[0-68]))\\\\d{4}|(?:2742|305[3-9]|472[247-9]|505[2-57-9]|983[2-47-9])\\\\d{6}|(?:2(?:0[1-35-9]|1[02-9]|2[03-57-9]|3[1459]|4[08]|5[1-46]|6[0279]|7[0269]|8[13])|3(?:0[1-47-9]|1[02-9]|2[0135-79]|3[0-24679]|4[167]|5[0-2]|6[01349]|8[056])|4(?:0[124-9]|1[02-579]|2[3-5]|3[0245]|4[023578]|58|6[349]|7[0589]|8[04])|5(?:0[1-47-9]|1[0235-8]|20|3[0149]|4[01]|5[179]|6[1-47]|7[0-5]|8[0256])|6(?:0[1-35-9]|1[024-9]|2[03689]|3[016]|4[0156]|5[01679]|6[0-279]|78|8[0-29])|7(?:0[1-46-8]|1[2-9]|2[04-8]|3[0-247]|4[0378]|5[47]|6[02359]|7[0-59]|8[156])|8(?:0[1-68]|1[02-8]|2[0168]|3[0-2589]|4[03578]|5[046-9]|6[02-5]|7[028])|9(?:0[1346-9]|1[02-9]|2[0589]|3[0146-8]|4[01357-9]|5[12469]|7[0-3589]|8[04-69]))[2-9]\\\\d{6}\"],[\"\"],[\"8(?:00|33|44|55|66|77|88)[2-9]\\\\d{6}\"],[\"900[2-9]\\\\d{6}\"],[\"52(?:3(?:[2-46-9][02-9]\\\\d|5(?:[02-46-9]\\\\d|5[0-46-9]))|4(?:[2-478][02-9]\\\\d|5(?:[034]\\\\d|2[024-9]|5[0-46-9])|6(?:0[1-9]|[2-9]\\\\d)|9(?:[05-9]\\\\d|2[0-5]|49)))\\\\d{4}|52[34][2-9]1[02-9]\\\\d{4}|5(?:00|2[125-9]|33|44|66|77|88)[2-9]\\\\d{6}\"],0,0,0,[\"305209\\\\d{4}\"]]],\"UY\":[\"598\",\"0(?:0|1[3-9]\\\\d)\",\"0004\\\\d{2,9}|[1249]\\\\d{7}|(?:[49]\\\\d|80)\\\\d{5}\",[6,7,8,9,10,11,12,13],[[\"(\\\\d{3})(\\\\d{3,4})\",\"$1 $2\",[\"0\"]],[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[49]0|8\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"9\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[124]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{2,4})\",\"$1 $2 $3\",[\"0\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})(\\\\d{2,4})\",\"$1 $2 $3 $4\",[\"0\"]]],\"0\",0,0,0,0,0,0,\"00\",\" int. \"],\"UZ\":[\"998\",\"00\",\"(?:20|33|[5-9]\\\\d)\\\\d{7}\",[9],[[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"[235-9]\"]]]],\"VA\":[\"39\",\"00\",\"0\\\\d{5,10}|3[0-8]\\\\d{7,10}|55\\\\d{8}|8\\\\d{5}(?:\\\\d{2,4})?|(?:1\\\\d|39)\\\\d{7,8}\",[6,7,8,9,10,11,12],0,0,0,0,0,0,\"06698\"],\"VC\":[\"1\",\"011\",\"(?:[58]\\\\d\\\\d|784|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-7]\\\\d{6})$|1\",\"784$1\",0,\"784\"],\"VE\":[\"58\",\"00\",\"[68]00\\\\d{7}|(?:[24]\\\\d|[59]0)\\\\d{8}\",[10],[[\"(\\\\d{3})(\\\\d{7})\",\"$1-$2\",[\"[24-689]\"],\"0$1\"]],\"0\"],\"VG\":[\"1\",\"011\",\"(?:284|[58]\\\\d\\\\d|900)\\\\d{7}\",[10],0,\"1\",0,\"([2-578]\\\\d{6})$|1\",\"284$1\",0,\"284\"],\"VI\":[\"1\",\"011\",\"[58]\\\\d{9}|(?:34|90)0\\\\d{7}\",[10],0,\"1\",0,\"([2-9]\\\\d{6})$|1\",\"340$1\",0,\"340\"],\"VN\":[\"84\",\"00\",\"[12]\\\\d{9}|[135-9]\\\\d{8}|[16]\\\\d{7}|[16-8]\\\\d{6}\",[7,8,9,10],[[\"(\\\\d{2})(\\\\d{5})\",\"$1 $2\",[\"80\"],\"0$1\",1],[\"(\\\\d{4})(\\\\d{4,6})\",\"$1 $2\",[\"1\"],0,1],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"6\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[357-9]\"],\"0$1\",1],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"2[48]\"],\"0$1\",1],[\"(\\\\d{3})(\\\\d{4})(\\\\d{3})\",\"$1 $2 $3\",[\"2\"],\"0$1\",1]],\"0\"],\"VU\":[\"678\",\"00\",\"[57-9]\\\\d{6}|(?:[238]\\\\d|48)\\\\d{3}\",[5,7],[[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"[57-9]\"]]]],\"WF\":[\"681\",\"00\",\"(?:40|72|8\\\\d{4})\\\\d{4}|[89]\\\\d{5}\",[6,9],[[\"(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3\",[\"[47-9]\"]],[\"(\\\\d{3})(\\\\d{2})(\\\\d{2})(\\\\d{2})\",\"$1 $2 $3 $4\",[\"8\"]]]],\"WS\":[\"685\",\"0\",\"(?:[2-6]|8\\\\d{5})\\\\d{4}|[78]\\\\d{6}|[68]\\\\d{5}\",[5,6,7,10],[[\"(\\\\d{5})\",\"$1\",[\"[2-5]|6[1-9]\"]],[\"(\\\\d{3})(\\\\d{3,7})\",\"$1 $2\",[\"[68]\"]],[\"(\\\\d{2})(\\\\d{5})\",\"$1 $2\",[\"7\"]]]],\"XK\":[\"383\",\"00\",\"2\\\\d{7,8}|3\\\\d{7,11}|(?:4\\\\d\\\\d|[89]00)\\\\d{5}\",[8,9,10,11,12],[[\"(\\\\d{3})(\\\\d{5})\",\"$1 $2\",[\"[89]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[2-4]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"2|39\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{7,10})\",\"$1 $2\",[\"3\"],\"0$1\"]],\"0\"],\"YE\":[\"967\",\"00\",\"(?:1|7\\\\d)\\\\d{7}|[1-7]\\\\d{6}\",[7,8,9],[[\"(\\\\d)(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"[1-6]|7(?:[24-6]|8[0-7])\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"7\"],\"0$1\"]],\"0\"],\"YT\":[\"262\",\"00\",\"7093\\\\d{5}|(?:80|9\\\\d)\\\\d{7}|(?:26|63)9\\\\d{6}\",[9],0,\"0\",0,0,0,0,0,[[\"269(?:0[0-467]|15|5[0-4]|6\\\\d|[78]0)\\\\d{4}\"],[\"(?:639(?:0[0-79]|1[019]|[267]\\\\d|3[09]|40|5[05-9]|9[04-79])|7093[5-7])\\\\d{4}\"],[\"80\\\\d{7}\"],0,0,0,0,0,[\"9(?:(?:39|47)8[01]|769\\\\d)\\\\d{4}\"]]],\"ZA\":[\"27\",\"00\",\"[1-79]\\\\d{8}|8\\\\d{4,9}\",[5,6,7,8,9,10],[[\"(\\\\d{2})(\\\\d{3,4})\",\"$1 $2\",[\"8[1-4]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{2,3})\",\"$1 $2 $3\",[\"8[1-4]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"860\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"[1-9]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"8\"],\"0$1\"]],\"0\"],\"ZM\":[\"260\",\"00\",\"800\\\\d{6}|(?:21|[579]\\\\d|63)\\\\d{7}\",[9],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[28]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{7})\",\"$1 $2\",[\"[579]\"],\"0$1\"]],\"0\"],\"ZW\":[\"263\",\"00\",\"2(?:[0-57-9]\\\\d{6,8}|6[0-24-9]\\\\d{6,7})|[38]\\\\d{9}|[35-8]\\\\d{8}|[3-6]\\\\d{7}|[1-689]\\\\d{6}|[1-3569]\\\\d{5}|[1356]\\\\d{4}\",[5,6,7,8,9,10],[[\"(\\\\d{3})(\\\\d{3,5})\",\"$1 $2\",[\"2(?:0[45]|2[278]|[49]8)|3(?:[09]8|17)|6(?:[29]8|37|75)|[23][78]|(?:33|5[15]|6[68])[78]\"],\"0$1\"],[\"(\\\\d)(\\\\d{3})(\\\\d{2,4})\",\"$1 $2 $3\",[\"[49]\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{4})\",\"$1 $2\",[\"80\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{7})\",\"$1 $2\",[\"24|8[13-59]|(?:2[05-79]|39|5[45]|6[15-8])2\",\"2(?:02[014]|4|[56]20|[79]2)|392|5(?:42|525)|6(?:[16-8]21|52[013])|8[13-59]\"],\"(0$1)\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"7\"],\"0$1\"],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"2(?:1[39]|2[0157]|[378]|[56][14])|3(?:12|29)\",\"2(?:1[39]|2[0157]|[378]|[56][14])|3(?:123|29)\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{6})\",\"$1 $2\",[\"8\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3,5})\",\"$1 $2\",[\"1|2(?:0[0-36-9]|12|29|[56])|3(?:1[0-689]|[24-6])|5(?:[0236-9]|1[2-4])|6(?:[013-59]|7[0-46-9])|(?:33|55|6[68])[0-69]|(?:29|3[09]|62)[0-79]\"],\"0$1\"],[\"(\\\\d{2})(\\\\d{3})(\\\\d{3,4})\",\"$1 $2 $3\",[\"29[013-9]|39|54\"],\"0$1\"],[\"(\\\\d{4})(\\\\d{3,5})\",\"$1 $2\",[\"(?:25|54)8\",\"258|5483\"],\"0$1\"]],\"0\"]},\"nonGeographic\":{\"800\":[\"800\",0,\"(?:00|[1-9]\\\\d)\\\\d{6}\",[8],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"\\\\d\"]]],0,0,0,0,0,0,[0,0,[\"(?:00|[1-9]\\\\d)\\\\d{6}\"]]],\"808\":[\"808\",0,\"[1-9]\\\\d{7}\",[8],[[\"(\\\\d{4})(\\\\d{4})\",\"$1 $2\",[\"[1-9]\"]]],0,0,0,0,0,0,[0,0,0,0,0,0,0,0,0,[\"[1-9]\\\\d{7}\"]]],\"870\":[\"870\",0,\"7\\\\d{11}|[235-7]\\\\d{8}\",[9,12],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"[235-7]\"]]],0,0,0,0,0,0,[0,[\"(?:[356]|774[45])\\\\d{8}|7[6-8]\\\\d{7}\"],0,0,0,0,0,0,[\"2\\\\d{8}\",[9]]]],\"878\":[\"878\",0,\"10\\\\d{10}\",[12],[[\"(\\\\d{2})(\\\\d{5})(\\\\d{5})\",\"$1 $2 $3\",[\"1\"]]],0,0,0,0,0,0,[0,0,0,0,0,0,0,0,[\"10\\\\d{10}\"]]],\"881\":[\"881\",0,\"6\\\\d{9}|[0-36-9]\\\\d{8}\",[9,10],[[\"(\\\\d)(\\\\d{3})(\\\\d{5})\",\"$1 $2 $3\",[\"[0-37-9]\"]],[\"(\\\\d)(\\\\d{3})(\\\\d{5,6})\",\"$1 $2 $3\",[\"6\"]]],0,0,0,0,0,0,[0,[\"6\\\\d{9}|[0-36-9]\\\\d{8}\"]]],\"882\":[\"882\",0,\"[13]\\\\d{6}(?:\\\\d{2,5})?|[19]\\\\d{7}|(?:[25]\\\\d\\\\d|4)\\\\d{7}(?:\\\\d{2})?\",[7,8,9,10,11,12],[[\"(\\\\d{2})(\\\\d{5})\",\"$1 $2\",[\"16|342\"]],[\"(\\\\d{2})(\\\\d{6})\",\"$1 $2\",[\"49\"]],[\"(\\\\d{2})(\\\\d{2})(\\\\d{4})\",\"$1 $2 $3\",[\"1[36]|9\"]],[\"(\\\\d{2})(\\\\d{4})(\\\\d{3})\",\"$1 $2 $3\",[\"3[23]\"]],[\"(\\\\d{2})(\\\\d{3,4})(\\\\d{4})\",\"$1 $2 $3\",[\"16\"]],[\"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"10|23|3(?:[15]|4[57])|4|51\"]],[\"(\\\\d{3})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"34\"]],[\"(\\\\d{2})(\\\\d{4,5})(\\\\d{5})\",\"$1 $2 $3\",[\"[1-35]\"]]],0,0,0,0,0,0,[0,[\"342\\\\d{4}|(?:337|49)\\\\d{6}|(?:3(?:2|47|7\\\\d{3})|50\\\\d{3})\\\\d{7}\",[7,8,9,10,12]],0,0,0,[\"348[57]\\\\d{7}\",[11]],0,0,[\"1(?:3(?:0[0347]|[13][0139]|2[035]|4[013568]|6[0459]|7[06]|8[15-8]|9[0689])\\\\d{4}|6\\\\d{5,10})|(?:345\\\\d|9[89])\\\\d{6}|(?:10|2(?:3|85\\\\d)|3(?:[15]|[69]\\\\d\\\\d)|4[15-8]|51)\\\\d{8}\"]]],\"883\":[\"883\",0,\"(?:[1-4]\\\\d|51)\\\\d{6,10}\",[8,9,10,11,12],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{2,8})\",\"$1 $2 $3\",[\"[14]|2[24-689]|3[02-689]|51[24-9]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3\",[\"510\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{4})\",\"$1 $2 $3\",[\"21\"]],[\"(\\\\d{4})(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"51[13]\"]],[\"(\\\\d{3})(\\\\d{3})(\\\\d{3})(\\\\d{3})\",\"$1 $2 $3 $4\",[\"[235]\"]]],0,0,0,0,0,0,[0,0,0,0,0,0,0,0,[\"(?:2(?:00\\\\d\\\\d|10)|(?:370[1-9]|51\\\\d0)\\\\d)\\\\d{7}|51(?:00\\\\d{5}|[24-9]0\\\\d{4,7})|(?:1[0-79]|2[24-689]|3[02-689]|4[0-4])0\\\\d{5,9}\"]]],\"888\":[\"888\",0,\"\\\\d{11}\",[11],[[\"(\\\\d{3})(\\\\d{3})(\\\\d{5})\",\"$1 $2 $3\"]],0,0,0,0,0,0,[0,0,0,0,0,0,[\"\\\\d{11}\"]]],\"979\":[\"979\",0,\"[1359]\\\\d{8}\",[9],[[\"(\\\\d)(\\\\d{4})(\\\\d{4})\",\"$1 $2 $3\",[\"[1359]\"]]],0,0,0,0,0,0,[0,0,0,[\"[1359]\\\\d{8}\"]]]}}"], "mappings": "AAAA;AACA;AACA;AACA,eAAe;EAAC,SAAS,EAAC,CAAC;EAAC,uBAAuB,EAAC;IAAC,GAAG,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC;IAAC,GAAG,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI,CAAC;IAAC,KAAK,EAAC,CAAC,IAAI;EAAC,CAAC;EAAC,WAAW,EAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,2BAA2B,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,gCAAgC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,SAAS,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,wDAAwD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,kBAAkB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,aAAa,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,8BAA8B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,8BAA8B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,mBAAmB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,wDAAwD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,eAAe,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,SAAS,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,+BAA+B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,MAAM,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,SAAS,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,YAAY,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,sCAAsC,EAAC,CAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,sFAAsF,EAAC,iNAAiN,EAAC,iSAAiS,EAAC,6WAA6W,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,CAAC,+BAA+B,EAAC,aAAa,EAAC,CAAC,wBAAwB,EAAC,2FAA2F,EAAC,uNAAuN,EAAC,2SAA2S,EAAC,sXAAsX,CAAC,EAAC,KAAK,EAAC,CAAC,EAAC,aAAa,CAAC,EAAC,CAAC,+BAA+B,EAAC,aAAa,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,EAAC,aAAa,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,+BAA+B,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,EAAC,aAAa,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,yjBAAyjB,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,8BAA8B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,kKAAkK,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,eAAe,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,qBAAqB,EAAC,OAAO,EAAC,CAAC,wDAAwD,EAAC,yDAAyD,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,gBAAgB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,qDAAqD,EAAC,8EAA8E,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,aAAa,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,6dAA6d,EAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,2GAA2G,EAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,wBAAwB,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,CAAC,EAAC,CAAC,iBAAiB,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,aAAa,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,0CAA0C,EAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,kDAAkD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,CAAC,CAAC,EAAC,MAAM,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,6BAA6B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,qDAAqD,EAAC,2FAA2F,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,uCAAuC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,gBAAgB,EAAC,oBAAoB,EAAC,2BAA2B,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,oCAAoC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,cAAc,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,aAAa,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,8BAA8B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,iFAAiF,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,gBAAgB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,sLAAsL,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,cAAc,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,iBAAiB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,qBAAqB,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,WAAW,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,+BAA+B,EAAC,aAAa,EAAC,CAAC,aAAa,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,yBAAyB,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,gDAAgD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,4BAA4B,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,iBAAiB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,yBAAyB,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,qCAAqC,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,WAAW,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,WAAW,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,YAAY,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,eAAe,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,uBAAuB,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,0BAA0B,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,UAAU,CAAC,CAAC,EAAC,CAAC,0CAA0C,EAAC,gBAAgB,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,4CAA4C,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,qCAAqC,CAAC,EAAC,CAAC,0GAA0G,CAAC,EAAC,CAAC,eAAe,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,4CAA4C,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,8BAA8B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,eAAe,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,aAAa,EAAC,oCAAoC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,eAAe,EAAC,OAAO,EAAC,CAAC,aAAa,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,IAAI,EAAC,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,UAAU,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,sBAAsB,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,OAAO,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,wCAAwC,EAAC,uFAAuF,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,sBAAsB,EAAC,4BAA4B,CAAC,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,eAAe,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,4DAA4D,CAAC,EAAC,MAAM,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,oBAAoB,CAAC,EAAC,MAAM,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,6DAA6D,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,8BAA8B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,wBAAwB,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,eAAe,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,sDAAsD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,gBAAgB,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,kIAAkI,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,KAAK,CAAC,EAAC,MAAM,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,KAAK,CAAC,EAAC,MAAM,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,mDAAmD,EAAC,uFAAuF,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,yBAAyB,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,OAAO,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,MAAM,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,OAAO,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,MAAM,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,yBAAyB,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,+BAA+B,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,qBAAqB,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,uNAAuN,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,EAAE,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,sCAAsC,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,gBAAgB,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,qPAAqP,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,WAAW,EAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,gBAAgB,EAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,qDAAqD,EAAC,oEAAoE,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,iBAAiB,EAAC,KAAK,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,yIAAyI,EAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,2GAA2G,EAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,wBAAwB,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,CAAC,EAAC,CAAC,iBAAiB,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,0CAA0C,EAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,kDAAkD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,CAAC,CAAC,EAAC,MAAM,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,4CAA4C,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,2BAA2B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,6BAA6B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,sBAAsB,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,WAAW,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,WAAW,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0CAA0C,EAAC,gBAAgB,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,YAAY,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,CAAC,+BAA+B,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,eAAe,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,wDAAwD,EAAC,oCAAoC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,KAAK,EAAC,MAAM,CAAC,EAAC,MAAM,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,SAAS,CAAC,EAAC,MAAM,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,kDAAkD,CAAC,EAAC,MAAM,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,uBAAuB,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,uCAAuC,EAAC,gBAAgB,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,4BAA4B,EAAC,0HAA0H,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,0TAA0T,EAAC,iWAAiW,EAAC,uXAAuX,EAAC,yXAAyX,EAAC,sXAAsX,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,8QAA8Q,EAAC,2SAA2S,EAAC,oUAAoU,EAAC,sUAAsU,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,WAAW,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,0BAA0B,EAAC,0BAA0B,EAAC,4DAA4D,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,+LAA+L,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,SAAS,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,2BAA2B,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,2BAA2B,EAAC,2CAA2C,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,EAAC,MAAM,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,gBAAgB,CAAC,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,EAAC,UAAU,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,0BAA0B,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,+CAA+C,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,cAAc,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,qCAAqC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,KAAK,EAAC,iDAAiD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,aAAa,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,iBAAiB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,eAAe,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,GAAG,EAAC,4BAA4B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,kCAAkC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,MAAM,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,qDAAqD,EAAC,oEAAoE,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,iBAAiB,EAAC,KAAK,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,2JAA2J,EAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,2GAA2G,EAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,wBAAwB,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,CAAC,EAAC,CAAC,iBAAiB,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,0CAA0C,EAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,kDAAkD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,CAAC,CAAC,EAAC,MAAM,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,0BAA0B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,oCAAoC,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,gBAAgB,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,+LAA+L,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,qBAAqB,EAAC,OAAO,EAAC,CAAC,gBAAgB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,qBAAqB,EAAC,OAAO,EAAC,CAAC,qGAAqG,EAAC,8GAA8G,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,qBAAqB,EAAC,OAAO,EAAC,CAAC,sGAAsG,EAAC,0bAA0b,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,qBAAqB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,qBAAqB,EAAC,OAAO,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,aAAa,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,qBAAqB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,KAAK,EAAC,MAAM,EAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,UAAU,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,eAAe,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,SAAS,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,mBAAmB,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,aAAa,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,8BAA8B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,0BAA0B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,cAAc,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,8BAA8B,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,iCAAiC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,OAAO,EAAC,CAAC,EAAC,UAAU,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,4CAA4C,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,kDAAkD,EAAC,8EAA8E,CAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,qBAAqB,EAAC,0BAA0B,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,wCAAwC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,iBAAiB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,8BAA8B,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,aAAa,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,SAAS,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,aAAa,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,aAAa,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,yBAAyB,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,SAAS,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,qDAAqD,EAAC,0EAA0E,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,UAAU,EAAC,IAAI,EAAC,CAAC,UAAU,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,2BAA2B,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,sBAAsB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,qBAAqB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,iBAAiB,EAAC,OAAO,EAAC,CAAC,wCAAwC,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,gBAAgB,EAAC,CAAC,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,WAAW,EAAC,oCAAoC,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,YAAY,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,aAAa,EAAC,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,0BAA0B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,aAAa,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,UAAU,EAAC,IAAI,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,qBAAqB,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,aAAa,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,MAAM,CAAC,EAAC,CAAC,uCAAuC,EAAC,gBAAgB,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,mCAAmC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,+BAA+B,EAAC,aAAa,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,SAAS,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,yDAAyD,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,oCAAoC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,QAAQ,EAAC,SAAS,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,wBAAwB,EAAC,oCAAoC,EAAC,kDAAkD,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,wBAAwB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,qBAAqB,EAAC,gCAAgC,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,y7CAAy7C,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,CAAC,EAAC,CAAC,2NAA2N,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,gCAAgC,CAAC,EAAC,CAAC,6DAA6D,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,sBAAsB,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,2FAA2F,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,8BAA8B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,4BAA4B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,4CAA4C,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,aAAa,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,iDAAiD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,mBAAmB,EAAC,QAAQ,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,mCAAmC,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,gCAAgC,CAAC,EAAC,CAAC,4DAA4D,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,sBAAsB,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,2FAA2F,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,2BAA2B,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,sBAAsB,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,4BAA4B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,aAAa,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,4BAA4B,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,4CAA4C,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,kFAAkF,CAAC,EAAC,CAAC,0GAA0G,CAAC,EAAC,CAAC,eAAe,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,4CAA4C,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,mCAAmC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,gDAAgD,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,4DAA4D,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,kCAAkC,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,8BAA8B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,oBAAoB,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,4BAA4B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,yBAAyB,EAAC,qDAAqD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,KAAK,EAAC,MAAM,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,gCAAgC,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,wBAAwB,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,8CAA8C,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,UAAU,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,eAAe,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,2BAA2B,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,SAAS,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,sDAAsD,CAAC,EAAC,SAAS,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,OAAO,CAAC,CAAC,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,QAAQ,EAAC,+EAA+E,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,cAAc,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,QAAQ,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,WAAW,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,+BAA+B,EAAC,aAAa,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,qDAAqD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,gCAAgC,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,yBAAyB,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,sBAAsB,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,+BAA+B,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,eAAe,EAAC,+CAA+C,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,KAAK,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,KAAK,CAAC,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,SAAS,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,KAAK,CAAC,CAAC,EAAC,CAAC,+BAA+B,EAAC,aAAa,EAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,oCAAoC,EAAC,aAAa,EAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,mCAAmC,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,mBAAmB,EAAC,QAAQ,EAAC,CAAC,EAAC,sBAAsB,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,yCAAyC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,UAAU,EAAC,IAAI,EAAC,CAAC,6BAA6B,EAAC,2CAA2C,EAAC,4CAA4C,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,KAAK,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,oCAAoC,EAAC,0DAA0D,EAAC,0FAA0F,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,qYAAqY,EAAC,keAAke,EAAC,ukBAAukB,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,gKAAgK,EAAC,uSAAuS,EAAC,iWAAiW,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,cAAc,EAAC,eAAe,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,SAAS,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,mCAAmC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,uCAAuC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,YAAY,EAAC,IAAI,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,0EAA0E,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,2BAA2B,EAAC,uBAAuB,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,6FAA6F,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,uCAAuC,EAAC,uDAAuD,CAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,gCAAgC,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,KAAK,CAAC,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,SAAS,CAAC,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,wBAAwB,CAAC,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,qBAAqB,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,6aAA6a,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,CAAC,EAAC,CAAC,gCAAgC,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,CAAC,EAAC,CAAC,uBAAuB,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,gHAAgH,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,CAAC,EAAC,CAAC,qBAAqB,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,CAAC,EAAC,CAAC,iBAAiB,EAAC,CAAC,EAAE,EAAC,EAAE,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,6BAA6B,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,mCAAmC,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,qBAAqB,EAAC,QAAQ,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,oBAAoB,CAAC,EAAC,CAAC,qDAAqD,CAAC,EAAC,CAAC,8BAA8B,CAAC,EAAC,CAAC,uGAAuG,CAAC,EAAC,CAAC,cAAc,CAAC,EAAC,CAAC,EAAC,CAAC,+GAA+G,CAAC,EAAC,CAAC,2FAA2F,CAAC,EAAC,CAAC,UAAU,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,8BAA8B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,SAAS,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,oCAAoC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,WAAW,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,KAAK,EAAC,yDAAyD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,eAAe,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,sFAAsF,EAAC,uKAAuK,EAAC,sLAAsL,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,uBAAuB,EAAC,yCAAyC,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,qVAAqV,EAAC,qoBAAqoB,EAAC,yvBAAyvB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,kCAAkC,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,SAAS,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,sCAAsC,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,0DAA0D,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,uBAAuB,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,qBAAqB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,aAAa,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,iCAAiC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,UAAU,EAAC,uBAAuB,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,kDAAkD,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,cAAc,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,0BAA0B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,OAAO,EAAC,gCAAgC,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,qDAAqD,EAAC,0GAA0G,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,8BAA8B,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,yBAAyB,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,SAAS,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,eAAe,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,2BAA2B,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,iCAAiC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,+BAA+B,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,8BAA8B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,oCAAoC,EAAC,CAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,SAAS,EAAC,CAAC,EAAC,MAAM,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,4CAA4C,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,mBAAmB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,2BAA2B,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,+CAA+C,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,8BAA8B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,mCAAmC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,qBAAqB,EAAC,yBAAyB,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,UAAU,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,aAAa,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,SAAS,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,gDAAgD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,YAAY,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,SAAS,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,2BAA2B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,8BAA8B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,SAAS,CAAC,EAAC,QAAQ,EAAC,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,MAAM,EAAC,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,qBAAqB,CAAC,EAAC,QAAQ,EAAC,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,EAAC,QAAQ,EAAC,CAAC,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,MAAM,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,wEAAwE,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,kEAAkE,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,kEAAkE,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,WAAW,CAAC,CAAC,EAAC,CAAC,oCAAoC,EAAC,aAAa,EAAC,CAAC,oBAAoB,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,gBAAgB,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,4CAA4C,EAAC,gBAAgB,EAAC,CAAC,oBAAoB,CAAC,CAAC,EAAC,CAAC,oCAAoC,EAAC,aAAa,EAAC,CAAC,iDAAiD,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,mDAAmD,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,uBAAuB,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,aAAa,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,aAAa,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,uCAAuC,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,+HAA+H,CAAC,EAAC,CAAC,yEAAyE,CAAC,EAAC,CAAC,eAAe,CAAC,EAAC,CAAC,UAAU,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,yCAAyC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,uBAAuB,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,uCAAuC,EAAC,gBAAgB,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,4BAA4B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,mCAAmC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,4CAA4C,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,wDAAwD,CAAC,EAAC,CAAC,0GAA0G,CAAC,EAAC,CAAC,eAAe,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,4CAA4C,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,YAAY,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,mBAAmB,EAAC,MAAM,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,iCAAiC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,eAAe,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,8BAA8B,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,+BAA+B,EAAC,aAAa,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,cAAc,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,mEAAmE,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,mFAAmF,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,yBAAyB,EAAC,UAAU,EAAC,CAAC,iBAAiB,EAAC,kBAAkB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,eAAe,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,yBAAyB,EAAC,UAAU,EAAC,CAAC,6BAA6B,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,+BAA+B,EAAC,aAAa,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,0BAA0B,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,YAAY,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,qCAAqC,EAAC,0CAA0C,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,iCAAiC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,6BAA6B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,4CAA4C,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,yBAAyB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,2BAA2B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,8BAA8B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,iBAAiB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,uCAAuC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,sBAAsB,EAAC,sCAAsC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,eAAe,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,WAAW,EAAC,0CAA0C,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,SAAS,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,mCAAmC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,SAAS,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,OAAO,EAAC,aAAa,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,aAAa,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,gCAAgC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,8BAA8B,EAAC,kDAAkD,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,+BAA+B,EAAC,aAAa,EAAC,CAAC,eAAe,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,kBAAkB,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,WAAW,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,cAAc,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,8BAA8B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,eAAe,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,YAAY,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,eAAe,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,kBAAkB,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,iCAAiC,EAAC,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,SAAS,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,6BAA6B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,8EAA8E,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,eAAe,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,yCAAyC,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,YAAY,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,yBAAyB,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,iBAAiB,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,8BAA8B,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,eAAe,EAAC,OAAO,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,mCAAmC,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,8CAA8C,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,wBAAwB,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,YAAY,EAAC,yFAAyF,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,SAAS,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,gBAAgB,EAAC,sBAAsB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,2BAA2B,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,kBAAkB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,UAAU,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,wBAAwB,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,4CAA4C,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,mDAAmD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,UAAU,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,yBAAyB,EAAC,uBAAuB,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,IAAI,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,eAAe,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,IAAI,EAAC,SAAS,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,8BAA8B,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,UAAU,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,aAAa,EAAC,kDAAkD,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,cAAc,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,iDAAiD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,eAAe,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,oEAAoE,EAAC,qHAAqH,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,uBAAuB,EAAC,+BAA+B,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,cAAc,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,oCAAoC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,iKAAiK,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,iJAAiJ,EAAC,qKAAqK,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,4DAA4D,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,IAAI,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,mDAAmD,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,QAAQ,CAAC,EAAC,OAAO,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,uDAAuD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,UAAU,EAAC,IAAI,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,UAAU,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,gFAAgF,EAAC,iFAAiF,CAAC,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,gDAAgD,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,6BAA6B,CAAC,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,kCAAkC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,2BAA2B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,SAAS,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,gCAAgC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,mCAAmC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,QAAQ,EAAC,gCAAgC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,sDAAsD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,uCAAuC,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,wDAAwD,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,sDAAsD,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,oBAAoB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,gDAAgD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,UAAU,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,iCAAiC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,sCAAsC,CAAC,EAAC,CAAC,4GAA4G,CAAC,EAAC,CAAC,UAAU,CAAC,EAAC,CAAC,kBAAkB,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,mDAAmD,CAAC,EAAC,CAAC,iCAAiC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,oCAAoC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,QAAQ,EAAC,YAAY,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,SAAS,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,OAAO,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,uGAAuG,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,sBAAsB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,qBAAqB,EAAC,OAAO,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,wBAAwB,EAAC,CAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,oBAAoB,EAAC,oDAAoD,EAAC,uEAAuE,CAAC,EAAC,QAAQ,EAAC,CAAC,CAAC,EAAC,CAAC,+BAA+B,EAAC,aAAa,EAAC,CAAC,qBAAqB,EAAC,0EAA0E,EAAC,4LAA4L,CAAC,EAAC,QAAQ,EAAC,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,QAAQ,EAAC,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,0BAA0B,CAAC,EAAC,QAAQ,EAAC,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,QAAQ,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,0MAA0M,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,4BAA4B,CAAC,EAAC,CAAC,cAAc,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,WAAW,EAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC,MAAM,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,gCAAgC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,0BAA0B,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,OAAO,EAAC,yBAAyB,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,YAAY,EAAC,wBAAwB,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,YAAY,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,yEAAyE,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,EAAC,UAAU,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,iBAAiB,CAAC,EAAC,KAAK,EAAC,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,wCAAwC,CAAC,EAAC,KAAK,EAAC,CAAC,EAAC,UAAU,CAAC,EAAC,CAAC,iCAAiC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,EAAC,aAAa,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,sHAAsH,CAAC,EAAC,KAAK,EAAC,CAAC,EAAC,UAAU,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,eAAe,CAAC,EAAC,KAAK,EAAC,CAAC,EAAC,UAAU,CAAC,EAAC,CAAC,oCAAoC,EAAC,aAAa,EAAC,CAAC,mDAAmD,CAAC,EAAC,KAAK,EAAC,CAAC,EAAC,aAAa,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,EAAC,CAAC,EAAC,aAAa,CAAC,EAAC,CAAC,+BAA+B,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,EAAC,aAAa,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,+EAA+E,CAAC,EAAC,KAAK,EAAC,CAAC,EAAC,aAAa,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,EAAC,aAAa,CAAC,EAAC,CAAC,0CAA0C,EAAC,gBAAgB,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,EAAC,CAAC,EAAC,gBAAgB,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,WAAW,EAAC,8CAA8C,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,yBAAyB,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,sBAAsB,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,OAAO,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,sBAAsB,EAAC,kCAAkC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,SAAS,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,uBAAuB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,+BAA+B,EAAC,aAAa,EAAC,CAAC,QAAQ,CAAC,EAAC,OAAO,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,+BAA+B,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,wCAAwC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,yBAAyB,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,aAAa,EAAC,iBAAiB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,+BAA+B,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,yBAAyB,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,SAAS,CAAC,EAAC,OAAO,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,yBAAyB,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,eAAe,EAAC,QAAQ,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,uBAAuB,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,sDAAsD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,IAAI,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,eAAe,EAAC,OAAO,EAAC,CAAC,+BAA+B,CAAC,CAAC,EAAC,CAAC,eAAe,EAAC,OAAO,EAAC,CAAC,gBAAgB,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,gBAAgB,CAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,6BAA6B,CAAC,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,+BAA+B,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,YAAY,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,mBAAmB,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,4CAA4C,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,qCAAqC,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,cAAc,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,0BAA0B,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,mCAAmC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,SAAS,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,8BAA8B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,OAAO,EAAC,6BAA6B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,IAAI,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,aAAa,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,SAAS,EAAC,4CAA4C,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,gBAAgB,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,KAAK,EAAC,MAAM,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,kBAAkB,CAAC,CAAC,EAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,mBAAmB,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,MAAM,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,gBAAgB,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,mCAAmC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,YAAY,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,uBAAuB,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,IAAI,CAAC,EAAC,QAAQ,CAAC,EAAC,CAAC,+BAA+B,EAAC,aAAa,EAAC,CAAC,OAAO,CAAC,EAAC,QAAQ,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,EAAC,MAAM,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,MAAM,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,gBAAgB,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,mDAAmD,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,iCAAiC,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,gDAAgD,EAAC,CAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,iBAAiB,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,gBAAgB,EAAC,oBAAoB,EAAC,qBAAqB,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,kBAAkB,CAAC,EAAC,OAAO,EAAC,CAAC,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,0BAA0B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,qBAAqB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,wBAAwB,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,kBAAkB,EAAC,8CAA8C,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,yBAAyB,EAAC,UAAU,EAAC,CAAC,uCAAuC,EAAC,8CAA8C,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,SAAS,EAAC,2BAA2B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,wBAAwB,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,6DAA6D,EAAC,iFAAiF,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,uFAAuF,EAAC,mGAAmG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,iBAAiB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,SAAS,EAAC,oCAAoC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,KAAK,EAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,yBAAyB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,qBAAqB,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,YAAY,EAAC,CAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,UAAU,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,quBAAquB,CAAC,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,sCAAsC,CAAC,EAAC,CAAC,gBAAgB,CAAC,EAAC,CAAC,yOAAyO,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,cAAc,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,kBAAkB,EAAC,gDAAgD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,SAAS,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,EAAC,CAAC,oCAAoC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,IAAI,EAAC,QAAQ,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,0BAA0B,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,8EAA8E,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,OAAO,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,8BAA8B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,sCAAsC,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,UAAU,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,8BAA8B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,GAAG,EAAC,KAAK,EAAC,6BAA6B,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,EAAC,KAAK,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,kDAAkD,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,SAAS,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,oCAAoC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,oCAAoC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,GAAG,EAAC,+CAA+C,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,UAAU,EAAC,IAAI,EAAC,CAAC,cAAc,CAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,+CAA+C,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,qBAAqB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,8BAA8B,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,yBAAyB,EAAC,UAAU,EAAC,CAAC,0BAA0B,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,+CAA+C,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,4CAA4C,CAAC,EAAC,CAAC,8EAA8E,CAAC,EAAC,CAAC,UAAU,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC;IAAC,IAAI,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,wBAAwB,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,oCAAoC,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG,CAAC;IAAC,IAAI,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,uHAAuH,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,wFAAwF,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,yBAAyB,EAAC,UAAU,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,4CAA4C,EAAC,4EAA4E,CAAC,EAAC,OAAO,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,8CAA8C,EAAC,+CAA+C,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,2IAA2I,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,iBAAiB,CAAC,EAAC,KAAK,CAAC,EAAC,CAAC,oBAAoB,EAAC,OAAO,EAAC,CAAC,YAAY,EAAC,UAAU,CAAC,EAAC,KAAK,CAAC,CAAC,EAAC,GAAG;EAAC,CAAC;EAAC,eAAe,EAAC;IAAC,KAAK,EAAC,CAAC,KAAK,EAAC,CAAC,EAAC,uBAAuB,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC;IAAC,KAAK,EAAC,CAAC,KAAK,EAAC,CAAC,EAAC,aAAa,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,aAAa,CAAC,CAAC,CAAC;IAAC,KAAK,EAAC,CAAC,KAAK,EAAC,CAAC,EAAC,wBAAwB,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,sCAAsC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAAC,KAAK,EAAC,CAAC,KAAK,EAAC,CAAC,EAAC,WAAW,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,WAAW,CAAC,CAAC,CAAC;IAAC,KAAK,EAAC,CAAC,KAAK,EAAC,CAAC,EAAC,wBAAwB,EAAC,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,UAAU,CAAC,CAAC,EAAC,CAAC,yBAAyB,EAAC,UAAU,EAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC;IAAC,KAAK,EAAC,CAAC,KAAK,EAAC,CAAC,EAAC,sEAAsE,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,OAAO,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,SAAS,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,4BAA4B,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,iEAAiE,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,eAAe,EAAC,CAAC,EAAE,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,+KAA+K,CAAC,CAAC,CAAC;IAAC,KAAK,EAAC,CAAC,KAAK,EAAC,CAAC,EAAC,0BAA0B,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC,EAAC,CAAC,CAAC,4BAA4B,EAAC,UAAU,EAAC,CAAC,mCAAmC,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,KAAK,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,kCAAkC,EAAC,aAAa,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,kIAAkI,CAAC,CAAC,CAAC;IAAC,KAAK,EAAC,CAAC,KAAK,EAAC,CAAC,EAAC,SAAS,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,CAAC,0BAA0B,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC;IAAC,KAAK,EAAC,CAAC,KAAK,EAAC,CAAC,EAAC,cAAc,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,uBAAuB,EAAC,UAAU,EAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,cAAc,CAAC,CAAC;EAAC;AAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}