{"version": 3, "file": "extractExtension.js", "names": ["createExtensionPattern", "EXTN_PATTERN", "RegExp", "extractExtension", "number", "start", "search", "numberWithoutExtension", "slice", "matches", "match", "i", "length", "ext"], "sources": ["../../../source/helpers/extension/extractExtension.js"], "sourcesContent": ["import createExtensionPattern from './createExtensionPattern.js'\r\n\r\n// Regexp of all known extension prefixes used by different regions followed by\r\n// 1 or more valid digits, for use when parsing.\r\nconst EXTN_PATTERN = new RegExp('(?:' + createExtensionPattern() + ')$', 'i')\r\n\r\n// Strips any extension (as in, the part of the number dialled after the call is\r\n// connected, usually indicated with extn, ext, x or similar) from the end of\r\n// the number, and returns it.\r\nexport default function extractExtension(number) {\r\n\tconst start = number.search(EXTN_PATTERN)\r\n\tif (start < 0) {\r\n\t\treturn {}\r\n\t}\r\n\t// If we find a potential extension, and the number preceding this is a viable\r\n\t// number, we assume it is an extension.\r\n\tconst numberWithoutExtension = number.slice(0, start)\r\n\tconst matches = number.match(EXTN_PATTERN)\r\n\tlet i = 1\r\n\twhile (i < matches.length) {\r\n\t\tif (matches[i]) {\r\n\t\t\treturn {\r\n\t\t\t\tnumber: numberWithoutExtension,\r\n\t\t\t\text: matches[i]\r\n\t\t\t}\r\n\t\t}\r\n\t\ti++\r\n\t}\r\n}"], "mappings": "AAAA,OAAOA,sBAAP,MAAmC,6BAAnC,C,CAEA;AACA;;AACA,IAAMC,YAAY,GAAG,IAAIC,MAAJ,CAAW,QAAQF,sBAAsB,EAA9B,GAAmC,IAA9C,EAAoD,GAApD,CAArB,C,CAEA;AACA;AACA;;AACA,eAAe,SAASG,gBAAT,CAA0BC,MAA1B,EAAkC;EAChD,IAAMC,KAAK,GAAGD,MAAM,CAACE,MAAP,CAAcL,YAAd,CAAd;;EACA,IAAII,KAAK,GAAG,CAAZ,EAAe;IACd,OAAO,EAAP;EACA,CAJ+C,CAKhD;EACA;;;EACA,IAAME,sBAAsB,GAAGH,MAAM,CAACI,KAAP,CAAa,CAAb,EAAgBH,KAAhB,CAA/B;EACA,IAAMI,OAAO,GAAGL,MAAM,CAACM,KAAP,CAAaT,YAAb,CAAhB;EACA,IAAIU,CAAC,GAAG,CAAR;;EACA,OAAOA,CAAC,GAAGF,OAAO,CAACG,MAAnB,EAA2B;IAC1B,IAAIH,OAAO,CAACE,CAAD,CAAX,EAAgB;MACf,OAAO;QACNP,MAAM,EAAEG,sBADF;QAENM,GAAG,EAAEJ,OAAO,CAACE,CAAD;MAFN,CAAP;IAIA;;IACDA,CAAC;EACD;AACD"}