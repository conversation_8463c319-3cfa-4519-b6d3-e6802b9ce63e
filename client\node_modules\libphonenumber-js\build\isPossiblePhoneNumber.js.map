{"version": 3, "file": "isPossiblePhoneNumber.js", "names": ["isPossiblePhoneNumber", "normalizeArguments", "arguments", "text", "options", "metadata", "extract", "phoneNumber", "parsePhoneNumber", "isPossible"], "sources": ["../source/isPossiblePhoneNumber.js"], "sourcesContent": ["import normalizeArguments from './normalizeArguments.js'\r\nimport parsePhoneNumber from './parsePhoneNumber_.js'\r\n\r\nexport default function isPossiblePhoneNumber() {\r\n\tlet { text, options, metadata } = normalizeArguments(arguments)\r\n\toptions = {\r\n\t\t...options,\r\n\t\textract: false\r\n\t}\r\n\tconst phoneNumber = parsePhoneNumber(text, options, metadata)\r\n\treturn phoneNumber && phoneNumber.isPossible() || false\r\n}"], "mappings": ";;;;;;;AAAA;;AACA;;;;;;;;;;AAEe,SAASA,qBAAT,GAAiC;EAC/C,0BAAkC,IAAAC,+BAAA,EAAmBC,SAAnB,CAAlC;EAAA,IAAMC,IAAN,uBAAMA,IAAN;EAAA,IAAYC,OAAZ,uBAAYA,OAAZ;EAAA,IAAqBC,QAArB,uBAAqBA,QAArB;;EACAD,OAAO,mCACHA,OADG;IAENE,OAAO,EAAE;EAFH,EAAP;EAIA,IAAMC,WAAW,GAAG,IAAAC,6BAAA,EAAiBL,IAAjB,EAAuBC,OAAvB,EAAgCC,QAAhC,CAApB;EACA,OAAOE,WAAW,IAAIA,WAAW,CAACE,UAAZ,EAAf,IAA2C,KAAlD;AACA"}