import { Component, OnInit, ViewChild } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormArray } from '@angular/forms';
import { Subject, takeUntil, Observable, concat, map, of } from 'rxjs';
import { ProspectsService } from '../prospects.service';
import { MessageService } from 'primeng/api';
import { Router } from '@angular/router';
import { Country, State } from 'country-state-city';
import { finalize } from 'rxjs/operators';
import {
  distinctUntilChanged,
  switchMap,
  tap,
  catchError,
} from 'rxjs/operators';
import { CountryWiseMobileComponent } from '../../common-form/country-wise-mobile/country-wise-mobile.component';

@Component({
  selector: 'app-add-prospect',
  templateUrl: './add-prospect.component.html',
  styleUrl: './add-prospect.component.scss',
})
export class AddProspectComponent implements OnInit {
  @ViewChild(CountryWiseMobileComponent)
  countryMobileComponent!: CountryWiseMobileComponent;
  private ngUnsubscribe = new Subject<void>();
  public ProspectForm: FormGroup = this.formBuilder.group({
    bp_full_name: ['', [Validators.required]],
    email_address: ['', [Validators.required, Validators.email]],
    website_url: [
      '',
      [
        Validators.pattern(
          /^(https?:\/\/)?([\w\-]+\.)+[\w\-]+(\/[\w\-./?%&=]*)?$/
        ),
      ],
    ],
    owner: [''],
    additional_street_prefix_name: [''],
    additional_street_suffix_name: [''],
    house_number: [''],
    street_name: [''],
    city_name: [''],
    region: ['', [Validators.required]],
    country: ['', [Validators.required]],
    postal_code: [
      '',
      [Validators.required, Validators.pattern(/^[A-Za-z0-9]{5}$/)],
    ],
    fax_number: [''],
    phone_number: ['', [Validators.required]],
    mobile: [''],
    contactexisting: [null],
    contacts: this.formBuilder.array([this.createContactFormGroup()]),
    employees: this.formBuilder.array([this.createEmployeeFormGroup()]),
  });

  public submitted = false;
  public saving = false;
  public existingMessage: string = '';
  public existingDialogVisible: boolean = false;
  public position: string = 'right';
  public partnerfunction: { label: string; value: string }[] = [];
  public partnerLoading = false;
  public countries: any[] = [];
  public states: any[] = [];
  public selectedCountry: string = '';
  public selectedState: string = '';
  public contacts$?: Observable<any[]>;
  public contactLoading = false;
  public contactInput$ = new Subject<string>();
  private defaultOptions: any = [];
  public cpDepartments: { name: string; value: string }[] = [];
  public phoneValidationMessage: string | null = null;
  public mobileValidationMessage: string | null = null;
  public selectedCountryForMobile: string =
  this.ProspectForm.get('country')?.value;

  constructor(
    private formBuilder: FormBuilder,
    private prospectsservice: ProspectsService,
    private messageservice: MessageService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.ProspectForm.get('country')?.valueChanges.subscribe((countryCode) => {
      this.selectedCountryForMobile = countryCode;
    });
    this.loadContacts();
    this.loadPartners();
    this.loadDepartment();
    this.loadCountries();
  }

  public loadDepartment(): void {
    this.prospectsservice
      .getCPDepartment()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((response: any) => {
        if (response && response.data) {
          this.cpDepartments = [
            { name: 'Select Department', value: null },
            ...response.data.map((item: any) => ({
              name: item.description,
              value: item.code,
            })),
          ];
        }
      });
  }

  loadCountries() {
    const allCountries = Country.getAllCountries()
      .map((country: any) => ({
        name: country.name,
        isoCode: country.isoCode,
      }))
      .filter(
        (country) => State.getStatesOfCountry(country.isoCode).length > 0
      );

    const unitedStates = allCountries.find((c) => c.isoCode === 'US');
    const canada = allCountries.find((c) => c.isoCode === 'CA');
    const others = allCountries
      .filter((c) => c.isoCode !== 'US' && c.isoCode !== 'CA')
      .sort((a, b) => a.name.localeCompare(b.name)); // sort others alphabetically

    this.countries = [unitedStates, canada, ...others].filter(Boolean);
  }

  onCountryChange() {
    this.states = State.getStatesOfCountry(this.selectedCountry).map(
      (state) => ({
        name: state.name,
        isoCode: state.isoCode,
      })
    );
    this.selectedState = ''; // Reset state
  }

  triggerMobileValidation() {
    this.countryMobileComponent.validatePhone();
  }

  private loadPartners(): void {
    this.partnerLoading = true;
    this.prospectsservice
      .getPartnerfunction()
      .pipe(finalize(() => (this.partnerLoading = false)))
      .subscribe({
        next: (data: any) => {
          // Replace `any` with the correct type if known
          this.partnerfunction = data;
        },
        error: (error) => {
          console.error('Error fetching partner data:', error);
        },
      });
  }

  private loadContacts() {
    this.contacts$ = concat(
      of(this.defaultOptions), // Default empty options
      this.contactInput$.pipe(
        distinctUntilChanged(),
        tap(() => (this.contactLoading = true)),
        switchMap((term: string) => {
          const params: any = {
            [`filters[roles][bp_role][$eq]`]: 'BUP001',
            [`fields[0]`]: 'bp_id',
            [`fields[1]`]: 'first_name',
            [`fields[2]`]: 'last_name',
            [`fields[3]`]: 'bp_full_name',
          };

          if (term) {
            params[`filters[$or][0][bp_id][$containsi]`] = term;
            params[`filters[$or][1][bp_full_name][$containsi]`] = term;
            return this.prospectsservice.getContacts(params).pipe(
              map((data: any) => {
                return data || []; // Make sure to return correct data structure
              }),
              tap(() => (this.contactLoading = false)),
              catchError((error) => {
                this.contactLoading = false;
                return of([]);
              })
            );
          }

          return of([]).pipe(tap(() => (this.contactLoading = false)));
        })
      )
    );
  }

  onChangeDepartment(event: any, contact: FormGroup) {
    contact.get('contact_person_department')?.patchValue(event.value.value);
    contact.get('contact_person_department_name')?.patchValue(event.value.name);
  }

  async onSubmit() {
    this.submitted = true;

    if (this.ProspectForm.invalid) {
      return;
    }

    this.saving = true;
    const value = { ...this.ProspectForm.value };

    const selectedcodewisecountry = this.countries.find(
      (c) => c.isoCode === this.selectedCountry
    );

    const selectedState = this.states.find(
      (state) => state.isoCode === value?.region
    );

    const contactsArray = (
      Array.isArray(value.contacts) ? value.contacts : []
    ).map((contact: any) => ({
      ...contact,
      destination_location_country: selectedcodewisecountry?.isoCode,
    }));

    const data = {
      bp_full_name: value?.bp_full_name,
      email_address: value?.email_address,
      fax_number: value?.fax_number,
      website_url: value?.website_url,
      phone_number: value?.phone_number,
      mobile: value?.mobile,
      house_number: value?.house_number,
      additional_street_prefix_name: value?.additional_street_prefix_name,
      additional_street_suffix_name: value?.additional_street_suffix_name,
      street_name: value?.street_name,
      city_name: value?.city_name,
      country: selectedcodewisecountry?.name,
      county_code: selectedcodewisecountry?.isoCode,
      destination_location_country: selectedcodewisecountry?.isoCode,
      postal_code: value?.postal_code,
      region: selectedState?.isoCode,
      contacts: contactsArray,
      employees: value.employees,
    };

    this.prospectsservice
      .createProspect(data)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (response: any) => {
          if (response?.data?.documentId) {
            sessionStorage.setItem(
              'prospectMessage',
              'Prospect created successfully!'
            );
            window.location.href = `${window.location.origin}#/store/prospects/${response?.data?.bp_id}/overview`;
          } else {
            console.error('Missing documentId in response:', response);
          }
        },
        error: (res: any) => {
          this.saving = false;
          const msg: any = res?.error?.message || null;
          if (msg) {
            if (
              msg &&
              msg.includes('unique constraint violated') &&
              msg.includes("constraint='EMAIL'")
            ) {
              this.messageservice.add({
                severity: 'error',
                detail: 'Given email address already in use.',
              });
            } else {
              this.messageservice.add({
                severity: 'error',
                detail: res?.error?.message,
              });
            }
          } else {
            this.messageservice.add({
              severity: 'error',
              detail: 'Error while processing your request.',
            });
          }
        },
      });
  }

  selectExistingContact() {
    this.addExistingContact(this.ProspectForm.value);
    this.existingDialogVisible = false; // Close dialog
  }

  addExistingContact(existing: any) {
    const name = existing.contactexisting.bp_full_name.split(' ');
    const contactForm = this.formBuilder.group({
      first_name: [name[0] || '', [Validators.required]],
      last_name: [name[1] || '', [Validators.required]],
      contact_person_department: [
        existing.contact_person_department || null,
        [Validators.required],
      ],
      contact_person_department_name: [
        existing.contact_person_department_name || '',
        [Validators.required],
      ],
      email_address: [
        existing.contactexisting.email || '',
        [Validators.required, Validators.email],
      ],
      mobile: [existing.contactexisting.mobile || '', [Validators.required]],
      bp_person_id: existing.contactexisting.bp_id,
    });
    this.contacts.push(contactForm);
    if (this.ProspectForm.value.contacts[0]?.first_name == '') {
      this.deleteContact(0);
    }
  }

  addNewContact() {
    this.contacts.push(this.createContactFormGroup());
  }

  addNewEmployee() {
    this.employees.push(this.createEmployeeFormGroup());
  }

  createContactFormGroup(): FormGroup {
    return this.formBuilder.group({
      first_name: ['', [Validators.required]],
      last_name: ['', [Validators.required]],
      contact_person_department_name: ['', [Validators.required]],
      contact_person_department: ['', [Validators.required]],
      email_address: ['', [Validators.required, Validators.email]],
      mobile: ['', [Validators.required]],
    });
  }

  createEmployeeFormGroup(): FormGroup {
    return this.formBuilder.group({
      partner_function: [null],
      bp_customer_number: [null],
    });
  }

  deleteContact(index: number) {
    if (this.contacts.length > 1) {
      this.contacts.removeAt(index);
    }
  }

  deleteEmployee(index: number) {
    if (this.employees.length > 1) {
      this.employees.removeAt(index);
    }
  }

  isFieldInvalid(index: number, field: string, arrayName: string) {
    const control = (this.ProspectForm.get(arrayName) as FormArray)
      .at(index)
      .get(field);
    return control?.invalid && (control?.touched || this.submitted);
  }

  get f(): any {
    return this.ProspectForm.controls;
  }

  get contacts(): any {
    return this.ProspectForm.get('contacts') as FormArray;
  }

  get employees(): any {
    return this.ProspectForm.get('employees') as FormArray;
  }

  showExistingDialog(position: string) {
    this.position = position;
    this.existingDialogVisible = true;
  }

  onCancel() {
    this.router.navigate(['/store/prospects']);
  }

  onReset(): void {
    this.submitted = false;
    this.ProspectForm.reset();
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
