import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CMS_APIContstant } from 'src/app/constants/api.constants';
import { ApiConstant } from 'src/app/constants/api.constants';
import { AuthService } from 'src/app/core/authentication/auth.service';
import {
  getCountryCallingCode,
  getCountries,
  CountryCode,
} from 'libphonenumber-js';

@Injectable({
  providedIn: 'root',
})
export class ProspectsService {
  public prospectSubject = new BehaviorSubject<any>(null);
  public prospect = this.prospectSubject.asObservable();

  constructor(private http: HttpClient, private authservice: AuthService) {}

  createProspect(data: any): Observable<any> {
    return this.http.post(`${CMS_APIContstant.REGISTER_PROSPECTS}`, data);
  }

  createMarketing(data: any): Observable<any> {
    return this.http.post(`${CMS_APIContstant.MARKETING_ATTRIBUTES}`, {
      data,
    });
  }

  createNote(data: any): Observable<any> {
    return this.http.post(`${CMS_APIContstant.CRM_NOTE}`, {
      data,
    });
  }

  createAddress(data: any): Observable<any> {
    return this.http.post(
      `${CMS_APIContstant.PROSPECT_ADDRESS_REGISTER}`,
      data
    );
  }

  createContact(data: any): Observable<any> {
    return this.http.post(`${CMS_APIContstant.CREATE_CONTACT}`, data);
  }

  createExistingContact(data: any): Observable<any> {
    return this.http.post(`${CMS_APIContstant.EXISTING_CONTACT}`, data);
  }

  createEmployee(data: any): Observable<any> {
    return this.http.post(
      `${CMS_APIContstant.REGISTER_PROSPECT_SALES_TEAM}`,
      data
    );
  }

  updateProspect(Id: string, data: any): Observable<any> {
    return this.http.put(`${CMS_APIContstant.PROSPECTS}/${Id}/save`, data);
  }

  updateMarketing(Id: string, data: any): Observable<any> {
    return this.http.put(`${CMS_APIContstant.MARKETING_ATTRIBUTES}/${Id}`, {
      data,
    });
  }

  updateAddress(Id: string, data: any): Observable<any> {
    return this.http.put(
      `${CMS_APIContstant.PROSPECT_ADDRESS}/${Id}/save`,
      data
    );
  }

  updateContact(Id: string, data: any): Observable<any> {
    return this.http.put(
      `${CMS_APIContstant.PROSPECT_CONTACT}/${Id}/save`,
      data
    );
  }

  updateReactivate(contactdata: any): Observable<any> {
    const data = {
      validity_end_date: '9999-12-29',
    };
    return this.http.put(
      `${CMS_APIContstant.PARTNERS_CONTACTS}/${contactdata.documentId}`,
      { data }
    );
  }

  updateEmployee(Id: string, data: any): Observable<any> {
    return this.http.put(`${CMS_APIContstant.SALES_TEAM}/${Id}/save`, data);
  }

  updateBpStatus(Id: string, data: any): Observable<any> {
    return this.http.put(`${CMS_APIContstant.PARTNERS}/${Id}`, { data });
  }

  updateNote(Id: string, data: any): Observable<any> {
    return this.http.put(`${CMS_APIContstant.CRM_NOTE}/${Id}`, {
      data,
    });
  }

  getProspects(
    page: number,
    pageSize: number,
    sortField?: string,
    sortOrder?: number,
    searchTerm?: string,
    obsolete?: boolean,
    myprospect?: boolean
  ): Observable<any[]> {
    let params = new HttpParams()
      .set('pagination[page]', Math.max(page, 1).toString())
      .set('pagination[pageSize]', Math.max(pageSize, 1).toString())
      .set('fields', 'bp_id,bp_full_name,is_marked_for_archiving')
      .set('filters[roles][bp_role][$in][0]', 'PRO001')
      .set('populate[addresses][fields][0]', 'house_number')
      .set('populate[addresses][fields][1]', 'street_name')
      .set('populate[addresses][fields][2]', 'city_name')
      .set('populate[addresses][fields][3]', 'region')
      .set('populate[addresses][fields][4]', 'country')
      .set('populate[addresses][fields][5]', 'postal_code')
      .set(
        'populate[addresses][populate][address_usages][fields][0]',
        'address_usage'
      )
      .set('populate[addresses][populate][emails][fields][0]', 'email_address')
      .set(
        'populate[addresses][populate][phone_numbers][fields][0]',
        'phone_number'
      )
      .set(
        'populate[addresses][populate][phone_numbers][fields][1]',
        'phone_number_type'
      )
      .set(
        'populate[addresses][populate][phone_numbers][fields][2]',
        'destination_location_country'
      )
      .set(
        'populate[contact_companies][populate][business_partner_person][fields][0]',
        'first_name'
      )
      .set(
        'populate[contact_companies][populate][business_partner_person][fields][1]',
        'last_name'
      )
      .set('populate[customer][fields][0]', 'id')
      .set(
        'populate[customer][populate][partner_functions][fields][0]',
        'partner_function'
      )
      .set(
        'populate[customer][populate][partner_functions][populate][business_partner][fields][0]',
        'bp_full_name'
      );

    if (sortField && sortField.trim() !== '' && sortOrder !== undefined) {
      const order = sortOrder === 1 ? 'asc' : 'desc';
      params = params.set('sort', `${sortField}:${order}`);
    }

    if (searchTerm && searchTerm.trim() !== '') {
      params = params
        .set('filters[$or][0][bp_id][$containsi]', searchTerm)
        .set('filters[$or][1][bp_full_name][$containsi]', searchTerm)
        .set('filters[$or][2][addresses][house_number][$containsi]', searchTerm)
        .set('filters[$or][3][addresses][city_name][$containsi]', searchTerm)
        .set(
          'filters[$or][4][addresses][emails][email_address][$containsi]',
          searchTerm
        )
        .set(
          'filters[$or][5][addresses][phone_numbers][phone_number][$containsi]',
          searchTerm
        )
        .set(
          'filters[$or][6][contact_companies][business_partner_person][first_name][$containsi]',
          searchTerm
        )
        .set(
          'filters[$or][7][contact_companies][business_partner_person][last_name][$containsi]',
          searchTerm
        );
    }

    // Combine obsolete and myprospect filters carefully to avoid index collision
    if (obsolete || myprospect) {
      let andIndex = 0;
      if (obsolete) {
        params = params.set(
          `filters[$and][${andIndex}][is_marked_for_archiving][$eq]`,
          'true'
        );
        andIndex++;
      }
      if (myprospect) {
        const email = this.authservice.getUserEmail();
        if (email) {
          params = params
            .set(
              `filters[$and][${andIndex}][customer][partner_functions][partner_function][$eq]`,
              'YI'
            )
            .set(
              `filters[$and][${
                andIndex + 1
              }][customer][partner_functions][business_partner][addresses][emails][email_address][$containsi]`,
              email
            );
        } else {
          console.warn('No email found for the logged-in user');
        }
      }
    }

    return this.http.get<any[]>(`${CMS_APIContstant.PARTNERS}`, { params });
  }

  getEmployee(params: any) {
    return this.http
      .get<any[]>(`${CMS_APIContstant.PARTNERS}`, {
        params,
      })
      .pipe(
        map((response: any) => {
          return response.data || [];
        })
      );
  }

  getContacts(params: any) {
    return this.http
      .get<any>(
        `${CMS_APIContstant.PARTNERS}?populate[addresses][populate]=*&populate[address_usages][populate]`,
        { params }
      )
      .pipe(
        map((response) =>
          (response?.data || []).map((item: any) => {
            const contact = item?.addresses?.[0];

            const email = contact?.emails?.[0]?.email_address || '';
            const mobile = (contact?.phone_numbers || [])
              .filter((item: any) => item.phone_number_type === '3')
              .map((item: any) => item.phone_number);

            return {
              bp_id: item?.bp_id || '',
              bp_full_name:
                (item?.first_name ? item.first_name : '') +
                (item?.last_name ? ' ' + item.last_name : ''),
              email: email,
              mobile: mobile,
            };
          })
        )
      );
  }

  getPartnerfunction(): Observable<{ label: string; value: string }[]> {
    let params = new HttpParams()
      .set('filters[usage][$eq]', 'CRM')
      .set('filters[is_active][$eq]', 'true')
      .set('filters[type][$eq]', 'PARTNER_FUNCTIONS');

    return this.http
      .get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params })
      .pipe(
        map((response: any) => {
          let data = response.data || [];
          return data.map((item: any) => ({
            label: item.description, // Display text
            value: item.code, // Stored value
          }));
        })
      );
  }

  getCPFunction() {
    let params = new HttpParams()
      .set('filters[is_active][$eq]', 'true')
      .set('filters[type][$eq]', 'FUNCTION_CP');

    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });
  }

  getSizeUnit() {
    let params = new HttpParams()
      .set('filters[is_active][$eq]', 'true')
      .set('filters[type][$eq]', 'BPMA_SIZE_UNIT');

    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });
  }

  getChainScale() {
    let params = new HttpParams()
      .set('filters[is_active][$eq]', 'true')
      .set('filters[type][$eq]', 'BPMA_STR_CHAIN_SCALE');

    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });
  }

  getCPDepartment() {
    let params = new HttpParams()
      .set('filters[is_active][$eq]', 'true')
      .set('filters[type][$eq]', 'CP_DEPARTMENTS');

    return this.http.get<any>(`${CMS_APIContstant.CONFIG_DATA}`, { params });
  }

  getGlobalNote(id: string) {
    let params = new HttpParams()
      .set('filters[is_global_note][$eq]', 'true')
      .set('filters[bp_id][$eq]', id);

    return this.http.get<any>(`${CMS_APIContstant.CRM_NOTE}`, {
      params,
    });
  }

  getDialCode(
    countryCodeRaw: string | undefined | null,
    mobileNumber: string
  ): string {
    // Normalise & validate ISO code
    const iso = (countryCodeRaw ?? '').trim().toUpperCase();
    if (!iso) {
      return mobileNumber;
    }

    const validCodes = getCountries(); // array of CountryCode
    if (!validCodes.includes(iso as CountryCode)) {
      return mobileNumber;
    }

    const dialCode = '+' + getCountryCallingCode(iso as CountryCode);

    if (mobileNumber.startsWith(dialCode)) {
      return mobileNumber; // already has correct dial code
    }

    if (mobileNumber.startsWith('+')) {
      // Strip wrong code so we don’t end up with two
      mobileNumber = mobileNumber.replace(/^\+\d+/, '');
    }

    return dialCode + mobileNumber;
  }

  delete(id: string) {
    return this.http.delete<any>(`${CMS_APIContstant.PROSPECTS}/${id}`);
  }

  deleteContact(id: string) {
    return this.http.delete<any>(`${CMS_APIContstant.PARTNERS_CONTACTS}/${id}`);
  }

  deleteNote(id: string) {
    return this.http.delete<any>(`${CMS_APIContstant.CRM_NOTE}/${id}`);
  }

  deleteAddress(id: string) {
    return this.http.delete<any>(`${CMS_APIContstant.PARTNERS_ADDRESS}/${id}`);
  }

  deleteEmployee(id: string) {
    return this.http.delete<any>(`${CMS_APIContstant.DELETE_SALES_TEAM}/${id}`);
  }

  getProspectByID(prospectId: string) {
    const params = new HttpParams()
      .set('filters[bp_id][$eq]', prospectId)
      .set('populate[addresses][populate]', '*')
      .set(
        'populate[customer][populate][partner_functions][populate][business_partner][populate][addresses][populate]',
        '*'
      )
      .set('populate[notes][populate]', '*')
      .set('populate[bp_extension][populate]', '*')
      .set('populate[marketing_attributes][populate]', '*')
      .set(
        'populate[contact_companies][populate][person_func_and_dept][populate]',
        '*'
      )
      .set(
        'populate[contact_companies][populate][business_partner_person][populate][addresses][populate]',
        '*'
      )
      .set(
        'populate[contact_companies][populate][business_partner_person][populate][bp_extension][populate]',
        '*'
      )
      .set('populate[address_usages][populate]', '*');

    return this.http
      .get<any[]>(`${CMS_APIContstant.PARTNERS}`, { params })
      .pipe(
        map((response: any) => {
          const prospectDetails = response?.data[0] || null;
          this.prospectSubject.next(prospectDetails);
          return response;
        })
      );
  }

  bpCreation(body: any): any {
    return this.http.post<any>(`${ApiConstant.BUSINESS_PARTNER}`, body);
  }
}
