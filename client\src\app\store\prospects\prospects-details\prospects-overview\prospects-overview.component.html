<div class="p-3 w-full bg-white border-round shadow-1">
  <div class="card-heading mb-2 flex align-items-center justify-content-start gap-2">
    <h4 class="m-0 pl-3 left-border relative flex">Prospect</h4>
    <p-button [label]="isEditMode ? 'Close' : 'Edit'" [icon]="!isEditMode ? 'pi pi-pencil' : ''" iconPos="right"
      class="ml-auto" [styleClass]="'w-5rem font-semibold px-3'" (click)="toggleEdit()" [rounded]="true" />

  </div>
  <div *ngIf="!isEditMode" class="p-fluid p-formgrid grid m-0">
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">person</span>
          Name
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ ProspectOverviewForm.value?.bp_full_name || "-" }}
        </div>
      </div>
    </div>

    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">mail</span>
          Email Address
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ ProspectOverviewForm.value?.email_address || "-" }}
        </div>
      </div>
    </div>
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">globe</span>
          Wesbite
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ ProspectOverviewForm.value?.website_url || "-" }}
        </div>
      </div>
    </div>
    <!-- <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">supervisor_account</span> Owner
                </label>
                <div class="readonly-field font-medium text-700 p-2">{{ ProspectForm.value?.owner || '-' }}</div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">pin_drop</span> Address Line
                </label>
                <div class="readonly-field font-medium text-700 p-2">{{
                    ProspectForm.value?.additional_street_prefix_name || '-' }}</div>
            </div>
        </div>
        <div class="col-12 lg:col-4 md:col-4 sm:col-6">
            <div class="input-main">
                <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
                    <span class="material-symbols-rounded text-2xl text-primary">pin_drop</span> Address Line 2
                </label>
                <div class="readonly-field font-medium text-700 p-2">{{
                    ProspectForm.value?.additional_street_suffix_name || '-' }}</div>
            </div>
        </div> -->
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">pin</span>
          House Number
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ ProspectOverviewForm.value?.house_number || "-" }}
        </div>
      </div>
    </div>
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">near_me</span>
          Street
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ ProspectOverviewForm.value?.street_name || "-" }}
        </div>
      </div>
    </div>

    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">home_pin</span>
          City
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ ProspectOverviewForm.value?.city_name || "-" }}
        </div>
      </div>
    </div>
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">map</span>
          Country
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ ProspectOverviewForm.value?.country || "-" }}
        </div>
      </div>
    </div>
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">location_on</span>
          State
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ prospectDetails?.[0]?.state || "-" }}
        </div>
      </div>
    </div>
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">code_blocks</span>
          Zip Code
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ ProspectOverviewForm.value?.postal_code || "-" }}
        </div>
      </div>
    </div>
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">fax</span>
          Fax Number
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ ProspectOverviewForm.value?.fax_number || "-" }}
        </div>
      </div>
    </div>

    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">phone_iphone</span>
          Mobile
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ prospectDetails?.[0]?.mobile || "-" }}
        </div>
      </div>
    </div>
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">phone</span>
          Phone
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ prospectDetails?.[0]?.phone_number || "-" }}
        </div>
      </div>
    </div>
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">check_circle</span>
          Status
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ prospectDetails?.[0]?.status || "-" }}
        </div>
      </div>
    </div>
  </div>
  <form *ngIf="isEditMode" [formGroup]="ProspectOverviewForm">
    <div class="p-fluid p-formgrid grid m-0">
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">person</span>
            Name
            <span class="text-red-500">*</span>
          </label>
          <input pInputText id="bp_full_name" type="text" formControlName="bp_full_name" placeholder="Name"
            [ngClass]="{ 'is-invalid': submitted && f['bp_full_name'].errors }" class="h-3rem w-full" />
          <div *ngIf="submitted && f['bp_full_name'].errors" class="invalid-feedback">
            <div *ngIf="
                submitted &&
                f['bp_full_name'].errors &&
                f['bp_full_name'].errors['required']
              ">
              Name is required.
            </div>
          </div>
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">mail</span>
            Email Address <span class="text-red-500">*</span>
          </label>
          <input pInputText id="email_address" type="text" formControlName="email_address" placeholder="Email Address"
            [ngClass]="{ 'is-invalid': submitted && f['email_address'].errors }" class="h-3rem w-full" />
          <div *ngIf="submitted && f['email_address'].errors" class="invalid-feedback">
            <div *ngIf="f['email_address'].errors['required']">
              Email is required.
            </div>
            <div *ngIf="f['email_address'].errors['email']">
              Email is invalid.
            </div>
          </div>
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">globe</span>
            Wesbite
          </label>
          <input pInputText id="website_url" type="text" formControlName="website_url" placeholder="Website"
            class="h-3rem w-full" [ngClass]="{ 'is-invalid': submitted && f['website_url'].errors }" />
          <div *ngIf="submitted && f['website_url'].errors" class="p-error">
            <div *ngIf="f['website_url'].errors['pattern']">
              Please enter a valid website URL.
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">supervisor_account</span> Owner
                    </label>
                    <input pInputText id="owner" type="text" formControlName="owner" placeholder="Owner"
                        class="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">pin_drop</span> Address Line
                    </label>
                    <input pInputText id="additional_street_prefix_name" type="text"
                        formControlName="additional_street_prefix_name" placeholder="Address Line 1"
                        class="h-3rem w-full" />
                </div>
            </div>
            <div class="col-12 lg:col-4 md:col-4 sm:col-6">
                <div class="input-main">
                    <label class="flex align-items-center gap-1 mb-2 font-medium">
                        <span class="material-symbols-rounded text-2xl text-600">pin_drop</span> Address Line 2
                    </label>
                    <input pInputText id="additional_street_suffix_name" type="text"
                        formControlName="additional_street_suffix_name" placeholder="Address Line 2"
                        class="h-3rem w-full" />
                </div>
            </div> -->
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">pin</span>
            House Number
          </label>
          <input pInputText id="house_number" type="text" formControlName="house_number" placeholder="House Number"
            class="h-3rem w-full" />
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">near_me</span>
            Street
          </label>
          <input pInputText id="street_name" type="text" formControlName="street_name" placeholder="Street"
            class="h-3rem w-full" />
        </div>
      </div>

      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">home_pin</span>
            City
          </label>
          <input pInputText id="city_name" type="text" formControlName="city_name" placeholder="City"
            class="h-3rem w-full" />
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">map</span>
            Country <span class="text-red-500">*</span>
          </label>
          <p-dropdown [options]="countries" optionLabel="name" optionValue="isoCode" [(ngModel)]="selectedCountry"
            (onChange)="onCountryChange()" [filter]="true" formControlName="country" [styleClass]="'h-3rem w-full'"
            placeholder="Select Country" [ngClass]="{ 'is-invalid': submitted && f['country'].errors }">
          </p-dropdown>
          <div *ngIf="submitted && f['country'].errors" class="p-error">
            <div *ngIf="
                submitted &&
                f['country'].errors &&
                f['country'].errors['required']
              ">
              Country is required.
            </div>
          </div>
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">location_on</span>
            State <span class="text-red-500">*</span>
          </label>
          <p-dropdown [options]="states" optionLabel="name" optionValue="isoCode" [(ngModel)]="selectedState"
            formControlName="region" placeholder="Select State" [disabled]="!selectedCountry"
            [styleClass]="'h-3rem w-full'" [ngClass]="{ 'is-invalid': submitted && f['region'].errors }">
          </p-dropdown>
          <div *ngIf="submitted && f['region'].errors" class="p-error">
            <div *ngIf="
                submitted &&
                f['region'].errors &&
                f['region'].errors['required']
              ">
              State is required.
            </div>
          </div>
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">code_blocks</span>
            Zip Code <span class="text-red-500">*</span>
          </label>
          <input pInputText id="postal_code" type="text" formControlName="postal_code" placeholder="Zip Code"
            class="h-3rem w-full" [ngClass]="{ 'is-invalid': submitted && f['postal_code'].errors }" />
          <div *ngIf="submitted && f['postal_code'].errors" class="p-error">
            <div *ngIf="
                submitted &&
                f['postal_code'].errors &&
                f['postal_code'].errors['required']
              ">
              Zip Code is required.
            </div>
          </div>
          <div
            *ngIf="ProspectOverviewForm.get('postal_code')?.touched && ProspectOverviewForm.get('postal_code')?.invalid">
            <div *ngIf="ProspectOverviewForm.get('postal_code')?.errors?.['pattern']" class="p-error">
              ZIP code must be exactly 5 letters or digits.
            </div>
          </div>
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">fax</span>
            Fax Number
          </label>
          <input pInputText id="fax_number" type="text" formControlName="fax_number" placeholder="Fax Number"
            class="h-3rem w-full" />
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">phone_iphone</span>
            Mobile
          </label>
          <div class="flex align-items-center gap-2">
            <app-country-wise-mobile [formGroup]="ProspectOverviewForm" controlName="country" phoneFieldName="mobile"
              [selectedCountry]="selectedCountryForMobile"
              (validationResult)="mobileValidationMessage = $event"></app-country-wise-mobile>
            <input pInputText id="mobile" type="text" formControlName="mobile" placeholder="Mobile"
              class="h-3rem w-full" (input)="triggerMobileValidation()" />
          </div>
          <div class="p-error" *ngIf="ProspectOverviewForm.get('mobile')?.touched">
            <div *ngIf="mobileValidationMessage">{{ mobileValidationMessage }}</div>
          </div>
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">phone</span>
            Phone <span class="text-red-500">*</span>
          </label>
          <div class="flex align-items-center gap-2">
            <app-country-wise-mobile [formGroup]="ProspectOverviewForm" controlName="country"
              phoneFieldName="phone_number" [selectedCountry]="selectedCountryForMobile"
              (validationResult)="phoneValidationMessage = $event"></app-country-wise-mobile>
            <input pInputText id="phone_number" type="text" formControlName="phone_number" placeholder="Phone"
              class="h-3rem w-full" [ngClass]="{ 'is-invalid': submitted && f['phone_number'].errors }"
              (input)="triggerMobileValidation()" />
          </div>
          <div class="p-error">
            <div *ngIf="(ProspectOverviewForm.get('phone_number')?.touched || submitted) && phoneValidationMessage">
              {{ phoneValidationMessage }}
            </div>
            <div
              *ngIf="(ProspectOverviewForm.get('phone_number')?.touched || submitted) && f['phone_number'].errors?.required">
              Phone is required.
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex align-items-center p-3 gap-3 mt-1">
      <button pButton type="submit" label="Save" class="p-button-rounded justify-content-center w-9rem h-3rem"
        (click)="onSubmit()"></button>
    </div>
  </form>
</div>
<div class="p-3 w-full bg-white border-round shadow-1 mt-5">
  <div class="card-heading mb-2 flex align-items-center justify-content-start gap-2">
    <h4 class="m-0 pl-3 left-border relative flex">Marketing Attributes</h4>
    <p-button [label]="isAttributeEditMode ? 'Close' : 'Edit'" [icon]="!isAttributeEditMode ? 'pi pi-pencil' : ''"
      iconPos="right" class="ml-auto" [styleClass]="'w-5rem font-semibold px-3'" (click)="toggleAttributeEdit()"
      [rounded]="true" />
  </div>
  <div *ngIf="!isAttributeEditMode" class="p-fluid p-formgrid grid m-0">
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">pool</span>
          Pool
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ marketingDetails?.pool || "-" }}
        </div>
      </div>
    </div>

    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">restaurant</span>
          Restaurant
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ marketingDetails?.restaurant || "-" }}
        </div>
      </div>
    </div>
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">meeting_room</span>
          Conference Room
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ marketingDetails?.conference_room || "-" }}
        </div>
      </div>
    </div>
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">fitness_center</span>
          Fitness Center / Gym
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ marketingDetails?.fitness_center || "-" }}
        </div>
      </div>
    </div>
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">bar_chart</span>
          STR Chain Scale
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ getChainScaleLabel(
          customer?.free_defined_attribute_03
          ) || "-"}}
        </div>
      </div>
    </div>
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">scale</span>
          Size
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ marketingDetails?.size || "-" }}
        </div>
      </div>
    </div>
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">straighten</span>
          Size Unit
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ getSizeUnitLabel(
          customer?.free_defined_attribute_04
          ) || "-"}}
        </div>
      </div>
    </div>
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">build</span>
          Renovation Date
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ marketingDetails?.renovation_date || "-" }}
        </div>
      </div>
    </div>
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">schedule</span>
          Date Opened
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ marketingDetails?.date_opened || "-" }}
        </div>
      </div>
    </div>
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">event</span>
          Seasonal Open Date
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ marketingDetails?.seasonal_open_date || "-" }}
        </div>
      </div>
    </div>
    <div class="col-12 lg:col-4 md:col-4 sm:col-6">
      <div class="input-main">
        <label class="flex align-items-center gap-2 mb-2 text-800 font-semibold">
          <span class="material-symbols-rounded text-2xl text-primary">event</span>
          Seasonal Close Date
        </label>
        <div class="readonly-field font-medium text-700 p-2">
          {{ marketingDetails?.seasonal_close_date || "-" }}
        </div>
      </div>
    </div>
  </div>
  <form *ngIf="isAttributeEditMode" [formGroup]="ProspectAttributeForm">
    <div class="p-fluid p-formgrid grid m-0">
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">pool</span>
            Pool
          </label>
          <p-dropdown [options]="marketingoptions" formControlName="pool" placeholder="Select a Pool"
            optionLabel="label" optionValue="value" styleClass="h-3rem w-full">
          </p-dropdown>
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">restaurant</span>
            Restaurant
          </label>
          <p-dropdown [options]="marketingoptions" formControlName="restaurant" placeholder="Select a Restaurant"
            optionLabel="label" optionValue="value" styleClass="h-3rem w-full">
          </p-dropdown>
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">meeting_room</span>
            Conference Room
          </label>
          <p-dropdown [options]="marketingoptions" formControlName="conference_room"
            placeholder="Select a Conference Room" optionLabel="label" optionValue="value" styleClass="h-3rem w-full">
          </p-dropdown>
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">fitness_center</span>
            Fitness Center / Gym
          </label>
          <p-dropdown [options]="marketingoptions" formControlName="fitness_center"
            placeholder="Select a Fitness Center / Gym" optionLabel="label" optionValue="value"
            styleClass="h-3rem w-full">
          </p-dropdown>
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">build</span>
            Renovation Date
          </label>
          <p-calendar formControlName="renovation_date" dateFormat="yy-mm-dd" placeholder="Renovation Date"
            [showIcon]="true" styleClass="h-3rem w-full"></p-calendar>
        </div>
      </div>

      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">schedule</span>
            Date Opened
          </label>
          <p-calendar formControlName="date_opened" dateFormat="yy-mm-dd" placeholder="Date Opened" [showIcon]="true"
            styleClass="h-3rem w-full"></p-calendar>
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">event</span>
            Seasonal Open Date
          </label>
          <p-dropdown [options]="months" formControlName="seasonal_open_date" placeholder="Select a Seasonal Open Date"
            optionLabel="label" optionValue="value" styleClass="h-3rem w-full">
          </p-dropdown>
        </div>
      </div>
      <div class="col-12 lg:col-4 md:col-4 sm:col-6">
        <div class="input-main">
          <label class="flex align-items-center gap-1 mb-2 font-medium">
            <span class="material-symbols-rounded text-2xl text-600">event</span>
            Seasonal Close Date
          </label>
          <p-dropdown [options]="months" formControlName="seasonal_close_date"
            placeholder="Select a Seasonal Close Date" optionLabel="label" optionValue="value"
            styleClass="h-3rem w-full">
          </p-dropdown>
        </div>
      </div>
    </div>
    <div class="flex align-items-center p-3 gap-3 mt-1">
      <button pButton type="submit" label="Save" class="p-button-rounded justify-content-center w-9rem h-3rem"
        (click)="onAttributeSubmit()"></button>
    </div>
  </form>
</div>