import type { <PERSON><PERSON>hang<PERSON>, SimpleChang<PERSON> } from '@angular/core';
import type { ControlV<PERSON>ueAccessor, FormControl, ValidationErrors, Validator } from '@angular/forms';
import type { CustomKeyboardEvent } from './custom-keyboard-event';
import type { NgxMaskConfig } from './ngx-mask.config';
import { NgxMaskService } from './ngx-mask.service';
import * as i0 from "@angular/core";
export declare class NgxMaskDirective implements ControlValueAccessor, OnChanges, Validator {
    mask: import("@angular/core").InputSignal<string | null | undefined>;
    specialCharacters: import("@angular/core").InputSignal<string[] | readonly string[]>;
    patterns: import("@angular/core").InputSignal<Record<string, {
        pattern: RegExp;
        optional?: boolean;
        symbol?: string;
    }>>;
    prefix: import("@angular/core").InputSignal<string>;
    suffix: import("@angular/core").InputSignal<string>;
    thousandSeparator: import("@angular/core").InputSignal<string>;
    decimalMarker: import("@angular/core").InputSignal<"." | "," | [".", ","]>;
    dropSpecialCharacters: import("@angular/core").InputSignal<boolean | string[] | readonly string[] | null>;
    hiddenInput: import("@angular/core").InputSignal<boolean | null>;
    showMaskTyped: import("@angular/core").InputSignal<boolean | null>;
    placeHolderCharacter: import("@angular/core").InputSignal<string | null>;
    shownMaskExpression: import("@angular/core").InputSignal<string | null>;
    clearIfNotMatch: import("@angular/core").InputSignal<boolean | null>;
    validation: import("@angular/core").InputSignal<boolean | null>;
    separatorLimit: import("@angular/core").InputSignal<string | null>;
    allowNegativeNumbers: import("@angular/core").InputSignal<boolean | null>;
    leadZeroDateTime: import("@angular/core").InputSignal<boolean | null>;
    leadZero: import("@angular/core").InputSignal<boolean | null>;
    triggerOnMaskChange: import("@angular/core").InputSignal<boolean | null>;
    apm: import("@angular/core").InputSignal<boolean | null>;
    inputTransformFn: import("@angular/core").InputSignal<import("./ngx-mask.config").InputTransformFn | null>;
    outputTransformFn: import("@angular/core").InputSignal<import("./ngx-mask.config").OutputTransformFn | null>;
    keepCharacterPositions: import("@angular/core").InputSignal<boolean | null>;
    instantPrefix: import("@angular/core").InputSignal<boolean | null>;
    maskFilled: import("@angular/core").OutputEmitterRef<void>;
    private _maskValue;
    private _inputValue;
    private _position;
    private _code;
    private _maskExpressionArray;
    private _justPasted;
    private _isFocused;
    /**For IME composition event */
    private _isComposing;
    _maskService: NgxMaskService;
    private readonly document;
    protected _config: NgxMaskConfig;
    onChange: (_: any) => void;
    onTouch: () => void;
    ngOnChanges(changes: SimpleChanges): void;
    validate({ value }: FormControl): ValidationErrors | null;
    onPaste(): void;
    onFocus(): void;
    onModelChange(value: string | undefined | null | number): void;
    onInput(e: CustomKeyboardEvent): void;
    onCompositionStart(): void;
    onCompositionEnd(e: CustomKeyboardEvent): void;
    onBlur(e: CustomKeyboardEvent): void;
    onClick(e: MouseEvent | CustomKeyboardEvent): void;
    onKeyDown(e: CustomKeyboardEvent): void;
    /** It writes the value in the input */
    writeValue(controlValue: unknown): Promise<void>;
    registerOnChange(fn: typeof this.onChange): void;
    registerOnTouched(fn: typeof this.onTouch): void;
    private _getActiveElement;
    checkSelectionOnDeletion(el: HTMLInputElement): void;
    /** It disables the input element */
    setDisabledState(isDisabled: boolean): void;
    private _applyMask;
    private _validateTime;
    private _getActualInputLength;
    private _createValidationError;
    private _setMask;
    private _areAllCharactersInEachStringSame;
    static ɵfac: i0.ɵɵFactoryDeclaration<NgxMaskDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<NgxMaskDirective, "input[mask], textarea[mask]", ["mask", "ngxMask"], { "mask": { "alias": "mask"; "required": false; "isSignal": true; }; "specialCharacters": { "alias": "specialCharacters"; "required": false; "isSignal": true; }; "patterns": { "alias": "patterns"; "required": false; "isSignal": true; }; "prefix": { "alias": "prefix"; "required": false; "isSignal": true; }; "suffix": { "alias": "suffix"; "required": false; "isSignal": true; }; "thousandSeparator": { "alias": "thousandSeparator"; "required": false; "isSignal": true; }; "decimalMarker": { "alias": "decimalMarker"; "required": false; "isSignal": true; }; "dropSpecialCharacters": { "alias": "dropSpecialCharacters"; "required": false; "isSignal": true; }; "hiddenInput": { "alias": "hiddenInput"; "required": false; "isSignal": true; }; "showMaskTyped": { "alias": "showMaskTyped"; "required": false; "isSignal": true; }; "placeHolderCharacter": { "alias": "placeHolderCharacter"; "required": false; "isSignal": true; }; "shownMaskExpression": { "alias": "shownMaskExpression"; "required": false; "isSignal": true; }; "clearIfNotMatch": { "alias": "clearIfNotMatch"; "required": false; "isSignal": true; }; "validation": { "alias": "validation"; "required": false; "isSignal": true; }; "separatorLimit": { "alias": "separatorLimit"; "required": false; "isSignal": true; }; "allowNegativeNumbers": { "alias": "allowNegativeNumbers"; "required": false; "isSignal": true; }; "leadZeroDateTime": { "alias": "leadZeroDateTime"; "required": false; "isSignal": true; }; "leadZero": { "alias": "leadZero"; "required": false; "isSignal": true; }; "triggerOnMaskChange": { "alias": "triggerOnMaskChange"; "required": false; "isSignal": true; }; "apm": { "alias": "apm"; "required": false; "isSignal": true; }; "inputTransformFn": { "alias": "inputTransformFn"; "required": false; "isSignal": true; }; "outputTransformFn": { "alias": "outputTransformFn"; "required": false; "isSignal": true; }; "keepCharacterPositions": { "alias": "keepCharacterPositions"; "required": false; "isSignal": true; }; "instantPrefix": { "alias": "instantPrefix"; "required": false; "isSignal": true; }; }, { "maskFilled": "maskFilled"; }, never, never, true, never>;
}
