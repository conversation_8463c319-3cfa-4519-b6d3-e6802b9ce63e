{"ast": null, "code": "// `parsePhoneNumber()` named export has been renamed to `parsePhoneNumberWithError()`.\nexport { parsePhoneNumberWithError, parsePhoneNumberWithError as parsePhoneNumber } from './exports/parsePhoneNumberWithError.js';\n// `parsePhoneNumberFromString()` named export is now considered legacy:\n// it has been promoted to a default export due to being too verbose.\nexport { parsePhoneNumber as parsePhoneNumberFromString, parsePhoneNumber as default } from './exports/parsePhoneNumber.js';\nexport { isValidPhoneNumber } from './exports/isValidPhoneNumber.js';\nexport { isPossiblePhoneNumber } from './exports/isPossiblePhoneNumber.js';\nexport { validatePhoneNumberLength } from './exports/validatePhoneNumberLength.js';\n\n// Deprecated.\nexport { findNumbers } from './exports/findNumbers.js';\nexport { searchNumbers } from './exports/searchNumbers.js';\nexport { findPhoneNumbersInText } from './exports/findPhoneNumbersInText.js';\nexport { searchPhoneNumbersInText } from './exports/searchPhoneNumbersInText.js';\nexport { PhoneNumberMatcher } from './exports/PhoneNumberMatcher.js';\nexport { AsYouType } from './exports/AsYouType.js';\nexport { isSupportedCountry } from './exports/isSupportedCountry.js';\nexport { getCountries } from './exports/getCountries.js';\nexport { getCountryCallingCode } from './exports/getCountryCallingCode.js';\nexport { getExtPrefix } from './exports/getExtPrefix.js';\nexport { Metadata } from './exports/Metadata.js';\nexport { getExampleNumber } from './exports/getExampleNumber.js';\nexport { formatIncompletePhoneNumber } from './exports/formatIncompletePhoneNumber.js';\nexport { PhoneNumber } from './exports/PhoneNumber.js';\nexport { ParseError, parseIncompletePhoneNumber, parsePhoneNumberCharacter, parseDigits, parseRFC3966, formatRFC3966, DIGIT_PLACEHOLDER } from '../core/index.js';", "map": {"version": 3, "names": ["parsePhoneNumberWithError", "parsePhoneNumber", "parsePhoneNumberFromString", "default", "isValidPhoneNumber", "isPossiblePhoneNumber", "validatePhoneNumberLength", "findNumbers", "searchNumbers", "findPhoneNumbersInText", "searchPhoneNumbersInText", "PhoneNumberMatcher", "AsYouType", "isSupportedCountry", "getCountries", "getCountryCallingCode", "getExtPrefix", "<PERSON><PERSON><PERSON>", "getExampleNumber", "formatIncompletePhoneNumber", "PhoneNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseIncompletePhoneNumber", "parsePhoneNumberCharacter", "parseDigits", "parseRFC3966", "formatRFC3966", "DIGIT_PLACEHOLDER"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/max/index.js"], "sourcesContent": ["// `parsePhoneNumber()` named export has been renamed to `parsePhoneNumberWithError()`.\r\nexport { parsePhoneNumberWithError, parsePhoneNumberWithError as parsePhoneNumber } from './exports/parsePhoneNumberWithError.js'\r\n// `parsePhoneNumberFromString()` named export is now considered legacy:\r\n// it has been promoted to a default export due to being too verbose.\r\nexport { parsePhoneNumber as parsePhoneNumberFromString, parsePhoneNumber as default } from './exports/parsePhoneNumber.js'\r\n\r\nexport { isValidPhoneNumber } from './exports/isValidPhoneNumber.js'\r\nexport { isPossiblePhoneNumber } from './exports/isPossiblePhoneNumber.js'\r\nexport { validatePhoneNumberLength } from './exports/validatePhoneNumberLength.js'\r\n\r\n// Deprecated.\r\nexport { findNumbers } from './exports/findNumbers.js'\r\nexport { searchNumbers } from './exports/searchNumbers.js'\r\n\r\nexport { findPhoneNumbersInText } from './exports/findPhoneNumbersInText.js'\r\nexport { searchPhoneNumbersInText } from './exports/searchPhoneNumbersInText.js'\r\nexport { PhoneNumberMatcher } from './exports/PhoneNumberMatcher.js'\r\n\r\nexport { AsYouType } from './exports/AsYouType.js'\r\n\r\nexport { isSupportedCountry } from './exports/isSupportedCountry.js'\r\nexport { getCountries } from './exports/getCountries.js'\r\nexport { getCountryCallingCode } from './exports/getCountryCallingCode.js'\r\nexport { getExtPrefix } from './exports/getExtPrefix.js'\r\n\r\nexport { Metadata } from './exports/Metadata.js'\r\nexport { getExampleNumber } from './exports/getExampleNumber.js'\r\n\r\nexport { formatIncompletePhoneNumber } from './exports/formatIncompletePhoneNumber.js'\r\nexport { PhoneNumber } from './exports/PhoneNumber.js'\r\n\r\nexport {\r\n\tParseError,\r\n\tparseIncompletePhoneNumber,\r\n\tparsePhoneNumberCharacter,\r\n\tparseDigits,\r\n\tparseRFC3966,\r\n\tformatRFC3966,\r\n\tDIGIT_PLACEHOLDER\r\n} from '../core/index.js'\r\n"], "mappings": "AAAA;AACA,SAASA,yBAAyB,EAAEA,yBAAyB,IAAIC,gBAAgB,QAAQ,wCAAwC;AACjI;AACA;AACA,SAASA,gBAAgB,IAAIC,0BAA0B,EAAED,gBAAgB,IAAIE,OAAO,QAAQ,+BAA+B;AAE3H,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,qBAAqB,QAAQ,oCAAoC;AAC1E,SAASC,yBAAyB,QAAQ,wCAAwC;;AAElF;AACA,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,aAAa,QAAQ,4BAA4B;AAE1D,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,wBAAwB,QAAQ,uCAAuC;AAChF,SAASC,kBAAkB,QAAQ,iCAAiC;AAEpE,SAASC,SAAS,QAAQ,wBAAwB;AAElD,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,qBAAqB,QAAQ,oCAAoC;AAC1E,SAASC,YAAY,QAAQ,2BAA2B;AAExD,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,gBAAgB,QAAQ,+BAA+B;AAEhE,SAASC,2BAA2B,QAAQ,0CAA0C;AACtF,SAASC,WAAW,QAAQ,0BAA0B;AAEtD,SACCC,UAAU,EACVC,0BAA0B,EAC1BC,yBAAyB,EACzBC,WAAW,EACXC,YAAY,EACZC,aAAa,EACbC,iBAAiB,QACX,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}