{"version": 3, "file": "stripIddPrefix.test.js", "names": ["describe", "it", "stripIddPrefix", "metadata", "should", "equal", "expect", "to", "be", "undefined"], "sources": ["../../source/helpers/stripIddPrefix.test.js"], "sourcesContent": ["import stripIddPrefix from './stripIddPrefix.js'\r\n\r\nimport metadata from '../../metadata.min.json' assert { type: 'json' }\r\n\r\ndescribe('stripIddPrefix', () => {\r\n\tit('should strip a valid IDD prefix', () => {\r\n\t\tstripIddPrefix('01178005553535', 'US', '1', metadata).should.equal('78005553535')\r\n\t})\r\n\r\n\tit('should strip a valid IDD prefix (no country calling code)', () => {\r\n\t\tstripIddPrefix('011', 'US', '1', metadata).should.equal('')\r\n\t})\r\n\r\n\tit('should strip a valid IDD prefix (valid country calling code)', () => {\r\n\t\tstripIddPrefix('0117', 'US', '1', metadata).should.equal('7')\r\n\t})\r\n\r\n\tit('should strip a valid IDD prefix (not a valid country calling code)', () => {\r\n\t\texpect(stripIddPrefix('0110', 'US', '1', metadata)).to.be.undefined\r\n\t})\r\n})"], "mappings": ";;AAAA;;AAEA;;;;AAEAA,QAAQ,CAAC,gBAAD,EAAmB,YAAM;EAChCC,EAAE,CAAC,iCAAD,EAAoC,YAAM;IAC3C,IAAAC,0BAAA,EAAe,gBAAf,EAAiC,IAAjC,EAAuC,GAAvC,EAA4CC,uBAA5C,EAAsDC,MAAtD,CAA6DC,KAA7D,CAAmE,aAAnE;EACA,CAFC,CAAF;EAIAJ,EAAE,CAAC,2DAAD,EAA8D,YAAM;IACrE,IAAAC,0BAAA,EAAe,KAAf,EAAsB,IAAtB,EAA4B,GAA5B,EAAiCC,uBAAjC,EAA2CC,MAA3C,CAAkDC,KAAlD,CAAwD,EAAxD;EACA,CAFC,CAAF;EAIAJ,EAAE,CAAC,8DAAD,EAAiE,YAAM;IACxE,IAAAC,0BAAA,EAAe,MAAf,EAAuB,IAAvB,EAA6B,GAA7B,EAAkCC,uBAAlC,EAA4CC,MAA5C,CAAmDC,KAAnD,CAAyD,GAAzD;EACA,CAFC,CAAF;EAIAJ,EAAE,CAAC,oEAAD,EAAuE,YAAM;IAC9EK,MAAM,CAAC,IAAAJ,0BAAA,EAAe,MAAf,EAAuB,IAAvB,EAA6B,GAA7B,EAAkCC,uBAAlC,CAAD,CAAN,CAAoDI,EAApD,CAAuDC,EAAvD,CAA0DC,SAA1D;EACA,CAFC,CAAF;AAGA,CAhBO,CAAR"}