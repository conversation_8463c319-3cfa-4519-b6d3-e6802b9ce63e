{"ast": null, "code": "// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\n// importing system which is even uncapable of importing \"*.json\" files.\nimport metadata from '../../metadata.max.json.js';\nimport { Metadata as _Metadata } from '../../core/index.js';\nexport function Metadata() {\n  return _Metadata.call(this, metadata);\n}\nMetadata.prototype = Object.create(_Metadata.prototype, {});\nMetadata.prototype.constructor = Metadata;", "map": {"version": 3, "names": ["metadata", "<PERSON><PERSON><PERSON>", "_Metadata", "call", "prototype", "Object", "create", "constructor"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-CRM/client/node_modules/libphonenumber-js/max/exports/Metadata.js"], "sourcesContent": ["// Importing from a \".js\" file is a workaround for Node.js \"ES Modules\"\r\n// importing system which is even uncapable of importing \"*.json\" files.\r\nimport metadata from '../../metadata.max.json.js'\r\n\r\nimport { Metadata as _Metadata } from '../../core/index.js'\r\n\r\nexport function Metadata() {\r\n\treturn _Metadata.call(this, metadata)\r\n}\r\n\r\nMetadata.prototype = Object.create(_Metadata.prototype, {})\r\nMetadata.prototype.constructor = Metadata"], "mappings": "AAAA;AACA;AACA,OAAOA,QAAQ,MAAM,4BAA4B;AAEjD,SAASC,QAAQ,IAAIC,SAAS,QAAQ,qBAAqB;AAE3D,OAAO,SAASD,QAAQA,CAAA,EAAG;EAC1B,OAAOC,SAAS,CAACC,IAAI,CAAC,IAAI,EAAEH,QAAQ,CAAC;AACtC;AAEAC,QAAQ,CAACG,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACJ,SAAS,CAACE,SAAS,EAAE,CAAC,CAAC,CAAC;AAC3DH,QAAQ,CAACG,SAAS,CAACG,WAAW,GAAGN,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}